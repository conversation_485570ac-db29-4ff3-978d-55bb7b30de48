# Alto CERO Energy Management System (EMS) - Frontend

A comprehensive Energy Management System frontend for monitoring, analyzing, and optimizing energy consumption across multiple buildings and facilities.

## Quick Start

```bash
# Clone repository
git clone <repository-url>
cd alto-cero-ems/frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

Access the application at `http://localhost:5173`

## Key Features

- **Real-time Monitoring** - Track electricity consumption across all meters and buildings
- **Advanced Analytics** - Analyze consumption patterns, trends, and anomalies
- **Alarm Management** - Configure alerts for abnormal consumption with email notifications
- **Interactive Diagrams** - Visualize power distribution and meter hierarchy
- **Comparison Tools** - Compare performance across meters, buildings, and time periods
- **Comprehensive Reporting** - Generate detailed energy consumption reports
- **Training Center** - Built-in training modules for all user roles
- **Role-based Access** - Secure access control for different user types

## Technology Stack

- **Framework**: React 18.3 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Charts**: ECharts + Recharts
- **Icons**: Lucide React
- **State Management**: React Context API
- **Routing**: React Router

## Project Structure

```
frontend/
├── src/
│   ├── components/       # Reusable UI components
│   ├── pages/           # Page components
│   ├── hooks/           # Custom React hooks
│   ├── lib/             # Utilities and API clients
│   ├── types/           # TypeScript type definitions
│   └── styles/          # Global styles and CSS
├── public/              # Static assets
└── dist/               # Production build output
```

## Development Setup

1. **Prerequisites**
   - Node.js 18+
   - npm or yarn

2. **Available Scripts**
   ```bash
   npm run dev      # Start development server
   npm run build    # Build for production
   npm run preview  # Preview production build
   npm run lint     # Run ESLint
   npm test         # Run tests
   ```

3. **Development Server**
   - Application: `http://localhost:5173`
   - Hot Module Replacement (HMR) enabled
   - TypeScript type checking

## Support

For technical support or questions:
- **Contact**: Jirayut Chatphet
- **Phone**: ************
- **Email**: <EMAIL>

## License

 2025 AltoTech Global. All Rights Reserved.

This software, known as the "Alto CERO Smart Meter Platform," is proprietary and confidential. 
The source code, design, and documentation are the exclusive property of AltoTech Global.

Unauthorized copying, distribution, modification, public display, or public performance of this proprietary software, 
in whole or in part, is strictly prohibited. This software is intended for use exclusively by authorized personnel 
of AltoTech Global and its licensed clients.

This software is provided "as is" without warranty of any kind, either expressed or implied, including, but not limited to, 
the implied warranties of merchantability and fitness for a particular purpose. In no event shall AltoTech Global be liable 
for any damages whatsoever including direct, indirect, incidental, consequential, loss of business profits, or special 
damages, even if AltoTech Global has been advised of the possibility of such damages.

For licensing inquiries, <NAME_EMAIL>
