version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
        - VITE_DJANGO_API_URL=${VITE_DJANGO_API_URL:-http://0.0.0.0:8001/api}
        - VITE_DJANGO_API_ACESS_TOKEN=a42f53895397abdf87f9d1020c1ff9f02880ec47
        - VITE_USE_MOCK_DATA=${USE_MOCK_DATA:-false}
        - VITE_API_BASE_URL=${API_BASE_URL:-/api}
    container_name: alto-cero-smart-meter-platform-frontend
    restart: unless-stopped
    ports:
      - "4173:4173"
    environment:
      - NODE_ENV=production
      - VITE_API_BASE_URL=${API_BASE_URL:-/api}
      - VITE_DJANGO_API_ACESS_TOKEN=a42f53895397abdf87f9d1020c1ff9f02880ec47
      - VITE_USE_MOCK_DATA=${USE_MOCK_DATA:-false}
      - VITE_DJANGO_API_URL=${VITE_DJANGO_API_URL:-http://0.0.0.0:8001/api}
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4173/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # Uncomment if you want to use the production Dockerfile with nginx
    # build:
    #   context: ./frontend
    #   dockerfile: Dockerfile.production

networks:
  app-network:
    driver: bridge
