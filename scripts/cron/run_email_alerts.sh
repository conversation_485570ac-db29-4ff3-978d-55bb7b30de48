#!/bin/bash

# Email Alert System Cron Job Script
# This script runs the email alert system to detect anomalies and send email notifications
# It should be scheduled to run periodically via cron

# Set environment variables
export DJANGO_SETTINGS_MODULE=config.settings

# Navigate to the backend directory
cd "$(dirname "$0")/../backend"

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# Set default values
DRY_RUN=false
ANOMALY_TYPE="all"
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --type)
            ANOMALY_TYPE="$2"
            shift
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        *)
            echo "Unknown option: $key"
            exit 1
            ;;
    esac
done

# Log file
LOG_DIR="logs"
mkdir -p $LOG_DIR
LOG_FILE="$LOG_DIR/email_alerts_$(date +%Y%m%d_%H%M%S).log"

# Run the email alert system
echo "Starting email alert system at $(date)" | tee -a "$LOG_FILE"

if [ "$VERBOSE" = true ]; then
    echo "Parameters:" | tee -a "$LOG_FILE"
    echo "  Dry Run: $DRY_RUN" | tee -a "$LOG_FILE"
    echo "  Anomaly Type: $ANOMALY_TYPE" | tee -a "$LOG_FILE"
fi

# Build the command
CMD="python manage.py run_email_alerts"

if [ "$DRY_RUN" = true ]; then
    CMD="$CMD --dry-run"
fi

if [ "$ANOMALY_TYPE" != "all" ]; then
    CMD="$CMD --type $ANOMALY_TYPE"
fi

if [ "$VERBOSE" = true ]; then
    CMD="$CMD --verbosity 2"
    echo "Running command: $CMD" | tee -a "$LOG_FILE"
    eval "$CMD" 2>&1 | tee -a "$LOG_FILE"
else
    echo "Running command: $CMD" >> "$LOG_FILE"
    eval "$CMD" >> "$LOG_FILE" 2>&1
fi

EXIT_CODE=$?

if [ $EXIT_CODE -eq 0 ]; then
    echo "Email alert system completed successfully at $(date)" | tee -a "$LOG_FILE"
else
    echo "Email alert system failed with exit code $EXIT_CODE at $(date)" | tee -a "$LOG_FILE"
fi

# Deactivate virtual environment if it was activated
if [ -d "venv" ]; then
    deactivate
fi

exit $EXIT_CODE
