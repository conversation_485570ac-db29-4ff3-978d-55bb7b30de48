const express = require('express');
const cors = require('cors');
const nodemailer = require('nodemailer');
const app = express();
const port = 3001;

// Configure middleware
app.use(cors());
app.use(express.json());

// Create a test SMTP transporter
// This is a fake transporter that will log emails instead of sending them
// since we don't have MailHog running
const transporter = nodemailer.createTransport({
  host: 'localhost',
  port: 1025,
  secure: false,
  tls: {
    rejectUnauthorized: false
  },
  // Use a logger instead of actually sending emails
  logger: true,
  debug: true,
  // This simulates email sending without requiring a real SMTP server
  // All emails will be "sent" successfully
  send: true,
  // Override the send function to just log the email
  sendMail: function(mail, callback) {
    console.log('Email would be sent:');
    console.log('To:', mail.data.to);
    console.log('Subject:', mail.data.subject);
    console.log('Text:', mail.data.text);
    
    // Simulate successful sending
    const info = {
      messageId: `<${Date.now()}@localhost>`,
      envelope: mail.data.envelope || {
        from: mail.data.from,
        to: mail.data.to
      },
      accepted: [mail.data.to],
      rejected: [],
      pending: [],
      response: '250 OK: Message accepted'
    };
    
    callback(null, info);
  }
});

// Test email endpoint
app.post('/api/email/test', async (req, res) => {
  try {
    const { to, subject, text, html } = req.body;
    
    if (!to || !subject || !text) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: to, subject, or text'
      });
    }
    
    // Send email (this will be logged rather than actually sent)
    const info = await transporter.sendMail({
      from: '<EMAIL>',
      to,
      subject,
      text,
      html: html || text
    });
    
    return res.status(200).json({
      success: true,
      messageId: info.messageId
    });
  } catch (error) {
    console.error('Email sending error:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Email settings endpoint
app.post('/api/email/settings', (req, res) => {
  // Just log the settings and return success
  console.log('Email settings updated:', req.body);
  return res.status(200).json({
    success: true
  });
});

// Start the server
app.listen(port, () => {
  console.log(`Email API server running at http://localhost:${port}`);
  console.log('Available endpoints:');
  console.log('- POST /api/email/test - Send a test email');
  console.log('- POST /api/email/settings - Update email settings');
});
