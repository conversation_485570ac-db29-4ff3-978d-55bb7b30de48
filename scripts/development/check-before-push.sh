#!/bin/bash

# Quick check script to run before pushing code
# Usage: ./check-before-push.sh

echo "🔍 Running pre-push checks..."
echo ""

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

# Function to check command result
check_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2 passed${NC}"
    else
        echo -e "${RED}❌ $2 failed${NC}"
        echo "Please fix errors before pushing!"
        exit 1
    fi
}

# 1. Frontend build check
echo "📦 Checking frontend build..."
cd frontend
npm run build > /dev/null 2>&1
check_result $? "Frontend build"

# 2. Docker build check (if Dock<PERSON> is running)
if docker info > /dev/null 2>&1; then
    echo "🐳 Checking Docker build..."
    docker build -t test-build . > /dev/null 2>&1
    check_result $? "Docker build"
    docker rmi test-build > /dev/null 2>&1
else
    echo "⚠️  Docker not running, skipping Docker build check"
fi

cd ..

echo ""
echo "✨ All checks passed! Safe to push."
echo ""
echo "To push your code:"
echo "  git push origin $(git branch --show-current)"