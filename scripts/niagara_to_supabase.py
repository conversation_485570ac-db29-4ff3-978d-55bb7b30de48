import os
import requests
import json
from supabase import create_client, Client
from dotenv import load_dotenv
from datetime import datetime, timezone
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables from .env file
load_dotenv()

# Configuration
NIAGARA_BASE_URL = os.getenv('NIAGARA_BASE_URL', 'http://your-niagara-server:8080')
NIAGARA_USERNAME = os.getenv('NIAGARA_USERNAME', 'your-username')
NIAGARA_PASSWORD = os.getenv('NIAGARA_PASSWORD', 'your-password')
SUPABASE_URL = os.getenv('SUPABASE_URL', 'http://localhost:8000') # Your self-hosted URL
SUPABASE_SERVICE_KEY = os.getenv('SUPABASE_SERVICE_KEY', 'your-service-key')

# Initialize Supabase client with service role key
try:
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)
    logging.info("Supabase client initialized successfully.")
except Exception as e:
    logging.error(f"Failed to initialize Supabase client: {e}")
    exit(1) # Exit if Supabase connection fails

# Load meters configuration from JSON file
def load_meters_config():
    """Load meter configurations from meters.json file."""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        meters_json_path = os.path.join(script_dir, 'meters.json')
        
        with open(meters_json_path, 'r') as f:
            meters = json.load(f)
            
        # Validate that each meter has required fields
        valid_meters = []
        for meter in meters:
            if all(key in meter for key in ['id', 'name', 'niagaraPath', 'valueType']):
                if meter['niagaraPath'] != 'PLEASE_FILL_IN_NIAGARA_PATH':
                    valid_meters.append(meter)
                else:
                    logging.warning(f"Skipping meter {meter['id']} ({meter['valueType']}) - Niagara path not configured")
            else:
                logging.warning(f"Skipping invalid meter configuration: {meter}")
                
        logging.info(f"Loaded {len(valid_meters)} valid meter configurations")
        return valid_meters
    except Exception as e:
        logging.error(f"Error loading meters configuration: {e}")
        return []

# Create a session object for persistent connections and cookie handling
session = requests.Session()

def authenticate_with_niagara() -> bool:
    """
    Authenticate with Niagara and store session cookies.
    Note: This is a simplified example. Actual authentication depends on your Niagara setup.
    This might involve different endpoints or mechanisms.
    Returns True if authentication is successful, False otherwise.
    """
    auth_url = f"{NIAGARA_BASE_URL}/j_security_check" # Adjust if different
    payload = {
        'j_username': NIAGARA_USERNAME,
        'j_password': NIAGARA_PASSWORD,
    }
    headers = {'Content-Type': 'application/x-www-form-urlencoded'}

    try:
        response = session.post(auth_url, data=payload, headers=headers, timeout=10)
        response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)

        # Check if login was successful (this depends on Niagara's response)
        # Example: Check for a redirect or specific content in the response
        if response.status_code == 200 and "Login successful" in response.text: # Adjust condition
             logging.info("Successfully authenticated with Niagara.")
             return True
        # Handle cases where login might succeed but redirect (status 302)
        elif response.history: # Check if there was a redirect
             logging.info("Successfully authenticated with Niagara (redirected).")
             return True
        else:
             logging.warning(f"Niagara authentication potentially failed. Status: {response.status_code}")
             # Log part of the response for debugging, be careful with sensitive info
             logging.debug(f"Niagara response snippet: {response.text[:200]}")
             return False # Or True depending on how Niagara handles success/failure

    except requests.exceptions.RequestException as e:
        logging.error(f"Niagara authentication error: {e}")
        return False

def fetch_meter_data(meter_config: dict) -> dict:
    """
    Fetch meter data from Niagara using the authenticated session.
    Note: Adjust the API endpoint and data processing based on your Niagara setup (oBIX, BACnet/WS, etc.).
    Returns a reading dictionary or None on failure.
    """
    # Example endpoint - adjust based on your Niagara API structure (e.g., oBIX)
    data_url = f"{NIAGARA_BASE_URL}{meter_config['niagaraPath']}" # Use the full path from config
    headers = {'Accept': 'application/json'}

    try:
        response = session.get(data_url, headers=headers, timeout=15)
        response.raise_for_status()
        data = response.json()

        # --- Process the data ---
        # This section is highly dependent on the structure of the JSON response from Niagara.
        current_time_iso = datetime.now(timezone.utc).isoformat()
        
        # Determine the unit based on valueType
        unit = 'kW' if meter_config['valueType'] == 'Power' else 'kWh'
        
        # Extract the value from the Niagara response
        # This is a simplified example - adjust based on your actual Niagara API response structure
        try:
            # Assuming the value is directly in the response
            # You may need to navigate the JSON structure differently
            value = float(data.get('value', 0))  # Default to 0 if key missing/invalid
            
            reading = {
                'meter_tridium_id': meter_config['id'],
                'timestamp': current_time_iso,
                'value': value,
                'unit': unit,
                'reading_type': meter_config['valueType']
            }
            
            logging.info(f"Fetched {meter_config['valueType']} reading for meter {meter_config['id']}: {value} {unit}")
            return reading
            
        except (ValueError, TypeError) as e:
            logging.warning(f"Could not parse value for {meter_config['id']} ({meter_config['valueType']}). Error: {e}")
            return None

    except requests.exceptions.RequestException as e:
        logging.error(f"Error fetching data for meter {meter_config['id']} ({meter_config['valueType']}): {e}")
        return None
    except Exception as e:
        logging.error(f"Error processing data for meter {meter_config['id']} ({meter_config['valueType']}): {e}")
        return None


def insert_readings_to_supabase(readings: list):
    """Insert a list of readings into the Supabase meter_readings table."""
    if not readings:
        logging.info("No readings to insert.")
        return

    try:
        # supabase-py >=2.0 returns a PostgrestResponse object
        response = supabase.table('meter_readings').insert(readings).execute()

        if response.error:
            logging.error(f"Supabase insert error: {response.error}")
        else:
            inserted_count = len(response.data or [])
            logging.info(
                f"Successfully inserted {inserted_count} readings into Supabase."
            )

    except Exception as e:
        logging.error(f"Error inserting readings to Supabase: {e}")

def main():
    logging.info("Starting Niagara to Supabase integration (Python script)...")

    # Load meter configurations
    meters = load_meters_config()
    if not meters:
        logging.error("No valid meter configurations found. Exiting.")
        return
    
    # Authenticate with Niagara first
    if not authenticate_with_niagara():
        logging.error("Niagara authentication failed. Exiting.")
        return # Stop if authentication fails

    # Process each meter configuration
    all_readings = []
    for meter_config in meters:
        logging.info(f"Fetching {meter_config['valueType']} data for meter {meter_config['id']} ({meter_config['name']})...")
        reading = fetch_meter_data(meter_config)
        if reading:
            all_readings.append(reading)

    # Insert all collected readings to Supabase
    insert_readings_to_supabase(all_readings)

    logging.info("Integration run completed.")

if __name__ == "__main__":
    main()
