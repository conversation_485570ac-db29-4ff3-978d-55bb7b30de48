/**
 * Niagara to Supabase Data Integration Script
 * 
 * This script fetches meter data from Tridium Niagara and inserts it into a Supabase database.
 * It's designed to run on a schedule (e.g., using cron or a similar scheduler).
 * 
 * Prerequisites:
 * - Node.js installed
 * - npm packages: @supabase/supabase-js, node-fetch, dotenv
 * - .env file with configuration (see below)
 * 
 * Install dependencies:
 * npm install @supabase/supabase-js node-fetch dotenv
 */

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fetch = require('node-fetch');

// Configuration (preferably from .env file)
const NIAGARA_BASE_URL = process.env.NIAGARA_BASE_URL || 'http://your-niagara-server:8080';
const NIAGARA_USERNAME = process.env.NIAGARA_USERNAME || 'your-username';
const NIAGARA_PASSWORD = process.env.NIAGARA_PASSWORD || 'your-password';
const SUPABASE_URL = process.env.SUPABASE_URL || 'http://localhost:8000';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || 'your-service-key';

// Initialize Supabase client with service role key for admin access
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// List of meters to fetch data for
const METERS = [
  { id: 'CH-02', name: 'Chiller 2', path: '/your/niagara/path/to/chiller2' },
  // Add more meters as needed
];

/**
 * Authenticate with Niagara and get session token
 * Note: This is a simplified example. Actual authentication depends on your Niagara setup.
 */
async function authenticateWithNiagara() {
  try {
    const response = await fetch(`${NIAGARA_BASE_URL}/j_security_check`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `j_username=${encodeURIComponent(NIAGARA_USERNAME)}&j_password=${encodeURIComponent(NIAGARA_PASSWORD)}`,
    });
    
    // Get cookies/session token from response
    const cookies = response.headers.get('set-cookie');
    return cookies;
  } catch (error) {
    console.error('Authentication error:', error);
    throw new Error('Failed to authenticate with Niagara');
  }
}

/**
 * Fetch meter data from Niagara
 * Note: This is a simplified example. Actual API endpoints and data format depend on your Niagara setup.
 * You may need to use oBIX, BACnet/WS, or other protocols supported by your Niagara instance.
 */
async function fetchMeterData(meter, authCookies) {
  try {
    // Example endpoint - adjust based on your Niagara API structure
    const response = await fetch(`${NIAGARA_BASE_URL}/api${meter.path}/points`, {
      headers: {
        'Cookie': authCookies,
        'Accept': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}`);
    }
    
    const data = await response.json();
    
    // Process the data - this will vary based on your Niagara's response format
    // Example: Extract power and energy values
    const readings = [
      {
        meter_tridium_id: meter.id,
        timestamp: new Date().toISOString(),
        value: parseFloat(data.power) || 0,
        unit: 'kW'
      },
      {
        meter_tridium_id: meter.id,
        timestamp: new Date().toISOString(),
        value: parseFloat(data.energy) || 0,
        unit: 'kWh'
      }
    ];
    
    return readings;
  } catch (error) {
    console.error(`Error fetching data for meter ${meter.id}:`, error);
    return [];
  }
}

/**
 * Insert readings into Supabase
 */
async function insertReadingsToSupabase(readings) {
  if (!readings || readings.length === 0) {
    console.log('No readings to insert');
    return;
  }
  
  try {
    const { data, error } = await supabase
      .from('meter_readings')
      .insert(readings);
      
    if (error) {
      throw error;
    }
    
    console.log(`Successfully inserted ${readings.length} readings`);
  } catch (error) {
    console.error('Error inserting readings to Supabase:', error);
  }
}

/**
 * Main function to run the integration
 */
async function main() {
  try {
    console.log('Starting Niagara to Supabase integration...');
    
    // Authenticate with Niagara
    const authCookies = await authenticateWithNiagara();
    console.log('Successfully authenticated with Niagara');
    
    // Process each meter
    let allReadings = [];
    for (const meter of METERS) {
      console.log(`Fetching data for meter ${meter.id} (${meter.name})...`);
      const readings = await fetchMeterData(meter, authCookies);
      allReadings = [...allReadings, ...readings];
    }
    
    // Insert all readings to Supabase
    await insertReadingsToSupabase(allReadings);
    
    console.log('Integration completed successfully');
  } catch (error) {
    console.error('Integration failed:', error);
  }
}

// Run the main function
main();
