#!/bin/bash

# Deploy frontend with Docker

echo "🚀 Starting frontend deployment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker Desktop first."
    exit 1
fi

# Build the frontend
echo "📦 Building frontend..."
docker-compose -f docker-compose.frontend.yml build

# Start the frontend service
echo "🔄 Starting frontend service..."
docker-compose -f docker-compose.frontend.yml up -d

# Wait for health check
echo "⏳ Waiting for frontend to be healthy..."
sleep 10

# Check if container is running
if docker-compose -f docker-compose.frontend.yml ps | grep -q "Up"; then
    echo "✅ Frontend deployed successfully!"
    echo "🌐 Access the application at: http://localhost:4173"
    echo ""
    echo "📋 Useful commands:"
    echo "  - View logs: docker-compose -f docker-compose.frontend.yml logs -f"
    echo "  - Stop service: docker-compose -f docker-compose.frontend.yml down"
    echo "  - Rebuild: docker-compose -f docker-compose.frontend.yml build --no-cache"
else
    echo "❌ Frontend deployment failed. Check logs with:"
    echo "docker-compose -f docker-compose.frontend.yml logs"
    exit 1
fi