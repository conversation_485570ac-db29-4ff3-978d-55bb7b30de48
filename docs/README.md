# Documentation Index

This directory contains all project documentation except for the main README.md.

## Structure

### Core Documentation
- [CLAUDE.md](./CLAUDE.md) - Claude Code AI assistant instructions and project design principles
- [CLAUDE.local.md](./CLAUDE.local.md) - Local/private Claude instructions (user-specific)

### Setup Guides
- [EMAIL_SETUP.md](./setup/EMAIL_SETUP.md) - Email server configuration and setup instructions

### Frontend Documentation
- [ALARMS_DESIGN_UPDATE.md](./frontend/ALARMS_DESIGN_UPDATE.md) - Alarms system design documentation
- [ALARMS_TEST_PLAN.md](./frontend/ALARMS_TEST_PLAN.md) - Testing plan for alarms functionality
- [COLOR_CONTRAST_GUIDE.md](./frontend/COLOR_CONTRAST_GUIDE.md) - Color contrast and accessibility guidelines
- [MOCK_DATA_GUIDE.md](./frontend/MOCK_DATA_GUIDE.md) - Guide for implementing mock data
- [MOCK_DATA_IMPLEMENTATION.md](./frontend/MOCK_DATA_IMPLEMENTATION.md) - Mock data implementation details
- [UI_EXCELLENCE_PLAN.md](./frontend/UI_EXCELLENCE_PLAN.md) - Comprehensive UI improvement roadmap

### Project Plans
- [plan.md](./plans/plan.md) - Overall project plan
- [screenshot-plan.md](./plans/screenshot-plan.md) - Screenshot functionality planning

### Other Documentation
- [status/2025-05-06-status.md](./status/2025-05-06-status.md) - Project status report

## Quick Links

- **For developers**: Start with [CLAUDE.md](./CLAUDE.md) for coding guidelines
- **For UI work**: See [COLOR_CONTRAST_GUIDE.md](./frontend/COLOR_CONTRAST_GUIDE.md) and [UI_EXCELLENCE_PLAN.md](./frontend/UI_EXCELLENCE_PLAN.md)
- **For testing**: Review [ALARMS_TEST_PLAN.md](./frontend/ALARMS_TEST_PLAN.md)
- **For setup**: Check [EMAIL_SETUP.md](./setup/EMAIL_SETUP.md)