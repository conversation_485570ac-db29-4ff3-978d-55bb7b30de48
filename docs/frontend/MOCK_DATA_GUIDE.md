# Mock Data Guide

This guide explains how to use the mock data system in the Alto Cero EMS frontend.

## Overview

The application supports both mock data and real API connections, allowing developers to work on the frontend without a running backend server. The system is designed to make switching between mock and real data seamless.

## Features

- **Easy Toggle**: Switch between mock data and real API with a single toggle
- **Environment Configuration**: Use environment variables for default settings
- **Visual Indicator**: Shows when mock data is active
- **Consistent API**: Same API interface for both mock and real data
- **Realistic Data**: Mock data provides realistic responses for development

## Configuration

### Environment Variables

Create a `.env.development` file in the frontend directory:

```env
# Enable mock data by default in development
VITE_USE_MOCK_DATA=true

# API URL (used when mock data is disabled)
VITE_API_URL=http://localhost:8000/api
```

### In-App Toggle

You can toggle between mock data and real API:

1. Go to **Settings** page
2. Under **Development Settings**, use the **Mock Data** toggle
3. The app will reload to apply the change

### Programmatic Control

```typescript
import { apiClient } from '@/lib/api/enhancedApiClient';

// Check if using mock data
const isUsingMock = apiClient.isUsingMockData();

// Toggle mock data
apiClient.setUseMockData(true);  // Enable mock data
apiClient.setUseMockData(false); // Use real API
```

## Mock Data Service

The mock data service provides realistic data for all API endpoints:

### Available Endpoints

- **Dashboard**: `/dashboard` - Overall system metrics
- **Buildings**: `/buildings/:id` - Building-specific data
- **Meters**: `/meters/:id` - Meter readings and status
- **Analytics**: `/analytics` - Time-series analytics data
- **Alarms**: `/alarms` - Active alarms and history
- **Notifications**: `/notifications` - System notifications
- **Site**: `/site` - Site information
- **Performance**: `/performance` - Performance metrics

### Customizing Mock Data

Edit `src/lib/services/mockDataService.ts` to customize mock responses:

```typescript
// Example: Customize dashboard data
async getDashboardData() {
  await this.simulateDelay();
  
  return {
    totalConsumption: 1234567,
    currentDemand: 890,
    peakDemand: 1050,
    // Add or modify data as needed
  };
}
```

### Simulating Delays and Errors

Configure the mock service behavior:

```typescript
import { mockDataService } from '@/lib/services/mockDataService';

// Configure delays
mockDataService.configure({
  delay: 500,      // 500ms delay
  shouldFail: false,
  errorRate: 0.1   // 10% error rate
});
```

## API Client Usage

The enhanced API client automatically handles mock/real data switching:

```typescript
import { apiClient } from '@/lib/api/enhancedApiClient';

// Same code works for both mock and real data
const data = await apiClient.get('/dashboard');
```

### Using API Hooks

```typescript
import { useApi } from '@/lib/hooks/useApi';

function MyComponent() {
  const { data, loading, error, refetch } = useApi('/dashboard');
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return <div>{/* Display data */}</div>;
}
```

## Visual Indicators

When mock data is active:
- A **"Using Mock Data"** indicator appears in the bottom-right corner
- The Settings page shows the current data source

## Best Practices

1. **Keep API Code Unchanged**: Write API code normally, the system handles mock/real switching
2. **Maintain Mock Data**: Keep mock data realistic and up-to-date
3. **Test Both Modes**: Regularly test with both mock and real data
4. **Document Changes**: Update mock data when API changes occur

## Troubleshooting

### Mock Data Not Working

1. Check environment variables are set correctly
2. Ensure `.env.development` file is in the frontend directory
3. Restart the development server after changing environment variables

### Toggle Not Persisting

- The toggle saves preferences to localStorage
- Clear browser data if experiencing issues

### API Errors with Real Backend

1. Verify backend is running
2. Check API URL in environment variables
3. Ensure CORS is configured on the backend

## Development Workflow

1. Start with mock data enabled for rapid development
2. Implement features using mock responses
3. Switch to real API to test integration
4. Use mock data for demos and testing

## Adding New Mock Endpoints

1. Add handler in `mockDataService.ts`:

```typescript
async getNewEndpoint(params: any) {
  await this.simulateDelay();
  return {
    // Mock response data
  };
}
```

2. Map endpoint in `enhancedApiClient.ts`:

```typescript
const mockHandlers: Record<string, () => Promise<any>> = {
  'new-endpoint': () => mockDataService.getNewEndpoint(options.params),
  // ... other handlers
};
```

3. Create API wrapper function:

```typescript
export const getNewData = async (params: any) => {
  const response = await apiClient.get('/new-endpoint', params);
  return response;
};
```

## Migration to Production

1. Set `VITE_USE_MOCK_DATA=false` in production environment
2. Ensure all API endpoints are implemented in the backend
3. Test thoroughly with real data before deployment