# Color Contrast Guide

## Overview
This guide ensures all text in the application has proper contrast for accessibility and readability.

## Quick Reference

### ✅ ALWAYS USE THESE COMBINATIONS

#### Buttons
```tsx
// Primary button
className="bg-blue-600 text-white hover:bg-blue-700"

// Secondary button  
className="bg-gray-100 text-gray-700 hover:bg-gray-200"

// Outline button
className="bg-white text-gray-700 border border-gray-300 hover:bg-gray-50"
```

#### Status Indicators
```tsx
// Success
className="bg-green-100 text-green-800"

// Warning  
className="bg-yellow-100 text-yellow-800"

// Error
className="bg-red-100 text-red-800"

// Info
className="bg-blue-100 text-blue-800"
```

#### Text on Backgrounds
```tsx
// On white/light backgrounds (gray-50 to gray-300)
className="text-gray-700" // Normal text
className="text-gray-900" // Emphasized text
className="text-gray-600" // Muted text

// On dark backgrounds (gray-700+, blue-600+, etc)
className="text-white"

// On medium backgrounds (gray-400 to gray-600)
className="text-gray-900" // Use dark text, not white
```

### ❌ NEVER USE THESE COMBINATIONS

1. **White text on light backgrounds**
   - ❌ `bg-white text-white`
   - ❌ `bg-gray-50 text-white`
   - ❌ `bg-gray-100 text-white`
   - ❌ `bg-gray-200 text-white`
   - ❌ `bg-gray-300 text-white`
   - ❌ `bg-gray-400 text-white` (use text-gray-900 instead)

2. **Light gray text on light backgrounds**
   - ❌ `bg-white text-gray-100`
   - ❌ `bg-gray-50 text-gray-200`
   - ❌ `bg-gray-100 text-gray-300`

3. **White text on yellow/amber backgrounds**
   - ❌ `bg-yellow-100 text-white`
   - ❌ `bg-amber-200 text-white`
   - Use `text-yellow-800` or `text-amber-800` instead

## Using the Safe Color System

### 1. Import the utilities
```tsx
import { safeColors, COLOR_COMBINATIONS } from '@/lib/constants/colors';
```

### 2. Use predefined combinations
```tsx
// Using the safeColors function
<button className={safeColors({ base: 'primaryButton' })}>
  Click me
</button>

// Using COLOR_COMBINATIONS directly
<div className={COLOR_COMBINATIONS.cardDefault}>
  Card content
</div>
```

### 3. Manual combinations with validation
```tsx
import { validateContrast } from '@/lib/utils/safeColors';

// This will log a warning in development if contrast is poor
<div className={validateContrast("bg-gray-100 text-white")}>
  Text
</div>
```

## ESLint Integration

The project includes an ESLint rule that will catch contrast issues:

```bash
# Run linting to check for contrast issues
npm run lint
```

Example ESLint error:
```
error  Poor contrast: text-white on bg-gray-100 may be hard to read  local/no-poor-contrast
```

## Testing for Contrast

1. **Visual Testing**: Always check your UI in different lighting conditions
2. **Browser DevTools**: Use Chrome's Lighthouse for accessibility audits
3. **Automated Testing**: Our ESLint rules catch common issues

## Common Pitfalls

1. **Dynamic Classes**: Be careful with template literals
   ```tsx
   // ❌ Bad - ESLint might miss this
   className={`bg-${color}-100 text-white`}
   
   // ✅ Good - Use conditional logic
   className={color === 'gray' ? 'bg-gray-100 text-gray-700' : 'bg-blue-100 text-blue-800'}
   ```

2. **Hover States**: Ensure hover states maintain contrast
   ```tsx
   // ✅ Good
   className="bg-white text-gray-700 hover:bg-gray-100 hover:text-gray-900"
   ```

3. **Disabled States**: Even disabled elements need some contrast
   ```tsx
   // ✅ Good - Gray on gray but still readable
   className="bg-gray-100 text-gray-400"
   ```

## Questions?

If you're unsure about a color combination:
1. Check this guide
2. Use the `safeColors()` function
3. Run `npm run lint` to check
4. When in doubt, use darker text on lighter backgrounds