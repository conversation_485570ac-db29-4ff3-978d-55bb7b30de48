# Docker Deployment Guide

## Frontend Deployment

This guide explains how to deploy the Alto Cero Smart Meter Platform frontend using Docker.

### Prerequisites

1. Docker Desktop installed and running
2. Docker Compose installed (comes with Docker Desktop)
3. Port 4173 available on your machine

### Quick Start

```bash
# Deploy frontend with a single command
./deploy-frontend.sh
```

### Manual Deployment

1. **Build the Docker image:**
   ```bash
   docker-compose -f docker-compose.frontend.yml build
   ```

2. **Start the frontend service:**
   ```bash
   docker-compose -f docker-compose.frontend.yml up -d
   ```

3. **Access the application:**
   Open http://localhost:4173 in your browser

### Configuration

#### Environment Variables

The frontend can be configured using environment variables in `docker-compose.frontend.yml`:

- `NODE_ENV`: Set to `production` for production builds
- `VITE_API_BASE_URL`: Base URL for API calls (default: `/api`)
- `VITE_USE_MOCK_DATA`: Enable/disable mock data (default: `false`)
- `VITE_DJANGO_API_URL`: Backend API URL (default: `http://backend:8000/api`)

#### Ports

- Frontend: `4173` (configurable in docker-compose.frontend.yml)

### Docker Files Explained

1. **Dockerfile**: Simple production build using Node.js and serve
   - Multi-stage build for smaller image size
   - Non-root user for security
   - Health check included

2. **Dockerfile.production**: Advanced nginx-based deployment (optional)
   - Nginx for better performance
   - Gzip compression enabled
   - Security headers configured
   - Client-side routing support

3. **docker-compose.frontend.yml**: Standalone frontend service
   - Can be used independently
   - Includes health checks
   - Environment variable configuration

4. **docker-compose.yml**: Full stack deployment (includes backend services)
   - Frontend service integrated
   - Connected to postal-network
   - Ready for full application deployment

### Useful Commands

```bash
# View logs
docker-compose -f docker-compose.frontend.yml logs -f

# Stop the service
docker-compose -f docker-compose.frontend.yml down

# Rebuild without cache
docker-compose -f docker-compose.frontend.yml build --no-cache

# Check service status
docker-compose -f docker-compose.frontend.yml ps

# Enter container shell
docker exec -it alto-cero-smart-meter-platform-frontend sh

# View resource usage
docker stats alto-cero-smart-meter-platform-frontend
```

### Troubleshooting

1. **Port already in use:**
   ```bash
   # Find process using port 4173
   lsof -i :4173
   # Kill the process or change the port in docker-compose.frontend.yml
   ```

2. **Container fails to start:**
   ```bash
   # Check logs
   docker-compose -f docker-compose.frontend.yml logs
   # Verify build completed successfully
   docker-compose -f docker-compose.frontend.yml build
   ```

3. **Application not accessible:**
   - Ensure Docker is running
   - Check if container is healthy: `docker ps`
   - Verify port mapping: `docker port alto-cero-smart-meter-platform-frontend`

### Production Deployment

For production deployment:

1. Use `Dockerfile.production` for nginx-based serving
2. Set appropriate environment variables
3. Consider using Docker Swarm or Kubernetes for orchestration
4. Enable HTTPS with proper certificates
5. Configure proper logging and monitoring

### Security Considerations

- Non-root user in container
- No sensitive data in images
- Environment variables for configuration
- Health checks for monitoring
- Regular security updates

### Performance Tips

- Use multi-stage builds to reduce image size
- Enable gzip compression (included in nginx config)
- Configure proper caching headers
- Use CDN for static assets in production
- Monitor resource usage with `docker stats`