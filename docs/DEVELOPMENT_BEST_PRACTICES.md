# Development Best Practices

## Pre-Push Checklist

**Always test your code before pushing!** This prevents broken builds in CI/CD and helps maintain code quality.

### Quick Check (Recommended)

Run this before every push:

```bash
./check-before-push.sh
```

This script will:
- ✅ Test the frontend build
- ✅ Test the Docker build (if <PERSON><PERSON> is running)
- ✅ Report any errors before you push

### Manual Checks

If you prefer to run checks manually:

```bash
# 1. Test frontend build
cd frontend
npm run build

# 2. Test Docker build
docker build -t test-build .
docker rmi test-build  # Clean up

# 3. Run type checking
npm run typecheck

# 4. Check for issues
npm run lint
```

### Comprehensive Check

For a more detailed pre-push check:

```bash
cd frontend
./pre-push-check.sh
```

This will:
- Run TypeScript type checking
- Test the production build
- Test Docker build
- Check for console.log statements
- Check for TODO comments
- Optionally push if all checks pass

## Why This Matters

1. **Prevents Build Failures**: Catches errors before they reach CI/CD
2. **Saves Time**: No need to wait for CI/CD to fail and then fix
3. **Maintains Quality**: Ensures code is production-ready
4. **Team Efficiency**: Prevents blocking other developers

## Git Hooks (Optional)

A pre-push hook is installed that automatically runs build checks. To disable:

```bash
mv .git/hooks/pre-push .git/hooks/pre-push.disabled
```

To re-enable:

```bash
mv .git/hooks/pre-push.disabled .git/hooks/pre-push
```

## Common Issues

### Build Fails Locally but Works in Dev

- Check Node version: `node --version` (should be v22)
- Clear cache: `rm -rf node_modules package-lock.json && npm install`
- Check environment variables in `.env`

### Docker Build Fails

- Ensure Docker Desktop is running
- Check Docker resources (memory/CPU)
- Review `.dockerignore` file
- Check file permissions

### Type Errors

- Run `npm run typecheck` to see all errors
- Fix one file at a time
- Use `// @ts-ignore` sparingly and with explanation

## Commit Message Guidelines

Use clear, descriptive commit messages:

```
feat: Add PDF export for training slides
fix: Resolve case sensitivity in Card component import
chore: Update Docker configuration for frontend
docs: Add pre-push checklist to development guide
```

## Branch Strategy

1. Create feature branches from `main` or `dev`
2. Name branches descriptively: `feature/pdf-export`, `fix/docker-build`
3. Keep branches small and focused
4. Delete branches after merging

Remember: **A few minutes of checking saves hours of debugging!** 🚀