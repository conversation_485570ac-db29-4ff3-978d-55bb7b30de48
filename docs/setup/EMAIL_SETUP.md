# Alto CERO EMS Email Notification System

This document outlines the setup and usage of the email notification system for the Alto CERO Energy Management System.

## Overview

The email notification system uses Postal 3 for sending emails. Postal is a modern, open-source mail delivery platform designed for sending transactional emails from websites and web applications.

## Setup Instructions

### 1. Install Docker

Docker is required to run Postal. Follow these steps to install Docker on macOS:

1. Visit the [Docker Desktop for Mac download page](https://www.docker.com/products/docker-desktop/)
2. Download the appropriate version for your Mac (Apple Silicon or Intel)
3. Install Docker Desktop by dragging it to your Applications folder
4. Start Docker Desktop from your Applications folder

### 2. Start Postal with Docker Compose

The project includes a docker-compose.yml file configured for Postal 3:

```bash
# Navigate to your project directory
cd /Users/<USER>/CascadeProjects/alto-cero-ems\ bolt

# Start Postal and its dependencies
docker-compose up -d
```

This will start:
- MySQL database for Postal
- RabbitMQ for message queuing
- Postal 3 server on port 5002

### 3. Access Postal Admin Interface

After Postal starts:

1. Access the Postal web interface at http://localhost:5002
2. Log in with the pre-configured admin account:
   - Email: admin@localhost
   - Password: password123
3. Create a new mail server within the default organization
4. Generate an API key for server access

### 4. Update Environment Variables

Once you have your Postal API key and server ID, update the following environment variables:

```
POSTAL_BASE_URL=http://localhost:5002
POSTAL_API_KEY=your-postal-api-key
POSTAL_SERVER_ID=your-server-id
```

## Integration with Frontend

### 1. Configure the Email Client

Update your email client configuration in `frontend/src/lib/api/emailClient.ts`:

```typescript
import axios from 'axios';

const EMAIL_API_BASE = 'http://localhost:5002/api/v1';
const API_KEY = process.env.POSTAL_API_KEY || 'your-api-key';
const SERVER_ID = process.env.POSTAL_SERVER_ID || 'your-server-id';

export const emailClient = axios.create({
  baseURL: EMAIL_API_BASE,
  headers: {
    'Content-Type': 'application/json',
    'X-Server-API-Key': API_KEY
  }
});

export const sendEmail = async (to: string, subject: string, body: string) => {
  try {
    const response = await emailClient.post(`/send/message`, {
      to,
      from: '<EMAIL>',
      subject,
      html_body: body
    });
    return response.data;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
};
```

### 2. Update the Email API Service

Ensure your `frontend/src/lib/api/email.ts` service is configured to use the Postal API:

```typescript
import { emailClient, sendEmail } from './emailClient';

export const emailService = {
  sendTestEmail: async (email: string) => {
    return sendEmail(
      email,
      'Test Email from Alto CERO EMS',
      '<h1>Test Email</h1><p>This is a test email from your Alto CERO Energy Management System.</p>'
    );
  },
  
  sendAlertEmail: async (email: string, alertData: any) => {
    const subject = `Alert: ${alertData.type} - ${alertData.deviceName}`;
    const html = `
      <h1>Energy Alert</h1>
      <p>Type: ${alertData.type}</p>
      <p>Device: ${alertData.deviceName}</p>
      <p>Time: ${new Date(alertData.timestamp).toLocaleString()}</p>
      <p>Details: ${alertData.message}</p>
    `;
    return sendEmail(email, subject, html);
  },
  
  updateEmailSettings: async (settings: any) => {
    // Save email settings to your backend
    // This would typically call your own API, not Postal directly
  }
};
```

## Why Postal 3?

Postal 3 offers several advantages for our email delivery needs:

1. **Modern Architecture**
   - Built on Ruby 3.x and Rails 7.x
   - Container-first approach with Docker Compose
   - Clean API design patterns

2. **High Deliverability**
   - Built-in tools to improve email deliverability
   - DKIM, SPF, and DMARC support
   - Reputation tracking

3. **Integration via API**
   - RESTful API design
   - Clean separation from application database
   - Stateless communication model

4. **Monitoring and Analytics**
   - Detailed delivery tracking
   - Opens and click tracking
   - Bounce and complaint handling

## Usage

### Sending Test Emails

1. Navigate to the Alert Notifications page in the Alto CERO EMS application
2. Go to the Settings tab
3. Enable email notifications
4. Enter your email address
5. Click "Send Test Email"

### Email Settings

The following settings can be configured:

- Enable/disable email notifications
- Email address for notifications
- Notification types:
  - Abnormal consumption alerts
  - Device offline alerts
- Quiet hours (to prevent notifications during specific hours)

## Troubleshooting

- If emails are not being sent, check Postal logs: `docker-compose logs postal`
- Verify your API key and server ID are correct in environment variables
- Ensure the recipient email address is correctly formatted
- Check for any network connectivity issues between your application and Postal
