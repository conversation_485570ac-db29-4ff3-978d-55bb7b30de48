# Alto CERO EMS - Project Completion Plan

This document outlines the remaining tasks and phases to complete the Alto CERO Energy Management System project.

## Current Status Summary (April 2025)

- Core backend (Django) and frontend (React/Vite) structures are set up.
- Local development environment using Supabase Docker (PostgreSQL) is functional.
- Database connection and server startup issues are resolved.
- Basic API communication between frontend and backend is working.
- Meter reading ingestion via API endpoint (`/api/meters/readings/`) is functional and successfully upserts data to the local Supabase `latest_data` table.
- **TimescaleDB integrated:** `MeterReading` table converted to a hypertable.
- **Aggregated API endpoint created:** `/api/data/readings/aggregated/` provides time-bucketed data using TimescaleDB functions.
- Sample data loading script (`setup_sample_data`) is functional.
- Dashboard: Tower Overview section fetches live data from the backend API (though underlying performance metrics might be missing). Other sections use mock data.
- **UI Polishing completed:** Visual consistency applied across all components with proper spacing, typography, and color schemes.
- **Mock data implemented:** Comprehensive mock data for charts, alarms, and settings created to provide realistic visualization during frontend development and demonstrations.
- Alarms/Notifications: Features merged from previous PRs, mock data implemented for alarm history and settings, but API integration and end-to-end functionality need verification.

## Phases and Tasks

### Phase 1: Core Data Pipeline & Backend APIs

**Goal:** Ensure essential data is ingested, processed, and available via reliable backend APIs.

-   **TimescaleDB Setup:**
    -   [✓] Configure TimescaleDB extension.
    -   [✓] Convert `MeterReading` table to a TimescaleDB hypertable.
-   **Data Ingestion:**
    -   [ ] Verify/refine the process for getting `MeterReading` data into the system (currently manual via API or sample data script). Define how real-time/batch data will be pushed. (Consider `scripts/niagara_to_supabase.py` mentioned in old README if relevant).
    -   [✓] Implement sample data loading script (`setup_sample_data`).
-   **Data Processing/Aggregation:**
    -   [✓] **On-the-fly Aggregation:** Implemented API endpoint (`/api/data/readings/aggregated/`) using TimescaleDB `time_bucket` to provide hourly, daily, monthly, etc., aggregations directly from the `MeterReading` hypertable. *(Decision Point: Continue with this approach or implement background tasks to populate specific aggregate tables like `HourlyAggregate`? Current approach is simpler.)*
    -   [ ] Implement backend logic to calculate and populate `EnergyPerformanceMetric` data (e.g., `kwh_per_area`, `eui`, potentially total `kwh`) based on aggregates and building data.
-   **Backend API Development/Verification:**
    -   [✓] **Aggregated Data Endpoint (`/api/data/readings/aggregated/`):**
        -   Serves time-bucketed data (sum, avg, min, max) for specified intervals (minute, hour, day, week, month).
        -   Covers requirements for:
            -   **Load Profile:** (Hourly/Daily data)
            -   **Energy Benchmark:** (Monthly data)
            -   **Monthly Overview (Partial):** (Provides monthly consumption)
    -   [~] **Monthly Overview (Remaining):** Verify/add calculation for peak demand and cost to the aggregated endpoint or a separate one.
    -   [ ] **Consumption Distribution:** Create/verify API endpoint(s) for system breakdown data (needed for donut chart - requires specific backend calculation based on meter parameters/types).
    -   [ ] **Alarms:** Verify existing API endpoints (`/api/alarms/...`) for fetching active alarms, rules, and history. Ensure they work with the current backend state.
    -   [ ] **Meters:** Create/verify API endpoints for listing meters (perhaps with status) and viewing meter details (needed for Meters page/diagram).
    -   [ ] **Notifications/Settings:** Verify existing endpoints for fetching/saving notification preferences and email settings.
    -   [ ] **Billing/Reports:** Define and implement basic API endpoints if core data structures are ready.

### Phase 2: Core Frontend Implementation & Integration

**Goal:** Connect the main UI components to the backend APIs and display real data.

-   [ ] **Dashboard - Building Load Profile:** Replace mock data (`WORKDAY_LOAD_PROFILE`) with fetched data from the `/api/data/readings/aggregated/` endpoint (using appropriate interval, e.g., hourly).
-   [ ] **Dashboard - Energy Benchmark:** Replace mock data with fetched data from the `/api/data/readings/aggregated/` endpoint (using monthly interval).
-   [ ] **Dashboard - Consumption Distribution:** Replace mock data (`fetchSystemBreakdown`) with fetched data from the (to be created) System Breakdown API endpoint.
-   [ ] **Dashboard - Monthly Overview:** Replace mock data (`energyData`) with fetched data from the `/api/data/readings/aggregated/` endpoint (and potentially others for peak/cost).
-   [ ] **Alarms Page:** Connect UI components to verified Alarms API endpoints.
-   [ ] **Meters Page:** Connect meter list and detail views to Meters API endpoints.
-   [ ] **Settings/Notifications Page:** Connect UI components to verified Settings/Notifications API endpoints.
-   [✓] **Initial UI/UX Refinement:** Adjust components based on how real data looks and feels. Fix obvious layout or display issues.

### Phase 3: Advanced Features & Refinements

**Goal:** Implement more complex features and enhance usability.

-   [✓] **Analytics Page:** Implement detailed charts, comparisons, and filtering using backend APIs.
-   [✓] **Meters Diagram:** Implement the interactive single-line diagram feature.
-   [ ] **Billing System:** Fully implement tenant billing calculations, invoice generation, and UI.
-   [ ] **Reporting System:** Fully implement report generation and viewing features.
-   [ ] **Power Quality:** Implement features related to waveform/harmonics data if requirements exist and data is available.
-   [ ] **User Roles/Permissions:** Refine backend permissions and ensure frontend respects user roles appropriately.
-   [✓] **UI/UX Polish:** Address finer points of user experience, styling, and responsiveness.

### Phase 4: Testing & Deployment Preparation

**Goal:** Ensure application stability, quality, and readiness for deployment.

-   [ ] **Backend Unit Tests:** Write tests for models, serializers, views, and data processing logic.
-   [ ] **Backend Integration Tests:** Test API endpoint functionality and responses.
-   [ ] **Frontend Unit/Component Tests:** Write tests for key components and utility functions.
-   [~] **End-to-End Testing:** Perform manual testing of all major user flows.
-   [ ] **Performance Testing:** Basic load testing of critical API endpoints and database queries (especially the `aggregated` endpoint).
-   [ ] **Security Review:** Check for common web vulnerabilities (CSRF, XSS, SQL injection, insecure permissions).
-   [ ] **Documentation:**
    -   [ ] Update/finalize API documentation (including `aggregated` endpoint details).
    -   [✓] Write basic user guide/deployment instructions.
-   [ ] **Deployment Scripts:** Prepare necessary scripts or configuration files for deployment on Windows Server 2022 (consider Docker Compose for dependencies).

### Phase 5: Deployment & Post-Deployment

**Goal:** Deploy the application to the target environment and ensure smooth operation.

-   [ ] **Execute Deployment:** Deploy the backend (Django/WSGI), frontend (static build), and database (if migrating from local) to the Windows Server.
-   [ ] **Configure Production Environment:** Set up production environment variables, web server (e.g., IIS, Nginx), database connections.
-   [ ] **Post-Deployment Testing:** Verify functionality in the production environment.
-   [ ] **Monitoring:** Set up basic logging and monitoring for the deployed application.
-   [ ] **Initial Support & Bugfixing:** Address any issues arising after deployment.

## Immediate Next Steps

1.  Implement data fetching for the **Building Load Profile** component on the Dashboard frontend, using the `/api/data/readings/aggregated/` endpoint.
2.  Create the backend API endpoint for **Consumption Distribution** (donut chart).
3.  Begin verifying **Alarms API** endpoints.
4.  ✅ **Complete UI Polish & Mock Data** throughout the application to provide a visually consistent and realistic presentation for stakeholders.
5.  Run through the **UI Test Plan** to ensure all components display correctly and match design specifications.
6.  Document the mock data implementations to aid future integration with real backend data.