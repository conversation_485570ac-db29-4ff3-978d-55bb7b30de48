# EMS Application Screenshot Capture Plan

## Completed Screenshots
- ✅ Analytics page (`/analytics`)
- ✅ Meter Diagram page (`/meter-diagram`)
- ✅ Reports page (`/reports`)
- ✅ Settings page (`/settings`)
- ✅ Manual page (Manual component)

## Remaining Screenshots to Capture

### 1. Dashboard/Overview Page
**URL:** `/`
**Capture Notes:** 
- Ensure Building Load Profile chart is visible
- Display real-time energy consumption metrics
- Show status indicators and alerts if present
- Capture the minimal design aesthetic
- Recommended filename: `page-dashboard.png`

### 2. Consumption Page
**URL:** `/performance-analytics`
**Capture Notes:**
- Show the Tesla-inspired minimalist design
- Capture pill buttons for time period selection
- Ensure the main metrics cards are visible
- Display any consumption trends charts
- Recommended filename: `page-consumption.png`

### 3. Compare Page
**URL:** `/compare`
**Capture Notes:**
- Show comparative charts between different periods
- Capture the selection controls
- Ensure legends are visible for clarity
- Capture tooltip if possible
- Recommended filename: `page-compare.png`

### 4. Meters Page
**URL:** `/meters`
**Capture Notes:**
- Show the list of meters with status indicators
- Capture meter group organization
- Show power/energy values if displayed
- Ensure filter controls are visible
- Recommended filename: `page-meters.png`

### 5. Billing Page
**URL:** `/billing`
**Capture Notes:**
- Capture cost breakdown sections
- Show billing period selection if present
- Display any cost charts or summaries
- Capture invoice sections if present
- Recommended filename: `page-billing.png`

### 6. Alerts Page
**URL:** `/alert-notifications`
**Capture Notes:**
- Show notification configuration options
- Capture alert thresholds if visible
- Show any active alerts in the system
- Display notification history if present
- Recommended filename: `page-alerts.png`

## Instructions for Capturing Screenshots
1. Navigate to each page in the EMS application
2. Ensure all relevant data is loaded
3. Capture full-page screenshots
4. Save files using recommended filenames to `/frontend/public/images/manual/`
5. Update the corresponding manual data file to include the new screenshot

## Integration Process
After capturing each screenshot:
1. Copy the screenshot to the public directory: `/frontend/public/images/manual/`
2. Update the respective manual data file to include the new image
3. Use the following image object format:
```typescript
{
  src: '/images/manual/page-name.png',
  alt: 'Page Name',
  caption: 'Description of the page and its features',
  width: '90%'
}
```
