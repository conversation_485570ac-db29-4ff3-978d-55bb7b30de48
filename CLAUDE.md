# Claude Code Memory

## Dashboard Design Principles

These principles should guide all dashboard component design and improvements to ensure cohesive, user-friendly interfaces:

### 1. Single-row metrics where possible
- Compress information to maximize chart/content display space
- Avoid unnecessary vertical text stacking
- Example: "Live Power: 450 kW" instead of separate label and value rows

### 2. Meaningful time comparisons
- Use same day/week patterns rather than simple day-to-day comparisons
- Energy usage patterns are more consistent week-over-week (same weekday)
- Example: Compare Monday to last Monday, not Monday to Sunday

### 3. Clean typography with proper hierarchy
- Clear, descriptive labels with consistent font sizes and weights
- Primary values bold, secondary info lighter
- Units and context in smaller, muted text

### 4. Efficient use of vertical space
- Minimize gaps between components
- Maximize data visualization area
- Reduce chart margins when safe to do so

### 5. Consistent visual styling
- Unified colors, spacing, shadows, and backgrounds across all components
- Gradient treatments and visual enhancements applied consistently
- Remove confusing or ambiguous indicators

### 6. CRITICAL: How to Fix White Text Visibility Issues
When encountering invisible or hard-to-read white text:

#### Understand the Design Intent FIRST
- If a component has `text-white` or `color: '#ffffff'`, white text is INTENTIONAL
- The issue is likely an incorrect background color, NOT the text color
- Look for design patterns - if similar components use dark backgrounds with white text, that's the intended design

#### Identify the Root Cause
- White text on white/light background = Background is wrong, NOT the text
- Common patterns: `bg-white text-white`, `bg-gray-100 text-white`, `bg-blue-100 text-white`
- Check if Tailwind classes aren't rendering (common in modals, overlays, slides)

#### Fix the Background, NOT the Text
```tsx
// ❌ WRONG APPROACH - Don't change the text color
<div className="bg-white text-gray-800">Content</div>

// ✅ CORRECT APPROACH - Fix the background color
<div style={{ backgroundColor: '#1e40af', color: '#ffffff' }}>Content</div>
```

#### Safe Dark Colors for White Text
- Blue: `#1e40af` (blue-800)
- Green: `#059669` (green-600)
- Red: `#dc2626` (red-600)
- Purple: `#7c3aed` (purple-600)
- Gray: `#374151` (gray-700)

#### Use Inline Styles for Critical UI Elements
- Training slides, modals, overlays often need inline styles
- Tailwind classes may not render properly in these contexts
- Inline styles guarantee the exact colors are applied

### 6. Color Contrast Requirements
- NEVER use white text on light backgrounds (gray-50 through gray-400)
- NEVER use white text on light color backgrounds (blue/green/red-50 through -400)
- Avoid -500 color variants with white text (especially yellow-500, orange-500, amber-500)
- Always use inline styles for critical UI elements to ensure colors render correctly
- Always ensure WCAG AA contrast ratios (4.5:1 for normal text)
- Use predefined color combinations from `@/lib/constants/colors` and `@/lib/constants/buttonColors`
- Use slide styles from `@/components/training/SlideStyles` for training materials
- Run `npm run lint:contrast` to check for contrast issues before committing
- Pre-commit hooks will automatically check for contrast issues
- See docs/frontend/COLOR_CONTRAST_GUIDE.md for detailed guidelines

#### Special Requirements for Training Slides
- ALWAYS use inline styles for colored backgrounds in training slides
- NEVER rely on Tailwind classes like `bg-blue-600 text-white` for critical elements
- For gradient backgrounds, always use inline styles with explicit color values
- When using colored backgrounds, explicitly set text color on ALL child elements
- Safe dark colors for white text: `#1e40af` (blue), `#059669` (green), `#dc2626` (red), `#7c3aed` (purple)
- Example of correct usage:
  ```tsx
  <div style={{ backgroundColor: '#1e40af', color: '#ffffff' }}>
    <h3 style={{ color: '#ffffff' }}>Title</h3>
    <p style={{ color: '#ffffff' }}>Content</p>
  </div>
  ```

#### Why Inline Styles for Critical Elements
- Tailwind classes may not render properly in certain contexts (overlays, modals, slides)
- Inline styles guarantee the exact color values are applied
- Prevents white text from appearing on light backgrounds due to CSS cascade issues
- Ensures consistent rendering across different browsers and environments

## Project Commands

### Testing
- Run tests: `npm test` (frontend)
- Run linting: `npm run lint` (frontend)

### Development
- Start frontend: `npm run dev` (in frontend directory)
- Start backend: `python manage.py runserver` (in backend directory)