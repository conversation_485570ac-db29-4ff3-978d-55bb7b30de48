import tokens from './src/styles/design-tokens.js';

/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  safelist: [
    'bg-blue-600', 
    'hover:bg-blue-700', 
    'text-white',
    'bg-amber-500', 
    'hover:bg-amber-600',
    'bg-red-600', 
    'hover:bg-red-700',
    'bg-blue-500',
    'bg-blue-700',
    'bg-amber-700',
    'bg-red-500',
    'hover:bg-amber-700',
    'hover:bg-red-600',
    'hover:bg-blue-600'
  ],
  theme: {
    extend: {
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(-10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        },
        slideIn: {
          '0%': { opacity: '0', transform: 'translateX(-20px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' }
        },
        pulse: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '.5' }
        },
        shimmer: {
          '0%': { transform: 'translateX(-150%)' },
          '50%': { transform: 'translateX(150%)' },
          '100%': { transform: 'translateX(150%)' }
        },
        flow: {
          '0%': { transform: 'rotate(0deg) scale(1)' },
          '50%': { transform: 'rotate(180deg) scale(1.02)' },
          '100%': { transform: 'rotate(360deg) scale(1)' }
        }
      },
      animation: {
        fadeIn: 'fadeIn 0.3s ease-out',
        slideIn: 'slideIn 0.3s ease-out',
        pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        shimmer: 'shimmer 2s ease-in-out infinite',
        flow: 'flow 3s ease-in-out infinite'
      },
      colors: {
        ...tokens.colors.brand,
        ...tokens.colors.neutral,
        ...tokens.colors.semantic,
        'primary-blue': '#065BA9',
        'primary-white': '#FFFFFF',
        'primary-dark-grey': '#788796',
        'primary-black': '#212529',
        'background-main': '#F9FAFF',
        'background-light-grey': '#EDEFF9',
        'background-white-blue': '#DBE4FF',
        borderLight: '#EDEFF9', // Add custom light border color
        // Add semantic color names for badge variants
        destructive: {
          DEFAULT: '#E53935', // Red color for critical alerts
          foreground: 'white',
        },
        primary: {
          DEFAULT: '#065BA9', // Using the primary-blue
          foreground: 'white',
        },
        secondary: {
          DEFAULT: '#788796', // Using the primary-dark-grey
          foreground: 'white',
        },
      },
      spacing: {
        ...tokens.spacing,
      },
      borderRadius: {
        ...tokens.radii,
      },
      fontFamily: {
        ...tokens.typography.fontFamily,
      },
      fontSize: {
        ...tokens.typography.fontSize,
      },
      lineHeight: {
        ...tokens.typography.lineHeight,
      },
      boxShadow: {
        ...tokens.shadows,
      },
    },
  },
  plugins: [
    function ({ addUtilities }) { // Plugin for scrollbar-hide
      addUtilities({
        '.scrollbar-hide': {
          /* IE and Edge */
          '-ms-overflow-style': 'none',
          /* Firefox */
          'scrollbar-width': 'none',
          /* Safari and Chrome */
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        },
        '.scrollbar-thin': {
          'scrollbar-width': 'thin',
          'scrollbar-color': '#a0aec0 #e2e8f0', // gray-500 gray-300
          '&::-webkit-scrollbar': {
            width: '8px'
          },
          '&::-webkit-scrollbar-track': {
            background: '#e2e8f0' // gray-300
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#a0aec0', // gray-500
            borderRadius: '10px',
            border: '2px solid #e2e8f0' // gray-300
          }
        }
      })
    }
  ],
};