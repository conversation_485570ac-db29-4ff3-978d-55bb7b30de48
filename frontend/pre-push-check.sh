#!/bin/bash

# Pre-push check script for frontend
# This script should be run before pushing code to ensure everything builds correctly

echo "🔍 Running pre-push checks..."
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to handle errors
handle_error() {
    echo -e "${RED}❌ Pre-push check failed: $1${NC}"
    exit 1
}

# Function to show success
show_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to show warning
show_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# 1. Check if we're in the frontend directory
if [ ! -f "package.json" ]; then
    echo "Please run this script from the frontend directory"
    exit 1
fi

echo "📋 Starting pre-push checklist..."
echo ""

# 2. Run TypeScript type checking
echo "1️⃣  Running TypeScript type check..."
npm run typecheck || handle_error "TypeScript type errors found"
show_success "TypeScript check passed"
echo ""

# 3. Run the build
echo "2️⃣  Running production build..."
npm run build || handle_error "Build failed"
show_success "Production build successful"
echo ""

# 4. Check if Docker is running
echo "3️⃣  Checking Docker status..."
if ! docker info > /dev/null 2>&1; then
    show_warning "Docker is not running. Skipping Docker build test."
    echo "   To test Docker build, start Docker Desktop and run:"
    echo "   docker build -t frontend-test ."
else
    # 5. Test Docker build
    echo "4️⃣  Testing Docker build..."
    docker build -t frontend-test . || handle_error "Docker build failed"
    show_success "Docker build successful"
    
    # Clean up test image
    docker rmi frontend-test > /dev/null 2>&1
    echo ""
fi

# 6. Check for common issues
echo "5️⃣  Checking for common issues..."

# Check for console.log statements
CONSOLE_LOGS=$(grep -r "console.log" src/ --exclude-dir=node_modules 2>/dev/null | wc -l | tr -d ' ')
if [ "$CONSOLE_LOGS" -gt 0 ]; then
    show_warning "Found $CONSOLE_LOGS console.log statements in src/"
fi

# Check for TODO comments
TODOS=$(grep -r "TODO" src/ --exclude-dir=node_modules 2>/dev/null | wc -l | tr -d ' ')
if [ "$TODOS" -gt 0 ]; then
    show_warning "Found $TODOS TODO comments in src/"
fi

echo ""
echo "✨ All pre-push checks completed!"
echo ""
echo "📝 Recommended next steps:"
echo "   1. Review any warnings above"
echo "   2. Commit your changes: git add -A && git commit -m 'your message'"
echo "   3. Push to remote: git push origin your-branch"
echo ""

# Optional: Ask if user wants to push now
read -p "Would you like to push now? (y/n) " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    current_branch=$(git branch --show-current)
    echo "Pushing to origin/$current_branch..."
    git push origin "$current_branch"
    show_success "Push completed!"
else
    echo "Push cancelled. Run 'git push' when ready."
fi