{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "tsc && vite build", "typecheck": "tsc --noEmit", "lint": "eslint src/components/ui src/styles --ext .ts,.tsx,.js || true", "lint:fix": "eslint src/components/ui src/hooks src/styles --ext .ts,.tsx,.js --fix", "lint:contrast": "node scripts/check-contrast.js", "test": "echo \"No tests specified\" && exit 0", "preview": "vite preview"}, "dependencies": {"@dagrejs/dagre": "^1.1.4", "@headlessui/react": "^2.2.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-tooltip": "^1.2.0", "@react-pdf/renderer": "^4.3.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.71.5", "@tanstack/react-table": "^8.21.2", "@types/dagre": "^0.7.52", "@types/html2canvas": "^0.5.35", "@types/luxon": "^3.6.2", "@types/react-datepicker": "^6.2.0", "@types/react-window": "^1.8.8", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dagre": "^0.8.5", "date-fns": "^4.1.0", "echarts": "^5.5.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.344.0", "luxon": "^3.6.1", "react": "^18.3.1", "react-datepicker": "^8.2.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-router-dom": "^6.22.3", "react-window": "^1.8.11", "reactflow": "^11.11.4", "recharts": "^2.15.1", "tailwind-merge": "^3.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.14.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "msw": "^2.7.3", "netlify-cli": "^16.4.2", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "terser": "^5.29.2", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.18"}, "msw": {"workerDirectory": ["public"]}}