# Build stage
FROM node:22-alpine AS builder

WORKDIR /app

# Accept build arguments for environment variables
ARG VITE_DJANGO_API_URL
ARG VITE_USE_MOCK_DATA
ARG VITE_API_BASE_URL
ARG VITE_DJANGO_API_ACESS_TOKEN

# Set environment variables from build args
ENV VITE_DJANGO_API_URL=$VITE_DJANGO_API_URL
ENV VITE_USE_MOCK_DATA=$VITE_USE_MOCK_DATA
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_DJANGO_API_ACESS_TOKEN=$VITE_DJANGO_API_ACESS_TOKEN

# Copy package files
COPY package*.json ./

# Install all dependencies (including devDependencies for build)
RUN npm ci

# Copy all source files
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM node:22-alpine AS production

WORKDIR /app

# Install serve globally to serve static files
RUN npm install -g serve

# Copy built files from builder stage
COPY --from=builder /app/dist ./dist

# Create a non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Change ownership
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port 4173 to match docker-compose
EXPOSE 4173

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:4173/ || exit 1

# Serve the static files
CMD ["serve", "-s", "dist", "-l", "4173", "--no-clipboard"]