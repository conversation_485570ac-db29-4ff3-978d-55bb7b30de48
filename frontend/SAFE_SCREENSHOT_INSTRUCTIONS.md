# Safe Screenshot Capture Instructions

## Quick Start (Development Only)

1. **Enable Mock Data First**
   - Go to Settings page
   - Toggle "Use Mock Data" ON
   - This ensures no production data is visible

2. **Access Screenshot Tool**
   - Navigate to: http://localhost:5173/screenshot-capture
   - Or use the floating camera button on any page

3. **Capture Screenshots**
   - Click "Start Capture" for automatic capture
   - Or manually capture each page

4. **Save Screenshots**
   - Create folder: `frontend/public/images/training/`
   - Save with descriptive names (e.g., `dashboard-overview.png`)

## Testing Without Production Impact

The screenshot system is designed to be safe:
- Works only in development environment
- Uses mock data when enabled
- Doesn't affect any production systems
- No server calls when mock data is active

## Integration

Once screenshots are captured:
1. They'll automatically work in training slides
2. Fallback placeholders show if images missing
3. Test by viewing training modules

## Important Notes

- Always use mock data for screenshots
- Never capture real production data
- Review all screenshots before committing
- The system is completely isolated from production