#!/bin/sh

# This script allows runtime environment variable injection
# It replaces placeholders in the built files with actual environment values

# Function to replace environment variables in JavaScript files
replace_env_vars() {
    # Define the environment variables to replace
    # Add more variables as needed
    find /usr/share/nginx/html -name '*.js' -exec sed -i \
        -e "s|VITE_API_BASE_URL_PLACEHOLDER|${VITE_API_BASE_URL:-/api}|g" \
        -e "s|VITE_USE_MOCK_DATA_PLACEHOLDER|${VITE_USE_MOCK_DATA:-false}|g" \
        -e "s|VITE_DJANGO_API_URL_PLACEHOLDER|${VITE_DJANGO_API_URL:-http://backend:8000/api}|g" \
        {} \;
}

# Only run replacement if environment variables are set
if [ -n "$VITE_API_BASE_URL" ] || [ -n "$VITE_USE_MOCK_DATA" ] || [ -n "$VITE_DJANGO_API_URL" ]; then
    echo "Injecting runtime environment variables..."
    replace_env_vars
    echo "Environment variables injected successfully"
fi