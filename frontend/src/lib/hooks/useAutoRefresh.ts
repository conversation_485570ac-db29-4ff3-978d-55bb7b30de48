import { useState, useEffect, useCallback } from 'react';

interface UseAutoRefreshProps {
  enabled: boolean;
  interval?: number;
  onRefresh: () => void | Promise<void>;
  onError?: (error: Error) => void;
}

export function useAutoRefresh({ enabled, interval = 30000, onRefresh, onError }: UseAutoRefreshProps) {
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refresh = useCallback(async () => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    try {
      await onRefresh();
      setLastRefresh(new Date());
    } catch (error) {
      onError?.(error as Error);
    } finally {
      setIsRefreshing(false);
    }
  }, [onRefresh, onError, isRefreshing]);

  useEffect(() => {
    if (!enabled) return;

    const timer = setInterval(refresh, interval);
    return () => clearInterval(timer);
  }, [enabled, interval, refresh]);

  return {
    lastRefresh,
    refresh,
    isRefreshing
  };
}