import { useState, useEffect } from 'react';
import type { ApiResponse } from '../api/types';
import { api } from '../api/client';

export function useApi<T>(endpoint: string): ApiResponse<T | null> {
  const [state, setState] = useState<ApiResponse<T | null>>({
    data: null,
    error: null,
    loading: true,
  });

  useEffect(() => {
    const abortController = new AbortController();

    async function fetchData() {
      try {
        const data = await api.get<T>(endpoint, {
          signal: abortController.signal,
          cache: 'no-cache',
        });
        setState({ data, error: null, loading: false });
      } catch (error) {
        setState({
          data: null,
          error: error instanceof Error ? error.message : 'An error occurred',
          loading: false,
        });
      }
    }

    fetchData();

    return () => {
      abortController.abort();
    };
  }, [endpoint]);

  return state;
}