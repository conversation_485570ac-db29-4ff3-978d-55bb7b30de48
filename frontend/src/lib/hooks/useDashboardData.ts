import { useState, useEffect } from 'react';
import { apiClient } from '../api/enhancedApiClient';
import { useAutoRefresh } from './useAutoRefresh';

export interface DashboardData {
  totalConsumption: number;
  currentDemand: number;
  peakDemand: number;
  powerFactor: number;
  monthlyTrend: Array<{
    month: string;
    consumption: number;
  }>;
  systemBreakdown: {
    hvac: number;
    lighting: number;
    equipment: number;
    other: number;
  };
  alerts: Array<{
    id: string;
    severity: string;
    message: string;
    timestamp: string;
  }>;
}

export const useDashboardData = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get<DashboardData>('/dashboard');
      setData(response);
      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  };

  useAutoRefresh({ enabled: true, interval: 30000, onRefresh: fetchData });

  useEffect(() => {
    fetchData();
  }, []);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
  };
};