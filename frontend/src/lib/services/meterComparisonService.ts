import type { HistoricalDataQuery, HistoricalDataRecord, StatisticalRecord, EnergyRecord } from '../../services/timescaleService';
import { fetchStatisticalData as fetchStatisticalDataAPI, fetchEnergyData as fetchEnergyDataAPI } from '../../services/timescaleService';

// Environment variable to control mock vs production data
// Check if we should use mock data - same logic as enhancedApiClient
const savedPreference = typeof window !== 'undefined' ? localStorage.getItem('useMockData') : null;
const USE_MOCK_DATA = savedPreference !== null 
  ? savedPreference === 'true'
  : import.meta.env.VITE_USE_MOCK_DATA === 'true'; // Default to false

// Mock data generators
const generateMockStatisticalData = (query: HistoricalDataQuery): HistoricalDataRecord<StatisticalRecord>[] => {
  const { device_id, datapoints, start_timestamp, end_timestamp } = query;
  const devices = Array.isArray(device_id) ? device_id : [device_id];
  
  return devices.map(deviceId => {
    const startDate = new Date(start_timestamp);
    const endDate = new Date(end_timestamp);
    const records: StatisticalRecord[] = [];
    
    // Generate hourly data for daily view
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      // Use local hours directly - assuming the system is in Bangkok timezone
      const hour = currentDate.getHours();
      
      // Generate realistic power consumption patterns
      let basePower = 0;
      
      // Different patterns for different device types - Bangkok building patterns
      if (deviceId.includes('chiller')) {
        // Chiller: Higher during day, peaks at 1 PM (hour 13)
        if (hour >= 9 && hour <= 17) {
          // Peak load between 9 AM and 5 PM, with maximum at 1 PM
          const peakOffset = Math.abs(hour - 13); // Distance from 1 PM
          basePower = 1200 - (peakOffset * 100); // 1200 kW at 1 PM, decreasing by 100 kW per hour away
        } else if (hour >= 6 && hour < 9) {
          // Morning ramp up
          basePower = 400 + ((hour - 6) * 200);
        } else if (hour > 17 && hour <= 22) {
          // Evening ramp down
          basePower = 800 - ((hour - 17) * 100);
        } else {
          // Night time base load
          basePower = 300;
        }
      } else if (deviceId.includes('air')) {
        // Air Side: Similar pattern but lower values, peaks at 2 PM
        if (hour >= 10 && hour <= 16) {
          const peakOffset = Math.abs(hour - 14); // Distance from 2 PM
          basePower = 600 - (peakOffset * 50);
        } else if (hour >= 7 && hour < 10) {
          basePower = 200 + ((hour - 7) * 100);
        } else if (hour > 16 && hour <= 20) {
          basePower = 400 - ((hour - 16) * 50);
        } else {
          basePower = 150;
        }
      } else if (deviceId.includes('light')) {
        // Lighting: High during office hours
        basePower = hour >= 8 && hour <= 18 ? 300 : 50;
      } else if (deviceId.includes('data')) {
        // Data Center: Constant high load with slight variations
        basePower = 500 + Math.sin(hour * Math.PI / 12) * 50;
      } else if (deviceId.includes('ev')) {
        // EV Charger: Peaks during arrival/departure times
        basePower = (hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19) ? 200 : 20;
      } else {
        // Default pattern - office building
        if (hour >= 9 && hour <= 17) {
          basePower = 150 + Math.sin((hour - 9) * Math.PI / 8) * 50; // Peak around 1 PM
        } else {
          basePower = 50;
        }
      }
      
      // Add some randomness
      const variation = (Math.random() - 0.5) * basePower * 0.1;
      const meanValue = Math.max(0, basePower + variation);
      
      records.push({
        timestamp: currentDate.toISOString(),
        start_datetime: currentDate.toISOString(),
        end_datetime: new Date(currentDate.getTime() + 3600000).toISOString(), // +1 hour
        mean_value: meanValue,
        max_value: meanValue * 1.2,
        min_value: meanValue * 0.8,
        first_value: meanValue * 0.95,
        last_value: meanValue * 1.05
      });
      
      currentDate.setHours(currentDate.getHours() + 1);
    }
    
    return {
      site_id: query.site_id,
      device_id: deviceId,
      datapoint: datapoints[0] || 'power',
      model: 'mock_model',
      records
    };
  });
};

const generateMockEnergyData = (query: HistoricalDataQuery): HistoricalDataRecord<EnergyRecord>[] => {
  const { device_id, datapoints, start_timestamp, end_timestamp } = query;
  const devices = Array.isArray(device_id) ? device_id : [device_id];
  
  return devices.map(deviceId => {
    const startDate = new Date(start_timestamp);
    const endDate = new Date(end_timestamp);
    const records: EnergyRecord[] = [];
    
    // Generate daily data
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      let baseEnergy = 0;
      const dayOfWeek = currentDate.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
      
      // Different patterns for different device types
      if (deviceId.includes('chiller')) {
        baseEnergy = isWeekend ? 8000 : 15000;
      } else if (deviceId.includes('air')) {
        baseEnergy = isWeekend ? 4000 : 8000;
      } else if (deviceId.includes('light')) {
        baseEnergy = isWeekend ? 1000 : 5000;
      } else if (deviceId.includes('data')) {
        baseEnergy = 12000; // Constant for data center
      } else if (deviceId.includes('ev')) {
        baseEnergy = isWeekend ? 500 : 2000;
      } else {
        baseEnergy = isWeekend ? 2000 : 4000;
      }
      
      // Add seasonal variation
      const month = currentDate.getMonth();
      const seasonalFactor = 1 + Math.sin((month - 3) * Math.PI / 6) * 0.2; // Peak in summer
      
      // Add randomness
      const variation = (Math.random() - 0.5) * baseEnergy * 0.15;
      const value = Math.max(0, baseEnergy * seasonalFactor + variation);
      
      records.push({
        timestamp: currentDate.toISOString(),
        start_datetime: currentDate.toISOString(),
        end_datetime: new Date(currentDate.getTime() + 86400000).toISOString(), // +1 day
        value: value,
        first_value: value * 0.95,
        last_value: value * 1.05
      });
      
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return {
      site_id: query.site_id,
      device_id: deviceId,
      datapoint: datapoints[0] || 'daily_energy',
      model: 'mock_model',
      records
    };
  });
};

// Service interface
export interface MeterComparisonService {
  fetchStatisticalData: (query: HistoricalDataQuery) => Promise<HistoricalDataRecord<StatisticalRecord>[]>;
  fetchEnergyData: (query: HistoricalDataQuery) => Promise<HistoricalDataRecord<EnergyRecord>[]>;
  isUsingMockData: () => boolean;
}

// Mock service implementation
const mockService: MeterComparisonService = {
  fetchStatisticalData: async (query: HistoricalDataQuery) => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));
    return generateMockStatisticalData(query);
  },
  
  fetchEnergyData: async (query: HistoricalDataQuery) => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));
    return generateMockEnergyData(query);
  },
  
  isUsingMockData: () => true
};

// Production service implementation
const productionService: MeterComparisonService = {
  fetchStatisticalData: fetchStatisticalDataAPI,
  fetchEnergyData: fetchEnergyDataAPI,
  isUsingMockData: () => false
};

// Export the service based on environment - check dynamically
export const meterComparisonService: MeterComparisonService = {
  fetchStatisticalData: async (query: HistoricalDataQuery) => {
    // Check localStorage at runtime
    const currentUseMockData = localStorage.getItem('useMockData') === 'true';
    if (currentUseMockData) {
      return mockService.fetchStatisticalData(query);
    }
    return productionService.fetchStatisticalData(query);
  },
  
  fetchEnergyData: async (query: HistoricalDataQuery) => {
    // Check localStorage at runtime
    const currentUseMockData = localStorage.getItem('useMockData') === 'true';
    if (currentUseMockData) {
      return mockService.fetchEnergyData(query);
    }
    return productionService.fetchEnergyData(query);
  },
  
  isUsingMockData: () => {
    // Check localStorage at runtime
    return localStorage.getItem('useMockData') === 'true';
  }
};

// Export individual functions for easier use
export const fetchStatisticalData = meterComparisonService.fetchStatisticalData;
export const fetchEnergyData = meterComparisonService.fetchEnergyData;
export const isUsingMockData = meterComparisonService.isUsingMockData;