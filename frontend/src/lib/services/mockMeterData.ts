import { SYSTEM_ICONS } from '../../lib/constants';

type MeterIconKey = keyof typeof SYSTEM_ICONS;

export interface MockMeter {
  id: string;
  name: string;
  type: MeterIconKey;
  category: string;
  device_id: string;
  model: string;
}

// Mock meters for each category
export const MOCK_METERS: MockMeter[] = [
  // Chiller Plant meters
  {
    id: 'chiller-001',
    name: 'Chiller 1 - Main',
    type: 'chillerPlant',
    category: 'Chiller Plant',
    device_id: 'CHR-001',
    model: 'chiller_main'
  },
  {
    id: 'chiller-002',
    name: 'Chiller 2 - Backup',
    type: 'chillerPlant',
    category: 'Chiller Plant',
    device_id: 'CHR-002',
    model: 'chiller_backup'
  },
  {
    id: 'chiller-003',
    name: 'Cooling Tower 1',
    type: 'chillerPlant',
    category: 'Chiller Plant',
    device_id: 'CLT-001',
    model: 'cooling_tower'
  },
  {
    id: 'chiller-004',
    name: 'Chilled Water Pump',
    type: 'chillerPlant',
    category: 'Chiller Plant',
    device_id: 'CWP-001',
    model: 'water_pump'
  },
  
  // Air Side meters
  {
    id: 'air-001',
    name: 'AHU Floor 1',
    type: 'airSide',
    category: 'Air Side',
    device_id: 'AHU-F01',
    model: 'air_handler'
  },
  {
    id: 'air-002',
    name: 'AHU Floor 2',
    type: 'airSide',
    category: 'Air Side',
    device_id: 'AHU-F02',
    model: 'air_handler'
  },
  {
    id: 'air-003',
    name: 'AHU Floor 3',
    type: 'airSide',
    category: 'Air Side',
    device_id: 'AHU-F03',
    model: 'air_handler'
  },
  {
    id: 'air-004',
    name: 'FCU Zone A',
    type: 'airSide',
    category: 'Air Side',
    device_id: 'FCU-A01',
    model: 'fan_coil'
  },
  {
    id: 'air-005',
    name: 'FCU Zone B',
    type: 'airSide',
    category: 'Air Side',
    device_id: 'FCU-B01',
    model: 'fan_coil'
  },
  
  // Light & Power meters
  {
    id: 'light-001',
    name: 'Lighting Circuit 1',
    type: 'light_power',
    category: 'Light & Power',
    device_id: 'LGT-C01',
    model: 'lighting_circuit'
  },
  {
    id: 'light-002',
    name: 'Lighting Circuit 2',
    type: 'light_power',
    category: 'Light & Power',
    device_id: 'LGT-C02',
    model: 'lighting_circuit'
  },
  {
    id: 'power-001',
    name: 'Power Panel A',
    type: 'light_power',
    category: 'Light & Power',
    device_id: 'PWR-A01',
    model: 'power_panel'
  },
  {
    id: 'power-002',
    name: 'Power Panel B',
    type: 'light_power',
    category: 'Light & Power',
    device_id: 'PWR-B01',
    model: 'power_panel'
  },
  {
    id: 'power-003',
    name: 'Emergency Power',
    type: 'light_power',
    category: 'Light & Power',
    device_id: 'PWR-EMG',
    model: 'emergency_power'
  },
  
  // Data Center & Others meters
  {
    id: 'data-001',
    name: 'Server Room 1',
    type: 'data_center_others',
    category: 'Data Center & Others',
    device_id: 'DC-SR01',
    model: 'server_room'
  },
  {
    id: 'data-002',
    name: 'Server Room 2',
    type: 'data_center_others',
    category: 'Data Center & Others',
    device_id: 'DC-SR02',
    model: 'server_room'
  },
  {
    id: 'data-003',
    name: 'Network Equipment',
    type: 'data_center_others',
    category: 'Data Center & Others',
    device_id: 'DC-NET',
    model: 'network_equipment'
  },
  {
    id: 'other-001',
    name: 'Kitchen Equipment',
    type: 'data_center_others',
    category: 'Data Center & Others',
    device_id: 'OTH-KIT',
    model: 'kitchen'
  },
  
  // EV Charger meters
  {
    id: 'ev-001',
    name: 'EV Station 1',
    type: 'evCharger',
    category: 'EV Charger',
    device_id: 'EV-ST01',
    model: 'ev_charger'
  },
  {
    id: 'ev-002',
    name: 'EV Station 2',
    type: 'evCharger',
    category: 'EV Charger',
    device_id: 'EV-ST02',
    model: 'ev_charger'
  },
  {
    id: 'ev-003',
    name: 'EV Fast Charger',
    type: 'evCharger',
    category: 'EV Charger',
    device_id: 'EV-FC01',
    model: 'ev_fast_charger'
  },
  
  // Elevator & Escalator meters
  {
    id: 'elev-001',
    name: 'Elevator Bank A',
    type: 'escalator_elevator',
    category: 'Elevator & Escalator',
    device_id: 'ELV-A01',
    model: 'elevator'
  },
  {
    id: 'elev-002',
    name: 'Elevator Bank B',
    type: 'escalator_elevator',
    category: 'Elevator & Escalator',
    device_id: 'ELV-B01',
    model: 'elevator'
  },
  {
    id: 'esc-001',
    name: 'Escalator Main Lobby',
    type: 'escalator_elevator',
    category: 'Elevator & Escalator',
    device_id: 'ESC-ML01',
    model: 'escalator'
  }
];

// Category mapping for consistency
export const MOCK_CATEGORY_MAP: Record<string, string> = {
  "chiller_plant": "Chiller Plant",
  "air_distribution_system": "Air Side",
  "light_and_power": "Light & Power",
  "data_center_and_others": "Data Center & Others",
  "ev_charger": "EV Charger",
  "elevator_escalator": "Elevator & Escalator"
};