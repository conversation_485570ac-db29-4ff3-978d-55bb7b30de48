// Mock Data Service

// Define types needed for mock data
export type AlarmSeverity = 'INFO' | 'WARNING' | 'CRITICAL';

export enum AlarmStatus {
  ACTIVE = 'ACTIVE',
  ACKNOWLEDGED = 'ACKNOWLEDGED',
  RESOLVED = 'RESOLVED',
}

export enum NotificationCategory {
  ALARM = 'alarm',
  SYSTEM = 'system',
  INFO = 'info',
}

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

interface MockDataConfig {
  delay?: number;
  shouldFail?: boolean;
  errorRate?: number;
}

// Import mock device data
import { 
  mockDeviceDetailsMap, 
  mockDeviceRelations, 
  mockZones, 
  getAllMockDevices,
  getMockDevicesByModel 
} from '../api/mockDeviceData';

class MockDataService {
  private config: MockDataConfig = {
    delay: 50, // Reduced from 300ms to 50ms
    shouldFail: false,
    errorRate: 0,
  };

  configure(config: MockDataConfig) {
    this.config = { ...this.config, ...config };
  }

  async simulateDelay() {
    if (this.config.delay) {
      await delay(this.config.delay);
    }
  }

  private shouldSimulateError(): boolean {
    if (this.config.shouldFail) return true;
    if (this.config.errorRate && Math.random() < this.config.errorRate) return true;
    return false;
  }

  // Dashboard Data
  async getDashboardData() {
    await this.simulateDelay();
    
    if (this.shouldSimulateError()) {
      throw new Error('Mock API Error: Failed to fetch dashboard data');
    }

    const currentMonth = new Date().getMonth();
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    // Bangkok building consumption patterns - higher in hot months
    const monthlyConsumptionPattern = [
      1850000, // Jan - Cool season
      2100000, // Feb - Getting warmer  
      2450000, // Mar - Hot season begins
      2680000, // Apr - Peak heat
      2520000, // May - Still hot
      2350000, // Jun - Rainy season
      2280000, // Jul
      2200000, // Aug
      2150000, // Sep
      2050000, // Oct
      1950000, // Nov - Cool again
      1820000, // Dec - Cool season
    ];

    return {
      totalConsumption: 2350000 + Math.random() * 100000,
      currentDemand: 3200 + Math.random() * 200,
      peakDemand: 3450,
      powerFactor: 0.91 + Math.random() * 0.02,
      monthlyTrend: monthlyConsumptionPattern.slice(0, currentMonth + 1).map((consumption, index) => ({
        month: monthNames[index],
        consumption: consumption + Math.random() * 50000,
      })),
      systemBreakdown: {
        hvac: 1175000, // 50% for cooling in Bangkok
        lighting: 282000, // 12%
        equipment: 423600, // 18%
        other: 469400, // 20% (elevators, pumps, etc.)
      },
      alerts: [
        {
          id: '1',
          severity: 'CRITICAL' as AlarmSeverity,
          message: 'Chiller Plant 1 efficiency degradation detected',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          severity: 'WARNING' as AlarmSeverity,
          message: 'Power factor approaching MEA penalty threshold',
          timestamp: new Date().toISOString(),
        },
      ],
    };
  }

  // Building Data - SET Building
  async getBuildingData(buildingId: string) {
    await this.simulateDelay();
    
    const buildingData: Record<string, any> = {
      'A': {
        id: 'A',
        name: 'SET Tower A',
        floors: 33,
        totalArea: 48000,
        occupancy: 92,
        energyIntensity: 128.5, // kWh/m²/year for financial district office
        monthlyConsumption: 514000,
        buildingType: 'Financial Trading Center',
        coolingLoad: 350000,
      },
      'B': {
        id: 'B',
        name: 'SET Tower B',
        floors: 27,
        totalArea: 38000,
        occupancy: 88,
        energyIntensity: 135.2,
        monthlyConsumption: 428000,
        buildingType: 'Office/Data Center',
        coolingLoad: 320000,
      },
      'C': {
        id: 'C',
        name: 'SET Education Center',
        floors: 15,
        totalArea: 22000,
        occupancy: 75,
        energyIntensity: 118.8,
        monthlyConsumption: 217600,
        buildingType: 'Education/Conference',
        coolingLoad: 180000,
      },
    };

    return buildingData[buildingId] || buildingData['A'];
  }

  // Meter Data - Bangkok Building Configuration
  async getMeterData(meterId?: string) {
    await this.simulateDelay();
    
    const meterDetails: Record<string, any> = {
      'main-meter': {
        id: 'main-meter',
        name: 'Main Building Meter',
        type: 'main',
        status: 'active',
        location: 'B2 - Main Electrical Room',
        currentReading: 2450 + Math.random() * 200,
        voltage: 380 + Math.random() * 5,
        current: 3200 + Math.random() * 200,
        powerFactor: 0.91 + Math.random() * 0.04,
        frequency: 50 + Math.random() * 0.2 - 0.1,
        thd: 3.2 + Math.random() * 0.5,
        temperature: 32 + Math.random() * 3,
        lastUpdate: new Date().toISOString(),
      },
      'chiller-meter-1': {
        id: 'chiller-meter-1',
        name: 'Chiller Plant 1',
        type: 'chiller',
        status: 'active',
        location: 'B3 - Chiller Room 1',
        currentReading: 850 + Math.random() * 100,
        voltage: 380 + Math.random() * 5,
        current: 1200 + Math.random() * 100,
        powerFactor: 0.88 + Math.random() * 0.04,
        coolingTons: 450 + Math.random() * 50,
        cop: 5.8 + Math.random() * 0.4,
        lastUpdate: new Date().toISOString(),
      },
      'tower-a-meter': {
        id: 'tower-a-meter',
        name: 'Tower A Main',
        type: 'building',
        status: 'active',
        location: 'Tower A - B1',
        currentReading: 890 + Math.random() * 80,
        voltage: 380 + Math.random() * 5,
        current: 1300 + Math.random() * 100,
        powerFactor: 0.92 + Math.random() * 0.03,
        monthlyEnergy: 678000,
        floors: 45,
        lastUpdate: new Date().toISOString(),
      },
      'floor-25-meter': {
        id: 'floor-25-meter',
        name: 'Floor 25 - Office',
        type: 'tenant',
        status: 'active',
        location: 'Tower A - Floor 25',
        currentReading: 45 + Math.random() * 10,
        voltage: 220 + Math.random() * 5,
        current: 180 + Math.random() * 20,
        powerFactor: 0.94 + Math.random() * 0.02,
        tenant: 'Tech Solutions Co., Ltd.',
        area: 1500,
        lastUpdate: new Date().toISOString(),
      },
      'datacenter-meter': {
        id: 'datacenter-meter',
        name: 'Data Center',
        type: 'critical',
        status: 'active',
        location: 'Floor 8 - DC Room',
        currentReading: 320 + Math.random() * 30,
        voltage: 380 + Math.random() * 2,
        current: 450 + Math.random() * 20,
        powerFactor: 0.98 + Math.random() * 0.01,
        pue: 1.45 + Math.random() * 0.1,
        uptime: '99.999%',
        lastUpdate: new Date().toISOString(),
      },
    };
    
    if (meterId && meterDetails[meterId]) {
      return meterDetails[meterId];
    }
    
    if (meterId) {
      // Generic meter if specific one not found
      return {
        id: meterId,
        name: `Meter ${meterId}`,
        type: 'power',
        status: 'active',
        location: 'Unknown',
        currentReading: Math.random() * 1000 + 500,
        voltage: 220 + Math.random() * 10,
        current: Math.random() * 100 + 50,
        powerFactor: 0.9 + Math.random() * 0.1,
        lastUpdate: new Date().toISOString(),
      };
    }

    // Return list of meters
    return [
      { id: 'main-meter', name: 'Main Building Meter', type: 'main', status: 'active', location: 'B2 - Main Electrical Room' },
      { id: 'chiller-meter-1', name: 'Chiller Plant 1', type: 'chiller', status: 'active', location: 'B3 - Chiller Room 1' },
      { id: 'chiller-meter-2', name: 'Chiller Plant 2', type: 'chiller', status: 'active', location: 'B3 - Chiller Room 2' },
      { id: 'tower-a-meter', name: 'Tower A Main', type: 'building', status: 'active', location: 'Tower A - B1' },
      { id: 'tower-b-meter', name: 'Tower B Main', type: 'building', status: 'active', location: 'Tower B - B1' },
      { id: 'tower-c-meter', name: 'Tower C Main', type: 'building', status: 'active', location: 'Tower C - B1' },
      { id: 'floor-25-meter', name: 'Floor 25 - Office', type: 'tenant', status: 'active', location: 'Tower A - Floor 25' },
      { id: 'datacenter-meter', name: 'Data Center', type: 'critical', status: 'active', location: 'Floor 8 - DC Room' },
      { id: 'retail-meter', name: 'Retail Podium', type: 'commercial', status: 'active', location: 'Podium Level 1-3' },
      { id: 'carpark-meter', name: 'Parking Facility', type: 'support', status: 'active', location: 'B1-B5' },
    ];
  }

  // Analytics Data - Bangkok Building Patterns
  async getAnalyticsData(params: any) {
    await this.simulateDelay();
    
    const { startDate, endDate, metric, system, interval = '15min' } = params;
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    const data = [];
    let current = new Date(start);
    
    // Bangkok specific patterns - high cooling load during day
    while (current <= end) {
      const hour = current.getHours();
      const isWeekend = current.getDay() === 0 || current.getDay() === 6;
      
      let baseValue = 0;
      let variability = 0;
      
      // Different patterns for different metrics and systems
      if (metric === 'power' || metric === 'energy') {
        if (system === 'chiller' || system === 'cooling') {
          // Chiller load pattern - peaks during hot hours
          if (hour >= 9 && hour <= 17) {
            baseValue = isWeekend ? 650 : 850; // High cooling during office hours
            variability = 100;
          } else if (hour >= 6 && hour <= 22) {
            baseValue = isWeekend ? 450 : 550;
            variability = 80;
          } else {
            baseValue = 350; // Night load
            variability = 50;
          }
        } else if (system === 'lighting') {
          // Lighting pattern
          if (hour >= 6 && hour <= 22) {
            baseValue = isWeekend ? 120 : 180;
            variability = 20;
          } else {
            baseValue = 60; // Emergency/security lighting
            variability = 10;
          }
        } else if (system === 'hvac' || system === 'airside') {
          // HVAC/AHU pattern
          if (hour >= 7 && hour <= 18) {
            baseValue = isWeekend ? 280 : 420;
            variability = 60;
          } else {
            baseValue = 120;
            variability = 30;
          }
        } else {
          // General power consumption
          if (hour >= 8 && hour <= 18) {
            baseValue = isWeekend ? 1200 : 2100;
            variability = 200;
          } else if (hour >= 6 && hour <= 22) {
            baseValue = isWeekend ? 800 : 1400;
            variability = 150;
          } else {
            baseValue = 600;
            variability = 100;
          }
        }
      } else if (metric === 'temperature') {
        // Bangkok outdoor temperature pattern
        if (hour >= 11 && hour <= 16) {
          baseValue = 34 + Math.sin((hour - 11) * Math.PI / 5) * 3; // Peak heat
        } else if (hour >= 6 && hour <= 18) {
          baseValue = 30 + Math.sin((hour - 6) * Math.PI / 12) * 4;
        } else {
          baseValue = 27 + Math.random() * 2;
        }
        variability = 1.5;
      } else if (metric === 'humidity') {
        // Bangkok humidity pattern
        if (hour >= 2 && hour <= 6) {
          baseValue = 85; // High humidity at night
        } else if (hour >= 11 && hour <= 16) {
          baseValue = 65; // Lower during hot hours
        } else {
          baseValue = 75;
        }
        variability = 5;
      } else if (metric === 'cop') {
        // Chiller efficiency - worse during hot hours
        if (hour >= 12 && hour <= 15) {
          baseValue = 4.8; // Lower COP during peak heat
        } else if (hour >= 9 && hour <= 17) {
          baseValue = 5.2;
        } else {
          baseValue = 6.0; // Better COP at night
        }
        variability = 0.3;
      }
      
      data.push({
        timestamp: current.toISOString(),
        value: baseValue + (Math.random() - 0.5) * variability,
      });
      
      // Increment based on interval
      if (interval === '15min') {
        current.setMinutes(current.getMinutes() + 15);
      } else if (interval === 'hourly') {
        current.setHours(current.getHours() + 1);
      } else if (interval === 'daily') {
        current.setDate(current.getDate() + 1);
      } else {
        current.setMinutes(current.getMinutes() + 15); // Default
      }
    }
    
    return {
      metric,
      system,
      data,
      summary: {
        total: data.reduce((sum, d) => sum + d.value, 0),
        average: data.reduce((sum, d) => sum + d.value, 0) / data.length,
        peak: Math.max(...data.map(d => d.value)),
        minimum: Math.min(...data.map(d => d.value)),
      },
    };
  }

  // Alarm Data - Bangkok Building Alarms
  async getAlarms(params?: any) {
    await this.simulateDelay();
    
    const currentTime = new Date();
    const oneHourAgo = new Date(currentTime.getTime() - 60 * 60 * 1000);
    const threeHoursAgo = new Date(currentTime.getTime() - 3 * 60 * 60 * 1000);
    
    return {
      alarms: [
        {
          id: '1',
          rule_name: 'Chiller Efficiency Degradation',
          severity: 'CRITICAL' as AlarmSeverity,
          status: AlarmStatus.ACTIVE,
          message: 'Chiller Plant 1 COP below threshold - possible condenser fouling',
          triggered_at: threeHoursAgo.toISOString(),
          metric_name: 'chiller_cop',
          metric_value: 4.2,
          threshold_value: 5.0,
          location: 'B3 - Chiller Plant 1',
          equipment: 'CH-01',
          impact: 'Increased energy consumption by 15%',
        },
        {
          id: '2',
          rule_name: 'High Space Temperature',
          severity: 'WARNING' as AlarmSeverity,
          status: AlarmStatus.ACTIVE,
          message: 'Office floor temperature above comfort range',
          triggered_at: oneHourAgo.toISOString(),
          metric_name: 'space_temperature',
          metric_value: 26.8,
          threshold_value: 25.0,
          location: 'Tower A - Floor 32',
          impact: 'Tenant comfort affected',
        },
        {
          id: '3',
          rule_name: 'Power Factor Penalty Risk',
          severity: 'WARNING' as AlarmSeverity,
          status: AlarmStatus.ACTIVE,
          message: 'Power factor approaching MEA penalty threshold',
          triggered_at: currentTime.toISOString(),
          metric_name: 'power_factor',
          metric_value: 0.86,
          threshold_value: 0.90,
          location: 'Main Switchboard - MSB-01',
          impact: 'Potential MEA penalty charges',
        },
        {
          id: '4',
          rule_name: 'AHU Filter Pressure Drop',
          severity: 'INFO' as AlarmSeverity,
          status: AlarmStatus.ACTIVE,
          message: 'AHU-25 filter differential pressure high - maintenance required',
          triggered_at: currentTime.toISOString(),
          metric_name: 'filter_dp',
          metric_value: 180,
          threshold_value: 150,
          location: 'Tower B - Floor 25 AHU Room',
          equipment: 'AHU-B-25',
        },
        {
          id: '5',
          rule_name: 'Peak Demand Warning',
          severity: 'CRITICAL' as AlarmSeverity,
          status: AlarmStatus.ACKNOWLEDGED,
          message: 'Approaching monthly peak demand limit',
          triggered_at: new Date(currentTime.getTime() - 2 * 60 * 60 * 1000).toISOString(),
          acknowledged_at: new Date(currentTime.getTime() - 1.5 * 60 * 60 * 1000).toISOString(),
          metric_name: 'demand',
          metric_value: 3380,
          threshold_value: 3400,
          location: 'Main Building',
          impact: 'Potential demand charges increase',
        },
      ],
      total: 5,
    };
  }

  // Notification Data - Bangkok Building Operations
  async getNotifications() {
    await this.simulateDelay();
    
    const now = new Date();
    const fifteenMinAgo = new Date(now.getTime() - 15 * 60 * 1000);
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    return [
      {
        id: '1',
        category: 'alarm' as NotificationCategory,
        title: 'Chiller Performance Alert',
        message: 'Chiller Plant 1 efficiency has dropped below acceptable levels. Immediate inspection recommended.',
        timestamp: fifteenMinAgo.toISOString(),
        status: 'unread',
        severity: 'CRITICAL' as AlarmSeverity,
        actionRequired: true,
        location: 'B3 - Chiller Plant 1',
      },
      {
        id: '2',
        category: 'alarm' as NotificationCategory,
        title: 'MEA Power Factor Warning',
        message: 'Current power factor is 0.86. Risk of penalty charges from MEA if it drops below 0.85.',
        timestamp: oneHourAgo.toISOString(),
        status: 'unread',
        severity: 'WARNING' as AlarmSeverity,
        actionRequired: true,
        location: 'Main Electrical Room',
      },
      {
        id: '3',
        category: 'system' as NotificationCategory,
        title: 'Monthly Energy Report Ready',
        message: 'The July 2024 energy consumption report is now available for review.',
        timestamp: yesterday.toISOString(),
        status: 'read',
        severity: 'INFO' as AlarmSeverity,
        reportUrl: '/reports/2024-07',
      },
      {
        id: '4',
        category: 'system' as NotificationCategory,
        title: 'Scheduled Maintenance Reminder',
        message: 'Quarterly chiller maintenance scheduled for tomorrow 2:00 AM - 6:00 AM.',
        timestamp: now.toISOString(),
        status: 'unread',
        severity: 'INFO' as AlarmSeverity,
        equipment: ['CH-01', 'CH-02'],
      },
      {
        id: '5',
        category: 'alarm' as NotificationCategory,
        title: 'High Occupancy Alert',
        message: 'Tower A occupancy at 95%. Additional cooling capacity may be required.',
        timestamp: now.toISOString(),
        status: 'unread',
        severity: 'WARNING' as AlarmSeverity,
        location: 'Tower Building',
        metric: { occupancy: 95, threshold: 90 },
      },
    ];
  }

  // Site Information - SET Building
  async getSiteInfo() {
    await this.simulateDelay();
    
    return {
      id: 'set',
      name: 'Stock Exchange of Thailand (SET)',
      location: 'Ratchadaphisek Road, Bangkok, Thailand',
      address: '93 Ratchadaphisek Rd, Din Daeng, Bangkok 10400',
      buildings: ['A', 'B', 'C'],
      totalArea: 108000, // m² total built area
      siteArea: 9200, // m² land area
      floors: {
        total: 75, // Combined floors
        'A': 33,
        'B': 27,
        'C': 15,
      },
      coordinates: { lat: 13.7650, lng: 100.5618 }, // Actual SET location
      climate: {
        zone: 'Tropical wet and dry',
        avgTemp: 29.2, // °C annual average
        avgHumidity: 73, // % annual average
        coolingDegreeDays: 3580,
      },
      utilities: {
        electricityProvider: 'Metropolitan Electricity Authority (MEA)',
        waterProvider: 'Metropolitan Waterworks Authority (MWA)',
        gridVoltage: '24kV',
        transformerCapacity: '12MVA',
      },
      certifications: ['LEED Platinum', 'ISO 50001', 'ISO 14001'],
      yearBuilt: 1974,
      lastRenovation: 2020,
      operatingHours: '8:00 - 18:00 (Trading hours: 9:30 - 16:30)',
    };
  }

  // Performance Data - SET Building Performance
  async getPerformanceData(buildingId: string, timeRange: string) {
    await this.simulateDelay();
    
    const performanceData: Record<string, any> = {
      'A': {
        buildingId: 'A',
        buildingName: 'SET Tower A',
        timeRange,
        eui: 128.5, // kWh/m²/year - financial trading center
        euiTrend: 'decreasing',
        euiChange: -3.2,
        pue: 1.48, // Data center in Tower A
        pueTrend: 'improving',
        pueChange: -0.03,
        emissions: 744.2, // tCO2e/month
        emissionsTrend: 'decreasing',
        emissionsChange: -2.8,
        chillerPlantEfficiency: 0.65, // kW/RT
        coolingLoad: 2800, // RT
        peakDemand: 2850, // kW
        loadFactor: 0.78,
      },
      'B': {
        buildingId: 'B',
        buildingName: 'SET Tower B',
        timeRange,
        eui: 135.2,
        euiTrend: 'stable',
        euiChange: 0.8,
        pue: 1.42, // Better due to newer data center
        pueTrend: 'stable',
        pueChange: 0.01,
        emissions: 619.6,
        emissionsTrend: 'stable',
        emissionsChange: 0.5,
        chillerPlantEfficiency: 0.63,
        coolingLoad: 2400,
        peakDemand: 2380,
        loadFactor: 0.82,
      },
      'C': {
        buildingId: 'C',
        buildingName: 'SET Education Center',
        timeRange,
        eui: 118.8,
        euiTrend: 'decreasing',
        euiChange: -4.1,
        emissions: 314.5,
        emissionsTrend: 'decreasing',
        emissionsChange: -3.5,
        chillerPlantEfficiency: 0.70,
        coolingLoad: 1200,
        peakDemand: 1150,
        loadFactor: 0.65,
      },
    };
    
    return performanceData[buildingId] || performanceData['A'];
  }
  
  // Compare Data - System Comparison
  async getCompareData(systems: string[], metric: string, dateRange: any) {
    await this.simulateDelay();
    
    const systemData: Record<string, any> = {
      'chiller-1': {
        name: 'Chiller Plant 1',
        type: 'chiller',
        capacity: '450 RT',
        avgEfficiency: 0.68, // kW/RT
        monthlyEnergy: 324500,
        trend: 'improving',
      },
      'chiller-2': {
        name: 'Chiller Plant 2',
        type: 'chiller',
        capacity: '450 RT',
        avgEfficiency: 0.72,
        monthlyEnergy: 298400,
        trend: 'stable',
      },
      'tower-a-hvac': {
        name: 'Tower A HVAC',
        type: 'hvac',
        avgPower: 420,
        monthlyEnergy: 302400,
        efficiency: 0.85,
        trend: 'decreasing',
      },
      'tower-b-hvac': {
        name: 'Tower B HVAC',
        type: 'hvac',
        avgPower: 380,
        monthlyEnergy: 273600,
        efficiency: 0.88,
        trend: 'stable',
      },
    };
    
    const start = new Date(dateRange.startDate);
    const end = new Date(dateRange.endDate);
    const days = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    
    const compareResults: any[] = [];
    
    for (const system of systems) {
      const systemInfo = systemData[system] || {
        name: system,
        type: 'unknown',
        avgPower: 300,
        monthlyEnergy: 216000,
      };
      
      const data = [];
      for (let i = 0; i < days; i++) {
        const date = new Date(start);
        date.setDate(date.getDate() + i);
        
        let baseValue = 0;
        if (metric === 'power') {
          baseValue = systemInfo.avgPower || 300;
        } else if (metric === 'energy') {
          baseValue = (systemInfo.monthlyEnergy || 200000) / 30;
        } else if (metric === 'efficiency') {
          baseValue = systemInfo.avgEfficiency || systemInfo.efficiency || 0.8;
        }
        
        data.push({
          timestamp: date.toISOString(),
          value: baseValue + (Math.random() - 0.5) * baseValue * 0.1,
        });
      }
      
      compareResults.push({
        system: system,
        name: systemInfo.name,
        type: systemInfo.type,
        data: data,
        summary: {
          average: baseValue,
          peak: baseValue * 1.2,
          minimum: baseValue * 0.8,
          total: baseValue * days,
        },
      });
    }
    
    return compareResults;
  }
  
  // Historical Data for trending
  async getHistoricalData(meterId: string, metric: string, period: string) {
    await this.simulateDelay();
    
    const periods: Record<string, number> = {
      '1d': 1,
      '7d': 7,
      '30d': 30,
      '90d': 90,
      '1y': 365,
    };
    
    const days = periods[period] || 30;
    const end = new Date();
    const start = new Date(end.getTime() - days * 24 * 60 * 60 * 1000);
    
    const data = [];
    const interval = days <= 1 ? 15 : days <= 7 ? 60 : days <= 30 ? 240 : 1440; // minutes
    
    let current = new Date(start);
    while (current <= end) {
      let baseValue = 0;
      const hour = current.getHours();
      
      if (metric === 'power') {
        // Daily power pattern
        if (hour >= 8 && hour <= 18) {
          baseValue = 2100;
        } else if (hour >= 6 && hour <= 22) {
          baseValue = 1400;
        } else {
          baseValue = 800;
        }
      } else if (metric === 'energy') {
        baseValue = 50000; // Daily energy
      } else if (metric === 'power_factor') {
        baseValue = 0.92;
      } else if (metric === 'thd') {
        baseValue = 3.5;
      }
      
      data.push({
        timestamp: current.toISOString(),
        value: baseValue + (Math.random() - 0.5) * baseValue * 0.05,
      });
      
      current = new Date(current.getTime() + interval * 60 * 1000);
    }
    
    return {
      meterId,
      metric,
      period,
      data,
      summary: {
        average: baseValue,
        peak: Math.max(...data.map(d => d.value)),
        minimum: Math.min(...data.map(d => d.value)),
        total: data.reduce((sum, d) => sum + d.value, 0),
      },
    };
  }


  // Device-related methods
  async getDeviceRelationsById(deviceId: string) {
    // Skip delay for faster loading
    // await this.simulateDelay();
    
    if (this.shouldSimulateError()) {
      throw new Error('Mock API Error: Failed to fetch device relations');
    }
    
    return mockDeviceRelations[deviceId] || [];
  }

  async getDeviceRelationsByModel(model: string) {
    // Skip delay for faster loading
    // await this.simulateDelay();
    
    if (this.shouldSimulateError()) {
      throw new Error('Mock API Error: Failed to fetch device relations by model');
    }
    
    return getMockDevicesByModel(model);
  }

  async getDeviceById(deviceId: string, expandOptions?: string[]) {
    // Skip delay for faster loading
    // await this.simulateDelay();
    
    if (this.shouldSimulateError()) {
      throw new Error('Mock API Error: Failed to fetch device');
    }
    
    // Always get fresh data with current timestamps
    const allDevices = getAllMockDevices();
    const device = allDevices.find(d => d.device_id === deviceId);
    
    if (!device) {
      throw new Error(`Device ${deviceId} not found`);
    }
    
    return device;
  }

  async getAllDevices(expandOptions?: string[]) {
    // Skip delay for faster loading
    // await this.simulateDelay();
    
    if (this.shouldSimulateError()) {
      throw new Error('Mock API Error: Failed to fetch devices');
    }
    
    return getAllMockDevices();
  }

  async getZonesWithDevices() {
    // Skip delay for faster loading
    // await this.simulateDelay();
    
    if (this.shouldSimulateError()) {
      throw new Error('Mock API Error: Failed to fetch zones');
    }
    
    return mockZones;
  }

  // Timescale data for meter charts - optimized
  async getTimescaleData(params: any) {
    // Skip delay for faster loading
    // await this.simulateDelay();
    
    if (this.shouldSimulateError()) {
      throw new Error('Mock API Error: Failed to fetch timescale data');
    }

    const { table_name, device_id, datapoints, start_timestamp, end_timestamp } = params;
    
    // Debug logs removed for performance

    // Generate mock data based on table type
    if (table_name === 'statistic_data_15min' && datapoints?.includes('power')) {
      // Generate hourly power data instead of 15-minute for faster loading
      const records = [];
      const now = new Date();
      const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const currentHour = now.getHours();
      
      // Generate only up to current hour + 1 for faster loading
      for (let i = 0; i <= Math.min(currentHour + 1, 23); i++) {
        const timestamp = new Date(startOfDay.getTime() + i * 60 * 60 * 1000);
        
        // Generate realistic power pattern
        const hour = timestamp.getHours();
        let basePower = 50; // Base power in kW
        
        // Daily pattern
        if (hour >= 8 && hour <= 18) {
          basePower = 150; // Daytime peak
        } else if (hour >= 6 && hour <= 22) {
          basePower = 100; // Morning/evening
        }
        
        // Add some random variation
        const power = basePower + (Math.random() - 0.5) * basePower * 0.2;
        
        records.push({
          timestamp: timestamp.toISOString(),
          mean_value: Math.round(power * 10) / 10,
          min_value: Math.round(power * 0.9 * 10) / 10,
          max_value: Math.round(power * 1.1 * 10) / 10,
        });
      }
      
      return [{
        datapoint: 'power',
        records
      }];
    } else if (table_name === 'energy_data_1day' && datapoints?.includes('daily_energy')) {
      // Generate only last 7 days for faster loading
      const records = [];
      const now = new Date();
      const currentDay = now.getDate();
      const startDay = Math.max(1, currentDay - 6); // Last 7 days
      
      for (let day = startDay; day <= currentDay; day++) {
        const timestamp = new Date(now.getFullYear(), now.getMonth(), day);
        
        // Generate realistic daily energy consumption (kWh)
        const baseEnergy = 2400; // Base daily consumption
        const dayOfWeek = timestamp.getDay();
        
        // Lower consumption on weekends
        const weekendFactor = (dayOfWeek === 0 || dayOfWeek === 6) ? 0.7 : 1.0;
        const energy = baseEnergy * weekendFactor + (Math.random() - 0.5) * 200;
        
        records.push({
          timestamp: timestamp.toISOString(),
          value: Math.round(energy),
        });
      }
      
      return [{
        datapoint: 'daily_energy',
        records
      }];
    }
    
    return [];
  }

  // Site details for timezone - optimized
  async getSiteDetails(siteId: string) {
    // Skip delay for faster loading
    // await this.simulateDelay();
    
    if (this.shouldSimulateError()) {
      throw new Error('Mock API Error: Failed to fetch site details');
    }
    
    return {
      id: siteId,
      name: 'Stock Exchange of Thailand (SET)',
      metadata: {
        timezone: 'Asia/Bangkok',
        location: 'Bangkok, Thailand',
        coordinates: { lat: 13.7650, lng: 100.5618 },
      }
    };
  }
}

export const mockDataService = new MockDataService();