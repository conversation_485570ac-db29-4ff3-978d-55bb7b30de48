import { ActiveAlarm } from '@/types/alarms';
import { emailApi } from '@/lib/api/emailClient';

/**
 * Service for sending email notifications for alarms
 */
export const alarmEmailService = {
  /**
   * Send an email notification for a critical alarm
   * 
   * @param alarm The alarm to send a notification for
   * @param recipientEmail The email address to send the notification to
   * @returns Promise resolving to success status
   */
  sendCriticalAlarmEmail: async (alarm: ActiveAlarm, recipientEmail: string): Promise<boolean> => {
    try {
      // Create a formatted timestamp
      const timestamp = new Date(alarm.trigger_time).toLocaleString();
      
      // Create the email subject
      const subject = `CRITICAL ALARM: ${alarm.rule_name}`;
      
      // Create the HTML email body with styling
      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="background-color: #f44336; color: white; padding: 15px; border-radius: 5px 5px 0 0; margin-bottom: 20px;">
            <h1 style="margin: 0; font-size: 24px;">Critical Alarm Notification</h1>
          </div>
          
          <div style="padding: 0 15px;">
            <p style="font-size: 16px; color: #333;">A critical alarm has been triggered in your energy management system:</p>
            
            <div style="background-color: #f9f9f9; border-left: 4px solid #f44336; padding: 15px; margin: 15px 0;">
              <h2 style="margin-top: 0; color: #d32f2f;">${alarm.rule_name}</h2>
              <p style="margin: 5px 0;"><strong>Meter:</strong> ${alarm.meter_name || 'N/A'}</p>
              <p style="margin: 5px 0;"><strong>Metric:</strong> ${alarm.metric.replace(/_/g, ' ')}</p>
              <p style="margin: 5px 0;"><strong>Trigger Value:</strong> ${alarm.trigger_value} ${getUnitForMetric(alarm.metric)}</p>
              <p style="margin: 5px 0;"><strong>Threshold:</strong> ${alarm.threshold} ${getUnitForMetric(alarm.metric)}</p>
              <p style="margin: 5px 0;"><strong>Time:</strong> ${timestamp}</p>
            </div>
            
            <p style="font-size: 16px; color: #333;">Please take immediate action to address this issue.</p>
            
            <div style="margin: 25px 0; text-align: center;">
              <a href="${window.location.origin}/alarms" style="background-color: #2196f3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                View Alarm Details
              </a>
            </div>
            
            <p style="color: #666; font-size: 14px; margin-top: 30px; border-top: 1px solid #eee; padding-top: 15px;">
              This is an automated message from the Alto CERO Energy Management System.
              Please do not reply to this email.
            </p>
          </div>
        </div>
      `;
      
      // Send the email using the emailApi
      const response = await emailApi.sendEmail(recipientEmail, subject, html);
      
      console.log('Critical alarm email sent successfully:', response);
      return true;
    } catch (error) {
      console.error('Failed to send critical alarm email:', error);
      return false;
    }
  }
};

/**
 * Helper function to get the appropriate unit for a metric
 */
function getUnitForMetric(metric: string): string {
  const metricLower = metric.toLowerCase();
  
  if (metricLower.includes('power') && !metricLower.includes('factor')) {
    return 'kW';
  }
  if (metricLower.includes('energy')) {
    return 'kWh';
  }
  if (metricLower.includes('voltage')) {
    return 'V';
  }
  if (metricLower.includes('current')) {
    return 'A';
  }
  if (metricLower.includes('frequency')) {
    return 'Hz';
  }
  if (metricLower.includes('factor')) {
    return ''; // Power factor is unitless
  }
  
  return '';
}

export default alarmEmailService;
