import type { DeviceDetails, DeviceChildRelation } from '../../services/deviceService';
import { fetchDeviceById as fetchDeviceByIdAPI, fetchChildDeviceRelationsById as fetchChildDeviceRelationsByIdAPI } from '../../services/deviceService';

// Helper to check if we should use mock data at runtime
const shouldUseMockData = () => {
  const savedPreference = typeof window !== 'undefined' ? localStorage.getItem('useMockData') : null;
  return savedPreference === 'true';
};

// Mock device configurations with realistic power values
const MOCK_DEVICE_DATA: Record<string, {
  details: DeviceDetails;
  children: string[];
}> = {
  'main': {
    details: {
      device_id: 'main',
      name: 'Main Distribution Board',
      type: 'main_distribution',
      model: 'ABB-MDB-2000',
      building: 1,
      floor: null,
      zone: null,
      is_gateway: false,
      is_virtual: false,
      latest_data: {
        power: { value: 2850.5 },
        voltage: { value: 230 },
        current: { value: 12.4 },
        power_factor: { value: 0.98 }
      }
    },
    children: ['C-BEDB1', 'tower_a', 'tower_b', 'tower_c', 'tower_a_6th_floor_tenants', 'chiller_plant', 'CB-EXD']
  },
  'C-BEDB1': {
    details: {
      device_id: 'C-BEDB1',
      name: 'Main Power Input',
      type: 'power_input',
      model: 'ABB-PI-1000',
      building: 1,
      floor: null,
      zone: null,
      is_gateway: true,
      is_virtual: false,
      latest_data: {
        power: { value: 2850.5 },
        voltage: { value: 230 },
        current: { value: 12.4 }
      }
    },
    children: ['main']
  },
  'tower_a': {
    details: {
      device_id: 'tower_a',
      name: 'Tower Building',
      type: 'tower',
      model: 'TOWER-A-DIST',
      building: 1,
      floor: null,
      zone: null,
      is_gateway: false,
      is_virtual: false,
      latest_data: {
        power: { value: 520.3 },
        voltage: { value: 230 },
        current: { value: 2.26 }
      }
    },
    children: Array.from({ length: 25 }, (_, i) => `tower_a_meter_${i + 1}`)
  },
  'tower_b': {
    details: {
      device_id: 'tower_b',
      name: 'Podium Building',
      type: 'tower',
      model: 'TOWER-B-DIST',
      building: 2,
      floor: null,
      zone: null,
      is_gateway: false,
      is_virtual: false,
      latest_data: {
        power: { value: 680.7 },
        voltage: { value: 230 },
        current: { value: 2.96 }
      }
    },
    children: Array.from({ length: 32 }, (_, i) => `tower_b_meter_${i + 1}`)
  },
  'tower_c': {
    details: {
      device_id: 'tower_c',
      name: 'Car Park Building',
      type: 'tower',
      model: 'TOWER-C-DIST',
      building: 3,
      floor: null,
      zone: null,
      is_gateway: false,
      is_virtual: false,
      latest_data: {
        power: { value: 450.2 },
        voltage: { value: 230 },
        current: { value: 1.96 }
      }
    },
    children: Array.from({ length: 20 }, (_, i) => `tower_c_meter_${i + 1}`)
  },
  'tower_a_6th_floor_tenants': {
    details: {
      device_id: 'tower_a_6th_floor_tenants',
      name: 'Tenant Meters',
      type: 'tenant',
      model: 'TENANT-DIST',
      building: 1,
      floor: 6,
      zone: null,
      is_gateway: false,
      is_virtual: false,
      latest_data: {
        power: { value: 120.5 },
        voltage: { value: 230 },
        current: { value: 0.52 }
      }
    },
    children: Array.from({ length: 15 }, (_, i) => `tenant_meter_${i + 1}`)
  },
  'chiller_plant': {
    details: {
      device_id: 'chiller_plant',
      name: 'Chiller Plant',
      type: 'plant',
      model: 'CHILLER-MAIN',
      building: 0,
      floor: null,
      zone: null,
      is_gateway: false,
      is_virtual: false,
      latest_data: {
        power: { value: 850.8 },
        voltage: { value: 400 },
        current: { value: 2.13 }
      }
    },
    children: ['chiller_1', 'chiller_2', 'cooling_tower_1', 'cooling_tower_2', 'pump_1', 'pump_2']
  },
  'CB-EXD': {
    details: {
      device_id: 'CB-EXD',
      name: 'Data Center & Others',
      type: 'datacenter',
      model: 'CALCULATED',
      building: 1,
      floor: -1,
      zone: null,
      is_gateway: false,
      is_virtual: true, // Virtual meter - calculated
      latest_data: {
        power: { value: 0 }, // Will be calculated dynamically
        voltage: { value: 230 },
        current: { value: 0 }
      }
    },
    children: [] // No actual meters - this is a calculated value
  },
  'data_center_and_others': {
    details: {
      device_id: 'data_center_and_others',
      name: 'Data Center & Others',
      type: 'datacenter',
      model: 'CALCULATED',
      building: 1,
      floor: -1,
      zone: null,
      is_gateway: false,
      is_virtual: true, // Virtual meter - calculated
      latest_data: {
        power: { value: 0 }, // Will be calculated dynamically
        voltage: { value: 230 },
        current: { value: 0 }
      }
    },
    children: [] // No actual meters - this is a calculated value
  }
};

// Add some variation to power values to simulate real-time changes
const addPowerVariation = (baseValue: number): number => {
  const variation = (Math.random() - 0.5) * baseValue * 0.05; // ±2.5% variation
  return parseFloat((baseValue + variation).toFixed(2));
};

// Mock implementation of fetchDeviceById
const mockFetchDeviceById = async (
  deviceId: string,
  include?: string[]
): Promise<DeviceDetails> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
  
  const mockData = MOCK_DEVICE_DATA[deviceId];
  if (!mockData) {
    throw new Error(`Device with ID ${deviceId} not found`);
  }
  
  // Create a copy of the device details
  const deviceDetails = { ...mockData.details };
  
  // Add variation to power values if latest_data is included
  if (include?.includes('latest_data') && deviceDetails.latest_data?.power?.value) {
    // Special case for Data Center & Others - calculate as total minus sum of others
    if (deviceId === 'CB-EXD' || deviceId === 'data_center_and_others') {
      // Get main power (total)
      const mainPower = MOCK_DEVICE_DATA['C-BEDB1'].details.latest_data?.power?.value || 0;
      
      // Calculate sum of all other meters (excluding main and data center)
      let sumOthers = 0;
      const metersToSum = ['tower_a', 'tower_b', 'tower_c', 'tower_a_6th_floor_tenants', 'chiller_plant'];
      
      for (const meterId of metersToSum) {
        const meterData = MOCK_DEVICE_DATA[meterId];
        if (meterData?.details?.latest_data?.power?.value) {
          sumOthers += meterData.details.latest_data.power.value;
        }
      }
      
      // Data Center = Total - Sum of others
      const calculatedPower = Math.max(0, mainPower - sumOthers);
      
      deviceDetails.latest_data = {
        ...deviceDetails.latest_data,
        power: {
          ...deviceDetails.latest_data.power,
          value: addPowerVariation(calculatedPower)
        }
      };
    } else {
      // Normal case - just add variation
      deviceDetails.latest_data = {
        ...deviceDetails.latest_data,
        power: {
          ...deviceDetails.latest_data.power,
          value: addPowerVariation(deviceDetails.latest_data.power.value)
        }
      };
    }
  }
  
  return deviceDetails;
};

// Mock implementation of fetchChildDeviceRelationsById
const mockFetchChildDeviceRelationsById = async (
  parentDeviceId: string
): Promise<DeviceChildRelation[]> => {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
  
  const mockData = MOCK_DEVICE_DATA[parentDeviceId];
  if (!mockData) {
    return [];
  }
  
  // Generate child relations
  return mockData.children.map((childId, index) => ({
    id: index + 1,
    parent_device_id: parentDeviceId,
    child_device_id: childId,
    relationship_type: 'power_distribution',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }));
};

// Service interface
export interface MeterDiagramService {
  fetchDeviceById: (deviceId: string, include?: string[]) => Promise<DeviceDetails>;
  fetchChildDeviceRelationsById: (parentDeviceId: string) => Promise<DeviceChildRelation[]>;
  isUsingMockData: () => boolean;
}

// Mock service implementation
const mockService: MeterDiagramService = {
  fetchDeviceById: mockFetchDeviceById,
  fetchChildDeviceRelationsById: mockFetchChildDeviceRelationsById,
  isUsingMockData: () => true
};

// Production service implementation
const productionService: MeterDiagramService = {
  fetchDeviceById: fetchDeviceByIdAPI,
  fetchChildDeviceRelationsById: fetchChildDeviceRelationsByIdAPI,
  isUsingMockData: () => false
};

// Export the service based on environment
export const meterDiagramService: MeterDiagramService = {
  fetchDeviceById: async (deviceId: string) => {
    if (shouldUseMockData()) {
      return mockService.fetchDeviceById(deviceId);
    }
    return productionService.fetchDeviceById(deviceId);
  },
  
  fetchChildRelations: async (deviceId: string) => {
    if (shouldUseMockData()) {
      return mockService.fetchChildRelations(deviceId);
    }
    return productionService.fetchChildRelations(deviceId);
  },
  
  isUsingMockData: () => shouldUseMockData()
};

// Export individual functions for easier use
export const fetchDeviceById = meterDiagramService.fetchDeviceById;
export const fetchChildDeviceRelationsById = meterDiagramService.fetchChildRelations;
export const isUsingMockData = meterDiagramService.isUsingMockData;