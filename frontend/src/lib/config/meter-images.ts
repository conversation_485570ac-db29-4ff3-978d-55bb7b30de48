// Default meter image path
export const DEFAULT_METER_IMAGE = '/assets/images/meters/default-meter.png';

// Function to get image path for a meter
export function getMeterImagePath(): string {
  return DEFAULT_METER_IMAGE;
}

// Function stub for backward compatibility
// This function no longer does anything as we're using a fixed image
export function saveMeterImage(_meterId: string, _imageData: string): void {
  // No-op - we're using a fixed image now
  console.warn('Image upload functionality has been removed. Using fixed meter image.');
}
