// Dashboard Configuration Constants
export const DASHBOARD_CONFIG = {
  refreshInterval: 30000, // 30 seconds
  sections: {
    energyPerformance: {
      id: 'energy-performance',
      title: '2025',
      subtitle: 'Energy Performance'
    },
    monthlyOverview: {
      id: 'monthly-overview',
      title: 'Monthly Energy Overview'
    },
    loadProfile: {
      id: 'load-profile',
      title: "Today's Load Profile"
    },
    buildingVisualization: {
      id: 'building-visualization',
      title: 'Building Status'
    },
    billing: {
      id: 'billing',
      title: 'E-Billing'
    },
    alerts: {
      id: 'alerts',
      title: 'System Alerts'
    }
  },
  layout: {
    leftColumn: 4, // Grid columns
    centerColumn: 5,
    rightColumn: 3
  }
} as const;

// Mock data generators
export const generateMockFloorData = (floors: number) => 
  Array.from({ length: floors }, (_, i) => ({
    floor: i + 1,
    consumption: Math.random() * 4000,
    hasAlert: i + 1 === 16,
    isDisconnected: i + 1 === 8
  }));

export const calculateBillingMetrics = () => ({
  currentMonth: 333673.20,
  previousMonth: 316989.54,
  unpaidInvoices: 3,
  nextDueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
});