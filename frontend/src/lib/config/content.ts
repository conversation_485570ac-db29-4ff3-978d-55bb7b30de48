// Centralized content configuration
export const PAGE_TITLES = {
  '/': 'Overview',
  '/meters': 'Meter Management',
  '/analytics': 'Electricity Analytics',
  '/reports': 'Electricity Consumption Reports',
  '/notifications': 'System Alerts & Notifications',
  '/settings': 'System Configuration'
} as const;

export const SECTION_TITLES = {
  energyPerformance: '2025 Energy Performance',
  yearlyOverview: '2025 Yearly Overview',
  monthlyOverview: () => {
    const currentMonth = new Date().toLocaleString('en-US', { month: 'long' });
    return `${currentMonth} Energy Overview`;
  },
  loadProfile: "Building Power Profile",
  billing: 'E-Billing',
  meters: 'Smart Meters'
} as const;

export const BUTTON_LABELS = {
  viewAnalytics: 'View Analytics',
  manageBilling: 'Manage billing',
  viewAllAlerts: 'View all alerts',
  autoRefreshOn: 'Auto-refresh On',
  autoRefreshOff: 'Auto-refresh Off'
} as const;