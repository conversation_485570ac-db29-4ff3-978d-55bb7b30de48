// UI Configuration Constants
export const SHADOW_STYLES = {
  card: 'shadow-[0_8px_16px_-4px_rgba(14,125,228,0.08)]',
  hover: {
    primary: 'hover:shadow-[0_16px_24px_-8px_rgba(6,91,169,0.12)]',
    secondary: 'hover:shadow-[0_4px_12px_rgba(6,91,169,0.12)]',
    danger: 'hover:shadow-[0_4px_12px_rgba(239,68,68,0.12)]'
  },
  button: 'shadow-[0_2px_4px_rgba(6,91,169,0.1)]'
} as const;

export const GRADIENT_STYLES = {
  card: 'bg-gradient-to-br from-white via-white/95 to-blue-50/5',
  hover: {
    primary: 'hover:bg-gradient-to-br hover:from-[#065BA9]/10 hover:via-[#065BA9]/5 hover:to-white',
    secondary: 'hover:bg-gradient-to-br hover:from-gray-50/80 hover:to-gray-100/40'
  },
  button: {
    primary: 'bg-gradient-to-br from-[#065BA9]/5 via-[#065BA9]/3 to-white',
    secondary: 'bg-gradient-to-br from-gray-50/80 to-gray-100/40',
  },
  meter: {
    online: 'bg-gradient-to-br from-blue-50/60 via-blue-50/40 to-white hover:from-blue-50/80 hover:via-blue-50/60 hover:to-white',
    offline: 'bg-gradient-to-br from-red-50/60 via-red-50/40 to-white hover:from-red-50/80 hover:via-red-50/60 hover:to-white'
  },
  icon: {
    container: 'bg-blue-100/50 group-hover:bg-blue-100/80 transition-colors duration-200'
  }
} as const;

export const BORDER_STYLES = {
  card: 'border border-[#EDEFF9] rounded-xl',
  button: {
    primary: 'border border-blue-100/80 rounded-lg',
    secondary: 'border border-gray-200/80 rounded-lg',
  }
} as const;

// Chart Configuration
export const CHART_STYLES = {
  colors: {
    primary: '#065BA9',  // Strong Dark Blue
    secondary: '#3B82F6', // Bright Blue
    peakZone: 'rgba(204, 235, 255, 0.25)', // Bleached Blue with opacity
    axis: '#CCEBFF', // Bleached Blue
    tooltip: {
      background: 'rgba(255, 255, 255, 0.95)',
      border: '#E2E8F0',
      text: '#1E293B'
    }
  },
  gradients: {
    area: [
      { offset: 0, color: 'rgba(6, 91, 169, 0.25)' }, // Reduced opacity for softer look
      { offset: 1, color: 'rgba(6, 91, 169, 0.02)' }
    ],
    secondaryArea: [
      { offset: 0, color: 'rgba(59, 130, 246, 0.15)' }, // Reduced opacity
      { offset: 1, color: 'rgba(59, 130, 246, 0.02)' }
    ]
  },
  axis: {
    label: {
      color: '#64748B', // Softer text color
      fontSize: 10,
      fontWeight: 500
    },
    line: {
      color: '#E2E8F0', // Lighter axis lines
      width: 1
    },
    split: {
      color: '#F1F5F9', // Very light grid lines
      type: 'dashed'
    }
  },
  series: {
    primary: {
      lineWidth: 2,
      symbolSize: 0, // Hide default symbols
      emphasis: {
        scale: true,
        symbolSize: 6, // Show symbols on hover
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(6, 91, 169, 0.25)'
        }
      },
      animation: {
        duration: 1000,
        easing: 'cubicInOut'
      }
    },
    secondary: {
      lineWidth: 1.5,
      symbolSize: 0,
      emphasis: {
        scale: true,
        symbolSize: 4
      },
      animation: {
        duration: 1000,
        delay: 300,
        easing: 'cubicInOut'
      }
    }
  },
  animation: {
    updateTransition: {
      duration: 300,
      easing: 'cubicInOut'
    }
  },
  tooltip: {
    padding: [12, 16],
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#E2E8F0',
    borderWidth: 1,
    textStyle: {
      color: '#1E293B',
      fontSize: 12
    },
    extraCssText: 'backdrop-filter: blur(4px);',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: 'rgba(148, 163, 184, 0.2)',
        width: 2,
        type: 'dashed'
      }
    }
  }
} as const;

// Animation keyframes
export const ANIMATION_STYLES = {
  transition: 'transition-all duration-300',
  hover: {
    container: {
      base: 'hover:shadow-[0_4px_12px_rgba(14,126,228,0.12)]',
      primary: 'hover:shadow-[0_16px_24px_-8px_rgba(14,125,228,0.15)]',
      secondary: 'hover:shadow-[0_4px_12px_rgba(14,126,228,0.12)]',
      danger: 'hover:shadow-[0_4px_12px_rgba(239,68,68,0.12)]'
    },
    scale: {
      sm: 'hover:scale-[1.02]',
      md: 'hover:scale-105',
      lg: 'hover:scale-110'
    }
  },
  chart: {
    fadeIn: 'animate-[fadeIn_0.3s_ease-out]',
    slideIn: 'animate-[slideIn_0.3s_ease-out]',
    pulse: 'animate-[pulse_2s_ease-in-out_infinite]'
  }
} as const;

// Text styles remain focused on the blue theme
export const TEXT_STYLES = {
  title: {
    base: 'text-xs font-medium text-gray-500',
    withTime: 'text-sm font-medium text-gray-700 flex items-center gap-2'
  },
  subtitle: 'text-sm text-gray-500',
  label: 'text-xs text-gray-500 font-medium',
  timestamp: 'text-[10px] text-gray-300',
  value: {
    primary: 'text-xs font-medium text-primary-blue',
    secondary: 'text-sm font-medium text-gray-700'
  },
  unit: 'text-xs text-gray-500'
} as const;