// Meter Data Configuration
import { METER_RANGES, getLoadPattern } from '../mock/meters';
import type { MeterType } from '../../types';

// Generate 24 hours of data for a meter
const generateMeterData = (type: MeterType) => {
  const range = METER_RANGES[type];
  const data = [];

  for (let hour = 0; hour < 24; hour++) {
    const pattern = getLoadPattern(range.pattern, hour);
    const baseValue = range.min + (range.max - range.min) * pattern;
    const variation = 0.95 + Math.random() * 0.1;
    const value = Number((baseValue * variation).toFixed(2));

    data.push({
      time: `${hour}:00`,
      demand: value
    });
  }

  return data;
};

// Generate yesterday's data with slight variations
const generateYesterdayData = (todayData: typeof METER_DATA.chillerPlant.today) => {
  return todayData.map(point => ({
    time: point.time,
    demand: point.demand * (0.95 + Math.random() * 0.1) // 95-105% of today's value
  }));
};

// Pre-generate data for each meter type
export const METER_DATA = {
  chillerPlant: {
    today: generateMeterData('chillerPlant'),
    yesterday: [] as typeof METER_DATA.chillerPlant.today
  },
  airSide: {
    today: generateMeterData('airSide'),
    yesterday: [] as typeof METER_DATA.airSide.today
  },
  lighting: {
    today: generateMeterData('lighting'),
    yesterday: [] as typeof METER_DATA.lighting.today
  },
  equipment: {
    today: generateMeterData('equipment'),
    yesterday: [] as typeof METER_DATA.equipment.today
  },
  evCharger: {
    today: generateMeterData('evCharger'),
    yesterday: [] as typeof METER_DATA.evCharger.today
  },
  others: {
    today: generateMeterData('others'),
    yesterday: [] as typeof METER_DATA.others.today
  }
} as const;

// Generate yesterday's data for all meter types
Object.keys(METER_DATA).forEach(type => {
  const key = type as keyof typeof METER_DATA;
  METER_DATA[key].yesterday = generateYesterdayData(METER_DATA[key].today);
});

// Helper function to get meter data
export function getMeterData(type: MeterType) {
  if (!type || !METER_DATA[type]) {
    return {
      current: [],
      comparison: []
    };
  }

  return {
    current: METER_DATA[type].today,
    comparison: METER_DATA[type].yesterday
  };
}

// Helper function to combine multiple meter data
export function getCombinedMeterData(types: MeterType[]) {
  const combined = Array(24).fill(0).map((_, i) => ({
    time: `${i}:00`,
    demand: 0
  }));

  types.forEach(type => {
    METER_DATA[type].today.forEach((point, i) => {
      combined[i].demand += point.demand;
    });
  });

  const yesterdayCombined = combined.map((point, i) => ({
    time: point.time,
    demand: types.reduce((sum, type) => sum + METER_DATA[type].yesterday[i].demand, 0)
  }));

  return {
    current: combined,
    comparison: yesterdayCombined
  };
}