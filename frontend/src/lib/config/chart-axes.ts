import { ViewType } from '../../types/analytics';
import { CHART_STYLES } from '../../constants/chartStyles';

export interface XAxisConfig {
  dataKey: string;
  tick: any;
  axisLine: any;
  tickLine: any;
  interval: number | string;
  tickFormatter?: (value: string) => string;
}

/**
 * Get x-axis configuration based on view type
 * Ensures consistent x-axis display across all chart components
 */
export const getXAxisConfig = (view: ViewType): XAxisConfig => {
  const baseConfig = {
    dataKey: 'time',
    tick: CHART_STYLES.axis.tick,
    axisLine: CHART_STYLES.axis.axisLine,
    tickLine: CHART_STYLES.axis.tickLine,
  };

  switch (view) {
    case 'day':
      return {
        ...baseConfig,
        interval: 0, // Show all 24 hours
        tickFormatter: (value: string) => value.split(':')[0], // Show only hour number
      };
    case 'week':
    case 'year':
    case 'multi-year':
      return {
        ...baseConfig,
        interval: 'preserveStartEnd', // Show first and last labels
      };
    case 'month':
      return {
        ...baseConfig,
        interval: 'preserveStartEnd', // Show first and last labels
        tickFormatter: (value: string) => {
          // Remove leading zeros from day numbers for month view
          const num = parseInt(value, 10);
          return isNaN(num) ? value : num.toString();
        },
      };
    default:
      return {
        ...baseConfig,
        interval: 'preserveStartEnd',
      };
  }
};

/**
 * Ensure data has all 24 hours for day view
 * Fills missing hours with zero values
 */
export const ensureAllHoursForDayView = (data: any[], view: ViewType) => {
  if (view !== 'day') return data;

  const hourlyMap = new Map<string, any>();
  
  // Initialize all 24 hours with zero values
  for (let hour = 0; hour < 24; hour++) {
    const timeKey = `${hour}:00`;
    hourlyMap.set(timeKey, {
      time: timeKey,
      actual: 0,
      sequence: hour,
    });
  }
  
  // Fill in actual data where available
  data.forEach((point) => {
    // Handle both "01:00" and "1:00" formats
    // First try exact match
    if (hourlyMap.has(point.time)) {
      hourlyMap.set(point.time, point);
    } else {
      // Try to normalize the time - remove leading zero for single digit hours
      const normalizedTime = point.time.replace(/^0(\d):/, '$1:');
      if (hourlyMap.has(normalizedTime)) {
        hourlyMap.set(normalizedTime, { ...point, time: normalizedTime });
      } else {
        // Try adding leading zero for single digit hours
        const paddedTime = point.time.replace(/^(\d):/, '0$1:');
        if (hourlyMap.has(paddedTime)) {
          hourlyMap.set(paddedTime, { ...point, time: paddedTime });
        }
      }
    }
  });
  
  // Convert map back to array
  return Array.from(hourlyMap.values());
};