// Load Profile Configuration Constants
export interface LoadProfileData {
  time: string;
  demand: number;
  lastWeek?: number;
  date?: string;
  month?: string;
  year?: number;
}

export const PEAK_HOURS = {
  start: 9,  // 9:00
  end: 22,  // 22:00
} as const;

// Generate weekly data based on daily pattern
export const generateWeeklyData = (baseDate: Date = new Date()): LoadProfileData[] => {
  const weekData: LoadProfileData[] = [];
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(baseDate);
    date.setDate(date.getDate() - date.getDay() + i);
    
    // Weekend days have lower consumption
    const isWeekend = i === 0 || i === 6;
    const multiplier = isWeekend ? 0.6 : 1;
    
    const dailyTotal = WORKDAY_LOAD_PROFILE.reduce((sum, hour) => 
      sum + hour.demand * multiplier, 0
    );
    
    weekData.push({
      time: dayNames[i],
      demand: dailyTotal,
      lastWeek: dailyTotal * (0.85 + Math.random() * 0.3), // 85-115% of current
      date: date.toISOString()
    });
  }
  
  return weekData;
};

// Generate monthly data
export const generateMonthlyData = (baseDate: Date = new Date()): LoadProfileData[] => {
  const monthData: LoadProfileData[] = [];
  const daysInMonth = new Date(baseDate.getFullYear(), baseDate.getMonth() + 1, 0).getDate();
  
  for (let i = 1; i <= daysInMonth; i++) {
    const date = new Date(baseDate.getFullYear(), baseDate.getMonth(), i);
    const isWeekend = date.getDay() === 0 || date.getDay() === 6;
    const multiplier = isWeekend ? 0.6 : 1;
    
    const dailyTotal = WORKDAY_LOAD_PROFILE.reduce((sum, hour) => 
      sum + hour.demand * multiplier, 0
    );
    
    monthData.push({
      time: i.toString(),
      demand: dailyTotal,
      lastWeek: dailyTotal * (0.85 + Math.random() * 0.3),
      date: date.toISOString()
    });
  }
  
  return monthData;
};

// Generate yearly data
export const generateYearlyData = (baseDate: Date = new Date()): LoadProfileData[] => {
  const yearData: LoadProfileData[] = [];
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  
  for (let i = 0; i < 12; i++) {
    // Seasonal variations
    let seasonalMultiplier = 1;
    if (i >= 2 && i <= 4) seasonalMultiplier = 1.2; // Spring: Higher
    else if (i >= 5 && i <= 7) seasonalMultiplier = 1.4; // Summer: Highest
    else if (i >= 8 && i <= 10) seasonalMultiplier = 1.1; // Fall: High
    else seasonalMultiplier = 0.9; // Winter: Lower
    
    const monthlyTotal = WORKDAY_LOAD_PROFILE.reduce((sum, hour) => 
      sum + hour.demand * seasonalMultiplier * 30, 0
    );
    
    yearData.push({
      time: monthNames[i],
      demand: monthlyTotal,
      lastWeek: monthlyTotal * (0.85 + Math.random() * 0.3),
      month: monthNames[i],
      year: baseDate.getFullYear()
    });
  }
  
  return yearData;
};
export const WORKDAY_LOAD_PROFILE: LoadProfileData[] = [
  { time: '00:00', demand: 150 },  // Midnight
  { time: '01:00', demand: 140 },
  { time: '02:00', demand: 130 },
  { time: '03:00', demand: 125 },
  { time: '04:00', demand: 120 },
  { time: '05:00', demand: 125 },
  { time: '06:00', demand: 200 },
  { time: '07:00', demand: 300 },
  { time: '08:00', demand: 450 },
  { time: '09:00', demand: 500 },
  { time: '10:00', demand: 550 },
  { time: '11:00', demand: 580 },
  { time: '12:00', demand: 600 },
  { time: '13:00', demand: 620 },
  { time: '14:00', demand: 630 },
  { time: '15:00', demand: 620 },
  { time: '16:00', demand: 570 },
  { time: '17:00', demand: 500 },
  { time: '18:00', demand: 400 },
  { time: '19:00', demand: 300 },
  { time: '20:00', demand: 250 },
  { time: '21:00', demand: 200 },
  { time: '22:00', demand: 175 },
  { time: '23:00', demand: 150 },
  { time: '24:00', demand: 150 },  // Next midnight
];

// Add last week's data for comparison
WORKDAY_LOAD_PROFILE.forEach((data, i) => {
  // Generate slightly different values for last week
  const variation = Math.random() * 0.2 + 0.9; // 90-110% of current value
  data.lastWeek = Math.round(data.demand * variation);
});

// Mock data for yesterday, aiming for a more natural variation
export const YESTERDAY_MOCK_LOAD_PROFILE: LoadProfileData[] = WORKDAY_LOAD_PROFILE.map(item => {
  const hour = parseInt(item.time.split(':')[0], 10);
  let multiplier = 1.0;

  // Higher peak afternoon (13:00 - 16:00) - Reduced intensity
  if (hour >= 13 && hour <= 16) {
    multiplier = 1.08 + (Math.random() * 0.07); // 8-15% higher
  } 
  // Lower overnight/early morning (00:00 - 05:00) - Reduced intensity
  else if (hour >= 0 && hour <= 5) {
     multiplier = 0.90 - (Math.random() * 0.05); // 5-10% lower
  }
  // Slightly lower during morning ramp up (more gradual)
  else if (hour >= 7 && hour <= 9) {
    multiplier = 0.97; // Less reduction
  }
  // Slightly higher evening ramp down
  else if (hour >= 18 && hour <= 20) {
      multiplier = 1.03;
  }

  return {
    ...item,
    // Remove hard floor, just ensure non-negative
    demand: Math.max(0, Math.round(item.demand * multiplier)) 
  };
});

// Helper functions
export const isPeakHour = (hour: number, date?: Date) => {
  // If no date provided, use current date
  const checkDate = date || new Date();
  const dayOfWeek = checkDate.getDay();
  
  // Only weekdays (Monday=1 to Friday=5)
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return false;
  }
  
  return hour >= PEAK_HOURS.start && hour <= PEAK_HOURS.end;
};

export const calculateDailyTotal = (data: LoadProfileData[]) => {
  return data.reduce((sum, hour) => sum + hour.demand, 0);
};