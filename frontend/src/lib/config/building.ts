// Building Configuration Constants
export const BUILDING_CONFIG = {
  gfa: 64545, // Building's Gross Floor Area in m²
  floorArea: 1000, // Area per floor in m²
  energyIntensity: {
    target: {
      bec: 171,  // Building Energy Code
      heps: 141, // High Energy Performance
      econ: 82,  // Energy Conservation
      zeb: 57,   // Zero Energy Building
      zero: 0    // Zero Energy
    },
    descriptions: {
      bec: 'Building Energy Code',
      heps: 'High Energy Performance',
      econ: 'Energy Conservation',
      zeb: 'Zero Energy Building',
      zero: 'Zero Energy'
    }
  }
} as const;

// Building Performance Scale Categories
export const SCALE_CATEGORIES = [
  { id: 'BEC', label: 'BEC', value: BUILDING_CONFIG.energyIntensity.target.bec, color: '#14B8A6', gradient: 'from-teal-500 to-teal-600' }, // Teal 500
  { id: 'HEPS', label: 'HEPS', value: BUILDING_CONFIG.energyIntensity.target.heps, color: '#0D9488', gradient: 'from-teal-600 to-teal-700' }, // Teal 600
  { id: 'ECON', label: 'ECON', value: BUILDING_CONFIG.energyIntensity.target.econ, color: '#0F766E', gradient: 'from-teal-700 to-teal-800' }, // Teal 700
  { id: 'ZEB', label: 'ZEB', value: BUILDING_CONFIG.energyIntensity.target.zeb, color: '#115E59', gradient: 'from-teal-800 to-teal-900' }, // Teal 800
  { id: 'ZERO', label: 'Zero', value: BUILDING_CONFIG.energyIntensity.target.zero, color: '#134E4A' } // Teal 900
] as const;