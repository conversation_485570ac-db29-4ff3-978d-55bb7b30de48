// Common style configurations for the application
// These styles are used across multiple components for consistent UI

export const GRADIENT_STYLES = {
  primary: 'bg-gradient-to-br from-blue-500 to-blue-600',
  secondary: 'bg-gradient-to-br from-gray-100 to-gray-200',
  card: 'bg-gradient-to-br from-white to-gray-50',
  success: 'bg-gradient-to-br from-green-500 to-green-600',
  warning: 'bg-gradient-to-br from-yellow-400 to-yellow-500',
  danger: 'bg-gradient-to-br from-red-500 to-red-600',
};

export const BORDER_STYLES = {
  default: 'border border-gray-200',
  primary: 'border border-blue-200',
  card: 'border border-gray-100 rounded-lg',
  input: 'border border-gray-300 focus:border-blue-500 rounded-md',
  divider: 'border-t border-gray-100',
};

export const SHADOW_STYLES = {
  sm: 'shadow-sm',
  md: 'shadow-md',
  lg: 'shadow-lg',
  card: 'shadow-sm',
  hover: {
    primary: 'hover:shadow-md transition-shadow duration-300',
    secondary: 'hover:shadow-lg transition-shadow duration-300',
  },
};

export const ANIMATION_STYLES = {
  transition: 'transition-all duration-300 ease-in-out',
  pulse: 'animate-pulse',
  spin: 'animate-spin',
  bounce: 'animate-bounce',
};

export const TEXT_STYLES = {
  heading: {
    h1: 'text-3xl font-bold text-gray-900',
    h2: 'text-2xl font-bold text-gray-800',
    h3: 'text-xl font-semibold text-gray-800',
    h4: 'text-lg font-semibold text-gray-700',
  },
  body: {
    default: 'text-base text-gray-600',
    small: 'text-sm text-gray-500',
    large: 'text-lg text-gray-700',
  },
  colors: {
    primary: 'text-blue-600',
    secondary: 'text-gray-600',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    danger: 'text-red-600',
    muted: 'text-gray-400',
  },
};

export const BUTTON_STYLES = {
  base: 'px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2',
  primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
  secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500',
  success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
  danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
  outline: {
    primary: 'border border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500',
    secondary: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500',
  },
  sizes: {
    sm: 'px-2 py-1 text-sm',
    md: 'px-4 py-2',
    lg: 'px-6 py-3 text-lg',
  },
};

export const LAYOUT_STYLES = {
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  section: 'py-6 sm:py-8 lg:py-12',
  card: 'bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden',
  grid: {
    cols2: 'grid grid-cols-1 md:grid-cols-2 gap-6',
    cols3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
    cols4: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6',
  },
  flex: {
    center: 'flex items-center justify-center',
    between: 'flex items-center justify-between',
    start: 'flex items-start justify-start',
    column: 'flex flex-col',
  },
};
