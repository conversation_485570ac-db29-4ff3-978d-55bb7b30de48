// Unit Configuration
export const UNITS = {
  power: {
    name: 'Power',
    base: 'kW',
    description: 'Instantaneous power demand'
  },
  energy: {
    name: 'Energy',
    base: 'kWh',
    description: 'Energy consumption over time'
  }
} as const;

// Unit mapping by view type
export const VIEW_UNITS = {
  daily: 'energy',    // Daily view shows energy consumption (kWh)
  weekly: 'energy',  // Weekly shows energy consumption (kWh)
  monthly: 'energy', // Monthly shows energy consumption (kWh)
  yearly: 'energy',  // Yearly shows energy consumption (kWh)
  'multi-year': 'energy' // Multi-year shows energy consumption (kWh)
} as const;

// Helper functions
export function getUnitForView(view: keyof typeof VIEW_UNITS): string {
  const unitType = VIEW_UNITS[view];
  return UNITS[unitType].base;
}

// Convert power (kW) to energy (kWh) based on time period
export function powerToEnergy(power: number, hours: number): number {
  return power * hours;
}

// Convert energy (kWh) to average power (kW)
export function energyToPower(energy: number, hours: number): number {
  return energy / hours;
}