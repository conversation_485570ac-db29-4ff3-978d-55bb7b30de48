// Meter Types Configuration
import type { MeterType } from '../../types';

export const METER_TYPES = {
  chillerPlant: {
    name: 'Chiller Plant',
    description: 'Central cooling system including chillers and cooling towers'
  },
  airSide: {
    name: 'Air Side',
    description: 'Air handling units and ventilation systems'
  },
  light_power: {
    name: 'Light & Power',
    description: 'Lighting systems and general power outlets'
  },
  lightPower: {
    name: 'Light & Power',
    description: 'Lighting systems and general power outlets'
  },
  data_center_others: {
    name: 'Data Center & Others',
    description: 'Data center IT equipment and other miscellaneous loads'
  },
  data_center_it: {
    name: 'Data Center IT',
    description: 'Data center IT equipment'
  },
  evCharger: {
    name: 'EV Charger',
    description: 'Electric vehicle charging stations'
  },
  escalator_elevator: {
    name: 'Escalator/Elevator',
    description: 'Power consumption for escalators and elevators'
  },
  tenant: {
    name: 'Tenant',
    description: 'Tenant specific meters'
  },
  others: {
    name: 'Others',
    description: 'Other miscellaneous meters'
  }
} as const;

// Main Distribution Meters
export const MAIN_METERS = {
  'MDB-1': { name: 'Main Distribution Board 1', type: 'power' },
  'MDB-2': { name: 'Main Distribution Board 2', type: 'power' },
  'MDB-3': { name: 'Main Distribution Board 3', type: 'power' },
  'MDB-4': { name: 'Main Distribution Board 4', type: 'power' },
  'EMDB-1': { name: 'Emergency MDB 1', type: 'power' },
  'EMDB-2': { name: 'Emergency MDB 2', type: 'power' }
} as const;