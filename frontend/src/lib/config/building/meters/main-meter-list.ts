import type { MeterType } from '../../../../types';

interface MeterGroup {
  name: string;
  meters: Array<{
    id: string;
    name: string;
    type: MeterType;
    system: 'BAS' | 'Power Meter';
  }>;
}

// Main Meter List based on provided data - 172 meters total
export const MAIN_METER_LIST: Record<string, MeterGroup> = {
  main: {
    name: 'Main',
    meters: [
      { id: 'HVSG', name: 'HVSG', type: 'data_center_others', system: 'BAS' },
      { id: 'HV-SS-1', name: 'HV-SS # 1', type: 'data_center_others', system: 'BAS' },
      { id: 'HV-HK-2', name: 'HV-HK # 2', type: 'data_center_others', system: 'BAS' },
      { id: 'MDB-OF01', name: 'MDB OF01', type: 'data_center_others', system: 'BAS' },
      { id: 'MDB-OF02', name: 'MDB OF02', type: 'data_center_others', system: 'BAS' },
      { id: 'MDB-OF03', name: 'MDB OF03', type: 'data_center_others', system: 'BAS' },
      { id: 'MDB-OF04', name: 'MDB OF04', type: 'data_center_others', system: 'BAS' },
      { id: 'EMDB01', name: 'EMDB01', type: 'data_center_others', system: 'BAS' },
      { id: 'EMDB02', name: 'EMDB02', type: 'data_center_others', system: 'BAS' },
      { id: 'UMDB', name: 'UMDB', type: 'data_center_others', system: 'BAS' }
    ]
  },
  
  mdb1: {
    name: 'MDB1',
    meters: [
      { id: 'T-1DB1', name: 'T-1DB1', type: 'data_center_others', system: 'BAS' },
      { id: 'AMCC1', name: 'AMCC1', type: 'airSide', system: 'BAS' },
      { id: 'T-BDB1', name: 'T-BDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'CH-01', name: 'CH-01', type: 'chillerPlant', system: 'BAS' },
      { id: 'BUSDUCT-T2', name: 'Busduct T2', type: 'data_center_others', system: 'BAS' },
      { id: 'BUSDUCT-C1', name: 'Busduct C1', type: 'data_center_others', system: 'BAS' }
    ]
  },

  mdb2: {
    name: 'MDB2',
    meters: [
      { id: 'BUSDUCT-C1-MDB2', name: 'Busduct C1', type: 'data_center_others', system: 'BAS' },
      { id: 'CH-2', name: 'CH-2', type: 'chillerPlant', system: 'BAS' },
      { id: 'AMCC2', name: 'AMCC2', type: 'airSide', system: 'BAS' }
    ]
  },

  mdb3: {
    name: 'MDB3',
    meters: [
      { id: 'P-BDB1', name: 'P-BDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'CH-3', name: 'CH-3', type: 'chillerPlant', system: 'BAS' },
      { id: 'BUSDUCT-T1', name: 'Busduct T1', type: 'data_center_others', system: 'BAS' },
      { id: 'BUSDUCT-P1', name: 'Busduct P1', type: 'data_center_others', system: 'BAS' }
    ]
  },

  mdb4: {
    name: 'MDB4',
    meters: [
      { id: 'C-BDB-MU', name: 'C-BDB-MU', type: 'data_center_others', system: 'BAS' },
      { id: 'AMCC3', name: 'AMCC3', type: 'airSide', system: 'BAS' },
      { id: 'P-1DB1', name: 'P-1DB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-2DB1', name: 'T-2DB1', type: 'data_center_others', system: 'BAS' },
      { id: 'AMCC5', name: 'AMCC5', type: 'airSide', system: 'BAS' },
      { id: 'CH-4', name: 'CH-4', type: 'chillerPlant', system: 'BAS' }
    ]
  },

  emdb1: {
    name: 'EMDB1',
    meters: [
      { id: 'T-2EDB1', name: 'T-2EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-5EDB1', name: 'T-5EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-11EDB1', name: 'T-11EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-17EDB1', name: 'T-17EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-23EDB1', name: 'T-23EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-28EDB1', name: 'T-28EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'B-EPB-01', name: 'B-EPB-01', type: 'data_center_others', system: 'BAS' },
      { id: 'C-PL1', name: 'C-PL1', type: 'escalator_elevator', system: 'BAS' },
      { id: 'T-PL1', name: 'T-PL1', type: 'escalator_elevator', system: 'BAS' },
      { id: 'T-PL2', name: 'T-PL2', type: 'escalator_elevator', system: 'BAS' },
      { id: 'T-PL3', name: 'T-PL3', type: 'escalator_elevator', system: 'BAS' },
      { id: 'EAMCC4', name: 'EAMCC4', type: 'airSide', system: 'BAS' }
    ]
  },

  emdb2: {
    name: 'EMDB2',
    meters: [
      { id: 'P-1EDB1', name: 'P-1EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'P-BEDB1', name: 'P-BEDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-BEDB1', name: 'T-BEDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-1EDB1', name: 'T-1EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-8EDB1', name: 'T-8EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-14EDB1', name: 'T-14EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-20EDB1', name: 'T-20EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'C-BEDB1', name: 'C-BEDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-26EDB1', name: 'T-26EDB1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-PL4', name: 'T-PL4', type: 'escalator_elevator', system: 'BAS' },
      { id: 'P-PL-1', name: 'P-PL-1', type: 'escalator_elevator', system: 'BAS' },
      { id: 'P-FL-1', name: 'P-FL-1', type: 'escalator_elevator', system: 'BAS' },
      { id: 'T-FL-1', name: 'T-FL-1', type: 'escalator_elevator', system: 'BAS' },
      { id: 'G-EPB-02', name: 'G-EPB-02', type: 'data_center_others', system: 'BAS' },
      { id: 'G-FPB-03', name: 'G-FPB-03', type: 'data_center_others', system: 'BAS' },
      { id: 'EAMCC6', name: 'EAMCC6', type: 'airSide', system: 'BAS' }
    ]
  },

  towerA: {
    name: 'อาคาร A',
    meters: [
      { id: 'T-BDB1-A', name: 'T- BDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-1DB1-A', name: 'T -1DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-2DB1-A', name: 'T- 2DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-4DB1', name: 'T- 4DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-5DB1', name: 'T- 5DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-6DB1', name: 'T -6DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-7DB1', name: 'T- 7DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-8DB1', name: 'T- 8DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-9DB1', name: 'T- 9DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-10DB1', name: 'T -10DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-10ADB1', name: 'T- 10A DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-11DB1', name: 'T- 11DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-12DB1', name: 'T- 12DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-14DB1', name: 'T- 14DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-15DB1', name: 'T- 15DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-16DB1', name: 'T- 16DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-17DB1', name: 'T- 17DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-18DB1', name: 'T- 18DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-19DB1', name: 'T- 19DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-20DB1', name: 'T- 20DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-21DB1', name: 'T- 21DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-22DB1', name: 'T- 22DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-23DB1', name: 'T- 23DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-24DB1', name: 'T- 24DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-25DB1', name: 'T- 25DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-26DB1', name: 'T- 26DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-27DB1', name: 'T- 27DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-28DB1', name: 'T- 28DB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-BEDB1-A', name: 'T-BEDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-1EDB1-A', name: 'T-1EDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-2EDB1-A', name: 'T-2EDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-5EDB1-A', name: 'T-5 EDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-8EDB1-A', name: 'T-8 EDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-10AEDB1', name: 'T-10A EDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-12EDB1', name: 'T-12 EDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-16EDB1', name: 'T-16EDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-19EDB1', name: 'T-19EDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-22EDB1', name: 'T-22EDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-25EDB1', name: 'T-25EDB 1', type: 'lightPower', system: 'BAS' },
      { id: 'T-28EDB1-A', name: 'T-28 EDB 1', type: 'lightPower', system: 'BAS' }
    ]
  },

  towerB: {
    name: 'อาคาร B',
    meters: [
      { id: 'P-BDB1-B', name: 'P-BDB 1 (ห้องสมุดหาวาย)', type: 'lightPower', system: 'BAS' },
      { id: 'P-BEDB1-B', name: 'P-BEDB1 (ห้องสมุดมาราย)', type: 'lightPower', system: 'BAS' },
      { id: 'P-1DB1-B', name: 'P-1DB1', type: 'lightPower', system: 'BAS' },
      { id: 'P-3DB1', name: 'P-3DB1', type: 'lightPower', system: 'BAS' },
      { id: 'P-6DB1', name: 'P-6DB1', type: 'lightPower', system: 'BAS' },
      { id: 'P-7DB1', name: 'P-7DB1', type: 'lightPower', system: 'BAS' },
      { id: 'P-1EDB1-B', name: 'P-1EDB1', type: 'lightPower', system: 'BAS' },
      { id: 'P-3EDB1', name: 'P-3EDB1', type: 'lightPower', system: 'BAS' },
      { id: 'P-6EDB1', name: 'P-6EDB1', type: 'lightPower', system: 'BAS' },
      { id: 'P-7EDB1', name: 'P-7EDB1', type: 'lightPower', system: 'BAS' },
      { id: 'P-1UDB-1', name: 'P-1UDB-1', type: 'data_center_others', system: 'BAS' }
    ]
  },

  towerC: {
    name: 'อาคาร C',
    meters: [
      { id: 'SET-TRADE', name: 'Set Trade (Amazon)', type: 'tenant', system: 'Power Meter' },
      { id: 'C-BDB1-MU-C', name: 'C-BDB1-MU (Investory)', type: 'lightPower', system: 'BAS' },
      { id: 'C-BEDB1-C', name: 'C-BEDB1 (Investory)', type: 'lightPower', system: 'BAS' },
      { id: 'C-2DB1', name: 'C-2DB1', type: 'lightPower', system: 'BAS' },
      { id: 'C-2EV1', name: 'C-2EV1 (EV Charger)', type: 'data_center_others', system: 'BAS' },
      { id: 'C-8DB1', name: 'C-8DB1', type: 'lightPower', system: 'BAS' },
      { id: 'C-9SDB', name: 'C-9SDB (Solar Cell)', type: 'data_center_others', system: 'BAS' },
      { id: 'C-9DB1', name: 'C-9DB1', type: 'lightPower', system: 'BAS' },
      { id: 'C-2EDB1', name: 'C-2EDB1', type: 'lightPower', system: 'BAS' },
      { id: 'C-1UDB-1', name: 'C-1UDB-1', type: 'data_center_others', system: 'BAS' }
    ]
  },

  foodCourt: {
    name: 'ศูนย์อาหาร',
    meters: [
      { id: 'CB-CS1', name: 'CB-CS1 (ร้านค้า 1)', type: 'tenant', system: 'BAS' },
      { id: 'CB-CS2', name: 'CB-CS2 (ร้านค้า 2)', type: 'tenant', system: 'BAS' },
      { id: 'CB-CS3', name: 'CB-CS3 (ร้านค้า 3)', type: 'tenant', system: 'BAS' },
      { id: 'CB-CS4', name: 'CB-CS4 (ร้านค้า 4)', type: 'tenant', system: 'BAS' },
      { id: 'CB-CS5', name: 'CB-CS5 (ร้านค้า 5)', type: 'tenant', system: 'BAS' },
      { id: 'CB-CS6', name: 'CB-CS6 (ร้านค้า 6)', type: 'tenant', system: 'BAS' }
    ]
  },

  towerA6Floor: {
    name: 'อาคาร A ชั้น 6 ผู้เช่า',
    meters: [
      { id: 'COFFEE-CANTEEN', name: 'Coffee Canteen', type: 'tenant', system: 'Power Meter' },
      { id: 'FITCO', name: 'FITCO', type: 'tenant', system: 'Power Meter' },
      { id: 'TIA', name: 'TIA', type: 'tenant', system: 'Power Meter' },
      { id: 'TFPA', name: 'TFPA', type: 'tenant', system: 'Power Meter' },
      { id: 'IAA', name: 'IAA', type: 'tenant', system: 'Power Meter' },
      { id: 'MAI', name: 'MAI', type: 'tenant', system: 'Power Meter' },
      { id: 'TLCA', name: 'TLCA', type: 'tenant', system: 'Power Meter' },
      { id: 'CBCE', name: 'CBCE', type: 'tenant', system: 'Power Meter' },
      { id: 'MEETING', name: 'Meeting', type: 'tenant', system: 'Power Meter' }
    ]
  },

  aisStation: {
    name: 'สถานีโทรศัพท์ไร้สาย AIS',
    meters: [
      { id: 'AIS', name: 'AIS', type: 'data_center_others', system: 'Power Meter' }
    ]
  },

  trueStation: {
    name: 'สถานีโทรศัพท์ไร้สาย TRUE',
    meters: [
      { id: 'TRUE', name: 'Ture', type: 'data_center_others', system: 'Power Meter' }
    ]
  },

  ups: {
    name: 'UPS',
    meters: [
      { id: 'T-2UBD-1', name: 'T-2UBD-1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-8UBD-1', name: 'T-8UBD-1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-16UBD-1', name: 'T-16UBD-1', type: 'data_center_others', system: 'BAS' },
      { id: 'P-1UDB-1-UPS', name: 'P-1UDB-1', type: 'data_center_others', system: 'BAS' },
      { id: 'C-1UDB-1-UPS', name: 'C-1UDB-1', type: 'data_center_others', system: 'BAS' }
    ]
  },

  chillerPlant: {
    name: 'Chiller Plant',
    meters: [
      { id: 'CH-01-CP', name: 'CH-01', type: 'chillerPlant', system: 'BAS' },
      { id: 'CH-02-CP', name: 'CH-02', type: 'chillerPlant', system: 'BAS' },
      { id: 'CH-03-CP', name: 'CH-03', type: 'chillerPlant', system: 'BAS' },
      { id: 'ECH-4-CP', name: 'ECH-4', type: 'chillerPlant', system: 'BAS' },
      { id: 'AMCC1-CP', name: 'AMCC1', type: 'airSide', system: 'BAS' },
      { id: 'AMCC2-CP', name: 'AMCC2', type: 'airSide', system: 'BAS' },
      { id: 'AMCC3-CP', name: 'AMCC3', type: 'airSide', system: 'BAS' },
      { id: 'EAMCC4-CP', name: 'EAMCC4', type: 'airSide', system: 'BAS' },
      { id: 'AMCC5-CP', name: 'AMCC5', type: 'airSide', system: 'BAS' },
      { id: 'EAMCC6-CP', name: 'EAMCC6', type: 'airSide', system: 'BAS' }
    ]
  },

  chiller: {
    name: 'Chiller',
    meters: [
      { id: 'CH-01-CH', name: 'CH-01', type: 'chillerPlant', system: 'BAS' },
      { id: 'CH-02-CH', name: 'CH-02', type: 'chillerPlant', system: 'BAS' },
      { id: 'CH-03-CH', name: 'CH-03', type: 'chillerPlant', system: 'BAS' },
      { id: 'ECH-4-CH', name: 'ECH-4', type: 'chillerPlant', system: 'BAS' }
    ]
  },

  pumpForChillerPlant: {
    name: 'Pump for Chiller Plant',
    meters: [
      { id: 'AMCC1-PCP', name: 'AMCC1', type: 'airSide', system: 'BAS' },
      { id: 'AMCC2-PCP', name: 'AMCC2', type: 'airSide', system: 'BAS' },
      { id: 'AMCC3-PCP', name: 'AMCC3', type: 'airSide', system: 'BAS' },
      { id: 'EAMCC4-PCP', name: 'EAMCC4', type: 'airSide', system: 'BAS' }
    ]
  },

  coolingTower: {
    name: 'Cooling Tower',
    meters: [
      { id: 'AMCC5-CT', name: 'AMCC5', type: 'airSide', system: 'BAS' },
      { id: 'EAMCC6-CT', name: 'EAMCC6', type: 'airSide', system: 'BAS' }
    ]
  },

  elevatorEscalator: {
    name: 'Elevator & Escalator',
    meters: [
      { id: 'T-FL-1-EE', name: 'T-FL-1', type: 'escalator_elevator', system: 'BAS' },
      { id: 'T-PL1-EE', name: 'T-PL1', type: 'escalator_elevator', system: 'BAS' },
      { id: 'T-PL2-EE', name: 'T-PL2', type: 'escalator_elevator', system: 'BAS' },
      { id: 'T-PL3-EE', name: 'T-PL3', type: 'escalator_elevator', system: 'BAS' },
      { id: 'T-PL4-EE', name: 'T-PL4', type: 'escalator_elevator', system: 'BAS' },
      { id: 'P-PL-1-EE', name: 'P-PL-1', type: 'escalator_elevator', system: 'BAS' },
      { id: 'P-FL-1-EE', name: 'P-FL-1', type: 'escalator_elevator', system: 'BAS' },
      { id: 'C-PL1-EE', name: 'C-PL1', type: 'escalator_elevator', system: 'BAS' }
    ]
  },

  pumpStation: {
    name: 'Pump Station',
    meters: [
      { id: 'T-BDB1-PS', name: 'T- BDB 1', type: 'data_center_others', system: 'BAS' },
      { id: 'T-BEDB1-PS', name: 'T-BEDB 1', type: 'data_center_others', system: 'BAS' }
    ]
  },

  wastewaterTreatment: {
    name: 'Wastewater Treatment Plant',
    meters: [
      { id: 'G-EPB-02-WT', name: 'G-EPB-02', type: 'data_center_others', system: 'BAS' },
      { id: 'G-FPB-03-WT', name: 'G-FPB-03', type: 'data_center_others', system: 'BAS' }
    ]
  }
};

// Helper function to get total meter count
export function getMainMetersTotalCount(): number {
  return Object.values(MAIN_METER_LIST).reduce((total, group) => {
    return total + group.meters.length;
  }, 0);
}

// Helper function to get all meters flat list
export function getAllMainMetersFlat() {
  return Object.entries(MAIN_METER_LIST).flatMap(([groupKey, group]) => 
    group.meters.map(meter => ({
      ...meter,
      groupKey,
      groupName: group.name
    }))
  );
}

// Helper function to get meters by system
export function getMainMetersBySystem(system: 'BAS' | 'Power Meter') {
  return getAllMainMetersFlat().filter(meter => meter.system === system);
}

// Helper function to get meters by type
export function getMainMetersByType(type: MeterType) {
  return getAllMainMetersFlat().filter(meter => meter.type === type);
}

// Helper function to get meters by group
export function getMainMetersByGroup(groupKey: keyof typeof MAIN_METER_LIST) {
  return MAIN_METER_LIST[groupKey]?.meters || [];
}