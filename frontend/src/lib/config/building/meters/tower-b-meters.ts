// Tower B (Podium Building) Meter Configuration
export const TOWER_B_METERS = {
  // Basement Floor
  'B': {
    panels: {
      'P-BDB1': {
        name: 'P-BDB1',
        meters: [
          { id: 'P-BLP1', name: 'P-BLP1', type: 'Lighting' },
          { id: 'P-BLP2', name: 'P-BLP2', type: 'Lighting' },
          { id: 'P-BPP1', name: 'P-BPP1', type: 'Power' },
          { id: 'P-BPP2', name: 'P-BPP2', type: 'Power' },
          { id: 'P-DB-CDB-B1', name: 'P-DB-CDB-B1', type: 'Power' }
        ]
      },
      'P-EBDB1': {
        name: 'P-EBDB1',
        meters: [
          { id: 'P-EDB-AHU-B2', name: 'P-EDB-AHU-B2', type: 'airSide' },
          { id: 'P-EBLP1', name: 'P-EBLP1', type: 'Lighting' },
          { id: 'P-ELP2', name: 'P-ELP2', type: 'Lighting' },
          { id: 'P-EBPP1', name: 'P-EBPP1', type: 'Power' },
          { id: 'P-EBPP2', name: 'P-EBPP2', type: 'Power' }
        ]
      }
    }
  },

  // 1st Floor
  1: {
    panels: {
      'P-1DB1': {
        name: 'P-1DB1',
        meters: [
          { id: 'P-1LP1', name: 'P-1LP1', type: 'Lighting' },
          { id: 'P-1PP2', name: 'P-1PP2', type: 'Power' },
          { id: 'Space-LP2', name: 'Space(LP2)', type: 'Lighting' },
          { id: 'Space-PP2', name: 'Space(PP2)', type: 'Power' },
          { id: 'CB-ES1', name: 'CB-ES1', type: 'equipment' },
          { id: 'CB-ES2', name: 'CB-ES2', type: 'equipment' },
          { id: 'CB-ES3', name: 'CB-ES3', type: 'equipment' },
          { id: 'CB-ES4', name: 'CB-ES4', type: 'equipment' }
        ]
      },
      'P-1EDB1': {
        name: 'P-1EDB1',
        meters: [
          { id: 'P-3EDB1', name: 'P-3EDB1', type: 'Power' },
          { id: 'P-6EDB1', name: 'P-6EDB1', type: 'Power' },
          { id: 'P-7EDB1', name: 'P-7EDB1', type: 'Power' },
          { id: 'ELC01', name: 'ELC01', type: 'Landscape' },
          { id: 'P-7EDB2', name: 'P-7EDB2', type: 'Lighting' },
          { id: 'P-1EPP1', name: 'P-1EPP1', type: 'Power' },
          { id: 'P-2EPP2', name: 'P-2EPP2', type: 'Power' },
          { id: 'P-2EPP1', name: 'P-2EPP1', type: 'Power' }
        ]
      }
    }
  },

  // 3rd Floor
  3: {
    panels: {
      'P-3DB1': {
        name: 'P-3DB1',
        meters: [
          { id: 'CB-ES8', name: 'CB-ES8', type: 'equipment' },
          { id: 'CB-LED-SCREEN', name: 'CB-LED SCREEN', type: 'Lighting' },
          { id: 'P-3LP1', name: 'P-3LP1', type: 'Lighting' },
          { id: 'P-3LPP1', name: 'P-3LPP1', type: 'Power' },
          { id: 'P-5LP1', name: 'P-5LP1', type: 'Lighting' },
          { id: 'P-5PP1', name: 'P-5PP1', type: 'Power' },
          { id: 'P-5LC1', name: 'P-5LC1', type: 'Power' },
          { id: 'P-DB-AHU-051', name: 'P-DB-AHU-051', type: 'airSide' },
          { id: 'P-DB-AHU-031', name: 'P-DB-AHU-031', type: 'airSide' },
          { id: 'P-DB-AHU-032', name: 'P-DB-AHU-032', type: 'airSide' },
          { id: 'CB-ES5', name: 'CB-ES5', type: 'equipment' },
          { id: 'CB-ES6', name: 'CB-ES6', type: 'equipment' },
          { id: 'CB-ES7', name: 'CB-ES7', type: 'equipment' }
        ]
      },
      'P-3EDB1': {
        name: 'P-3EDB1',
        meters: [
          { id: 'P-3ELP1', name: 'P-3ELP1', type: 'Lighting' },
          { id: 'P-5ELP1', name: 'P-5ELP1', type: 'Lighting' },
          { id: 'P-5EPP1', name: 'P-5EPP1', type: 'Power' },
          { id: 'P-5ELC1', name: 'P-5ELC1', type: 'Power' }
        ]
      }
    }
  },

  // 6th Floor
  6: {
    panels: {
      'P-6DB1': {
        name: 'P-6DB1',
        meters: [
          { id: 'P-6LP1', name: 'P-6LP1', type: 'Lighting' },
          { id: 'P-6PP1', name: 'P-6PP1', type: 'Power' },
          { id: 'P-6LCM1', name: 'P-6LCM1', type: 'Power' },
          { id: 'P-6LCM2', name: 'P-6LCM2', type: 'Power' },
          { id: 'P-6LCM3', name: 'P-6LCM3', type: 'Power' },
          { id: 'P-6LCM4', name: 'P-6LCM4', type: 'Power' },
          { id: 'P-6LCM5', name: 'P-6LCM5', type: 'Power' },
          { id: 'CB-ES9', name: 'CB-ES9', type: 'equipment' },
          { id: 'CB-ES10', name: 'CB-ES10', type: 'equipment' },
          { id: 'P-DB-AHU-061', name: 'P-DB-AHU-061', type: 'airSide' }
        ]
      },
      'P-6EDB1': {
        name: 'P-6EDB1',
        meters: [
          { id: 'P-6ELP1', name: 'P-6ELP1', type: 'Lighting' },
          { id: 'P-6EPP1', name: 'P-6EPP1', type: 'Power' },
          { id: 'P-6ELCM1', name: 'P-6ELCM1', type: 'Power' }
        ]
      }
    }
  },

  // 7th Floor
  7: {
    panels: {
      'P-7DB1': {
        name: 'P-7DB1',
        meters: [
          { id: 'P-7LP1', name: 'P-7LP1', type: 'Lighting' },
          { id: 'P-7PP1', name: 'P-7PP1', type: 'Power' },
          { id: 'P-RLCO-7F', name: 'P-RLCO 7F', type: 'Landscape' },
          { id: 'P-7LCM1', name: 'P-7LCM1', type: 'Power' },
          { id: 'P-7LCM2', name: 'P-7LCM2', type: 'Power' },
          { id: 'P-7LCM3', name: 'P-7LCM3', type: 'Power' },
          { id: 'P-7LCM4', name: 'P-7LCM4', type: 'Power' },
          { id: 'P-7LCM5', name: 'P-7LCM5', type: 'Power' },
          { id: 'P-7LCM6', name: 'P-7LCM6', type: 'Power' },
          { id: 'CB-ES11', name: 'CB-ES11', type: 'equipment' },
          { id: 'CB-ES12', name: 'CB-ES12', type: 'equipment' },
          { id: 'P-DB-AHU-071', name: 'P-DB-AHU-071', type: 'airSide' }
        ]
      },
      'P-7EDB1': {
        name: 'P-7EDB1',
        meters: [
          { id: 'P-7ELP1', name: 'P-7ELP1', type: 'Lighting' },
          { id: 'P-7EPP1', name: 'P-7EPP1', type: 'Power' },
          { id: 'P-7ELCM1', name: 'P-7ELCM1', type: 'Power' },
          { id: 'P-RLCO-7E', name: 'P-RLCO 7E', type: 'Landscape' }
        ]
      }
    }
  }
} as const;