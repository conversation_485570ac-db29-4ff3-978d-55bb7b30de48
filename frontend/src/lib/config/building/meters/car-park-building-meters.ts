import type { MeterType } from '../../../../types';

interface CarParkMeter {
  id: string;
  name: string;
  type: MeterType;
  floor: string;
  category: string;
}

// Car Park Building Meter List - 49 meters total
export const CAR_PARK_BUILDING_METERS: Record<string, CarParkMeter[]> = {
  floorB: [
    // Office Equipment (1)
    { id: 'C-BEDB-MU', name: 'C-BEDB-MU', type: 'data_center_others', floor: 'B', category: 'Office Equipment' },
    // Sanitary (2)
    { id: 'B-EPB-06', name: 'B-EPB-06', type: 'data_center_others', floor: 'B', category: 'Sanitary' },
    { id: 'E-EPB-07', name: 'E-EPB-07', type: 'data_center_others', floor: 'B', category: 'Sanitary' }
  ],
  
  floor1: [
    // Interior Lighting (2)
    { id: 'C-1LP1', name: 'C-1LP1', type: 'lightPower', floor: '1', category: 'Interior Lighting' },
    { id: 'C-1ELP1', name: 'C-1ELP1', type: 'lightPower', floor: '1', category: 'Interior Lighting' },
    // Air side (1)
    { id: 'EDB-CP-KEF-011', name: 'EDB-CP-KEF-011', type: 'airSide', floor: '1', category: 'Air side' },
    // Office Equipment (12)
    { id: 'C-1EDB1', name: 'C-1EDB1', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    { id: 'C-1PP2', name: 'C-1PP2', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    { id: 'CB-CS1-CP', name: 'CB-CS1', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    { id: 'CB-CS2-CP', name: 'CB-CS2', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    { id: 'CB-CS3-CP', name: 'CB-CS3', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    { id: 'CB-CS4-CP', name: 'CB-CS4', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    { id: 'CB-CS5-CP', name: 'CB-CS5', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    { id: 'CB-CS6-CP', name: 'CB-CS6', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    { id: 'CB-EXD', name: 'CB-EXD', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    { id: 'SPARE', name: 'SPARE', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    { id: 'CB-MDF2', name: 'CB-MDF2', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    { id: 'C-1UDB-1-CP', name: 'C-1UDB-1', type: 'data_center_others', floor: '1', category: 'Office Equipment' },
    // Sanitary (1)
    { id: 'G-EPB-01', name: 'G-EPB-01', type: 'data_center_others', floor: '1', category: 'Sanitary' }
  ],

  floor2: [
    // Interior Lighting (2)
    { id: 'C-2LP1', name: 'C-2LP1', type: 'lightPower', floor: '2', category: 'Interior Lighting' },
    { id: 'C-2ELP1', name: 'C-2ELP1', type: 'lightPower', floor: '2', category: 'Interior Lighting' },
    // Office Equipment (1)
    { id: 'C-2EDB1-CP', name: 'C-2EDB1', type: 'data_center_others', floor: '2', category: 'Office Equipment' },
    // EV Charger (1)
    { id: 'EV-C-2DB', name: 'EV-C-2DB', type: 'data_center_others', floor: '2', category: 'EV Charger' }
  ],

  floor5: [
    // Interior Lighting (2)
    { id: 'C-5LP2', name: 'C-5LP2', type: 'lightPower', floor: '5', category: 'Interior Lighting' },
    { id: 'C-5ELP1', name: 'C-5ELP1', type: 'lightPower', floor: '5', category: 'Interior Lighting' }
  ],

  floor7: [
    // Interior Lighting (1)
    { id: 'C-7LP1', name: 'C-7LP1', type: 'lightPower', floor: '7', category: 'Interior Lighting' },
    // Office Equipment (2)
    { id: 'P-7PP1-CP', name: 'P-7PP1', type: 'data_center_others', floor: '7', category: 'Office Equipment' },
    { id: 'C-7ELC1', name: 'C-7ELC1', type: 'data_center_others', floor: '7', category: 'Office Equipment' }
  ],

  floor8: [
    // Interior Lighting (1)
    { id: 'C-8LP1', name: 'C-8LP1', type: 'lightPower', floor: '8', category: 'Interior Lighting' },
    // Air side (3)
    { id: 'C-DB-AHU-081', name: 'C-DB-AHU-081', type: 'airSide', floor: '8', category: 'Air side' },
    { id: 'C-DB-AHU-082', name: 'C-DB-AHU-082', type: 'airSide', floor: '8', category: 'Air side' },
    { id: 'C-DB-AHU-083', name: 'C-DB-AHU-083', type: 'airSide', floor: '8', category: 'Air side' },
    // Office Equipment (3)
    { id: 'C-8PP1', name: 'C-8PP1', type: 'data_center_others', floor: '8', category: 'Office Equipment' },
    { id: 'C-8ELC1', name: 'C-8ELC1', type: 'data_center_others', floor: '8', category: 'Office Equipment' },
    { id: 'C-8EDB1', name: 'C-8EDB1', type: 'data_center_others', floor: '8', category: 'Office Equipment' }
  ],

  floor9: [
    // Interior Lighting (2)
    { id: 'C-9LP1', name: 'C-9LP1', type: 'lightPower', floor: '9', category: 'Interior Lighting' },
    { id: 'C-9LP2', name: 'C-9LP2', type: 'lightPower', floor: '9', category: 'Interior Lighting' },
    // Office Equipment (4)
    { id: 'C-9EDB1', name: 'C-9EDB1', type: 'data_center_others', floor: '9', category: 'Office Equipment' },
    { id: 'C-9PP1', name: 'C-9PP1', type: 'data_center_others', floor: '9', category: 'Office Equipment' },
    { id: 'C-9ELC1-1', name: 'C-9ELC1', type: 'data_center_others', floor: '9', category: 'Office Equipment' },
    { id: 'C-9ELC1-2', name: 'C-9ELC1', type: 'data_center_others', floor: '9', category: 'Office Equipment' }
  ],

  floor10: [
    // Elevator & Escalator (1)
    { id: 'C-PL1-CP', name: 'C-PL1', type: 'escalator_elevator', floor: '10', category: 'Elevator & Escalator' },
    // Data Center (6)
    { id: 'MCC-B', name: 'MCC-B', type: 'data_center_it', floor: '10', category: 'Data Center' },
    { id: 'DB-CRAC-B', name: 'DB CRAC-B', type: 'data_center_it', floor: '10', category: 'Data Center' },
    { id: 'DB-IN-B1', name: 'DB IN-B/1', type: 'data_center_it', floor: '10', category: 'Data Center' },
    { id: 'ESN-B1', name: 'ESN-B/1', type: 'data_center_it', floor: '10', category: 'Data Center' },
    { id: 'ATS-EDB10', name: 'ATS-EDB10', type: 'data_center_it', floor: '10', category: 'Data Center' },
    { id: 'EDB-10AB', name: 'EDB 10A/B', type: 'data_center_it', floor: '10', category: 'Data Center' }
  ]
};

// Helper function to get all meters as flat array
export function getAllCarParkBuildingMeters(): CarParkMeter[] {
  return Object.values(CAR_PARK_BUILDING_METERS).flat();
}

// Helper function to get total meter count
export function getCarParkBuildingMeterCount(): number {
  return getAllCarParkBuildingMeters().length;
}

// Helper function to get meters by floor
export function getCarParkMetersByFloor(floor: string): CarParkMeter[] {
  const floorKey = `floor${floor}`.replace('floorfloor', 'floor');
  return CAR_PARK_BUILDING_METERS[floorKey] || [];
}

// Helper function to get meters by category
export function getCarParkMetersByCategory(category: string): CarParkMeter[] {
  return getAllCarParkBuildingMeters().filter(meter => meter.category === category);
}

// Helper function to get category counts
export function getCarParkMeterCategoryCounts(): Record<string, number> {
  const counts: Record<string, number> = {};
  getAllCarParkBuildingMeters().forEach(meter => {
    counts[meter.category] = (counts[meter.category] || 0) + 1;
  });
  return counts;
}