// Tower A Meter Configuration
export const TOWER_A_METERS = {
  // Basement Floor
  'B': {
    panels: {
      'T-BDB1': {
        name: 'T-BDB1',
        meters: [
          { id: 'T-BLC1', name: 'T-BLC1', type: 'Load Center' },
          { id: 'DB-TW-AHU-B1', name: 'DB-TW-AHU-B1', type: 'airSide' }
        ]
      },
      'T-BEDB1': {
        name: 'T-BEDB1',
        meters: [
          { id: 'BELC1', name: 'BELC1', type: 'Load Center' },
          { id: 'T-BEDB-SN', name: 'T-BEDB-SN', type: 'SN' },
          { id: 'T-EDB-AHU-B0', name: 'T-EDB-AHU-B0', type: 'airSide' },
          { id: 'EDB-TW-EF-B2', name: 'EDB-TW-EF-B2', type: 'Emergency' },
          { id: 'EDB-TW-MF-B1', name: 'EDB-TW-MF-B1', type: 'Emergency' },
          { id: 'EDB-TW-MF-B2', name: 'EDB-TW-MF-B2', type: 'Emergency' }
        ]
      },
      'T-BEDB-SN': {
        name: 'T-BEDB-SN',
        meters: [
          { id: 'B-EPB-02', name: 'B-EPB-02', type: 'Drainage Pumps' },
          { id: 'B-EPB-03', name: 'B-EPB-03', type: 'Drainage Pumps' },
          { id: 'B-EPB-04', name: 'B-EPB-04', type: 'Drainage Pumps' },
          { id: 'B-EPB-05', name: 'B-EPB-05', type: 'Drainage Pumps' },
          { id: 'B-NPB-01', name: 'B-NPB-01', type: 'Water Treatment' },
          { id: 'B-EPB-08', name: 'B-EPB-08', type: 'Drainage Pumps' }
        ]
      }
    }
  },

  // 1st Floor
  1: {
    panels: {
      'T-1DB1': {
        name: 'T-1DB1',
        meters: [
          { id: 'T-1LP1', name: 'T-1LP1', type: 'Lighting' },
          { id: 'T-1LP2', name: 'T-1LP2', type: 'Lighting' },
          { id: 'T-1PP1', name: 'T-1PP1', type: 'Power' }
        ]
      },
      'T-1EDB1': {
        name: 'T-1EDB1',
        meters: [
          { id: 'T-1ELP1', name: 'T-1ELP1', type: 'Lighting' },
          { id: 'T-1EPP1', name: 'T-1EPP1', type: 'Power' },
          { id: 'T-1ELC-CCTV', name: 'T-1ELC-CCTV', type: 'CCTV' },
          { id: 'CB-MDF1', name: 'CB-MDF1', type: 'MDF' }
        ]
      }
    }
  },

  // 2nd Floor
  2: {
    panels: {
      'T-2DB1': {
        name: 'T-2DB1',
        meters: [
          { id: 'T-DB-AHU-04', name: 'T-DB-AHU-04', type: 'airSide' }
        ]
      },
      'T-2EDB1': {
        name: 'T-2EDB1',
        meters: [
          { id: 'T-2ELC1', name: 'T-2ELC1', type: 'Emergency' },
          { id: 'T-EDB-AHU-021', name: 'T-EDB-AHU-021', type: 'airSide' }
        ]
      }
    }
  },

  // 5th Floor
  5: {
    panels: {
      'T-5DB1': {
        name: 'T-5DB1',
        meters: [
          { id: 'T-5LP1', name: 'T-5LP1', type: 'Lighting' },
          { id: 'T-5PP1', name: 'T-5PP1', type: 'Power' },
          { id: 'T-5PP2', name: 'T-5PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-051', name: 'T-DB-AHU-051', type: 'airSide' },
          { id: 'T-DB-AHU-052', name: 'T-DB-AHU-052', type: 'airSide' }
        ]
      },
      'T-5EDB1': {
        name: 'T-5EDB1',
        meters: [
          { id: 'T-5ELP1', name: 'T-5ELP1', type: 'Lighting' },
          { id: 'T-5EPP1', name: 'T-5EPP1', type: 'Power' },
          { id: 'T-7ELP1', name: 'T-7ELP1', type: 'Lighting' },
          { id: 'T-6ELP1', name: 'T-6ELP1', type: 'Lighting' },
          { id: 'T-6ELP2', name: 'T-6ELP2', type: 'Power' },
          { id: 'T-EPP1', name: 'T-EPP1', type: 'Power' }
        ]
      }
    }
  },

  // 6th Floor
  6: {
    panels: {
      'T-6DB1': {
        name: 'T-6DB1',
        meters: [
          { id: 'T-6LP1', name: 'T-6LP1', type: 'Lighting' },
          { id: 'T-6PP1', name: 'T-6PP1', type: 'Power' },
          { id: 'T-6PP2', name: 'T-6PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-061', name: 'T-DB-AHU-061', type: 'airSide' },
          { id: 'T-DB-AHU-062', name: 'T-DB-AHU-062', type: 'airSide' },
          { id: 'T-DB-AHU-063', name: 'T-DB-AHU-063', type: 'airSide' }
        ]
      }
    }
  },

  // 7th Floor
  7: {
    panels: {
      'T-7DB1': {
        name: 'T-7DB1',
        meters: [
          { id: 'T-7LP1', name: 'T-7LP1', type: 'Lighting' },
          { id: 'T-7PP1', name: 'T-7PP1', type: 'Power' },
          { id: 'T-7PP2', name: 'T-7PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-071', name: 'T-DB-AHU-071', type: 'airSide' },
          { id: 'T-DB-AHU-072', name: 'T-DB-AHU-072', type: 'airSide' }
        ]
      }
    }
  },

  // 8th Floor
  8: {
    panels: {
      'T-8DB1': {
        name: 'T-8DB1',
        meters: [
          { id: 'T-8LP1', name: 'T-8LP1', type: 'Lighting' },
          { id: 'T-8PP1', name: 'T-8PP1', type: 'Power' },
          { id: 'T-8PP2', name: 'T-8PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-081', name: 'T-DB-AHU-081', type: 'airSide' },
          { id: 'T-DB-AHU-082', name: 'T-DB-AHU-082', type: 'airSide' }
        ]
      },
      'T-8EDB1': {
        name: 'T-8EDB1',
        meters: [
          { id: 'T-8ELP1', name: 'T-8ELP1', type: 'Lighting' },
          { id: 'T-8EPP1', name: 'T-8EPP1', type: 'Power' },
          { id: 'T-10ELP1', name: 'T-10ELP1', type: 'Lighting' },
          { id: 'T-9ELP1', name: 'T-9ELP1', type: 'Lighting' },
          { id: 'T-9ELP2', name: 'T-9ELP2', type: 'Power' },
          { id: 'T-10EPP1', name: 'T-10EPP1', type: 'Power' }
        ]
      }
    }
  },

  // 9th Floor
  9: {
    panels: {
      'T-9DB1': {
        name: 'T-9DB1',
        meters: [
          { id: 'T-9LP1', name: 'T-9LP1', type: 'Lighting' },
          { id: 'T-9PP1', name: 'T-9PP1', type: 'Power' },
          { id: 'T-DB-AHU-091', name: 'T-DB-AHU-091', type: 'airSide' },
          { id: 'T-DB-AHU-092', name: 'T-DB-AHU-092', type: 'airSide' }
        ]
      }
    }
  },

  // 10th Floor
  10: {
    panels: {
      'T-10DB1': {
        name: 'T-10DB1',
        meters: [
          { id: 'T-10LP1', name: 'T-10LP1', type: 'Lighting' },
          { id: 'T-10PP1', name: 'T-10PP1', type: 'Power' },
          { id: 'T-01PP2', name: 'T-01PP2', type: 'Power' },
          { id: 'T-11LC1', name: 'T-11LC1', type: 'Light&Power' },
          { id: 'T-12LC1', name: 'T-12LC1', type: 'Light&Power' },
          { id: 'T-DB-AHU-101', name: 'T-DB-AHU-101', type: 'airSide' },
          { id: 'T-DB-AHU-102', name: 'T-DB-AHU-102', type: 'airSide' }
        ]
      },
      'T-10ADB1': {
        name: 'T-10ADB1',
        meters: [
          { id: 'T-10AELP1', name: 'T-10AELP1', type: 'Lighting' },
          { id: 'T-10BEPP1', name: 'T-10BEPP1', type: 'Power' },
          { id: 'T-11ELP1', name: 'T-11ELP1', type: 'Lighting' },
          { id: 'T-11EPP1', name: 'T-11EPP1', type: 'Power' },
          { id: 'T-10AELC-08', name: 'T-10AELC-08', type: 'Power' }
        ]
      }
    }
  },

  // 11th Floor
  11: {
    panels: {
      'T-11DB1': {
        name: 'T-11DB1',
        meters: [
          { id: 'T-11LP1', name: 'T-11LP1', type: 'Lighting' },
          { id: 'T-11PP1', name: 'T-11PP1', type: 'Power' },
          { id: 'T-11PP2', name: 'T-11PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-111', name: 'T-DB-AHU-111', type: 'airSide' },
          { id: 'T-DB-AHU-112', name: 'T-DB-AHU-112', type: 'airSide' }
        ]
      }
    }
  },

  // 12th Floor
  12: {
    panels: {
      'T-12DB1': {
        name: 'T-12DB1',
        meters: [
          { id: 'T-12LP1', name: 'T-12LP1', type: 'Lighting' },
          { id: 'T-12PP1', name: 'T-12PP1', type: 'Power' },
          { id: 'T-12PP2', name: 'T-12PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-121', name: 'T-DB-AHU-121', type: 'airSide' },
          { id: 'T-DB-AHU-122', name: 'T-DB-AHU-122', type: 'airSide' }
        ]
      },
      'T-12EDB1': {
        name: 'T-12EDB1',
        meters: [
          { id: 'T-12ELP1', name: 'T-12ELP1', type: 'Lighting' },
          { id: 'T-12EPP1', name: 'T-12EPP1', type: 'Power' },
          { id: 'T-14ELP2', name: 'T-14ELP2', type: 'Power' },
          { id: 'T-15EPP1', name: 'T-15EPP1', type: 'Power' }
        ]
      }
    }
  },

  // 14th Floor
  14: {
    panels: {
      'T-14DB1': {
        name: 'T-14DB1',
        meters: [
          { id: 'T-14LP1', name: 'T-14LP1', type: 'Lighting' },
          { id: 'T-14PP1', name: 'T-14PP1', type: 'Power' },
          { id: 'T-14PP2', name: 'T-14PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-141', name: 'T-DB-AHU-141', type: 'airSide' },
          { id: 'T-DB-AHU-142', name: 'T-DB-AHU-142', type: 'airSide' }
        ]
      }
    }
  },

  // 15th Floor
  15: {
    panels: {
      'T-15DB1': {
        name: 'T-15DB1',
        meters: [
          { id: 'T-15LP1', name: 'T-15LP1', type: 'Lighting' },
          { id: 'T-15PP1', name: 'T-15PP1', type: 'Power' },
          { id: 'T-15PP2', name: 'T-15PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-151', name: 'T-DB-AHU-151', type: 'airSide' },
          { id: 'T-DB-AHU-152', name: 'T-DB-AHU-152', type: 'airSide' }
        ]
      }
    }
  },

  // 16th Floor
  16: {
    panels: {
      'T-16DB1': {
        name: 'T-16DB1',
        meters: [
          { id: 'T-16LP1', name: 'T-16LP1', type: 'Lighting' },
          { id: 'T-16PP1', name: 'T-16PP1', type: 'Power' },
          { id: 'T-16PP2', name: 'T-16PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-161', name: 'T-DB-AHU-161', type: 'airSide' },
          { id: 'T-DB-AHU-162', name: 'T-DB-AHU-162', type: 'airSide' }
        ]
      },
      'T-16EDB1': {
        name: 'T-16EDB1',
        meters: [
          { id: 'T-16ELP1', name: 'T-16ELP1', type: 'Lighting' },
          { id: 'T-16EPP1', name: 'T-16EPP1', type: 'Power' },
          { id: 'T-18ELP1', name: 'T-18ELP1', type: 'Lighting' },
          { id: 'T-17ELP1', name: 'T-17ELP1', type: 'Lighting' },
          { id: 'T-17ELP2', name: 'T-17ELP2', type: 'Power' },
          { id: 'T-18EPP1', name: 'T-18EPP1', type: 'Power' }
        ]
      }
    }
  },

  // 17th Floor
  17: {
    panels: {
      'T-17DB1': {
        name: 'T-17DB1',
        meters: [
          { id: 'T-17LP1', name: 'T-17LP1', type: 'Lighting' },
          { id: 'T-17PP1', name: 'T-17PP1', type: 'Power' },
          { id: 'T-17PP2', name: 'T-17PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-171', name: 'T-DB-AHU-171', type: 'airSide' },
          { id: 'T-DB-AHU-172', name: 'T-DB-AHU-172', type: 'airSide' }
        ]
      }
    }
  },

  // 18th Floor
  18: {
    panels: {
      'T-18DB1': {
        name: 'T-18DB1',
        meters: [
          { id: 'T-18LP1', name: 'T-18LP1', type: 'Lighting' },
          { id: 'T-18PP1', name: 'T-18PP1', type: 'Power' },
          { id: 'T-18PP2', name: 'T-18PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-181', name: 'T-DB-AHU-181', type: 'airSide' },
          { id: 'T-DB-AHU-182', name: 'T-DB-AHU-182', type: 'airSide' }
        ]
      }
    }
  },

  // 19th Floor
  19: {
    panels: {
      'T-19DB1': {
        name: 'T-19DB1',
        meters: [
          { id: 'T-19LP1', name: 'T-19LP1', type: 'Lighting' },
          { id: 'T-19PP1', name: 'T-19PP1', type: 'Power' },
          { id: 'T-19PP2', name: 'T-19PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-191', name: 'T-DB-AHU-191', type: 'airSide' },
          { id: 'T-DB-AHU-192', name: 'T-DB-AHU-192', type: 'airSide' }
        ]
      },
      'T-19EDB1': {
        name: 'T-19EDB1',
        meters: [
          { id: 'T-19ELP1', name: 'T-19ELP1', type: 'Lighting' },
          { id: 'T-19EPP1', name: 'T-19EPP1', type: 'Power' },
          { id: 'T-20ELP1', name: 'T-20ELP1', type: 'Lighting' },
          { id: 'T-21ELP1', name: 'T-21ELP1', type: 'Lighting' },
          { id: 'T-21ELP2', name: 'T-21ELP2', type: 'Power' },
          { id: 'T-21EPP1', name: 'T-21EPP1', type: 'Power' },
          { id: 'T-21ELC-OB', name: 'T-21ELC-OB', type: 'Obstruction light' }
        ]
      }
    }
  },

  // 20th Floor
  20: {
    panels: {
      'T-20DB1': {
        name: 'T-20DB1',
        meters: [
          { id: 'T-20LP1', name: 'T-20LP1', type: 'Lighting' },
          { id: 'T-20PP1', name: 'T-20PP1', type: 'Power' },
          { id: 'T-20PP2', name: 'T-20PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-201', name: 'T-DB-AHU-201', type: 'airSide' },
          { id: 'T-DB-AHU-202', name: 'T-DB-AHU-202', type: 'airSide' }
        ]
      }
    }
  },

  // 21st Floor
  21: {
    panels: {
      'T-21DB1': {
        name: 'T-21DB1',
        meters: [
          { id: 'T-21LP1', name: 'T-21LP1', type: 'Lighting' },
          { id: 'T-21PP1', name: 'T-21PP1', type: 'Power' },
          { id: 'T-21PP2', name: 'T-21PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-211', name: 'T-DB-AHU-211', type: 'airSide' },
          { id: 'T-DB-AHU-212', name: 'T-DB-AHU-212', type: 'airSide' }
        ]
      }
    }
  },

  // 22nd Floor
  22: {
    panels: {
      'T-22DB1': {
        name: 'T-22DB1',
        meters: [
          { id: 'T-22LP1', name: 'T-22LP1', type: 'Lighting' },
          { id: 'T-22PP1', name: 'T-22PP1', type: 'Power' },
          { id: 'T-22PP2', name: 'T-22PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-221', name: 'T-DB-AHU-221', type: 'airSide' },
          { id: 'T-DB-AHU-222', name: 'T-DB-AHU-222', type: 'airSide' }
        ]
      },
      'T-22EDB1': {
        name: 'T-22EDB1',
        meters: [
          { id: 'T-22ELP1', name: 'T-22ELP1', type: 'Lighting' },
          { id: 'T-22EPP1', name: 'T-22EPP1', type: 'Power' },
          { id: 'T-24ELP1', name: 'T-24ELP1', type: 'Lighting' },
          { id: 'T-23ELP1', name: 'T-23ELP1', type: 'Lighting' },
          { id: 'T-23ELP2', name: 'T-23ELP2', type: 'Power' },
          { id: 'T-24EPP1', name: 'T-24EPP1', type: 'Power' }
        ]
      }
    }
  },

  // 23rd Floor
  23: {
    panels: {
      'T-23DB1': {
        name: 'T-23DB1',
        meters: [
          { id: 'T-23LP1', name: 'T-23LP1', type: 'Lighting' },
          { id: 'T-23PP1', name: 'T-23PP1', type: 'Power' },
          { id: 'T-23PP2', name: 'T-23PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-231', name: 'T-DB-AHU-231', type: 'airSide' },
          { id: 'T-DB-AHU-232', name: 'T-DB-AHU-232', type: 'airSide' }
        ]
      }
    }
  },

  // 24th Floor
  24: {
    panels: {
      'T-24DB1': {
        name: 'T-24DB1',
        meters: [
          { id: 'T-24LP1', name: 'T-24LP1', type: 'Lighting' },
          { id: 'T-24PP1', name: 'T-24PP1', type: 'Power' },
          { id: 'T-24PP2', name: 'T-24PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-241', name: 'T-DB-AHU-241', type: 'airSide' },
          { id: 'T-DB-AHU-242', name: 'T-DB-AHU-242', type: 'airSide' }
        ]
      }
    }
  },

  // 25th Floor
  25: {
    panels: {
      'T-25DB1': {
        name: 'T-25DB1',
        meters: [
          { id: 'T-25LP1', name: 'T-25LP1', type: 'Lighting' },
          { id: 'T-25PP1', name: 'T-25PP1', type: 'Power' },
          { id: 'T-25PP2', name: 'T-25PP2', type: 'Receptacle' },
          { id: 'T-DB-AHU-251', name: 'T-DB-AHU-251', type: 'airSide' },
          { id: 'T-DB-AHU-252', name: 'T-DB-AHU-252', type: 'airSide' }
        ]
      },
      'T-25EDB1': {
        name: 'T-25EDB1',
        meters: [
          { id: 'T-25ELP1', name: 'T-25ELP1', type: 'Lighting' },
          { id: 'T-25EPP1', name: 'T-25EPP1', type: 'Power' },
          { id: 'T-27ELP1', name: 'T-27ELP1', type: 'Lighting' },
          { id: 'T-26ELP1', name: 'T-26ELP1', type: 'Lighting' },
          { id: 'T-26ELP2', name: 'T-26ELP2', type: 'Power' },
          { id: 'T-27EPP1', name: 'T-27EPP1', type: 'Power' },
          { id: 'T-26ELCO', name: 'T-26ELCO', type: 'Landscape' },
          { id: 'EDB-T-AHU-261', name: 'EDB-T-AHU-261', type: 'airSide' },
          { id: 'EDB-T-AHU-262', name: 'EDB-T-AHU-262', type: 'airSide' },
          { id: 'EDB-T-AHU-263', name: 'EDB-T-AHU-263', type: 'airSide' },
          { id: 'T-DB-EF-251', name: 'T-DB-EF-251', type: 'AC' },
          { id: 'T-DB-EF-252', name: 'T-DB-EF-252', type: 'AC' },
          { id: 'EDB-T-AHU-271', name: 'EDB-T-AHU-271', type: 'airSide' },
          { id: 'EDB-T-AHU-272', name: 'EDB-T-AHU-272', type: 'airSide' },
          { id: 'EDB-T-KEF-261', name: 'EDB-T-KEF-261', type: 'Power' },
          { id: 'T-26ELCK1', name: 'T-26ELCK1', type: 'Power' }
        ]
      }
    }
  },

  // 26th Floor
  26: {
    panels: {
      'T-26DB1': {
        name: 'T-26DB1',
        meters: [
          { id: 'T-26LP1', name: 'T-26LP1', type: 'Lighting' },
          { id: 'T-26PP1', name: 'T-26PP1', type: 'Power' },
          { id: 'T-26LCK1', name: 'T-26LCK1', type: 'Power' }
        ]
      }
    }
  },

  // 27th Floor
  27: {
    panels: {
      'T-27DB1': {
        name: 'T-27DB1',
        meters: [
          { id: 'T-27LP1', name: 'T-27LP1', type: 'Lighting' },
          { id: 'T-27PP1', name: 'T-27PP1', type: 'Power' },
          { id: 'T-27PP2', name: 'T-27PP2', type: 'Receptacle' }
        ]
      }
    }
  },

  // 28th Floor
  28: {
    panels: {
      'T-28DB1': {
        name: 'T-28DB1',
        meters: [
          { id: 'T-28LP1', name: 'T-28LP1', type: 'Lighting' },
          { id: 'T-28PP1', name: 'T-28PP1', type: 'Power' },
          { id: 'T-RLC1', name: 'T-RLC1', type: 'Power' },
          { id: 'T-EPB-01', name: 'T-EPB-01', type: 'CWP' }
        ]
      },
      'T-28EDB1': {
        name: 'T-28EDB1',
        meters: [
          { id: 'T-28ELP1', name: 'T-28ELP1', type: 'Lighting' },
          { id: 'T-28EPP1', name: 'T-28EPP1', type: 'Power' },
          { id: 'T-RELC1', name: 'T-RELC1', type: 'Roof' },
          { id: 'EDB-T-AHU-281', name: 'EDB-T-AHU-281', type: 'airSide' },
          { id: 'EDB-T-AHU-282', name: 'EDB-T-AHU-282', type: 'airSide' }
        ]
      }
    }
  }
} as const;

// Helper functions
export function getTotalPanels(): number {
  return Object.values(TOWER_A_METERS).reduce((total, floor) => {
    return total + Object.keys(floor.panels).length;
  }, 0);
}

export function getTotalMeters(): number {
  return Object.values(TOWER_A_METERS).reduce((floorTotal, floor) => {
    return floorTotal + Object.values(floor.panels).reduce((panelTotal, panel) => {
      return panelTotal + panel.meters.length;
    }, 0);
  }, 0);
}

export function getMetersByType(type: string): Array<{id: string; name: string; floor: number | 'B'; panel: string}> {
  const meters: Array<{id: string; name: string; floor: number | 'B'; panel: string}> = [];
  
  Object.entries(TOWER_A_METERS).forEach(([floor, floorData]) => {
    Object.entries(floorData.panels).forEach(([panelId, panel]) => {
      panel.meters.forEach((meter: { id: string; name: string; type: string }) => {
        if (meter.type === type) {
          meters.push({
            id: meter.id,
            name: meter.name,
            floor: floor === 'B' ? 'B' : parseInt(floor),
            panel: panelId
          });
        }
      });
    });
  });

  return meters;
}

export function getMetersByFloor(floor: number | 'B'): Array<{id: string; name: string; type: string; panel: string}> {
  // Use type assertion to handle the indexing issue
  const floorData = TOWER_A_METERS[floor as keyof typeof TOWER_A_METERS];
  if (!floorData) return [];

  const meters: Array<{id: string; name: string; type: string; panel: string}> = [];
  
  Object.entries(floorData.panels).forEach(([panelId, panel]) => {
    panel.meters.forEach((meter: { id: string; name: string; type: string }) => {
      meters.push({
        id: meter.id,
        name: meter.name,
        type: meter.type,
        panel: panelId
      });
    });
  });

  return meters;
}

export function getMetersByPanel(floor: number | 'B', panelId: string): Array<{id: string; name: string; type: string}> {
  // Use type assertion to handle the indexing issue
  const floorData = TOWER_A_METERS[floor as keyof typeof TOWER_A_METERS];
  if (!floorData) return [];

  // Use type assertion for the panels object
  const panel = floorData.panels[panelId as keyof typeof floorData.panels];
  if (!panel) return [];

  // Use type assertion to ensure TypeScript recognizes the meters property
  return (panel as { meters: Array<{id: string; name: string; type: string}> }).meters.map(meter => ({
    id: meter.id,
    name: meter.name,
    type: meter.type
  }));
}