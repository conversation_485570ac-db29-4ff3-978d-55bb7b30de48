// Tower C (Car Park Building) Meter Configuration
export const TOWER_C_METERS = {
  // Basement Floor
  'B': {
    panels: {
      'C-BEDB1': {
        name: 'C-BEDB1',
        meters: [
          { id: 'C-1EDB1', name: 'C-1EDB1', type: 'Power' },
          { id: 'C-BEDB-MU', name: 'C-BEDB-MU', type: 'Power' },
          { id: 'C-2EDB1', name: 'C-2EDB1', type: 'Power' },
          { id: 'C-8EDB1', name: 'C-8EDB1', type: 'Power' },
          { id: 'C-9EDB1', name: 'C-9EDB1', type: 'Power' },
          { id: 'B-EPB-06', name: 'B-EPB-06', type: 'Power' },
          { id: 'E-EPB-07', name: 'E-EPB-07', type: 'Power' }
        ]
      }
    }
  },

  // 1st Floor
  '1': {
    panels: {
      'C-1DB1': {
        name: 'C-1DB1',
        meters: [
          { id: 'C-1LP1', name: 'C-1LP1', type: 'Lighting' },
          { id: 'C-1PP2', name: 'C-1PP2', type: 'Power' },
          { id: 'CB-CS1', name: 'CB-CS1', type: 'Power' },
          { id: 'CB-CS2', name: 'CB-CS2', type: 'Power' },
          { id: 'CB-CS3', name: 'CB-CS3', type: 'Power' },
          { id: 'CB-CS4', name: 'CB-CS4', type: 'Power' },
          { id: 'CB-CS5', name: 'CB-CS5', type: 'Power' },
          { id: 'CB-CS6', name: 'CB-CS6', type: 'Power' }
        ]
      },
      'C-1EDB1': {
        name: 'C-1EDB1',
        meters: [
          { id: 'CB-EXD', name: 'CB-EXD', type: 'Power' },
          { id: 'C-BELC1', name: 'C-BELC1', type: 'Power' },
          { id: 'C-1ELP1', name: 'C-1ELP1', type: 'Lighting' },
          { id: 'SPARE', name: 'SPARE', type: 'Power' },
          { id: 'G-EPB-01', name: 'G-EPB-01', type: 'Power' },
          { id: 'CB-MDF2', name: 'CB-MDF2', type: 'Power' },
          { id: 'EDB-CP-KEF-011', name: 'EDB-CP-KEF-011', type: 'airSide' }
        ]
      }
    }
  },

  // 2nd Floor
  '2': {
    panels: {
      'C-2DB1': {
        name: 'C-2DB1',
        meters: [
          { id: 'C-2LP1', name: 'C-2LP1', type: 'Lighting' },
          { id: 'C-5LP2', name: 'C-5LP2', type: 'Lighting' }
        ]
      },
      'C-1EDB1': {
        name: 'C-1EDB1',
        meters: [
          { id: 'C-2ELP1', name: 'C-2ELP1', type: 'Lighting' },
          { id: 'C-5ELP1', name: 'C-5ELP1', type: 'Lighting' }
        ]
      }
    }
  },

  // 8th Floor
  '8': {
    panels: {
      'C-8DB1': {
        name: 'C-8DB1',
        meters: [
          { id: 'C-7LP1', name: 'C-7LP1', type: 'Lighting' },
          { id: 'P-7PP1', name: 'P-7PP1', type: 'Power' },
          { id: 'C-8LP1', name: 'C-8LP1', type: 'Lighting' },
          { id: 'C-8PP1', name: 'C-8PP1', type: 'Power' },
          { id: 'C-DB-AHU-081', name: 'C-DB-AHU-081', type: 'airSide' },
          { id: 'C-DB-AHU-082', name: 'C-DB-AHU-082', type: 'airSide' },
          { id: 'C-DB-AHU-083', name: 'C-DB-AHU-083', type: 'airSide' }
        ]
      },
      'C-8EDB1': {
        name: 'C-8EDB1',
        meters: [
          { id: 'C-7ELC1', name: 'C-7ELC1', type: 'Power' },
          { id: 'C-8ELC1', name: 'C-8ELC1', type: 'Power' }
        ]
      }
    }
  },

  // 9th Floor
  '9': {
    panels: {
      'C-9DB1': {
        name: 'C-9DB1',
        meters: [
          { id: 'C-9LP1', name: 'C-9LP1', type: 'Lighting' },
          { id: 'C-9LP2', name: 'C-9LP2', type: 'Lighting' },
          { id: 'C-9PP1', name: 'C-9PP1', type: 'Power' }
        ]
      },
      'C-9EDB1': {
        name: 'C-9EDB1',
        meters: [
          { id: 'C-9ELC1-A', name: 'C-9ELC1 A', type: 'Power' },
          { id: 'C-9ELC1-B', name: 'C-9ELC1 B', type: 'Power' }
        ]
      }
    }
  }
} as const;