import type { MeterType } from '../../../../types';

interface PodiumMeter {
  id: string;
  name: string;
  type: MeterType;
  floor: string;
  category: string;
}

// Podium Building Meter List - 75 meters total
export const PODIUM_BUILDING_METERS: Record<string, PodiumMeter[]> = {
  floorB: [
    // Interior Lighting (4)
    { id: 'P-BLP1', name: 'P-BLP1', type: 'lightPower', floor: 'B', category: 'Interior Lighting' },
    { id: 'P-BLP2', name: 'P-BLP2', type: 'lightPower', floor: 'B', category: 'Interior Lighting' },
    { id: 'P-EBLP1', name: 'P-EBLP1', type: 'lightPower', floor: 'B', category: 'Interior Lighting' },
    { id: 'P-ELP2', name: 'P-ELP2', type: 'lightPower', floor: 'B', category: 'Interior Lighting' },
    // Air side (1)
    { id: 'P-EDB-AHU-B2', name: 'P-EDB-AHU-B2', type: 'airSide', floor: 'B', category: 'Air side' },
    // Office Equipment (5)
    { id: 'P-BPP1', name: 'P-BPP1', type: 'data_center_others', floor: 'B', category: 'Office Equipment' },
    { id: 'P-BPP2', name: 'P-BPP2', type: 'data_center_others', floor: 'B', category: 'Office Equipment' },
    { id: 'P-DB-CDB-B1', name: 'P-DB-CDB-B1', type: 'data_center_others', floor: 'B', category: 'Office Equipment' },
    { id: 'P-EBPP1', name: 'P-EBPP1', type: 'data_center_others', floor: 'B', category: 'Office Equipment' },
    { id: 'P-EBPP2', name: 'P-EBPP2', type: 'data_center_others', floor: 'B', category: 'Office Equipment' }
  ],
  
  floor1: [
    // Interior Lighting (2)
    { id: 'P-1LP1', name: 'P-1LP1', type: 'lightPower', floor: '1', category: 'Interior Lighting' },
    { id: 'Space-LP2', name: 'Space(LP2)', type: 'lightPower', floor: '1', category: 'Interior Lighting' },
    // Air side (4)
    { id: 'P-1UDB-1-POD', name: 'P-1UDB-1', type: 'airSide', floor: '1', category: 'Air side' },
    { id: 'P-1PP2', name: 'P-1PP2', type: 'airSide', floor: '1', category: 'Air side' },
    { id: 'Space-PP2', name: 'Space(PP2)', type: 'airSide', floor: '1', category: 'Air side' },
    { id: 'P-1EPP1', name: 'P-1EPP1', type: 'airSide', floor: '1', category: 'Air side' },
    // Elevator & Escalator (4)
    { id: 'CB-ES1', name: 'CB-ES1', type: 'escalator_elevator', floor: '1', category: 'Elevator & Escalator' },
    { id: 'CB-ES2', name: 'CB-ES2', type: 'escalator_elevator', floor: '1', category: 'Elevator & Escalator' },
    { id: 'CB-ES3', name: 'CB-ES3', type: 'escalator_elevator', floor: '1', category: 'Elevator & Escalator' },
    { id: 'CB-ES4', name: 'CB-ES4', type: 'escalator_elevator', floor: '1', category: 'Elevator & Escalator' },
    // Other (1)
    { id: 'ELC01', name: 'ELC01', type: 'data_center_others', floor: '1', category: 'Other' }
  ],

  floor2: [
    // Air side (2)
    { id: 'P-2EPP2', name: 'P-2EPP2', type: 'airSide', floor: '2', category: 'Air side' },
    { id: 'P-2EPP1', name: 'P-2EPP1', type: 'airSide', floor: '2', category: 'Air side' }
  ],

  floor3: [
    // Interior Lighting (3)
    { id: 'CB-LED-SCREEN', name: 'CB-LED SCREEN', type: 'lightPower', floor: '3', category: 'Interior Lighting' },
    { id: 'P-3LP1', name: 'P-3LP1', type: 'lightPower', floor: '3', category: 'Interior Lighting' },
    { id: 'P-3ELP1', name: 'P-3ELP1', type: 'lightPower', floor: '3', category: 'Interior Lighting' },
    // Air side (4)
    { id: 'P-DB-AHU-031', name: 'P-DB-AHU-031', type: 'airSide', floor: '3', category: 'Air side' },
    { id: 'P-DB-AHU-032', name: 'P-DB-AHU-032', type: 'airSide', floor: '3', category: 'Air side' },
    { id: 'P-3EDB1', name: 'P-3EDB1', type: 'airSide', floor: '3', category: 'Air side' },
    { id: 'P-3LPP1', name: 'P-3LPP1', type: 'airSide', floor: '3', category: 'Air side' },
    // Elevator & Escalator (5)
    { id: 'CB-ES8-1', name: 'CB-ES8', type: 'escalator_elevator', floor: '3', category: 'Elevator & Escalator' },
    { id: 'CB-ES5', name: 'CB-ES5', type: 'escalator_elevator', floor: '3', category: 'Elevator & Escalator' },
    { id: 'CB-ES6', name: 'CB-ES6', type: 'escalator_elevator', floor: '3', category: 'Elevator & Escalator' },
    { id: 'CB-ES7', name: 'CB-ES7', type: 'escalator_elevator', floor: '3', category: 'Elevator & Escalator' },
    { id: 'CB-ES8-2', name: 'CB-ES8', type: 'escalator_elevator', floor: '3', category: 'Elevator & Escalator' }
  ],

  floor5: [
    // Interior Lighting (2)
    { id: 'P-5LP1', name: 'P-5LP1', type: 'lightPower', floor: '5', category: 'Interior Lighting' },
    { id: 'P-5ELP1', name: 'P-5ELP1', type: 'lightPower', floor: '5', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'P-DB-AHU-051', name: 'P-DB-AHU-051', type: 'airSide', floor: '5', category: 'Air side' },
    { id: 'P-5PP1', name: 'P-5PP1', type: 'airSide', floor: '5', category: 'Air side' },
    { id: 'P-5LC1', name: 'P-5LC1', type: 'airSide', floor: '5', category: 'Air side' },
    { id: 'P-5EPP1', name: 'P-5EPP1', type: 'airSide', floor: '5', category: 'Air side' },
    { id: 'P-5ELC1', name: 'P-5ELC1', type: 'airSide', floor: '5', category: 'Air side' }
  ],

  floor6: [
    // Interior Lighting (2)
    { id: 'P-6LP1', name: 'P-6LP1', type: 'lightPower', floor: '6', category: 'Interior Lighting' },
    { id: 'P-6ELP1', name: 'P-6ELP1', type: 'lightPower', floor: '6', category: 'Interior Lighting' },
    // Air side (10)
    { id: 'P-DB-AHU-061', name: 'P-DB-AHU-061', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'P-6EDB1', name: 'P-6EDB1', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'P-6PP1', name: 'P-6PP1', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'P-6LCM1', name: 'P-6LCM1', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'P-6LCM2', name: 'P-6LCM2', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'P-6LCM3', name: 'P-6LCM3', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'P-6LCM4', name: 'P-6LCM4', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'P-6LCM5', name: 'P-6LCM5', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'P-6EPP1', name: 'P-6EPP1', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'P-6ELCM1', name: 'P-6ELCM1', type: 'airSide', floor: '6', category: 'Air side' },
    // Elevator & Escalator (2)
    { id: 'CB-ES9', name: 'CB-ES9', type: 'escalator_elevator', floor: '6', category: 'Elevator & Escalator' },
    { id: 'CB-ES10', name: 'CB-ES10', type: 'escalator_elevator', floor: '6', category: 'Elevator & Escalator' }
  ],

  floor7: [
    // Interior Lighting (3)
    { id: 'P-7LP1', name: 'P-7LP1', type: 'lightPower', floor: '7', category: 'Interior Lighting' },
    { id: 'P-7EDB2', name: 'P-7EDB2', type: 'lightPower', floor: '7', category: 'Interior Lighting' },
    { id: 'P-7ELP1', name: 'P-7ELP1', type: 'lightPower', floor: '7', category: 'Interior Lighting' },
    // Air side (11)
    { id: 'P-DB-AHU-071', name: 'P-DB-AHU-071', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'P-7EDB1', name: 'P-7EDB1', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'P-7PP1-POD', name: 'P-7PP1', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'P-7LCM1', name: 'P-7LCM1', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'P-7LCM2', name: 'P-7LCM2', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'P-7LCM3', name: 'P-7LCM3', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'P-7LCM4', name: 'P-7LCM4', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'P-7LCM5', name: 'P-7LCM5', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'P-7LCM6', name: 'P-7LCM6', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'P-7EPP1', name: 'P-7EPP1', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'P-7ELCM1', name: 'P-7ELCM1', type: 'airSide', floor: '7', category: 'Air side' },
    // Elevator & Escalator (4)
    { id: 'P-PL-1-POD', name: 'P-PL-1', type: 'escalator_elevator', floor: '7', category: 'Elevator & Escalator' },
    { id: 'P-FL-1-POD', name: 'P-FL-1', type: 'escalator_elevator', floor: '7', category: 'Elevator & Escalator' },
    { id: 'CB-ES11', name: 'CB-ES11', type: 'escalator_elevator', floor: '7', category: 'Elevator & Escalator' },
    { id: 'CB-ES12', name: 'CB-ES12', type: 'escalator_elevator', floor: '7', category: 'Elevator & Escalator' },
    // Other (2)
    { id: 'P-RLCO-1', name: 'P-RLCO', type: 'data_center_others', floor: '7', category: 'Other' },
    { id: 'P-RLCO-2', name: 'P-RLCO', type: 'data_center_others', floor: '7', category: 'Other' }
  ]
};

// Helper function to get all meters as flat array
export function getAllPodiumBuildingMeters(): PodiumMeter[] {
  return Object.values(PODIUM_BUILDING_METERS).flat();
}

// Helper function to get total meter count
export function getPodiumBuildingMeterCount(): number {
  return getAllPodiumBuildingMeters().length;
}

// Helper function to get meters by floor
export function getPodiumMetersByFloor(floor: string): PodiumMeter[] {
  const floorKey = `floor${floor}`.replace('floorfloor', 'floor');
  return PODIUM_BUILDING_METERS[floorKey] || [];
}

// Helper function to get meters by category
export function getPodiumMetersByCategory(category: string): PodiumMeter[] {
  return getAllPodiumBuildingMeters().filter(meter => meter.category === category);
}

// Helper function to get category counts
export function getPodiumMeterCategoryCounts(): Record<string, number> {
  const counts: Record<string, number> = {};
  getAllPodiumBuildingMeters().forEach(meter => {
    counts[meter.category] = (counts[meter.category] || 0) + 1;
  });
  return counts;
}