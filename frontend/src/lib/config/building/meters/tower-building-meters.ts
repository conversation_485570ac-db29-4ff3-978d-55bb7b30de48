import type { MeterType } from '../../../../types';

interface TowerMeter {
  id: string;
  name: string;
  type: MeterType;
  floor: string;
  category: string;
}

// Comprehensive Tower Building Meter List (Tower A) - 222 meters total
export const TOWER_BUILDING_METERS: Record<string, TowerMeter[]> = {
  floorB: [
    // Interior Lighting (2)
    { id: 'T-BLC1', name: 'T-BLC1', type: 'lightPower', floor: 'B', category: 'Interior Lighting' },
    { id: 'BELC1', name: 'BELC1', type: 'lightPower', floor: 'B', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'DB-TW-AHU-B1', name: 'DB-TW-AHU-B1', type: 'airSide', floor: 'B', category: 'Air side' },
    { id: 'T-EDB-AHU-B0', name: 'T-EDB-AHU-B0', type: 'airSide', floor: 'B', category: 'Air side' },
    { id: 'EDB-TW-EF-B2', name: 'EDB-TW-EF-B2', type: 'airSide', floor: 'B', category: 'Air side' },
    { id: 'EDB-TW-MF-B1', name: 'EDB-TW-MF-B1', type: 'airSide', floor: 'B', category: 'Air side' },
    { id: 'EDB-TW-MF-B2', name: 'EDB-TW-MF-B2', type: 'airSide', floor: 'B', category: 'Air side' },
    // Sanitary (7)
    { id: 'T-BEDB-SN', name: 'T-BEDB-SN', type: 'data_center_others', floor: 'B', category: 'Sanitary' },
    { id: 'B-EPB-02', name: 'B-EPB-02', type: 'data_center_others', floor: 'B', category: 'Sanitary' },
    { id: 'B-EPB-03', name: 'B-EPB-03', type: 'data_center_others', floor: 'B', category: 'Sanitary' },
    { id: 'B-EPB-04', name: 'B-EPB-04', type: 'data_center_others', floor: 'B', category: 'Sanitary' },
    { id: 'B-EPB-05', name: 'B-EPB-05', type: 'data_center_others', floor: 'B', category: 'Sanitary' },
    { id: 'B-NPB-01', name: 'B-NPB-01', type: 'data_center_others', floor: 'B', category: 'Sanitary' },
    { id: 'B-EPB-08', name: 'B-EPB-08', type: 'data_center_others', floor: 'B', category: 'Sanitary' },
    // Chiller (4)
    { id: 'CH-01-B', name: 'CH-01', type: 'chillerPlant', floor: 'B', category: 'Chiller' },
    { id: 'CH-02-B', name: 'CH-02', type: 'chillerPlant', floor: 'B', category: 'Chiller' },
    { id: 'CH-03-B', name: 'CH-03', type: 'chillerPlant', floor: 'B', category: 'Chiller' },
    { id: 'ECH-4-B', name: 'ECH-4', type: 'chillerPlant', floor: 'B', category: 'Chiller' }
  ],
  
  floor1: [
    // Interior Lighting (3)
    { id: 'T-1LP1', name: 'T-1LP1', type: 'lightPower', floor: '1', category: 'Interior Lighting' },
    { id: 'T-1LP2', name: 'T-1LP2', type: 'lightPower', floor: '1', category: 'Interior Lighting' },
    { id: 'T-1ELP1', name: 'T-1ELP1', type: 'lightPower', floor: '1', category: 'Interior Lighting' },
    // Air side (4)
    { id: 'T-1PP1', name: 'T-1PP1', type: 'airSide', floor: '1', category: 'Air side' },
    { id: 'T-1EPP1', name: 'T-1EPP1', type: 'airSide', floor: '1', category: 'Air side' },
    { id: 'T-1ELC-CCTV', name: 'T-1ELC-CCTV', type: 'airSide', floor: '1', category: 'Air side' },
    { id: 'CB-MDF1', name: 'CB-MDF1', type: 'airSide', floor: '1', category: 'Air side' },
    // Sanitary (2)
    { id: 'G-EPB-02-F1', name: 'G-EPB-02', type: 'data_center_others', floor: '1', category: 'Sanitary' },
    { id: 'G-FPB-03-F1', name: 'G-FPB-03', type: 'data_center_others', floor: '1', category: 'Sanitary' }
  ],

  floor2: [
    // Air side (4)
    { id: 'T-DB-AHU-04', name: 'T-DB-AHU-04', type: 'airSide', floor: '2', category: 'Air side' },
    { id: 'T-EDB-AHU-021', name: 'T-EDB-AHU-021', type: 'airSide', floor: '2', category: 'Air side' },
    { id: 'T-2ELC1', name: 'T-2ELC1', type: 'airSide', floor: '2', category: 'Air side' },
    { id: 'T-2UBD-1', name: 'T-2UBD-1', type: 'airSide', floor: '2', category: 'Air side' },
    // Elevator & Escalator (5)
    { id: 'T-FL-1-F2', name: 'T-FL-1', type: 'escalator_elevator', floor: '2', category: 'Elevator & Escalator' },
    { id: 'T-PL1-F2', name: 'T-PL1', type: 'escalator_elevator', floor: '2', category: 'Elevator & Escalator' },
    { id: 'T-PL2-F2', name: 'T-PL2', type: 'escalator_elevator', floor: '2', category: 'Elevator & Escalator' },
    { id: 'T-PL3-F2', name: 'T-PL3', type: 'escalator_elevator', floor: '2', category: 'Elevator & Escalator' },
    { id: 'T-PL4-F2', name: 'T-PL4', type: 'escalator_elevator', floor: '2', category: 'Elevator & Escalator' },
    // Pump for Chiller plant (4)
    { id: 'AMCC1-F2', name: 'AMCC1', type: 'airSide', floor: '2', category: 'Pump for Chiller plant' },
    { id: 'AMCC2-F2', name: 'AMCC2', type: 'airSide', floor: '2', category: 'Pump for Chiller plant' },
    { id: 'AMCC3-F2', name: 'AMCC3', type: 'airSide', floor: '2', category: 'Pump for Chiller plant' },
    { id: 'EAMCC4-F2', name: 'EAMCC4', type: 'airSide', floor: '2', category: 'Pump for Chiller plant' },
    // Cooling Tower (2)
    { id: 'AMCC5-F2', name: 'AMCC5', type: 'airSide', floor: '2', category: 'Cooling Tower' },
    { id: 'EAMCC6-F2', name: 'EAMCC6', type: 'airSide', floor: '2', category: 'Cooling Tower' }
  ],

  floor4: [
    // Other (6)
    { id: 'MCC-A', name: 'MCC-A', type: 'data_center_others', floor: '4', category: 'Other' },
    { id: 'DB-CRAC-A', name: 'DB CRAC-A', type: 'data_center_others', floor: '4', category: 'Other' },
    { id: 'DB-IN-A1', name: 'DB IN-A/1', type: 'data_center_others', floor: '4', category: 'Other' },
    { id: 'ESN-A1', name: 'ESN-A/1', type: 'data_center_others', floor: '4', category: 'Other' },
    { id: 'ATS-ED54', name: 'ATS-ED54', type: 'data_center_others', floor: '4', category: 'Other' },
    { id: 'EDB-10AA', name: 'EDB 10A/A', type: 'data_center_others', floor: '4', category: 'Other' }
  ],

  floor5: [
    // Interior Lighting (2)
    { id: 'T-5LP1', name: 'T-5LP1', type: 'lightPower', floor: '5', category: 'Interior Lighting' },
    { id: 'T-5ELP1', name: 'T-5ELP1', type: 'lightPower', floor: '5', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-051', name: 'T-DB-AHU-051', type: 'airSide', floor: '5', category: 'Air side' },
    { id: 'T-DB-AHU-052', name: 'T-DB-AHU-052', type: 'airSide', floor: '5', category: 'Air side' },
    { id: 'T-5PP1', name: 'T-5PP1', type: 'airSide', floor: '5', category: 'Air side' },
    { id: 'T-5PP2', name: 'T-5PP2', type: 'airSide', floor: '5', category: 'Air side' },
    { id: 'T-5EPP1', name: 'T-5EPP1', type: 'airSide', floor: '5', category: 'Air side' }
  ],

  floor6: [
    // Interior Lighting (2)
    { id: 'T-6LP1', name: 'T-6LP1', type: 'lightPower', floor: '6', category: 'Interior Lighting' },
    { id: 'T-6ELP1', name: 'T-6ELP1', type: 'lightPower', floor: '6', category: 'Interior Lighting' },
    // Air side (6)
    { id: 'T-DB-AHU-061', name: 'T-DB-AHU-061', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'T-DB-AHU-062', name: 'T-DB-AHU-062', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'T-DB-AHU-063', name: 'T-DB-AHU-063', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'T-6ELP2', name: 'T-6ELP2', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'T-6PP1', name: 'T-6PP1', type: 'airSide', floor: '6', category: 'Air side' },
    { id: 'T-6PP2', name: 'T-6PP2', type: 'airSide', floor: '6', category: 'Air side' }
  ],

  floor7: [
    // Interior Lighting (2)
    { id: 'T-7LP1', name: 'T-7LP1', type: 'lightPower', floor: '7', category: 'Interior Lighting' },
    { id: 'T-7ELP1', name: 'T-7ELP1', type: 'lightPower', floor: '7', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-071', name: 'T-DB-AHU-071', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'T-DB-AHU-072', name: 'T-DB-AHU-072', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'T-EPP1', name: 'T-EPP1', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'T-7PP1', name: 'T-7PP1', type: 'airSide', floor: '7', category: 'Air side' },
    { id: 'T-7PP2', name: 'T-7PP2', type: 'airSide', floor: '7', category: 'Air side' }
  ],

  floor8: [
    // Interior Lighting (2)
    { id: 'T-8LP1', name: 'T-8LP1', type: 'lightPower', floor: '8', category: 'Interior Lighting' },
    { id: 'T-8ELP1', name: 'T-8ELP1', type: 'lightPower', floor: '8', category: 'Interior Lighting' },
    // Air side (6)
    { id: 'T-DB-AHU-081', name: 'T-DB-AHU-081', type: 'airSide', floor: '8', category: 'Air side' },
    { id: 'T-DB-AHU-082', name: 'T-DB-AHU-082', type: 'airSide', floor: '8', category: 'Air side' },
    { id: 'T-8PP1', name: 'T-8PP1', type: 'airSide', floor: '8', category: 'Air side' },
    { id: 'T-8PP2', name: 'T-8PP2', type: 'airSide', floor: '8', category: 'Air side' },
    { id: 'T-8EPP1', name: 'T-8EPP1', type: 'airSide', floor: '8', category: 'Air side' },
    { id: 'T-8UBD-1', name: 'T-8UBD-1', type: 'airSide', floor: '8', category: 'Air side' }
  ],

  floor9: [
    // Interior Lighting (2)
    { id: 'T-9LP1', name: 'T-9LP1', type: 'lightPower', floor: '9', category: 'Interior Lighting' },
    { id: 'T-9ELP1', name: 'T-9ELP1', type: 'lightPower', floor: '9', category: 'Interior Lighting' },
    // Air side (4)
    { id: 'T-DB-AHU-091', name: 'T-DB-AHU-091', type: 'airSide', floor: '9', category: 'Air side' },
    { id: 'T-DB-AHU-092', name: 'T-DB-AHU-092', type: 'airSide', floor: '9', category: 'Air side' },
    { id: 'T-9PP1', name: 'T-9PP1', type: 'airSide', floor: '9', category: 'Air side' },
    { id: 'T-9ELP2', name: 'T-9ELP2', type: 'airSide', floor: '9', category: 'Air side' }
  ],

  floor10: [
    // Interior Lighting (2)
    { id: 'T-10LP1', name: 'T-10LP1', type: 'lightPower', floor: '10', category: 'Interior Lighting' },
    { id: 'T-10ELP1', name: 'T-10ELP1', type: 'lightPower', floor: '10', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-101', name: 'T-DB-AHU-101', type: 'airSide', floor: '10', category: 'Air side' },
    { id: 'T-DB-AHU-102', name: 'T-DB-AHU-102', type: 'airSide', floor: '10', category: 'Air side' },
    { id: 'T-10EPP1', name: 'T-10EPP1', type: 'airSide', floor: '10', category: 'Air side' },
    { id: 'T-10PP1', name: 'T-10PP1', type: 'airSide', floor: '10', category: 'Air side' },
    { id: 'T-10PP2', name: 'T-10PP2', type: 'airSide', floor: '10', category: 'Air side' }
  ],

  floor10A: [
    // Interior Lighting (1)
    { id: 'T-10AELP1', name: 'T-10AELP1', type: 'lightPower', floor: '10A', category: 'Interior Lighting' },
    // Air side (2)
    { id: 'T-10BEPP1', name: 'T-10BEPP1', type: 'airSide', floor: '10A', category: 'Air side' },
    { id: 'T-10AELC-08', name: 'T-10AELC-08', type: 'airSide', floor: '10A', category: 'Air side' }
  ],

  dataCenter: [
    // Air side (2)
    { id: 'T-11LC1', name: 'T-11LC1', type: 'data_center_others', floor: 'Data Center', category: 'Data Center' },
    { id: 'T-12LC1', name: 'T-12LC1', type: 'data_center_others', floor: 'Data Center', category: 'Data Center' }
  ],

  floor11: [
    // Interior Lighting (2)
    { id: 'T-11LP1', name: 'T-11LP1', type: 'lightPower', floor: '11', category: 'Interior Lighting' },
    { id: 'T-11ELP1', name: 'T-11ELP1', type: 'lightPower', floor: '11', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-111', name: 'T-DB-AHU-111', type: 'airSide', floor: '11', category: 'Air side' },
    { id: 'T-DB-AHU-112', name: 'T-DB-AHU-112', type: 'airSide', floor: '11', category: 'Air side' },
    { id: 'T-11EPP1', name: 'T-11EPP1', type: 'airSide', floor: '11', category: 'Air side' },
    { id: 'T-11PP1', name: 'T-11PP1', type: 'airSide', floor: '11', category: 'Air side' },
    { id: 'T-11PP2', name: 'T-11PP2', type: 'airSide', floor: '11', category: 'Air side' }
  ],

  floor12: [
    // Interior Lighting (2)
    { id: 'T-12LP1', name: 'T-12LP1', type: 'lightPower', floor: '12', category: 'Interior Lighting' },
    { id: 'T-12ELP1-F12', name: 'T-12ELP1', type: 'lightPower', floor: '12', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-121', name: 'T-DB-AHU-121', type: 'airSide', floor: '12', category: 'Air side' },
    { id: 'T-DB-AHU-122', name: 'T-DB-AHU-122', type: 'airSide', floor: '12', category: 'Air side' },
    { id: 'T-12PP1', name: 'T-12PP1', type: 'airSide', floor: '12', category: 'Air side' },
    { id: 'T-12PP2', name: 'T-12PP2', type: 'airSide', floor: '12', category: 'Air side' },
    { id: 'T-12EPP1', name: 'T-12EPP1', type: 'airSide', floor: '12', category: 'Air side' }
  ],

  floor14: [
    // Interior Lighting (2)
    { id: 'T-14LP1', name: 'T-14LP1', type: 'lightPower', floor: '14', category: 'Interior Lighting' },
    { id: 'T-12ELP1-F14', name: 'T-12ELP1', type: 'lightPower', floor: '14', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-141', name: 'T-DB-AHU-141', type: 'airSide', floor: '14', category: 'Air side' },
    { id: 'T-DB-AHU-142', name: 'T-DB-AHU-142', type: 'airSide', floor: '14', category: 'Air side' },
    { id: 'T-14ELP2', name: 'T-14ELP2', type: 'airSide', floor: '14', category: 'Air side' },
    { id: 'T-14PP1', name: 'T-14PP1', type: 'airSide', floor: '14', category: 'Air side' },
    { id: 'T-14PP2', name: 'T-14PP2', type: 'airSide', floor: '14', category: 'Air side' }
  ],

  floor15: [
    // Interior Lighting (2)
    { id: 'T-15LP1', name: 'T-15LP1', type: 'lightPower', floor: '15', category: 'Interior Lighting' },
    { id: 'T-12ELP1-F15', name: 'T-12ELP1', type: 'lightPower', floor: '15', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-151', name: 'T-DB-AHU-151', type: 'airSide', floor: '15', category: 'Air side' },
    { id: 'T-DB-AHU-152', name: 'T-DB-AHU-152', type: 'airSide', floor: '15', category: 'Air side' },
    { id: 'T-15EPP1', name: 'T-15EPP1', type: 'airSide', floor: '15', category: 'Air side' },
    { id: 'T-15PP1', name: 'T-15PP1', type: 'airSide', floor: '15', category: 'Air side' },
    { id: 'T-15PP2', name: 'T-15PP2', type: 'airSide', floor: '15', category: 'Air side' }
  ],

  floor16: [
    // Interior Lighting (2)
    { id: 'T-16LP1', name: 'T-16LP1', type: 'lightPower', floor: '16', category: 'Interior Lighting' },
    { id: 'T-16ELP1', name: 'T-16ELP1', type: 'lightPower', floor: '16', category: 'Interior Lighting' },
    // Air side (6)
    { id: 'T-DB-AHU-161', name: 'T-DB-AHU-161', type: 'airSide', floor: '16', category: 'Air side' },
    { id: 'T-DB-AHU-162', name: 'T-DB-AHU-162', type: 'airSide', floor: '16', category: 'Air side' },
    { id: 'T-16PP1', name: 'T-16PP1', type: 'airSide', floor: '16', category: 'Air side' },
    { id: 'T-16PP2', name: 'T-16PP2', type: 'airSide', floor: '16', category: 'Air side' },
    { id: 'T-16EPP1', name: 'T-16EPP1', type: 'airSide', floor: '16', category: 'Air side' },
    { id: 'T-16UBD-1', name: 'T-16UBD-1', type: 'airSide', floor: '16', category: 'Air side' }
  ],

  floor17: [
    // Interior Lighting (2)
    { id: 'T-17LP1', name: 'T-17LP1', type: 'lightPower', floor: '17', category: 'Interior Lighting' },
    { id: 'T-17ELP2', name: 'T-17ELP2', type: 'lightPower', floor: '17', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-171', name: 'T-DB-AHU-171', type: 'airSide', floor: '17', category: 'Air side' },
    { id: 'T-DB-AHU-172', name: 'T-DB-AHU-172', type: 'airSide', floor: '17', category: 'Air side' },
    { id: 'T-17ELP1', name: 'T-17ELP1', type: 'airSide', floor: '17', category: 'Air side' },
    { id: 'T-17PP1', name: 'T-17PP1', type: 'airSide', floor: '17', category: 'Air side' },
    { id: 'T-17PP2', name: 'T-17PP2', type: 'airSide', floor: '17', category: 'Air side' }
  ],

  floor18: [
    // Interior Lighting (2)
    { id: 'T-18LP1', name: 'T-18LP1', type: 'lightPower', floor: '18', category: 'Interior Lighting' },
    { id: 'T-18ELP1', name: 'T-18ELP1', type: 'lightPower', floor: '18', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-181', name: 'T-DB-AHU-181', type: 'airSide', floor: '18', category: 'Air side' },
    { id: 'T-DB-AHU-182', name: 'T-DB-AHU-182', type: 'airSide', floor: '18', category: 'Air side' },
    { id: 'T-18EPP1', name: 'T-18EPP1', type: 'airSide', floor: '18', category: 'Air side' },
    { id: 'T-18PP1', name: 'T-18PP1', type: 'airSide', floor: '18', category: 'Air side' },
    { id: 'T-18PP2', name: 'T-18PP2', type: 'airSide', floor: '18', category: 'Air side' }
  ],

  floor19: [
    // Interior Lighting (2)
    { id: 'T-19LP1', name: 'T-19LP1', type: 'lightPower', floor: '19', category: 'Interior Lighting' },
    { id: 'T-19ELP1', name: 'T-19ELP1', type: 'lightPower', floor: '19', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-191', name: 'T-DB-AHU-191', type: 'airSide', floor: '19', category: 'Air side' },
    { id: 'T-DB-AHU-192', name: 'T-DB-AHU-192', type: 'airSide', floor: '19', category: 'Air side' },
    { id: 'T-19PP1', name: 'T-19PP1', type: 'airSide', floor: '19', category: 'Air side' },
    { id: 'T-19PP2', name: 'T-19PP2', type: 'airSide', floor: '19', category: 'Air side' },
    { id: 'T-19EPP1', name: 'T-19EPP1', type: 'airSide', floor: '19', category: 'Air side' }
  ],

  floor20: [
    // Interior Lighting (2)
    { id: 'T-20LP1', name: 'T-20LP1', type: 'lightPower', floor: '20', category: 'Interior Lighting' },
    { id: 'T-20ELP1', name: 'T-20ELP1', type: 'lightPower', floor: '20', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-201', name: 'T-DB-AHU-201', type: 'airSide', floor: '20', category: 'Air side' },
    { id: 'T-DB-AHU-202', name: 'T-DB-AHU-202', type: 'airSide', floor: '20', category: 'Air side' },
    { id: 'T-21ELP2', name: 'T-21ELP2', type: 'airSide', floor: '20', category: 'Air side' },
    { id: 'T-20PP1', name: 'T-20PP1', type: 'airSide', floor: '20', category: 'Air side' },
    { id: 'T-20PP2', name: 'T-20PP2', type: 'airSide', floor: '20', category: 'Air side' }
  ],

  floor21: [
    // Interior Lighting (2)
    { id: 'T-21LP1', name: 'T-21LP1', type: 'lightPower', floor: '21', category: 'Interior Lighting' },
    { id: 'T-21ELP1', name: 'T-21ELP1', type: 'lightPower', floor: '21', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-211', name: 'T-DB-AHU-211', type: 'airSide', floor: '21', category: 'Air side' },
    { id: 'T-DB-AHU-212', name: 'T-DB-AHU-212', type: 'airSide', floor: '21', category: 'Air side' },
    { id: 'T-21EPP1', name: 'T-21EPP1', type: 'airSide', floor: '21', category: 'Air side' },
    { id: 'T-21PP1', name: 'T-21PP1', type: 'airSide', floor: '21', category: 'Air side' },
    { id: 'T-21PP2', name: 'T-21PP2', type: 'airSide', floor: '21', category: 'Air side' },
    // Other (1)
    { id: 'T-21ELC-OB', name: 'T-21ELC-OB', type: 'data_center_others', floor: '21', category: 'Other' }
  ],

  floor22: [
    // Interior Lighting (2)
    { id: 'T-22LP1', name: 'T-22LP1', type: 'lightPower', floor: '22', category: 'Interior Lighting' },
    { id: 'T-22ELP1', name: 'T-22ELP1', type: 'lightPower', floor: '22', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-221', name: 'T-DB-AHU-221', type: 'airSide', floor: '22', category: 'Air side' },
    { id: 'T-DB-AHU-222', name: 'T-DB-AHU-222', type: 'airSide', floor: '22', category: 'Air side' },
    { id: 'T-22PP1', name: 'T-22PP1', type: 'airSide', floor: '22', category: 'Air side' },
    { id: 'T-22PP2', name: 'T-22PP2', type: 'airSide', floor: '22', category: 'Air side' },
    { id: 'T-22EPP1', name: 'T-22EPP1', type: 'airSide', floor: '22', category: 'Air side' }
  ],

  floor23: [
    // Interior Lighting (2)
    { id: 'T-23LP1', name: 'T-23LP1', type: 'lightPower', floor: '23', category: 'Interior Lighting' },
    { id: 'T-23ELP1', name: 'T-23ELP1', type: 'lightPower', floor: '23', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-231', name: 'T-DB-AHU-231', type: 'airSide', floor: '23', category: 'Air side' },
    { id: 'T-DB-AHU-232', name: 'T-DB-AHU-232', type: 'airSide', floor: '23', category: 'Air side' },
    { id: 'T-23ELP2', name: 'T-23ELP2', type: 'airSide', floor: '23', category: 'Air side' },
    { id: 'T-23PP1', name: 'T-23PP1', type: 'airSide', floor: '23', category: 'Air side' },
    { id: 'T-23PP2', name: 'T-23PP2', type: 'airSide', floor: '23', category: 'Air side' }
  ],

  floor24: [
    // Interior Lighting (2)
    { id: 'T-24LP1', name: 'T-24LP1', type: 'lightPower', floor: '24', category: 'Interior Lighting' },
    { id: 'T-24ELP1', name: 'T-24ELP1', type: 'lightPower', floor: '24', category: 'Interior Lighting' },
    // Air side (5)
    { id: 'T-DB-AHU-241', name: 'T-DB-AHU-241', type: 'airSide', floor: '24', category: 'Air side' },
    { id: 'T-DB-AHU-242', name: 'T-DB-AHU-242', type: 'airSide', floor: '24', category: 'Air side' },
    { id: 'T-24EPP1', name: 'T-24EPP1', type: 'airSide', floor: '24', category: 'Air side' },
    { id: 'T-24PP1', name: 'T-24PP1', type: 'airSide', floor: '24', category: 'Air side' },
    { id: 'T-24PP2', name: 'T-24PP2', type: 'airSide', floor: '24', category: 'Air side' }
  ],

  floor25: [
    // Interior Lighting (2)
    { id: 'T-25LP1', name: 'T-25LP1', type: 'lightPower', floor: '25', category: 'Interior Lighting' },
    { id: 'T-25ELP1', name: 'T-25ELP1', type: 'lightPower', floor: '25', category: 'Interior Lighting' },
    // Air side (7)
    { id: 'T-DB-AHU-251', name: 'T-DB-AHU-251', type: 'airSide', floor: '25', category: 'Air side' },
    { id: 'T-DB-AHU-252', name: 'T-DB-AHU-252', type: 'airSide', floor: '25', category: 'Air side' },
    { id: 'T-DB-EF-262', name: 'T-DB-EF-262', type: 'airSide', floor: '25', category: 'Air side' },
    { id: 'T-DB-EF-251', name: 'T-DB-EF-251', type: 'airSide', floor: '25', category: 'Air side' },
    { id: 'T-25PP1', name: 'T-25PP1', type: 'airSide', floor: '25', category: 'Air side' },
    { id: 'T-25PP2', name: 'T-25PP2', type: 'airSide', floor: '25', category: 'Air side' },
    { id: 'T-25EPP1', name: 'T-25EPP1', type: 'airSide', floor: '25', category: 'Air side' }
  ],

  floor26: [
    // Interior Lighting (1)
    { id: 'T-26ELP1', name: 'T-26ELP1', type: 'lightPower', floor: '26', category: 'Interior Lighting' },
    // Air side (6)
    { id: 'EDB-T-KEF-261', name: 'EDB-T-KEF-261', type: 'airSide', floor: '26', category: 'Air side' },
    { id: 'EDB-T-AHU-261', name: 'EDB-T-AHU-261', type: 'airSide', floor: '26', category: 'Air side' },
    { id: 'EDB-T-AHU-262', name: 'EDB-T-AHU-262', type: 'airSide', floor: '26', category: 'Air side' },
    { id: 'EDB-T-AHU-263', name: 'EDB-T-AHU-263', type: 'airSide', floor: '26', category: 'Air side' },
    { id: 'T-26ELCK1', name: 'T-26ELCK1', type: 'airSide', floor: '26', category: 'Air side' },
    { id: 'T-26ELP2', name: 'T-26ELP2', type: 'airSide', floor: '26', category: 'Air side' },
    // Other (1)
    { id: 'T-26ELCO', name: 'T-26ELCO', type: 'data_center_others', floor: '26', category: 'Other' }
  ],

  floor27: [
    // Interior Lighting (3)
    { id: 'T-27ELP1', name: 'T-27ELP1', type: 'lightPower', floor: '27', category: 'Interior Lighting' },
    { id: 'T-27LP1-1', name: 'T-27LP1', type: 'lightPower', floor: '27', category: 'Interior Lighting' },
    { id: 'T-27LP1-2', name: 'T-27LP1', type: 'lightPower', floor: '27', category: 'Interior Lighting' },
    // Air side (6)
    { id: 'EDB-T-AHU-271', name: 'EDB-T-AHU-271', type: 'airSide', floor: '27', category: 'Air side' },
    { id: 'EDB-T-AHU-272', name: 'EDB-T-AHU-272', type: 'airSide', floor: '27', category: 'Air side' },
    { id: 'T-27EPP1', name: 'T-27EPP1', type: 'airSide', floor: '27', category: 'Air side' },
    { id: 'T-27PP1-1', name: 'T-27PP1', type: 'airSide', floor: '27', category: 'Air side' },
    { id: 'T-27PP1-2', name: 'T-27PP1', type: 'airSide', floor: '27', category: 'Air side' },
    { id: 'T-27LCK1', name: 'T-27LCK1', type: 'airSide', floor: '27', category: 'Air side' }
  ],

  floor28: [
    // Interior Lighting (2)
    { id: 'T-28LP1', name: 'T-28LP1', type: 'lightPower', floor: '28', category: 'Interior Lighting' },
    { id: 'T-28ELP1', name: 'T-28ELP1', type: 'lightPower', floor: '28', category: 'Interior Lighting' },
    // Air side (4)
    { id: 'EDB-T-AHU-281', name: 'EDB-T-AHU-281', type: 'airSide', floor: '28', category: 'Air side' },
    { id: 'EDB-T-AHU-282', name: 'EDB-T-AHU-282', type: 'airSide', floor: '28', category: 'Air side' },
    { id: 'T-28PP1', name: 'T-28PP1', type: 'airSide', floor: '28', category: 'Air side' },
    { id: 'T-28Epp1', name: 'T-28Epp1', type: 'airSide', floor: '28', category: 'Air side' }
  ],

  roof: [
    // Air side (2)
    { id: 'T-RLC1', name: 'T-RLC1', type: 'airSide', floor: 'Roof', category: 'Air side' },
    { id: 'T-RELC1', name: 'T-RELC1', type: 'airSide', floor: 'Roof', category: 'Air side' },
    // Sanitary (1)
    { id: 'T-EPB-01', name: 'T-EPB-01', type: 'data_center_others', floor: 'Roof', category: 'Sanitary' }
  ]
};

// Helper function to get all meters as flat array
export function getAllTowerBuildingMeters(): TowerMeter[] {
  return Object.values(TOWER_BUILDING_METERS).flat();
}

// Helper function to get total meter count
export function getTowerBuildingMeterCount(): number {
  return getAllTowerBuildingMeters().length;
}

// Helper function to get meters by floor
export function getTowerMetersByFloor(floor: string): TowerMeter[] {
  const floorKey = `floor${floor}`.replace('floorfloor', 'floor');
  return TOWER_BUILDING_METERS[floorKey] || [];
}

// Helper function to get meters by category
export function getTowerMetersByCategory(category: string): TowerMeter[] {
  return getAllTowerBuildingMeters().filter(meter => meter.category === category);
}

// Helper function to get category counts
export function getTowerMeterCategoryCounts(): Record<string, number> {
  const counts: Record<string, number> = {};
  getAllTowerBuildingMeters().forEach(meter => {
    counts[meter.category] = (counts[meter.category] || 0) + 1;
  });
  return counts;
}