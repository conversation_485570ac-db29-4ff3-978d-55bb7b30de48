import { MAIN_METER_LIST } from './building/meters';

import { SYSTEM_BREAKDOWN_COLORS } from '../utils/chart-colors';

// Energy System Types and their associated meters
export const SYSTEM_TYPES = {
  chillerPlant: {
    id: 'chillerPlant',
    name: 'Chiller Plant',
    color: SYSTEM_BREAKDOWN_COLORS.chillerPlant,
    meters: MAIN_METER_LIST.chillerPlant.meters,
    description: 'Central cooling system including chillers and cooling towers'
  },
  airSide: {
    id: 'airSide',
    name: 'Air Side',
    color: SYSTEM_BREAKDOWN_COLORS.airSide,
    meters: [], // Map AHU meters here
    description: 'Air handling units and ventilation systems'
  },
  data_center_others: {
    id: 'data_center_others',
    name: 'Data Center & Others',
    color: SYSTEM_BREAKDOWN_COLORS.data_center_others,
    meters: [],
    description: 'Data center, IT equipment, and miscellaneous power consumption'
  },
  light_power: {
    id: 'light_power',
    name: 'Light & Power',
    color: SYSTEM_BREAKDOWN_COLORS.light_power,
    meters: [], // Map equipment meters here
    description: 'Lighting, office equipment and general power'
  },
  evCharger: {
    id: 'evCharger',
    name: 'EV Charger',
    color: SYSTEM_BREAKDOWN_COLORS.evCharger,
    meters: [], // Map EV charger meters here
    description: 'Electric vehicle charging stations'
  },
  escalator_elevator: {
    id: 'escalator_elevator',
    name: 'Escalator / Elevator',
    color: SYSTEM_BREAKDOWN_COLORS.escalator_elevator,
    meters: [], // Map escalator/elevator meters here
    description: 'Escalators and elevators throughout the building'
  }
} as const;

// Helper function to calculate system consumption
export function calculateSystemConsumption(systemId: keyof typeof SYSTEM_TYPES, readings: Record<string, number>) {
  const system = SYSTEM_TYPES[systemId];
  return system.meters.reduce((total, meter) => {
    return total + (readings[meter.id] || 0);
  }, 0);
}

// Helper function to get system breakdown
export function getSystemBreakdown(readings: Record<string, number>) {
  const totalConsumption = Object.values(readings).reduce((sum, value) => sum + value, 0);

  return Object.entries(SYSTEM_TYPES).map(([id, system]) => {
    const consumption = calculateSystemConsumption(id as keyof typeof SYSTEM_TYPES, readings);
    const percentage = totalConsumption > 0 ? (consumption / totalConsumption) * 100 : 0;

    return {
      id,
      name: system.name,
      color: system.color,
      value: Math.round(percentage),
      consumption: Math.round(consumption)
    };
  });
}

// Default mock breakdown for initial rendering
const TOTAL_CONSUMPTION = 238338; // 953,352 / 4

export const DEFAULT_SYSTEM_BREAKDOWN = [
  { id: 'chillerPlant', name: 'Chiller Plant', value: 32, consumption: Math.round(TOTAL_CONSUMPTION * 0.32), color: SYSTEM_TYPES.chillerPlant.color },
  { id: 'airSide', name: 'Air Side', value: 22, consumption: Math.round(TOTAL_CONSUMPTION * 0.22), color: SYSTEM_TYPES.airSide.color },
  { id: 'data_center_others', name: 'Data Center & Others', value: 24, consumption: Math.round(TOTAL_CONSUMPTION * 0.24), color: SYSTEM_TYPES.data_center_others.color },
  { id: 'light_power', name: 'Light & Power', value: 16, consumption: Math.round(TOTAL_CONSUMPTION * 0.16), color: SYSTEM_TYPES.light_power.color },
  { id: 'evCharger', name: 'EV Charger', value: 4, consumption: Math.round(TOTAL_CONSUMPTION * 0.04), color: SYSTEM_TYPES.evCharger.color },
  { id: 'escalator_elevator', name: 'Escalator / Elevator', value: 2, consumption: Math.round(TOTAL_CONSUMPTION * 0.02), color: SYSTEM_TYPES.escalator_elevator.color }
];