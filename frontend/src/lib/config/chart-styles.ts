// Unified Chart Styles for both canvas and SVG charts
// This configuration ensures consistent appearance across all visualizations

export const UNIFIED_CHART_STYLES = {
  // Base colors
  colors: {
    primary: '#065BA9',    // Strong Dark Blue - primary series
    secondary: '#3B82F6',  // Bright Blue - secondary/comparison series
    accent: '#10B981',     // Green for positive values
    warning: '#F59E0B',    // Amber for warnings
    danger: '#EF4444',     // Red for negative/error values
    purple: '#8B5CF6',     // Purple for additional data series
    pink: '#EC4899',       // Pink for additional data series
    
    // Background and container colors
    background: '#FFFFFF',
    containerBg: '#F9FAFB',
    
    // Grid and axis colors
    grid: '#E5E7EB',
    axis: '#94A3B8',
    
    // Text colors
    text: {
      primary: '#1E293B',
      secondary: '#64748B',
      label: '#94A3B8'
    }
  },
  
  // Shared font settings
  fonts: {
    family: 'Inter, system-ui, sans-serif',
    size: {
      xs: 8,
      sm: 10,
      md: 12,
      lg: 14
    },
    weight: {
      normal: 400,
      medium: 500,
      semibold: 600
    }
  },
  
  // Spacing and sizing
  spacing: {
    padding: {
      sm: 4,
      md: 8,
      lg: 16
    },
    margin: {
      sm: 4,
      md: 8,
      lg: 16
    }
  },
  
  // Border styles
  border: {
    radius: {
      sm: 4,
      md: 6,
      lg: 8
    },
    width: 1,
    color: '#E2E8F0'
  },
  
  // Chart element styles
  chart: {
    lineWidth: 2,
    barWidth: 0.7, // as percentage
    pointSize: 4,
    areaOpacity: 0.15,
    
    // Shared tooltip styles
    tooltip: {
      background: 'rgba(255, 255, 255, 0.98)',
      border: '#E2E8F0',
      shadow: '0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
      text: '#1E293B'
    }
  }
};

// Helper functions for consistent styling across chart libraries
export const getChartColors = (count: number = 1): string[] => {
  const baseColors = [
    UNIFIED_CHART_STYLES.colors.primary,
    UNIFIED_CHART_STYLES.colors.secondary,
    UNIFIED_CHART_STYLES.colors.accent,
    UNIFIED_CHART_STYLES.colors.warning,
    UNIFIED_CHART_STYLES.colors.purple,
    UNIFIED_CHART_STYLES.colors.pink,
    UNIFIED_CHART_STYLES.colors.danger
  ];
  
  // Return requested number of colors (with wrapping if more are requested)
  return Array(count).fill(0).map((_, i) => baseColors[i % baseColors.length]);
};

// Export specific configurations for different chart libraries
export const RECHARTS_CONFIG = {
  // Specific settings for Recharts (SVG-based)
  lineStyle: {
    strokeWidth: UNIFIED_CHART_STYLES.chart.lineWidth,
    dot: {
      r: UNIFIED_CHART_STYLES.chart.pointSize,
      strokeWidth: 1,
      fill: UNIFIED_CHART_STYLES.colors.background,
      stroke: UNIFIED_CHART_STYLES.colors.primary
    }
  },
  barStyle: {
    barSize: UNIFIED_CHART_STYLES.chart.barWidth * 20, // Converting percentage to pixels
    radius: [UNIFIED_CHART_STYLES.border.radius.sm, UNIFIED_CHART_STYLES.border.radius.sm, 0, 0]
  },
  gridStyle: {
    stroke: UNIFIED_CHART_STYLES.colors.grid,
    strokeDasharray: '3 3',
    vertical: true,
    horizontal: true
  },
  axisStyle: {
    stroke: UNIFIED_CHART_STYLES.colors.axis,
    fontSize: UNIFIED_CHART_STYLES.fonts.size.xs,
    fontWeight: UNIFIED_CHART_STYLES.fonts.weight.normal,
    tickSize: 4,
    tickMargin: 8
  },
  tooltipStyle: {
    contentStyle: {
      background: UNIFIED_CHART_STYLES.chart.tooltip.background,
      border: `1px solid ${UNIFIED_CHART_STYLES.chart.tooltip.border}`,
      borderRadius: UNIFIED_CHART_STYLES.border.radius.md,
      boxShadow: UNIFIED_CHART_STYLES.chart.tooltip.shadow,
      padding: UNIFIED_CHART_STYLES.spacing.padding.md
    },
    itemStyle: {
      color: UNIFIED_CHART_STYLES.colors.text.primary,
      fontSize: UNIFIED_CHART_STYLES.fonts.size.sm,
      padding: '3px 0'
    },
    labelStyle: {
      color: UNIFIED_CHART_STYLES.colors.text.secondary,
      fontSize: UNIFIED_CHART_STYLES.fonts.size.xs,
      fontWeight: UNIFIED_CHART_STYLES.fonts.weight.medium,
      marginBottom: 4
    }
  }
};

export const CANVAS_CONFIG = {
  // Specific settings for Canvas-based charts (ECharts, Canvas API)
  lineWidth: UNIFIED_CHART_STYLES.chart.lineWidth,
  pointRadius: UNIFIED_CHART_STYLES.chart.pointSize,
  fontFamily: UNIFIED_CHART_STYLES.fonts.family,
  fontSize: {
    axis: UNIFIED_CHART_STYLES.fonts.size.xs,
    label: UNIFIED_CHART_STYLES.fonts.size.sm,
    title: UNIFIED_CHART_STYLES.fonts.size.md
  },
  padding: {
    top: UNIFIED_CHART_STYLES.spacing.padding.lg,
    right: UNIFIED_CHART_STYLES.spacing.padding.md,
    bottom: UNIFIED_CHART_STYLES.spacing.padding.lg,
    left: UNIFIED_CHART_STYLES.spacing.padding.lg * 1.5 // Slightly larger for y-axis labels
  },
  grid: {
    color: UNIFIED_CHART_STYLES.colors.grid,
    width: 1,
    dash: [3, 3]
  },
  barStyle: {
    width: UNIFIED_CHART_STYLES.chart.barWidth,
    cornerRadius: UNIFIED_CHART_STYLES.border.radius.sm
  },
  tooltip: {
    backgroundColor: UNIFIED_CHART_STYLES.chart.tooltip.background,
    borderColor: UNIFIED_CHART_STYLES.chart.tooltip.border,
    textStyle: {
      color: UNIFIED_CHART_STYLES.colors.text.primary,
      fontSize: UNIFIED_CHART_STYLES.fonts.size.sm
    },
    padding: [
      UNIFIED_CHART_STYLES.spacing.padding.md,
      UNIFIED_CHART_STYLES.spacing.padding.md,
      UNIFIED_CHART_STYLES.spacing.padding.md,
      UNIFIED_CHART_STYLES.spacing.padding.md
    ]
  }
};

// Export the specific color palette for use in both chart types
export const CHART_COLORS = getChartColors(7);

// ECharts specific line chart configuration for Building Load Profile style
export const ECHARTS_LINE_CONFIG = {
  gradient: {
    area: [
      { offset: 0, color: 'rgba(6, 91, 169, 0.25)' },
      { offset: 1, color: 'rgba(6, 91, 169, 0.02)' }
    ],
    visualMap: [
      { offset: 0, color: 'rgba(96, 165, 250, 0.25)' },
      { offset: 1, color: 'rgba(96, 165, 250, 0.01)' }
    ]
  },
  series: {
    type: 'line',
    smooth: true,
    symbol: 'circle',
    symbolSize: 0,
    showSymbol: false,
    z: 2,
    zlevel: 1,
    lineStyle: {
      width: 2,
      color: UNIFIED_CHART_STYLES.colors.secondary,
      shadowBlur: 4,
      shadowColor: 'rgba(59, 130, 246, 0.1)'
    },
    emphasis: {
      focus: 'series',
      scale: true,
      symbolSize: 6,
      itemStyle: {
        borderWidth: 3,
        shadowBlur: 8,
        shadowColor: 'rgba(59, 130, 246, 0.25)'
      },
      lineStyle: {
        width: 3,
        shadowBlur: 6,
        shadowColor: 'rgba(59, 130, 246, 0.2)'
      }
    }
  },
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderColor: '#E2E8F0',
    borderWidth: 1,
    padding: [12, 16],
    textStyle: {
      color: '#1E293B',
      fontSize: 12
    },
    extraCssText: 'backdrop-filter: blur(4px); box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);',
    axisPointer: {
      type: 'line',
      lineStyle: {
        color: 'rgba(148, 163, 184, 0.2)',
        width: 2,
        type: 'dashed'
      }
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '5%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      fontSize: 10,
      color: '#64748B',
      fontWeight: 500
    },
    splitLine: {
      show: false
    }
  },
  yAxis: {
    type: 'value',
    nameTextStyle: {
      color: '#64748B',
      fontSize: 11,
      padding: [0, 0, 10, 0],
      fontWeight: 500
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: '#E2E8F0'
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      fontSize: 10,
      color: '#64748B'
    },
    splitLine: {
      z: -10,
      zlevel: -1,
      lineStyle: {
        color: '#F1F5F9',
        type: 'dashed'
      }
    }
  }
};

// Recharts specific line chart configuration for consistency
export const RECHARTS_LINE_CONFIG = {
  margins: { top: 20, right: 20, left: 0, bottom: 60 },
  cartesianGrid: {
    strokeDasharray: '3 3',
    vertical: false,
    stroke: '#F1F5F9',
    strokeOpacity: 1
  },
  xAxis: {
    axisLine: false,
    tickLine: false,
    tick: {
      fontSize: 10,
      fill: '#64748B',
      fontWeight: 500
    }
  },
  yAxis: {
    axisLine: false,
    tickLine: false,
    tick: {
      fontSize: 10,
      fill: '#64748B',
      fontWeight: 500
    },
    label: {
      angle: -90,
      position: 'insideLeft',
      fill: '#64748B',
      fontSize: 11,
      fontWeight: 500
    }
  },
  line: {
    type: 'monotone' as const,
    stroke: UNIFIED_CHART_STYLES.colors.secondary,
    strokeWidth: 2,
    dot: false,
    activeDot: {
      r: 4,
      strokeWidth: 2,
      fill: '#fff'
    },
    animationDuration: 1000,
    animationEasing: 'ease-in-out' as const
  },
  area: {
    fillOpacity: 0.15,
    animationDuration: 1000,
    animationEasing: 'ease-in-out' as const
  },
  tooltip: {
    contentStyle: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      border: '1px solid #E2E8F0',
      borderRadius: '8px',
      padding: '12px',
      fontSize: '12px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      backdropFilter: 'blur(4px)'
    },
    cursor: {
      stroke: 'rgba(148, 163, 184, 0.2)',
      strokeWidth: 2,
      strokeDasharray: '3 3'
    }
  }
};
