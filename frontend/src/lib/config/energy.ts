import type { BuildingId } from '../../types';
import { BUILDINGS } from '../constants';

// Energy consumption rates by space type (kWh/day)
export const ENERGY_CONSUMPTION_RATES = {
  'Office': {
    base: 1855.5,
    breakdown: {
      hvac: 0.45,      // 45% HVAC
      lighting: 0.25,   // 25% Lighting
      equipment: 0.20,  // 20% Equipment
      others: 0.10      // 10% Others
    },
    description: 'High occupancy, computers, lighting'
  },
  'Meeting Room': {
    base: 1452.0,
    breakdown: {
      hvac: 0.40,
      lighting: 0.30,
      equipment: 0.25,
      others: 0.05
    },
    description: 'AV equipment, variable occupancy'
  },
  'Storage': {
    base: 458.0,
    breakdown: {
      hvac: 0.20,
      lighting: 0.40,
      equipment: 0.30,
      others: 0.10
    },
    description: 'Minimal lighting, some ventilation'
  },
  'Equipment Room': {
    base: 2853.0,
    breakdown: {
      hvac: 0.50,
      lighting: 0.10,
      equipment: 0.35,
      others: 0.05
    },
    description: 'Servers, cooling systems'
  },
  'Lobby': {
    base: 1652.0,
    breakdown: {
      hvac: 0.35,
      lighting: 0.30,
      equipment: 0.25,
      others: 0.10
    },
    description: '24/7 operation, lighting, security'
  },
  'Amazon': {
    base: 3255.0,
    breakdown: {
      hvac: 0.40,
      lighting: 0.30,
      equipment: 0.25,
      others: 0.05
    },
    description: 'High cooling load, equipment'
  },
  'Counter TSD': {
    base: 1423.0,
    breakdown: {
      hvac: 0.35,
      lighting: 0.25,
      equipment: 0.35,
      others: 0.05
    },
    description: 'Computers, lighting'
  },
  'Canteen': {
    base: 2786.0,
    breakdown: {
      hvac: 0.30,
      lighting: 0.20,
      equipment: 0.45,
      others: 0.05
    },
    description: 'Kitchen equipment, cooling'
  },
  'Inventory': {
    base: 684.0,
    breakdown: {
      hvac: 0.25,
      lighting: 0.35,
      equipment: 0.35,
      others: 0.05
    },
    description: 'Lighting, ventilation'
  },
  'Basement': {
    base: 957.0,
    breakdown: {
      hvac: 0.30,
      lighting: 0.30,
      equipment: 0.35,
      others: 0.05
    },
    description: 'Constant ventilation, lighting'
  }
} as const;

// Calculate tower consumption based on floor usage
export function calculateBuildingConsumption(buildingId: BuildingId): number {
  const building = BUILDINGS[buildingId];
  let totalConsumption = 0;
  
  Object.entries(building.floorUsage).forEach(([_, usage]) => {
    const rate = ENERGY_CONSUMPTION_RATES[usage as keyof typeof ENERGY_CONSUMPTION_RATES];
    if (rate) {
      totalConsumption += rate.base;
    }
  });
  
  return totalConsumption;
}

// Calculate CO2 emissions based on consumption (kgCO2e)
export function calculateCO2Emissions(consumption: number, fromDate?: Date): number {
  const CO2_FACTOR = 0.5; // kgCO2e per kWh
  
  // If no date provided, calculate single consumption CO2
  if (!fromDate) {
    return Math.max(0, consumption * CO2_FACTOR);
  }

  // Calculate days from start of year to given date
  const now = fromDate;
  const startOfYear = new Date(now.getFullYear(), 0, 1);
  const daysPassed = Math.floor((now.getTime() - startOfYear.getTime()) / (1000 * 60 * 60 * 24));
  
  // Estimate daily consumption and multiply by days passed
  const yearToDateConsumption = consumption * daysPassed;
  return Math.max(0, yearToDateConsumption * CO2_FACTOR);
}

// Pre-calculate tower consumptions
const buildingConsumptions = {
  A: calculateBuildingConsumption('A'),
  B: calculateBuildingConsumption('B'),
  C: calculateBuildingConsumption('C')
};

// Tower Energy Configuration Summary
export const ENERGY_CONFIG = {
  version: '1.0.0',
  buildings: {
    A: {
      consumption: buildingConsumptions.A,
      peakDemand: 22505.0,
      baseload: 8502.0
    },
    B: {
      consumption: buildingConsumptions.B,
      peakDemand: 11503.0,
      baseload: 4258.0
    },
    C: {
      consumption: buildingConsumptions.C,
      peakDemand: 9807.0,
      baseload: 3854.0
    }
  },
  thresholds: {
    consumption: {
      low: 10000,    // kWh/day
      medium: 20000,  // kWh/day
      high: 30000,    // kWh/day
      critical: 40000 // kWh/day
    },
    peakDemand: {
      low: 5000,     // kW
      medium: 10000,  // kW
      high: 15000,    // kW
      critical: 20000 // kW
    }
  }
} as const;