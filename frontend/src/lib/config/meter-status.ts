// Meter Status Configuration
export const METER_STATUS_CONFIG = {
  // Distribution of meters across buildings
  buildingWeights: {
    A: 0.6, // 60% in Tower A
    B: 0.25, // 25% in Tower B
    C: 0.15  // 15% in Tower C
  },

  // Status distribution
  statusDistribution: {
    active: 0.94,      // 94% active
    disconnected: 0.04, // 4% disconnected
    warning: 0.02      // 2% with warnings
  },

  // Update intervals (ms)
  updateIntervals: {
    readings: 5000,     // 5 seconds
    status: 30000,      // 30 seconds
    quality: 60000      // 1 minute
  },

  // Alert thresholds
  thresholds: {
    voltage: {
      min: 207, // -10% of 230V
      max: 253  // +10% of 230V
    },
    current: {
      warning: 0.8,  // 80% of rated
      critical: 0.95 // 95% of rated
    },
    powerFactor: {
      min: 0.85
    },
    thd: {
      voltage: 5,  // 5% THD for voltage
      current: 15  // 15% THD for current
    }
  }
} as const;