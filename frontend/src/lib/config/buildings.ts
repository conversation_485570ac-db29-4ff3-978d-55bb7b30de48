// Building Configuration Constants
export const TOWER_A_CONFIG = {
  id: 'A',
  name: 'Tower Building',
  floors: 28,
  hasBasement: false,
  totalArea: 64545, // m²
  floorArea: 1000,  // m² per floor
  floorUsage: {
    28: { type: 'Office', description: 'Office' },
    27: { type: 'Empty Office', description: 'Empty Office' },
    26: { type: 'Meeting Room', description: 'Meeting Room' },
    25: { type: 'Office', description: 'Office' },
    24: { type: 'Office', description: 'Office' },
    23: { type: 'Storage', description: 'Storage' },
    22: { type: 'Empty Office', description: 'Empty Office' },
    21: { type: 'Empty Office', description: 'Empty Office' },
    20: { type: 'Office', description: 'Office' },
    19: { type: 'Office', description: 'Office' },
    18: { type: 'Office', description: 'Office' },
    17: { type: 'Office', description: 'Office' },
    16: { type: 'Office', description: 'Office' },
    15: { type: 'Office', description: 'Office' },
    14: { type: 'Office', description: 'Office' },
    12: { type: 'Office', description: 'Office' },
    11: { type: 'Office', description: 'Office' },
    10: { type: 'Office', description: 'Office' },
    9: { type: 'Multipurpose', description: 'Multipurpose' },
    8: { type: 'Office', description: 'Office' },
    7: { type: 'Office', description: 'Office' },
    6: { type: 'Office', description: 'Office' },
    5: { type: 'Meeting Room', description: 'Meeting Room' },
    4: { type: 'System Room', description: 'System Room' },
    3: { type: 'System Room', description: 'System Room' },
    2: { type: 'System Room', description: 'System Room' },
    1: { type: 'Reception Hall', description: 'Reception Hall' }
  },
  systems: {
    hvac: {
      chillers: 4,
      coolingTowers: 3,
      ahus: 28, // One per floor
      capacity: '2000TR' // Total cooling capacity
    },
    electrical: {
      transformers: 3,
      generators: 2,
      ups: 4,
      mainSwitchboards: 4
    },
    elevators: {
      passenger: 6,
      service: 2,
      zones: ['Low', 'Mid', 'High']
    },
    security: {
      accessPoints: 32,
      cameras: 64,
      controlRooms: 1
    }
  },
  energyMeters: {
    main: 4,
    subMeters: {
      hvac: 32,
      lighting: 29,
      power: 30,
      equipment: 25
    }
  },
  operatingHours: {
    weekday: {
      start: '06:00',
      end: '20:00'
    },
    weekend: {
      start: '08:00',
      end: '17:00'
    }
  },
  peakHours: {
    morning: { start: '08:00', end: '11:00' },
    afternoon: { start: '13:00', end: '16:00' }
  }
} as const;

// Building Types
export const SPACE_TYPES = {
  Office: {
    description: 'Office workspace',
    occupancyDensity: 10, // m² per person
    operatingHours: '08:00-18:00',
    systems: ['HVAC', 'Lighting', 'Power']
  },
  'Empty Office': {
    description: 'Empty office space',
    occupancyDensity: 0,
    operatingHours: '24/7',
    systems: ['Ventilation']
  },
  'Meeting Room': {
    description: 'Meeting spaces',
    occupancyDensity: 3, // m² per person
    operatingHours: '08:00-20:00',
    systems: ['HVAC', 'Lighting', 'AV']
  },
  Storage: {
    description: 'Storage areas',
    occupancyDensity: 50, // m² per person
    operatingHours: '24/7',
    systems: ['Ventilation', 'Lighting']
  },
  'System Room': {
    description: 'System equipment room',
    occupancyDensity: 100, // m² per person
    operatingHours: '24/7',
    systems: ['Precision Cooling', 'Power', 'UPS']
  },
  'Reception Hall': {
    description: 'Reception hall',
    occupancyDensity: 5, // m² per person
    operatingHours: '06:00-20:00',
    systems: ['HVAC', 'Lighting', 'Security']
  },
  'Multipurpose': {
    description: 'Multipurpose area',
    occupancyDensity: 8,
    operatingHours: '08:00-20:00',
    systems: ['HVAC', 'Lighting', 'Power', 'AV']
  },
  'Waiting Room': {
    description: 'Waiting area',
    occupancyDensity: 4,
    operatingHours: '08:00-20:00',
    systems: ['HVAC', 'Lighting']
  },
  'Sukri': {
    description: 'Sukri area',
    occupancyDensity: 6,
    operatingHours: '08:00-20:00',
    systems: ['HVAC', 'Lighting', 'Power']
  },
  'Arena/Boxing Ring': {
    description: 'Arena and boxing ring',
    occupancyDensity: 3,
    operatingHours: '08:00-22:00',
    systems: ['HVAC', 'Lighting', 'AV', 'Power']
  },
  'Parking Area': {
    description: 'Parking area',
    occupancyDensity: 30,
    operatingHours: '24/7',
    systems: ['Ventilation', 'Lighting']
  },
  'Inventory': {
    description: 'Inventory storage',
    occupancyDensity: 40,
    operatingHours: '24/7',
    systems: ['Ventilation', 'Lighting', 'Power']
  },
  'Maruay Library': {
    description: 'Maruay Library',
    occupancyDensity: 5,
    operatingHours: '08:00-20:00',
    systems: ['HVAC', 'Lighting', 'Power', 'AV']
  },
} as const;