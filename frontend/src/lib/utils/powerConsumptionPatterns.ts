/**
 * Intelligent Power Consumption Pattern Generator
 * 
 * This module generates realistic power consumption patterns based on:
 * - Time of day (business hours, peak hours, night time)
 * - Day of week (weekday vs weekend)
 * - Season (summer = higher cooling load, winter = lower)
 * - Building type (office, retail, residential)
 * - Equipment type (chiller, AHU, lighting, etc.)
 * - Special events (holidays, maintenance)
 * - Weather conditions (temperature affects HVAC load)
 * - Building occupancy patterns
 */

import { format, isWeekend, getDay, getMonth, getHours, addHours, subHours } from 'date-fns';

// Thailand public holidays (simplified - add more as needed)
const THAILAND_HOLIDAYS = [
  { month: 0, day: 1 },   // New Year's Day
  { month: 3, day: 13 },  // Songkran
  { month: 3, day: 14 },  // Songkran
  { month: 3, day: 15 },  // Songkran
  { month: 4, day: 1 },   // Labour Day
  { month: 7, day: 12 },  // Queen's Birthday
  { month: 9, day: 23 },  // Chulalongkorn Day
  { month: 11, day: 5 },  // <PERSON>'s Birthday
  { month: 11, day: 10 }, // Constitution Day
  { month: 11, day: 31 }, // New Year's Eve
];

// Equipment characteristics
interface EquipmentProfile {
  baseLoad: number;           // Base load as percentage of rated power
  peakLoad: number;           // Peak load as percentage of rated power
  nightLoad: number;          // Night load as percentage of rated power
  weekendFactor: number;      // Weekend load reduction factor
  seasonalVariation: number;  // How much season affects load (0-1)
  occupancySensitive: boolean; // Does load vary with occupancy?
  weatherSensitive: boolean;   // Does load vary with weather?
  startupTime: number;        // Hours to reach full load
  shutdownTime: number;       // Hours to reach minimum load
}

const EQUIPMENT_PROFILES: Record<string, EquipmentProfile> = {
  chiller: {
    baseLoad: 0.3,
    peakLoad: 0.95,
    nightLoad: 0.2,
    weekendFactor: 0.6,
    seasonalVariation: 0.8,
    occupancySensitive: true,
    weatherSensitive: true,
    startupTime: 1.5,
    shutdownTime: 1,
  },
  power_meter: {
    baseLoad: 0.4,
    peakLoad: 0.85,
    nightLoad: 0.25,
    weekendFactor: 0.5,
    seasonalVariation: 0.3,
    occupancySensitive: true,
    weatherSensitive: false,
    startupTime: 0,
    shutdownTime: 0,
  },
  ahu: {
    baseLoad: 0.2,
    peakLoad: 0.9,
    nightLoad: 0.1,
    weekendFactor: 0.4,
    seasonalVariation: 0.6,
    occupancySensitive: true,
    weatherSensitive: true,
    startupTime: 0.5,
    shutdownTime: 0.5,
  },
  cooling_tower: {
    baseLoad: 0.3,
    peakLoad: 0.95,
    nightLoad: 0.2,
    weekendFactor: 0.6,
    seasonalVariation: 0.8,
    occupancySensitive: false,
    weatherSensitive: true,
    startupTime: 0.25,
    shutdownTime: 0.25,
  },
  pump: {
    baseLoad: 0.5,
    peakLoad: 0.9,
    nightLoad: 0.3,
    weekendFactor: 0.6,
    seasonalVariation: 0.4,
    occupancySensitive: false,
    weatherSensitive: false,
    startupTime: 0.1,
    shutdownTime: 0.1,
  },
  lighting: {
    baseLoad: 0.1,
    peakLoad: 1.0,
    nightLoad: 0.05,
    weekendFactor: 0.2,
    seasonalVariation: 0.1,
    occupancySensitive: true,
    weatherSensitive: false,
    startupTime: 0,
    shutdownTime: 0,
  },
  elevator: {
    baseLoad: 0.1,
    peakLoad: 0.7,
    nightLoad: 0.05,
    weekendFactor: 0.3,
    seasonalVariation: 0,
    occupancySensitive: true,
    weatherSensitive: false,
    startupTime: 0,
    shutdownTime: 0,
  },
  data_center: {
    baseLoad: 0.7,
    peakLoad: 0.9,
    nightLoad: 0.7,
    weekendFactor: 0.95,
    seasonalVariation: 0.2,
    occupancySensitive: false,
    weatherSensitive: true,
    startupTime: 0,
    shutdownTime: 0,
  },
  ev_charger: {
    baseLoad: 0.1,
    peakLoad: 0.8,
    nightLoad: 0.3,
    weekendFactor: 1.2, // Higher on weekends
    seasonalVariation: 0,
    occupancySensitive: false,
    weatherSensitive: false,
    startupTime: 0,
    shutdownTime: 0,
  },
};

// Building occupancy patterns
interface OccupancyPattern {
  weekdayStart: number;    // Hour when people start arriving
  weekdayPeak: number;     // Hour of peak occupancy
  weekdayEnd: number;      // Hour when people leave
  weekendOccupancy: number; // Weekend occupancy factor (0-1)
  lunchDip: boolean;       // Does occupancy dip during lunch?
}

const BUILDING_OCCUPANCY: Record<string, OccupancyPattern> = {
  office: {
    weekdayStart: 7,
    weekdayPeak: 10,
    weekdayEnd: 18,
    weekendOccupancy: 0.1,
    lunchDip: true,
  },
  retail: {
    weekdayStart: 9,
    weekdayPeak: 14,
    weekdayEnd: 21,
    weekendOccupancy: 1.2, // Higher on weekends
    lunchDip: false,
  },
  mixed: {
    weekdayStart: 7,
    weekdayPeak: 11,
    weekdayEnd: 20,
    weekendOccupancy: 0.6,
    lunchDip: true,
  },
};

// Weather simulation for Bangkok
export function getSimulatedTemperature(date: Date): number {
  const hour = getHours(date);
  const month = getMonth(date);
  
  // Base temperature by month (Bangkok climate)
  const monthlyAvgTemp = [26, 28, 29, 30, 30, 29, 28, 28, 28, 27, 26, 25];
  const baseTemp = monthlyAvgTemp[month];
  
  // Daily temperature variation
  const hourlyVariation = [
    -3, -3.5, -4, -4, -3.5, -3, -2, -1, 0, 1, 2, 3,
    4, 4.5, 5, 5, 4.5, 4, 3, 2, 1, 0, -1, -2
  ];
  
  // Add some randomness
  const randomVariation = (Math.random() - 0.5) * 2;
  
  return baseTemp + hourlyVariation[hour] + randomVariation;
}

// Check if date is a holiday
export function isThailandHoliday(date: Date): boolean {
  const month = getMonth(date);
  const day = date.getDate();
  
  return THAILAND_HOLIDAYS.some(h => h.month === month && h.day === day);
}

// Calculate building occupancy
export function getBuildingOccupancy(
  date: Date,
  buildingType: 'office' | 'retail' | 'mixed' = 'office'
): number {
  const hour = getHours(date);
  const dayOfWeek = getDay(date);
  const pattern = BUILDING_OCCUPANCY[buildingType];
  
  // Check if holiday
  if (isThailandHoliday(date)) {
    return 0.1; // Minimal occupancy on holidays
  }
  
  // Weekend occupancy
  if (isWeekend(date)) {
    return pattern.weekendOccupancy * getTimeBasedOccupancy(hour, pattern, false);
  }
  
  // Weekday occupancy
  return getTimeBasedOccupancy(hour, pattern, true);
}

function getTimeBasedOccupancy(
  hour: number,
  pattern: OccupancyPattern,
  isWeekday: boolean
): number {
  if (!isWeekday) {
    // Simple weekend pattern
    if (hour >= 9 && hour <= 18) {
      return 0.7;
    }
    return 0.1;
  }
  
  // Before opening
  if (hour < pattern.weekdayStart) {
    return 0.05;
  }
  
  // Opening ramp-up
  if (hour < pattern.weekdayPeak) {
    const rampDuration = pattern.weekdayPeak - pattern.weekdayStart;
    const progress = (hour - pattern.weekdayStart) / rampDuration;
    return 0.05 + (0.95 * progress);
  }
  
  // Peak hours
  if (hour >= pattern.weekdayPeak && hour < 12) {
    return 1.0;
  }
  
  // Lunch dip
  if (pattern.lunchDip && hour >= 12 && hour < 13) {
    return 0.7;
  }
  
  // Afternoon peak
  if (hour >= 13 && hour < pattern.weekdayEnd) {
    return 0.9;
  }
  
  // Closing ramp-down
  if (hour >= pattern.weekdayEnd && hour < pattern.weekdayEnd + 2) {
    const rampDuration = 2;
    const progress = (hour - pattern.weekdayEnd) / rampDuration;
    return 0.9 * (1 - progress) + 0.05;
  }
  
  // After hours
  return 0.05;
}

// Main power calculation function
export function calculatePowerConsumption(
  date: Date,
  ratedPower: number,
  equipmentType: string,
  buildingType: 'office' | 'retail' | 'mixed' = 'office'
): number {
  const profile = EQUIPMENT_PROFILES[equipmentType] || EQUIPMENT_PROFILES.power_meter;
  const hour = getHours(date);
  const month = getMonth(date);
  
  // Base load calculation
  let load = profile.baseLoad;
  
  // Time of day factor
  const occupancy = getBuildingOccupancy(date, buildingType);
  if (profile.occupancySensitive) {
    load = profile.nightLoad + (profile.peakLoad - profile.nightLoad) * occupancy;
  } else {
    // Non-occupancy sensitive equipment still has time patterns
    if (hour >= 6 && hour <= 18) {
      load = profile.peakLoad;
    } else if (hour >= 19 && hour <= 22) {
      load = profile.baseLoad;
    } else {
      load = profile.nightLoad;
    }
  }
  
  // Weekend factor
  if (isWeekend(date) && profile.weekendFactor < 1) {
    load *= profile.weekendFactor;
  }
  
  // Seasonal variation
  if (profile.seasonalVariation > 0) {
    const temperature = getSimulatedTemperature(date);
    const coolingLoad = Math.max(0, (temperature - 25) / 10); // More cooling needed above 25°C
    
    if (profile.weatherSensitive) {
      load += coolingLoad * profile.seasonalVariation * 0.3;
    }
  }
  
  // Add realistic random variations
  const randomFactor = 1 + (Math.random() - 0.5) * 0.1; // ±5% random variation
  load *= randomFactor;
  
  // Ensure load stays within bounds
  load = Math.max(profile.nightLoad, Math.min(profile.peakLoad, load));
  
  return Math.round(ratedPower * load * 10) / 10; // Round to 1 decimal place
}

// Generate historical power data
export function generateHistoricalPowerData(
  startDate: Date,
  endDate: Date,
  intervalMinutes: number,
  ratedPower: number,
  equipmentType: string,
  buildingType: 'office' | 'retail' | 'mixed' = 'office'
): Array<{ timestamp: Date; power: number }> {
  const data = [];
  const current = new Date(startDate);
  
  while (current <= endDate) {
    const power = calculatePowerConsumption(current, ratedPower, equipmentType, buildingType);
    data.push({
      timestamp: new Date(current),
      power,
    });
    
    current.setMinutes(current.getMinutes() + intervalMinutes);
  }
  
  return data;
}

// Generate power data for meter details chart (15-minute intervals)
export function generateRealisticPowerTimeSeries(
  currentPower: number,
  equipmentType: string,
  hours: number = 24
): Array<{ time: string; demand: number }> {
  const now = new Date();
  const data = [];
  
  // Determine rated power from current power
  const profile = EQUIPMENT_PROFILES[equipmentType] || EQUIPMENT_PROFILES.power_meter;
  const currentHour = getHours(now);
  const currentOccupancy = getBuildingOccupancy(now);
  
  // Estimate rated power based on current conditions
  let estimatedLoadFactor = profile.baseLoad;
  if (profile.occupancySensitive) {
    estimatedLoadFactor = profile.nightLoad + (profile.peakLoad - profile.nightLoad) * currentOccupancy;
  } else if (currentHour >= 6 && currentHour <= 18) {
    estimatedLoadFactor = profile.peakLoad;
  }
  
  const estimatedRatedPower = currentPower / estimatedLoadFactor;
  
  // Generate historical data at 15-minute intervals
  const intervalsPerHour = 4; // 15-minute intervals
  const totalIntervals = hours * intervalsPerHour;
  
  for (let i = totalIntervals - 1; i >= 0; i--) {
    const time = new Date(now);
    time.setMinutes(time.getMinutes() - (i * 15)); // Go back in 15-minute increments
    
    const power = calculatePowerConsumption(time, estimatedRatedPower, equipmentType);
    
    // Add slight variation between 15-minute intervals within the same hour
    const intervalVariation = (Math.random() - 0.5) * 0.02; // ±1% variation
    const adjustedPower = power * (1 + intervalVariation);
    
    data.push({
      time: format(time, 'HH:mm'),
      demand: Math.round(adjustedPower * 10) / 10,
    });
  }
  
  // Ensure the last point matches the current power exactly
  if (data.length > 0) {
    // Find the closest 15-minute mark to now
    const nowMinutes = now.getMinutes();
    const roundedMinutes = Math.round(nowMinutes / 15) * 15;
    const lastTime = new Date(now);
    lastTime.setMinutes(roundedMinutes, 0, 0);
    
    data[data.length - 1] = {
      time: format(lastTime, 'HH:mm'),
      demand: currentPower
    };
  }
  
  return data;
}

// Generate daily energy consumption data
export function generateDailyEnergyData(
  monthDate: Date,
  ratedPower: number,
  equipmentType: string,
  buildingType: 'office' | 'retail' | 'mixed' = 'office'
): Array<{ day: number; value: number }> {
  const year = monthDate.getFullYear();
  const month = monthDate.getMonth();
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  const data = [];
  
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day);
    let dailyEnergy = 0;
    
    // Calculate energy consumption for each hour of the day
    for (let hour = 0; hour < 24; hour++) {
      const hourDate = new Date(year, month, day, hour);
      const power = calculatePowerConsumption(hourDate, ratedPower, equipmentType, buildingType);
      dailyEnergy += power; // kWh (power for 1 hour)
    }
    
    data.push({
      day,
      value: Math.round(dailyEnergy * 10) / 10, // Round to 1 decimal place
    });
  }
  
  return data;
}

// Special event adjustments
export function applySpecialEvents(
  power: number,
  date: Date,
  events: Array<{ start: Date; end: Date; factor: number }>
): number {
  for (const event of events) {
    if (date >= event.start && date <= event.end) {
      return power * event.factor;
    }
  }
  return power;
}

// Maintenance window adjustments
export function applyMaintenanceWindow(
  power: number,
  date: Date,
  equipmentType: string
): number {
  const dayOfWeek = getDay(date);
  const hour = getHours(date);
  
  // Equipment-specific maintenance windows
  if (equipmentType === 'chiller' && dayOfWeek === 0 && hour >= 2 && hour <= 6) {
    // Chiller maintenance on Sunday early morning
    return power * 0.5; // Reduced capacity during maintenance
  }
  
  if (equipmentType === 'ahu' && hour === 3) {
    // AHU filter cleaning at 3 AM daily
    return 0;
  }
  
  return power;
}

// Export utility function for creating realistic variations
export function addRealisticNoise(value: number, noiseLevel: number = 0.02): number {
  // Add Gaussian noise
  const u1 = Math.random();
  const u2 = Math.random();
  const gaussian = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
  
  return value * (1 + gaussian * noiseLevel);
}