// import { SYSTEM_TYPES } from '../config/energy-systems';

// Updated color palette for system breakdown charts with more vibrant colors
export const SYSTEM_BREAKDOWN_COLORS = {
  chillerPlant: '#0284C7',         // Bright blue for cooling systems
  airSide: '#10B981',            // Emerald green for air systems
  data_center_others: '#F59E0B',  // Amber for data center & others (replaces dataCenter and dataCenterOthers)
  light_power: '#8B5CF6',         // Purple for light & power (replaces equipment)
  evCharger: '#EC4899',          // Pink for EV charging
  escalator_elevator: '#6B7280',  // Gray for escalator/elevator (replaces escalator and others)

  // By name keys can be removed if IDs are consistently used and match display names appropriately elsewhere
  // If names are different from IDs and still needed for lookup, they can be kept but should map to the standard ID colors.
  // For now, simplifying by removing them as direct lookups.
} as const;

/**
 * Gets the correct color for a system by id or name
 */
export function getSystemColor(systemIdOrName: keyof typeof SYSTEM_BREAKDOWN_COLORS): string {
  return SYSTEM_BREAKDOWN_COLORS[systemIdOrName] || '#6B7280'; // Default color
}