export function formatNumber(value: number, options: { decimals?: number; unit?: string } = {}) {
  const { decimals = 1, unit = '' } = options;
  return `${value.toLocaleString(undefined, { 
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals 
  })}${unit ? ` ${unit}` : ''}`;
}

export function formatDate(date: Date | string): string {
  if (typeof date === 'string') {
    date = new Date(date);
  }
  const dayOfWeek = date.toLocaleString('en-US', { weekday: 'short' });
  const day = date.getDate().toString().padStart(2, '0');
  const month = date.toLocaleString('en-US', { month: 'short' });
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  
  return `${dayOfWeek} ${day} ${month} ${year} ${hours}:${minutes}:${seconds}`;
}

export function formatPercentage(value: number, decimals = 1): string {
  return `${value.toFixed(decimals)}%`;
}