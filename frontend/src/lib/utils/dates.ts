import { ViewType as AnalyticsViewType } from '../../types/analytics';
import { ViewType as ApiViewType } from '../../types';

/**
 * Map internal ViewType to API ViewType
 */
export const mapViewToApiView = (view: AnalyticsViewType): ApiViewType => {
  switch (view) {
    case 'day': return 'daily';
    case 'week': return 'weekly';
    case 'month': return 'monthly';
    case 'year': return 'yearly';
    case 'multi-year': return 'multi-year';
    default: return 'daily';
  }
};

/**
 * Formats a date object into a string suitable for display based on the view type.
 */
export function formatDisplayDate(date: Date | null | undefined, view: AnalyticsViewType): string {
  // Handle invalid date inputs gracefully
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    console.warn('[formatDisplayDate] Received invalid date:', date);
    return ""; // Return empty string for invalid dates
  }

  const options: Intl.DateTimeFormatOptions = {};
  
  switch (view) {
    case 'day':
      options.year = 'numeric';
      options.month = 'short';
      options.day = 'numeric';
      break;
      
    case 'week':
      const weekStart = new Date(date);
      const weekEnd = new Date(date);
      
      const dayOfWeek = date.getDay();
      weekStart.setDate(date.getDate() - dayOfWeek);
      weekEnd.setDate(weekStart.getDate() + 6);
      
      if (weekStart.getMonth() !== weekEnd.getMonth() || weekStart.getFullYear() !== weekEnd.getFullYear()) {
        return `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
      }
      
      return `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.getDate()}, ${weekEnd.getFullYear()}`;
      
    case 'month':
      options.year = 'numeric';
      options.month = 'long';
      break;
      
    case 'year':
    case 'multi-year':
      options.year = 'numeric';
      break;
  }
  
  return date.toLocaleDateString('en-US', options);
}

/**
 * Get the start and end dates for a specific view.
 */
export function getDateRange(date: Date, view: AnalyticsViewType): { start: Date; end: Date } {
  const start = new Date(date);
  const end = new Date(date);
  
  switch (view) {
    case 'day':
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      break;
      
    case 'week':
      const day = date.getDay();
      start.setDate(date.getDate() - day);
      start.setHours(0, 0, 0, 0);
      
      end.setDate(start.getDate() + 6);
      end.setHours(23, 59, 59, 999);
      break;
      
    case 'month':
      start.setDate(1);
      start.setHours(0, 0, 0, 0);
      
      end.setMonth(date.getMonth() + 1);
      end.setDate(0);
      end.setHours(23, 59, 59, 999);
      break;
      
    case 'year':
      start.setMonth(0, 1);
      start.setHours(0, 0, 0, 0);
      
      end.setFullYear(date.getFullYear() + 1);
      end.setMonth(0, 0);
      end.setHours(23, 59, 59, 999);
      break;
      
    case 'multi-year':
      // For multi-year view, start from 2 years before the selected year
      start.setFullYear(date.getFullYear() - 2);
      start.setMonth(0, 1);
      start.setHours(0, 0, 0, 0);
      
      // End date is the end of the current year
      end.setMonth(11, 31);
      end.setHours(23, 59, 59, 999);
      break;
  }
  
  return { start, end };
}

/**
 * Get a descriptive label for the previous time period.
 */
export function getPreviousPeriodLabel(view: AnalyticsViewType): string {
  switch (view) {
    case 'day': return 'Yesterday';
    case 'week': return 'Last Week';
    case 'month': return 'Last Month';
    case 'year': return 'Last Year';
    case 'multi-year': return 'Previous 3 Years';
    default: return 'Previous Period';
  }
}

/**
 * Get a previous time period based on view.
 */
export function getPreviousPeriod(date: Date, view: AnalyticsViewType): Date {
  const result = new Date(date);
  
  switch (view) {
    case 'day':
      result.setDate(date.getDate() - 1);
      break;
      
    case 'week':
      result.setDate(date.getDate() - 7);
      break;
      
    case 'month':
      result.setMonth(date.getMonth() - 1);
      break;
      
    case 'year':
      result.setFullYear(date.getFullYear() - 1);
      break;
      
    case 'multi-year':
      result.setFullYear(date.getFullYear() - 3);
      break;
  }
  
  return result;
}

/**
 * Get a next time period based on view.
 */
export function getNextPeriod(date: Date, view: AnalyticsViewType): Date {
  const result = new Date(date);
  const today = new Date();
  
  // First, calculate what the next period would be
  switch (view) {
    case 'day':
      result.setDate(date.getDate() + 1);
      break;
      
    case 'week':
      result.setDate(date.getDate() + 7);
      break;
      
    case 'month':
      result.setMonth(date.getMonth() + 1);
      break;
      
    case 'year':
      result.setFullYear(date.getFullYear() + 1);
      break;
      
    case 'multi-year':
      result.setFullYear(date.getFullYear() + 3);
      break;
  }
  
  // Don't allow navigating to future dates
  if (result > today) {
    return date; // Return the original date if next would be in the future
  }
  
  return result;
}

/**
 * Calculate the date for a comparison period based on a current date.
 */
export function getComparisonDate(currentDate: Date, period: 'day' | 'week' | 'month' | 'year'): Date {
  const result = new Date(currentDate);
  
  switch (period) {
    case 'day':
      result.setDate(currentDate.getDate() - 1);
      break;
      
    case 'week':
      result.setDate(currentDate.getDate() - 7);
      break;
      
    case 'month':
      result.setMonth(currentDate.getMonth() - 1);
      break;
      
    case 'year':
      result.setFullYear(currentDate.getFullYear() - 1);
      break;
  }
  
  return result;
}
