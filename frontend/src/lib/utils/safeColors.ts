import { COLOR_COMBINATIONS, TEXT_COLORS, isContrastSafe } from '@/lib/constants/colors';
import { cn } from '@/lib/utils';

/**
 * Type-safe color combination builder
 * Ensures text and background colors have proper contrast
 */

type ColorCombination = keyof typeof COLOR_COMBINATIONS;
type TextColor = keyof typeof TEXT_COLORS;

interface SafeColorOptions {
  base?: ColorCombination;
  text?: TextColor;
  background?: string;
  hover?: string;
  additional?: string;
}

/**
 * Creates a safe color combination className
 * @example
 * safeColors({ base: 'primaryButton' })
 * // Returns: "bg-blue-600 text-white hover:bg-blue-700"
 * 
 * safeColors({ background: 'bg-gray-100', text: 'onLight' })
 * // Returns: "bg-gray-100 text-gray-700"
 */
export function safeColors(options: SafeColorOptions): string {
  const classes: string[] = [];

  if (options.base) {
    classes.push(COLOR_COMBINATIONS[options.base]);
  } else {
    if (options.background) {
      classes.push(options.background);
    }
    if (options.text) {
      classes.push(TEXT_COLORS[options.text]);
    }
  }

  if (options.hover) {
    classes.push(options.hover);
  }

  if (options.additional) {
    classes.push(options.additional);
  }

  return cn(...classes);
}

/**
 * Validates a className string for contrast issues
 * Throws an error in development if poor contrast is detected
 */
export function validateContrast(className: string): string {
  if (process.env.NODE_ENV === 'development') {
    const classes = className.split(' ');
    const textClass = classes.find(c => c.startsWith('text-'));
    const bgClass = classes.find(c => c.startsWith('bg-'));

    if (textClass && bgClass && !isContrastSafe(textClass, bgClass)) {
      console.error(
        `⚠️ Poor contrast detected: ${textClass} on ${bgClass}\n` +
        `Consider using safeColors() or COLOR_COMBINATIONS instead.`
      );
    }
  }

  return className;
}

/**
 * HOC to validate contrast in components
 */
export function withContrastValidation<P extends { className?: string }>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  return (props: P) => {
    if (props.className) {
      validateContrast(props.className);
    }
    return <Component {...props} />;
  };
}