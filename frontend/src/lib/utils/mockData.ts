import { AnalyticsData, HourlyConsumptionPoint, SystemBreakdownItem, ViewType, PeakDemand } from '../../types/analytics';
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, eachHourOfInterval, eachDayOfInterval, eachMonthOfInterval, subYears } from 'date-fns';
import { SYSTEM_BREAKDOWN_COLORS } from './chart-colors'; // Correct import name

// --- Helper Functions ---

const generateRandomValue = (base: number, variance: number): number => {
  return base + (Math.random() - 0.5) * variance;
};

// Helper function to get seasonal factor based on month index
const getSeasonalFactor = (monthIndex: number): number => {
  // Simulate seasonal variation (higher in summer/winter)
  if (monthIndex >= 5 && monthIndex <= 7) return 1.2; // Summer peak
  if (monthIndex === 11 || monthIndex <= 1) return 1.1; // Winter bump
  return 1.0; // Normal consumption for spring/fall
};

const generateHourlyPoints = (date: Date, baseConsumption: number = 5, variance: number = 3): HourlyConsumptionPoint[] => {
  const start = startOfDay(date);
  const end = endOfDay(date);
  const hours = eachHourOfInterval({ start, end });

  return hours.map((hour: Date) => {
    const hourOfDay = hour.getHours();
    // Simulate lower consumption at night, higher during the day
    const consumptionMultiplier = (hourOfDay >= 6 && hourOfDay <= 20) ? 1 : 0.4;
    const actual = generateRandomValue(baseConsumption * consumptionMultiplier, variance * consumptionMultiplier);
    const predicted = generateRandomValue(actual, variance * 0.5); // Predicted is close to actual
    return {
      time: format(hour, 'HH:mm'), // e.g., "14:00"
      actual: Math.max(0, parseFloat(actual.toFixed(1))), // Ensure non-negative & format
      predicted: Math.max(0, parseFloat(predicted.toFixed(1))),
    };
  });
};

const generateDailyPoints = (date: Date, view: 'week' | 'month', baseConsumption: number = 120, variance: number = 40): HourlyConsumptionPoint[] => {
  let start, end;
  if (view === 'week') {
    start = startOfWeek(date, { weekStartsOn: 1 }); // Monday start
    end = endOfWeek(date, { weekStartsOn: 1 }); // Need end for week interval
  } else { // month
    start = startOfMonth(date);
    end = endOfMonth(date);
  }
  // Ensure start and end are valid Dates before passing to eachDayOfInterval
  if (!start || !end) return [];

  const days = eachDayOfInterval({ start, end });

  return days.map((day: Date) => {
    const isWeekend = day.getDay() === 0 || day.getDay() === 6;
    const consumptionMultiplier = isWeekend ? 0.8 : 1; // Slightly lower on weekends
    const actual = generateRandomValue(baseConsumption * consumptionMultiplier, variance);
    const predicted = generateRandomValue(actual, variance * 0.5);

    // For daily/monthly points, we store aggregate actual/predicted, time is the day/month label
    return {
      time: view === 'week' ? format(day, 'EEE') : format(day, 'dd'), // "Mon" or "01"
      actual: Math.max(0, parseFloat(actual.toFixed(1))),
      predicted: Math.max(0, parseFloat(predicted.toFixed(1))),
    };
  });
};

const generateMonthlyPoints = (date: Date, baseConsumption: number = 3500, variance: number = 1000): HourlyConsumptionPoint[] => {
  const start = startOfYear(date);
  const end = endOfYear(date);
  const months = eachMonthOfInterval({ start, end });

  return months.map((month: Date) => {
    const seasonalFactor = getSeasonalFactor(month.getMonth());
    const actual = generateRandomValue(baseConsumption * seasonalFactor, variance);
    const predicted = generateRandomValue(actual, variance * 0.4);

    return {
      time: format(month, 'MMM'), // e.g., "Jan", "Feb"
      actual: Math.max(0, parseFloat(actual.toFixed(1))),
      predicted: Math.max(0, parseFloat(predicted.toFixed(1))),
    };
  });
};

const generateYearlyPoints = (date: Date, baseConsumption: number = 42000, variance: number = 5000): HourlyConsumptionPoint[] => {
  const result: HourlyConsumptionPoint[] = [];

  // Generate data for current year and previous 4 years (total 5 years)
  for (let i = 4; i >= 0; i--) {
    const yearDate = subYears(date, i);
    const year = yearDate.getFullYear();

    // Add year-to-year growth trend (consumption increases by ~5% each year)
    const yearlyGrowthFactor = 0.95 + (0.05 * (4 - i));

    // Add some randomness to annual consumption
    const yearConsumption = generateRandomValue(baseConsumption * yearlyGrowthFactor, variance);

    result.push({
      time: year.toString(),
      actual: Math.max(0, parseFloat(yearConsumption.toFixed(1))),
      predicted: Math.max(0, parseFloat((yearConsumption * 1.02).toFixed(1))), // Slightly higher prediction
    });
  }

  return result;
};

// --- Main Mock Data Generators ---

export const generateMockAnalyticsData = (view: ViewType, date: Date, building?: string): AnalyticsData => {
  console.log(`Generating mock data for view: ${view}, date: ${date.toISOString().split('T')[0]}, building: ${building || 'default'}`);

  let hourlyConsumption: HourlyConsumptionPoint[] = [];
  let totalConsumption = 0;
  let peakDemand: PeakDemand = { value: 0, time: '' };
  let averageLoad = 0;

  // Apply building-specific multipliers to make each building's data unique
  let buildingMultiplier = 1.0;
  if (building === 'A') buildingMultiplier = 1.0;
  else if (building === 'B') buildingMultiplier = 1.2; // Building B uses 20% more energy
  else if (building === 'C') buildingMultiplier = 0.8; // Building C uses 20% less energy

  try {
    // Generate consumption data based on view
    switch (view) {
      case 'day':
        hourlyConsumption = generateHourlyPoints(date, 5 * buildingMultiplier);
        break;

      case 'week':
        hourlyConsumption = generateDailyPoints(date, 'week', 120 * buildingMultiplier);
        break;

      case 'month':
        hourlyConsumption = generateDailyPoints(date, 'month', 120 * buildingMultiplier);
        break;

      case 'year':
        hourlyConsumption = generateMonthlyPoints(date, 3500 * buildingMultiplier);
        break;

      case 'multi-year':
        hourlyConsumption = generateYearlyPoints(date, 42000 * buildingMultiplier);
        break;
    }

    // Calculate derived metrics
    if (hourlyConsumption.length > 0) {
      // Sum all consumption values
      totalConsumption = hourlyConsumption.reduce((sum, point) => sum + point.actual, 0);

      // Find peak demand point (assuming kW = kWh × 4 for simplification)
      const peakPoint = [...hourlyConsumption].sort((a, b) => b.actual - a.actual)[0];
      peakDemand = {
        value: parseFloat((peakPoint.actual * 4).toFixed(1)), // Convert kWh to peak kW (simplified)
        time: peakPoint.time
      };

      // Calculate average load
      averageLoad = parseFloat((totalConsumption / hourlyConsumption.length).toFixed(1));
    }

    // Round totalConsumption
    totalConsumption = parseFloat(totalConsumption.toFixed(1));

  } catch (error) {
    console.error('Error generating mock data:', error);
    // Return empty data on error
    return {
      hourlyConsumption: [],
      totalConsumption: 0,
      peakDemand: { value: 0, time: '' },
      averageLoad: 0
    };
  }

  return {
    hourlyConsumption,
    totalConsumption,
    peakDemand,
    averageLoad
  };
};

/**
 * Combines analytics data from multiple buildings into a single dataset
 * @param dataArray Array of AnalyticsData objects to combine
 * @returns Combined AnalyticsData object
 */
export const combineAnalyticsData = (dataArray: AnalyticsData[]): AnalyticsData => {
  if (!dataArray.length) {
    // Return empty analytics data structure if no data is provided
    return {
      hourlyConsumption: [],
      totalConsumption: 0,
      peakDemand: { value: 0, time: '' },
      averageLoad: 0,
      systemBreakdown: []
    };
  }
  
  if (dataArray.length === 1) return dataArray[0];

  // Start with a copy of the first dataset's structure
  const result: AnalyticsData = JSON.parse(JSON.stringify(dataArray[0]));
  
  // Create a map for hourly consumption points
  const timeMap = new Map<string, HourlyConsumptionPoint>();
  
  // Initialize with first dataset's points
  result.hourlyConsumption.forEach(point => {
    timeMap.set(point.time, { ...point });
  });
  
  // Add data from other datasets
  for (let i = 1; i < dataArray.length; i++) {
    const data = dataArray[i];
    
    // Sum up hourly consumption
    data.hourlyConsumption.forEach(point => {
      const existingPoint = timeMap.get(point.time);
      if (existingPoint) {
        existingPoint.actual += point.actual;
        if (existingPoint.predicted !== undefined && point.predicted !== undefined) {
          existingPoint.predicted += point.predicted;
        }
        if (point.accumulatedValue !== undefined && existingPoint.accumulatedValue !== undefined) {
          existingPoint.accumulatedValue += point.accumulatedValue;
        }
      } else {
        timeMap.set(point.time, { ...point });
      }
    });
    
    // Sum up total consumption
    result.totalConsumption += data.totalConsumption;
    
    // Take the highest peak demand
    if (data.peakDemand.value > result.peakDemand.value) {
      result.peakDemand = { ...data.peakDemand };
    }
    
    // Sum up average load (will divide by count later)
    result.averageLoad += data.averageLoad;
    
    // Sum up system breakdown items
    if (data.systemBreakdown && result.systemBreakdown) {
      data.systemBreakdown.forEach((item, index) => {
        if (result.systemBreakdown && result.systemBreakdown[index]) {
          result.systemBreakdown[index].value += item.value;
        }
      });
    }
  }
  
  // Calculate the average for averageLoad
  result.averageLoad /= dataArray.length;
  
  // Convert the map back to an array and sort by time
  result.hourlyConsumption = Array.from(timeMap.values())
    .sort((a, b) => a.time.localeCompare(b.time));
  
  return result;
};

export const generateMockSystemBreakdown = (): SystemBreakdownItem[] => {
  // Simulate breakdown for different systems
  const systems = ['HVAC', 'Lighting', 'Plug Loads', 'Process', 'Other'];
  // Use the imported colors, access by name or define a simple array mapping
  const systemColors = [
    SYSTEM_BREAKDOWN_COLORS['Air Side'], // Assuming HVAC maps to Air Side for color
    SYSTEM_BREAKDOWN_COLORS['Light & Power'], // Assuming Lighting maps to Light & Power
    SYSTEM_BREAKDOWN_COLORS['Data Center'], // Assuming Plug Loads maps to Data Center (example)
    SYSTEM_BREAKDOWN_COLORS['Chiller Plant'], // Assuming Process maps to Chiller Plant (example)
    SYSTEM_BREAKDOWN_COLORS['Others']
  ];
  let total = 0;
  const breakdown = systems.map((name, index) => {
    const value = Math.random() * 30 + 5; // Random value between 5 and 35
    total += value;
    return {
      id: name.toLowerCase().replace(' ', '-'), // e.g., plug-loads
      name,
      value: parseFloat(value.toFixed(1)),
      color: systemColors[index % systemColors.length]
    };
  });

  // Calculate percentages
  return breakdown.map(item => ({
    ...item,
    percentage: total > 0 ? parseFloat(((item.value / total) * 100).toFixed(1)) : 0
  }));
};
