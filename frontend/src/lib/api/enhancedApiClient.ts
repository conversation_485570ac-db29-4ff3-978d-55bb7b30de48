// Enhanced API Client with Mock Data Support
import { mockDataService } from '../services/mockDataService';

interface ApiConfig {
  baseURL?: string;
  headers?: Record<string, string>;
  timeout?: number;
}

interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, any>;
}

class EnhancedApiClient {
  private config: ApiConfig;
  private useMockData: boolean;

  constructor(config: ApiConfig = {}) {
    this.config = {
      baseURL: import.meta.env.VITE_DJANGO_API_URL || 'http://0.0.0.0:8001/api',
      headers: {
        'Content-Type': 'application/json',
        ...config.headers,
      },
      timeout: config.timeout || 30000,
    };
    
    // Check if we should use mock data
    // First check for temporary override from floating toggle
    const temporaryOverride = sessionStorage.getItem('floatingMockDataOverride');
    if (temporaryOverride !== null) {
      this.useMockData = temporaryOverride === 'true';
    } else {
      // Then check the master setting
      const savedPreference = localStorage.getItem('useMockData');
      if (savedPreference !== null) {
        this.useMockData = savedPreference === 'true';
      } else if (import.meta.env.VITE_USE_MOCK_DATA !== undefined) {
        this.useMockData = import.meta.env.VITE_USE_MOCK_DATA === 'true';
      } else {
        // Default to NOT using mock data
        this.useMockData = false;
        localStorage.setItem('useMockData', 'false');
      }
    }
    
    console.log('Enhanced API Client initialized:', {
      baseURL: this.config.baseURL,
      useMockData: this.useMockData,
      temporaryOverride,
      masterSetting: localStorage.getItem('useMockData'),
      envMockData: import.meta.env.VITE_USE_MOCK_DATA
    });
  }

  private buildURL(endpoint: string, params?: Record<string, any>): string {
    // Ensure base URL ends with slash and endpoint doesn't start with slash for proper joining
    const baseURL = this.config.baseURL!.endsWith('/') ? this.config.baseURL : this.config.baseURL + '/';
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    const url = new URL(cleanEndpoint, baseURL);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }
    
    return url.toString();
  }

  async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    // If using mock data, handle it through the mock service
    if (this.useMockData) {
      return this.handleMockRequest<T>(endpoint, options);
    }

    const { method = 'GET', headers = {}, body, params } = options;
    const url = this.buildURL(endpoint, params);

    try {
      const response = await fetch(url, {
        method,
        headers: {
          ...this.config.headers,
          ...headers,
          'Authorization': `Bearer ${import.meta.env.VITE_DJANGO_API_ACESS_TOKEN}`,
        },
        body: body ? JSON.stringify(body) : undefined,
        signal: AbortSignal.timeout(this.config.timeout!),
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // If the API returns data wrapped in a response object, extract it
      // Exception: Don't extract for AFDD endpoints that need the full response
      const isAFDDEndpoint = endpoint.includes('/afdd/');
      if (data && data.data !== undefined && !isAFDDEndpoint) {
        return data.data;
      }
      
      return data;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Network Error: ${error.message}`);
      }
      throw error;
    }
  }

  private async handleMockRequest<T>(endpoint: string, options: RequestOptions): Promise<T> {
    // For PATCH requests, just return success
    if (options.method === 'PATCH') {
      console.log('Mock PATCH request:', endpoint, options.body);
      return Promise.resolve({} as T);
    }

    // Extract the main resource from the endpoint
    const pathParts = endpoint.split('/').filter(Boolean);
    const resource = pathParts[0];
    const id = pathParts[1];

    // Map endpoints to mock data methods
    const mockHandlers: Record<string, () => Promise<any>> = {
      'dashboard': () => mockDataService.getDashboardData(),
      'buildings': () => id ? mockDataService.getBuildingData(id) : mockDataService.getBuildingData('A'),
      'meters': () => mockDataService.getMeterData(id),
      'analytics': () => mockDataService.getAnalyticsData(options.params),
      'alarms': () => mockDataService.getAlarms(options.params),
      'notifications': () => mockDataService.getNotifications(),
      'site': () => mockDataService.getSiteInfo(),
      'performance': () => mockDataService.getPerformanceData(
        options.params?.buildingId || 'building-a',
        options.params?.timeRange || '1d'
      ),
      'compare': () => mockDataService.getCompareData(
        options.params?.systems || [],
        options.params?.metric || 'power',
        options.params?.dateRange || { startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), endDate: new Date() }
      ),
      'historical': () => mockDataService.getHistoricalData(
        options.params?.meterId || 'main-meter',
        options.params?.metric || 'power',
        options.params?.period || '7d'
      ),
      'device_relations': async () => {
        // Handle device relations endpoints
        if (pathParts[1] === 'child') {
          if (pathParts[2]) {
            return mockDataService.getDeviceRelationsById(pathParts[2]);
          } else if (options.params?.model) {
            return mockDataService.getDeviceRelationsByModel(options.params.model);
          }
        }
        return Promise.resolve([]);
      },
      'devices': () => {
        if (id) {
          return mockDataService.getDeviceById(id, options.params?.expand?.split(','));
        } else {
          return mockDataService.getAllDevices(options.params?.expand?.split(','));
        }
      },
      'zones': () => mockDataService.getZonesWithDevices(),
      // Add timescale data endpoints
      'timescale': async () => {
        if (pathParts[1] === 'historical') {
          // Handle both POST body and GET params
          const params = options.body || options.params;
          return mockDataService.getTimescaleData(params);
        }
        return Promise.resolve([]);
      },
      'historical_data': async () => {
        // Handle historical data endpoints for timescale
        if (pathParts[1] === 'timescaledb') {
          if (pathParts[2] === 'statistical_data' && pathParts[3] === 'query') {
            // Return mock statistical data in the expected format
            const params = options.body || options.params;
            return {
              data: mockDataService.getTimescaleData(params)
            };
          }
          const params = options.body || options.params;
          return mockDataService.getTimescaleData(params);
        }
        return Promise.resolve([]);
      },
      'sites': () => {
        // Handle site data endpoint
        if (id) {
          return mockDataService.getSiteDetails(id);
        }
        return mockDataService.getSiteInfo();
      },
    };

    const handler = mockHandlers[resource];
    
    if (handler) {
      try {
        const data = await handler();
        return data as T;
      } catch (error) {
        throw new Error(`Mock Data Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Default fallback
    throw new Error(`No mock handler found for endpoint: ${endpoint}`);
  }

  // Convenience methods
  get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    return this.request<T>(endpoint, { method: 'GET', params });
  }

  post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, { method: 'POST', body: data });
  }

  put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, { method: 'PUT', body: data });
  }

  delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  patch<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, { method: 'PATCH', body: data });
  }

  // Toggle between mock and real data
  setUseMockData(useMock: boolean) {
    console.log('API Client: setUseMockData called', { from: this.useMockData, to: useMock });
    this.useMockData = useMock;
    localStorage.setItem('useMockData', useMock.toString());
    // Clear any temporary override when master setting changes
    sessionStorage.removeItem('floatingMockDataOverride');
  }

  // Set temporary override (used by floating toggle)
  setTemporaryMockDataOverride(useMock: boolean) {
    console.log('API Client: setTemporaryMockDataOverride called', { from: this.useMockData, to: useMock });
    this.useMockData = useMock;
    sessionStorage.setItem('floatingMockDataOverride', useMock.toString());
  }

  isUsingMockData(): boolean {
    return this.useMockData;
  }
  
  // Check if mock data is enabled in settings (master control)
  isMockDataEnabledInSettings(): boolean {
    return localStorage.getItem('useMockData') === 'true';
  }
}

// Export singleton instance
export const apiClient = new EnhancedApiClient();

// Export class for custom instances
export { EnhancedApiClient };