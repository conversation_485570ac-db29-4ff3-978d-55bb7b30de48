import { apiClient } from './enhancedApiClient';

export interface BuildingData {
  id: string;
  name: string;
  floors: number;
  totalArea: number;
  occupancy: number;
  energyIntensity: number;
  monthlyConsumption: number;
}

export const getBuildingData = async (buildingId: string): Promise<BuildingData> => {
  try {
    const response = await apiClient.get<BuildingData>(`/buildings/${buildingId}`);
    return response;
  } catch (error) {
    console.error('Error fetching building data:', error);
    throw error;
  }
};

export const getBuildingList = async (): Promise<BuildingData[]> => {
  try {
    const response = await apiClient.get<BuildingData[]>('/buildings');
    return response;
  } catch (error) {
    console.error('Error fetching building list:', error);
    throw error;
  }
};