import { BuildingId } from '../../types';
import { apiClient } from './enhancedApiClient';
import type { Meter, Threshold, ThresholdInput } from '../../types/models';

// Simplified types for the diagram structure
export interface MeterNodeData {
  id: string;
  type?: 'input' | 'output' | 'default' | string; // React Flow node types
  data: {
    label: string;
    status?: 'online' | 'offline' | 'warning';
    reading?: number; // Current reading (e.g., kW)
    meterType?: string; // e.g., 'main', 'submeter', 'panel'
  };
  // Position will be calculated by layout algorithm
  position?: { x: number; y: number }; 
}

export interface MeterEdge {
  id: string;
  source: string;
  target: string;
  type?: string; // e.g., 'smoothstep'
  animated?: boolean;
}

export interface MeterDiagramData {
  nodes: MeterNodeData[];
  edges: MeterEdge[];
}

// --- Meter API Functions --- 

/**
 * Fetches all meters.
 */
export const getMeters = async (): Promise<Meter[]> => {
  try {
    const response = await apiClient.get('/meters/');
    // Handle Django REST Framework's paginated response format
    if (response.data && 'results' in response.data) {
      return response.data.results;
    }
    return response.data;
  } catch (error) {
    console.error('Error fetching meters:', error);
    throw error;
  }
};

/**
 * Fetches a specific meter by its ID.
 * @param meterId - The unique ID (e.g., 'METER_001') of the meter.
 */
export const getMeterById = async (meterId: string): Promise<Meter> => {
  try {
    // Since apiClient already has baseURL: '/api', we don't need to include it in the path
    const response = await apiClient.get(`meters/${meterId}/`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching meter ${meterId}:`, error);
    throw error;
  }
};

// --- Threshold API Functions --- 

/**
 * Fetches thresholds for a specific meter.
 * @param meterId - The unique ID (e.g., 'METER_001') of the meter.
 * @returns Array of thresholds for the specified meter.
 */
export const getThresholdsForMeter = async (meterId: string): Promise<Threshold[]> => {
  try {
    console.log(`Fetching thresholds for meter ${meterId}`);
    // The correct URL format for nested routers is /meters/{meter_id}/thresholds/
    const response = await apiClient.get<{ results: Threshold[] }>(`meters/${meterId}/thresholds/`);
    console.log('Threshold response:', response.data);
    
    // Handle Django REST Framework's paginated response
    if (response.data && response.data.results) {
      return response.data.results;
    }
    // Fallback for non-paginated response
    return response.data as unknown as Threshold[];
  } catch (error) {
    console.error(`Error fetching thresholds for meter ${meterId}:`, error);
    throw error;
  }
};

/**
 * Creates a new threshold for a specific meter.
 * @param meterId - The unique ID (e.g., 'METER_001') of the meter.
 * @param thresholdData - The data for the new threshold.
 */
export const addThresholdForMeter = async (meterId: string, thresholdData: ThresholdInput): Promise<Threshold> => {
  try {
    console.log(`Adding threshold for meter ${meterId}:`, thresholdData);
    
    // First, get the meter to find its numeric ID
    const meter = await getMeterById(meterId);
    if (!meter) {
      throw new Error(`Meter with ID ${meterId} not found.`);
    }
    console.log('Retrieved meter:', meter);
    
    // Create the threshold with the numeric ID
    const dataWithMeter = {
      ...thresholdData,
      meter: meter.id // Use the numeric ID obtained from getMeterById
    };
    
    console.log('Sending data:', dataWithMeter);

    // The correct URL format for nested routers is /meters/{meter_id}/thresholds/
    const response = await apiClient.post<Threshold>(`/meters/${encodeURIComponent(meterId)}/thresholds/`, dataWithMeter);
    return response.data;
  } catch (error: any) {
    console.error(`Error adding threshold for meter ${meterId}:`, error);
    console.error('Error response:', error.response?.data);
    throw error;
  }
};

/**
 * Updates an existing threshold.
 * @param meterId - The unique ID of the meter.
 * @param thresholdId - The ID of the threshold to update.
 * @param thresholdData - The updated threshold data.
 */
export const updateThreshold = async (
  meterId: string,
  thresholdId: number,
  thresholdData: Partial<ThresholdInput>
): Promise<Threshold> => {
  try {
    // First, get the meter to find its numeric ID
    const meter = await getMeterById(meterId);
     if (!meter) {
      throw new Error(`Meter with ID ${meterId} not found.`);
    }

    // Include the numeric meter ID in the PATCH payload
    const dataWithMeter = {
      ...thresholdData,
      meter: meter.id // Use the numeric ID obtained from getMeterById
    };
    
    // Use the correct URL structure for the nested router
    const response = await apiClient.patch<Threshold>(
      `/meters/${encodeURIComponent(meterId)}/thresholds/${thresholdId}/`,
      dataWithMeter
    );
    return response.data;
  } catch (error: any) {
    console.error(`Error updating threshold ${thresholdId} for meter ${meterId}:`, error);
    throw error;
  }
};

/**
 * Deletes a threshold.
 * @param meterId - The unique ID of the meter.
 * @param thresholdId - The ID of the threshold to delete.
 */
export const deleteThreshold = async (meterId: string, thresholdId: number): Promise<void> => {
  try {
    // Use the correct URL structure for the nested router with meter_id as lookup field
    await apiClient.delete(`meters/${meterId}/thresholds/${thresholdId}/`);
  } catch (error) {
    console.error(`Error deleting threshold ${thresholdId} for meter ${meterId}:`, error);
    throw error;
  }
};

// Fetch meter hierarchy data for SET building
export const fetchMeterDiagramData = async (
  building?: BuildingId | 'all'
): Promise<MeterDiagramData> => {
  try {
    const response = await apiClient.get('/meters/diagram', { building });
    return response;
  } catch (error) {
    console.error('Error fetching meter diagram:', error);
    
    // Return SET building-specific mock data
    const nodes: MeterNodeData[] = [
      { id: 'grid', type: 'input', data: { label: 'MEA Grid (24kV)' } },
      { id: 'main-set', type: 'default', data: { label: 'SET Main Meter', status: 'online', reading: 2850.5, meterType: 'main' } },
      
      // Tower Building (Trading Center)
      { id: 'tower-a', type: 'default', data: { label: 'Tower Building Main', status: 'online', reading: 1200.8, meterType: 'main' } },
      { id: 'panel-a-trading', type: 'output', data: { label: 'Trading Floor Panel', meterType: 'panel' } },
      { id: 'sub-a-servers', type: 'default', data: { label: 'Trading Servers', status: 'online', reading: 420.5, meterType: 'submeter' } },
      { id: 'sub-a-ups', type: 'default', data: { label: 'UPS Systems', status: 'online', reading: 280.3, meterType: 'submeter' } },
      { id: 'sub-a-hvac', type: 'default', data: { label: 'Tower Building HVAC', status: 'online', reading: 500.0, meterType: 'submeter' } },
      
      // Podium Building (Offices)
      { id: 'tower-b', type: 'default', data: { label: 'Podium Building Main', status: 'online', reading: 950.2, meterType: 'main' } },
      { id: 'panel-b-office', type: 'output', data: { label: 'Office Panel B', meterType: 'panel' } },
      { id: 'sub-b-datacenter', type: 'default', data: { label: 'Data Center', status: 'online', reading: 320.1, meterType: 'submeter' } },
      { id: 'sub-b-office', type: 'default', data: { label: 'Office Floors', status: 'online', reading: 380.1, meterType: 'submeter' } },
      { id: 'sub-b-hvac', type: 'default', data: { label: 'Podium Building HVAC', status: 'online', reading: 250.0, meterType: 'submeter' } },
      
      // Car Park Building (Education)
      { id: 'tower-c', type: 'default', data: { label: 'Car Park Building Main', status: 'online', reading: 450.5, meterType: 'main' } },
      { id: 'panel-c-edu', type: 'output', data: { label: 'Education Panel', meterType: 'panel' } },
      { id: 'sub-c-classroom', type: 'default', data: { label: 'Classrooms', status: 'online', reading: 150.2, meterType: 'submeter' } },
      { id: 'sub-c-conference', type: 'default', data: { label: 'Conference Center', status: 'online', reading: 100.3, meterType: 'submeter' } },
      { id: 'sub-c-hvac', type: 'default', data: { label: 'Car Park Building HVAC', status: 'online', reading: 200.0, meterType: 'submeter' } },
      
      // Central Systems
      { id: 'chiller-plant', type: 'default', data: { label: 'Central Chiller Plant', status: 'online', reading: 850.0, meterType: 'main' } },
    ];

    const edges: MeterEdge[] = [
      { id: 'e-grid-main', source: 'grid', target: 'main-set', type: 'smoothstep', animated: true },
      // Tower Building connections
      { id: 'e-main-ta', source: 'main-set', target: 'tower-a', type: 'smoothstep', animated: true },
      { id: 'e-ta-panel', source: 'tower-a', target: 'panel-a-trading', type: 'smoothstep', animated: true },
      { id: 'e-pa-servers', source: 'panel-a-trading', target: 'sub-a-servers', type: 'smoothstep', animated: true },
      { id: 'e-pa-ups', source: 'panel-a-trading', target: 'sub-a-ups', type: 'smoothstep', animated: true },
      { id: 'e-ta-hvac', source: 'tower-a', target: 'sub-a-hvac', type: 'smoothstep', animated: true },
      
      // Podium Building connections
      { id: 'e-main-tb', source: 'main-set', target: 'tower-b', type: 'smoothstep', animated: true },
      { id: 'e-tb-panel', source: 'tower-b', target: 'panel-b-office', type: 'smoothstep', animated: true },
      { id: 'e-pb-dc', source: 'panel-b-office', target: 'sub-b-datacenter', type: 'smoothstep', animated: true },
      { id: 'e-pb-office', source: 'panel-b-office', target: 'sub-b-office', type: 'smoothstep', animated: true },
      { id: 'e-tb-hvac', source: 'tower-b', target: 'sub-b-hvac', type: 'smoothstep', animated: true },
      
      // Car Park Building connections
      { id: 'e-main-tc', source: 'main-set', target: 'tower-c', type: 'smoothstep', animated: true },
      { id: 'e-tc-panel', source: 'tower-c', target: 'panel-c-edu', type: 'smoothstep', animated: true },
      { id: 'e-pc-class', source: 'panel-c-edu', target: 'sub-c-classroom', type: 'smoothstep', animated: true },
      { id: 'e-pc-conf', source: 'panel-c-edu', target: 'sub-c-conference', type: 'smoothstep', animated: true },
      { id: 'e-tc-hvac', source: 'tower-c', target: 'sub-c-hvac', type: 'smoothstep', animated: true },
      
      // Chiller plant connection
      { id: 'e-main-chiller', source: 'main-set', target: 'chiller-plant', type: 'smoothstep', animated: true },
    ];

    return { nodes, edges };
  }
}; 