import { apiClient } from './enhancedApiClient';

export interface SiteInfo {
  id: string;
  name: string;
  location: string;
  buildings: string[];
  totalArea: number;
  coordinates: {
    lat: number;
    lng: number;
  };
}

export const getSiteInfo = async (): Promise<SiteInfo> => {
  try {
    const response = await apiClient.get<SiteInfo>('/site');
    return response;
  } catch (error) {
    console.error('Error fetching site info:', error);
    throw error;
  }
};