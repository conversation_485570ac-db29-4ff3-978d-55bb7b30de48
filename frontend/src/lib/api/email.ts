import { emailApi } from './emailClient';

export interface EmailRequest {
  to: string;
  subject: string;
  text: string;
  html?: string;
}

export interface EmailResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Send a test email notification
 * @param data Email request data
 * @returns Response with success status
 */
export const sendTestEmail = async (data: EmailRequest): Promise<EmailResponse> => {
  return emailApi.sendEmail(data.to, data.subject, data.html ?? data.text);
};

/**
 * Update email notification settings
 * @param settings Email settings to update
 * @returns Response with success status
 */
export const updateEmailSettings = async (settings: any): Promise<EmailResponse> => {
  return emailApi.sendEmail(
    settings.to,
    settings.subject,
    settings.html ?? settings.text ?? ''
  );
};
