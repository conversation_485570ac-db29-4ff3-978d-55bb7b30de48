import { apiClient } from './enhancedApiClient';

export interface TimeseriesData {
  timestamp: string;
  value: number;
}

export interface AnalyticsParams {
  startDate: string;
  endDate: string;
  metric: string;
  system?: string;
  interval?: string;
}

export interface AnalyticsResponse {
  metric: string;
  system?: string;
  data: TimeseriesData[];
  summary: {
    total: number;
    average: number;
    peak: number;
    minimum: number;
  };
}

export const getAnalyticsData = async (params: AnalyticsParams): Promise<AnalyticsResponse> => {
  try {
    const response = await apiClient.get<AnalyticsResponse>('/analytics', params);
    return response;
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    throw error;
  }
};

export interface HistoricalDataQuery {
  table_name: string;
  site_id: string;
  device_id: string | string[];
  datapoints: string[];
  start_timestamp: string;
  end_timestamp: string;
}

export interface EnergyRecord {
  timestamp: string;
  start_datetime: string;
  end_datetime: string;
  value: number;
  first_value: number;
  last_value: number;
}

export interface HistoricalDataRecord<T> {
  site_id: string;
  device_id: string;
  datapoint: string;
  model: string;
  data: T[];
}

export const fetchEnergyData = async (
  query: HistoricalDataQuery
): Promise<HistoricalDataRecord<EnergyRecord>[]> => {
  try {
    const response = await apiClient.post<any>('/historical_data/timescaledb/energy_data/query/', query);
    return response;
  } catch (error) {
    console.error('Error fetching energy data:', error);
    throw error;
  }
};