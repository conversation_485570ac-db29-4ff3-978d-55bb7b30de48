/**
 * Mock Data Refresher
 * 
 * This module periodically updates mock device data to simulate real-time changes
 * ensuring the data remains fresh and realistic whenever accessed
 */

import { calculatePowerConsumption } from '../utils/powerConsumptionPatterns';
import type { DeviceDetails } from '@/services/deviceService';

// Cache for device power values to ensure consistency
const devicePowerCache = new Map<string, {
  ratedPower: number;
  lastUpdate: Date;
  lastPower: number;
}>();

// Update all device timestamps and power values
export function refreshAllDeviceData(devices: Record<string, DeviceDetails>): void {
  const now = new Date();
  const timestamp = now.toISOString();
  
  Object.values(devices).forEach(device => {
    if (!device.latest_data) return;
    
    // Skip offline devices
    if (device.latest_data.status?.value === 0) return;
    
    // Get or calculate rated power for this device
    let cachedData = devicePowerCache.get(device.device_id);
    if (!cachedData || now.getTime() - cachedData.lastUpdate.getTime() > 300000) { // Refresh every 5 minutes
      // Estimate rated power from current values
      const currentPower = device.latest_data.power?.value || device.latest_data.power_total?.value || 100;
      const ratedPower = currentPower * 1.2; // Assume 20% headroom
      
      cachedData = {
        ratedPower,
        lastUpdate: now,
        lastPower: currentPower
      };
      devicePowerCache.set(device.device_id, cachedData);
    }
    
    // Calculate new power based on current time and patterns
    const buildingType = device.metadata?.location?.includes('Tower') ? 'office' : 'mixed';
    const newPower = calculatePowerConsumption(
      now,
      cachedData.ratedPower,
      device.model,
      buildingType as any
    );
    
    // Update all timestamps
    Object.keys(device.latest_data).forEach(key => {
      const field = device.latest_data![key as keyof typeof device.latest_data];
      if (field && typeof field === 'object' && 'updated_at' in field) {
        field.updated_at = timestamp;
      }
    });
    
    // Update power values
    if (device.latest_data.power) {
      device.latest_data.power.value = newPower;
    }
    if (device.latest_data.power_total) {
      device.latest_data.power_total.value = newPower;
    }
    
    // Update per-phase power (distribute load realistically)
    const phaseDistribution = getPhaseDistribution();
    if (device.latest_data.power_l1) {
      device.latest_data.power_l1.value = Math.round(newPower * phaseDistribution[0] * 10) / 10;
    }
    if (device.latest_data.power_l2) {
      device.latest_data.power_l2.value = Math.round(newPower * phaseDistribution[1] * 10) / 10;
    }
    if (device.latest_data.power_l3) {
      device.latest_data.power_l3.value = Math.round(newPower * phaseDistribution[2] * 10) / 10;
    }
    
    // Update currents based on new power (assuming constant voltage and power factor)
    const avgVoltage = 220; // Line-to-neutral voltage
    const powerFactor = device.latest_data.power_factor?.value || 0.92;
    
    const totalCurrent = newPower * 1000 / (Math.sqrt(3) * 380 * powerFactor); // Total current in Amps
    
    if (device.latest_data.current) {
      device.latest_data.current.value = Math.round(totalCurrent / 3 * 10) / 10; // Average per phase
    }
    if (device.latest_data.current_total) {
      device.latest_data.current_total.value = Math.round(totalCurrent * 10) / 10;
    }
    
    // Update per-phase currents
    if (device.latest_data.current_l1) {
      device.latest_data.current_l1.value = Math.round(totalCurrent * phaseDistribution[0] * 10) / 10;
    }
    if (device.latest_data.current_l2) {
      device.latest_data.current_l2.value = Math.round(totalCurrent * phaseDistribution[1] * 10) / 10;
    }
    if (device.latest_data.current_l3) {
      device.latest_data.current_l3.value = Math.round(totalCurrent * phaseDistribution[2] * 10) / 10;
    }
    
    // Update reactive and apparent power
    const reactivePower = newPower * Math.tan(Math.acos(powerFactor));
    const apparentPower = newPower / powerFactor;
    
    if (device.latest_data.reactive_power) {
      device.latest_data.reactive_power.value = Math.round(reactivePower * 10) / 10;
    }
    if (device.latest_data.reactive_power_total) {
      device.latest_data.reactive_power_total.value = Math.round(reactivePower * 10) / 10;
    }
    if (device.latest_data.apparent_power) {
      device.latest_data.apparent_power.value = Math.round(apparentPower * 10) / 10;
    }
    if (device.latest_data.apparent_power_total) {
      device.latest_data.apparent_power_total.value = Math.round(apparentPower * 10) / 10;
    }
    
    // Update cumulative energy (increment based on power and time)
    const timeDiff = 5 / 60; // 5 minutes in hours
    const energyIncrement = newPower * timeDiff; // kWh
    
    if (device.latest_data.cumulative_energy) {
      device.latest_data.cumulative_energy.value += energyIncrement;
      device.latest_data.cumulative_energy.value = Math.round(device.latest_data.cumulative_energy.value);
    }
    if (device.latest_data.energy_import) {
      device.latest_data.energy_import.value += energyIncrement;
      device.latest_data.energy_import.value = Math.round(device.latest_data.energy_import.value * 10) / 10;
    }
    
    // Cache the updated power
    cachedData.lastPower = newPower;
    cachedData.lastUpdate = now;
  });
}

// Get realistic phase distribution (slight imbalance)
function getPhaseDistribution(): [number, number, number] {
  const imbalance = 0.05; // 5% maximum imbalance
  const base = 1 / 3;
  
  const variation1 = (Math.random() - 0.5) * imbalance;
  const variation2 = (Math.random() - 0.5) * imbalance;
  
  const phase1 = base + variation1;
  const phase2 = base + variation2;
  const phase3 = 3 - phase1 - phase2; // Ensure total is 3
  
  // Normalize to sum to 1
  const total = phase1 + phase2 + phase3;
  return [phase1 / total, phase2 / total, phase3 / total];
}

// Start automatic refresh interval
let refreshInterval: NodeJS.Timeout | null = null;
let cachedDevices: Record<string, DeviceDetails> | null = null;

export function startMockDataRefresh(devices: Record<string, DeviceDetails>, intervalMs: number = 60000): void { // Default 1 minute
  stopMockDataRefresh(); // Clear any existing interval
  cachedDevices = devices;
  
  // Initial refresh
  refreshAllDeviceData(devices);
  
  // Set up periodic refresh
  refreshInterval = setInterval(() => {
    if (cachedDevices) {
      refreshAllDeviceData(cachedDevices);
    }
  }, intervalMs);
}

export function stopMockDataRefresh(): void {
  if (refreshInterval) {
    clearInterval(refreshInterval);
    refreshInterval = null;
  }
  cachedDevices = null;
}