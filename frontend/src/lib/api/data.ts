import { apiClient } from './enhancedApiClient';
import { subDays } from 'date-fns';
import type { LoadProfileData } from '../config/load-profile';

// Types for API responses
export interface HourlyAggregateResponse {
  id: number;
  hour_start: string;
  hour_end: string;
  parameter: number;
  device: number;
  value: number;
  parameter_details: {
    id: number;
    name: string;
    unit: string;
    category: string;
  };
  device_details: {
    id: number;
    name: string;
    type: string;
    building: number;
    floor: number | null;
    zone: number | null;
  };
}

export interface DailyAggregateResponse {
  id: number;
  day_start: string;
  day_end: string;
  parameter: number;
  device: number;
  value: number;
  parameter_details: {
    id: number;
    name: string;
    unit: string;
    category: string;
  };
  device_details: {
    id: number;
    name: string;
    type: string;
    building: number;
    floor: number | null;
    zone: number | null;
  };
}

export interface MonthlyAggregateResponse {
  id: number;
  month_start: string;
  month_end: string;
  parameter: number;
  device: number;
  value: number;
  parameter_details: {
    id: number;
    name: string;
    unit: string;
    category: string;
  };
  device_details: {
    id: number;
    name: string;
    type: string;
    building: number;
    floor: number | null;
    zone: number | null;
  };
}

// New type for the aggregated readings endpoint
export interface AggregatedReadingsResponse {
  timestamp: string;
  values: {
    [parameterName: string]: number;
  };
}

// Function to fetch hourly aggregate data for building load profile
export async function fetchBuildingLoadProfile({
  parameterId,
  startDate,
  endDate,
  deviceIds,
}: {
  parameterId?: number;
  startDate?: Date;
  endDate?: Date;
  deviceIds?: number[];
}): Promise<LoadProfileData[]> {
  try {
    const params = new URLSearchParams();
    
    // Add parameters to the query for the new aggregated endpoint
    if (parameterId !== undefined) {
      params.append('parameter_id', parameterId.toString());
    }
    
    // For device filtering, we can use parameter__device_id
    if (deviceIds && deviceIds.length > 0) {
      deviceIds.forEach(id => {
        params.append('parameter__device_id', id.toString());
      });
    }
    
    // Set the interval to hourly for load profile
    params.append('interval', 'hour');
    
    // Use avg aggregation for power/demand values
    params.append('aggregation', 'avg');
    
    // Convert dates to ISO format for the new endpoint
    if (startDate) {
      params.append('start_time', startDate.toISOString());
    }
    
    if (endDate) {
      params.append('end_time', endDate.toISOString());
    }
    
    console.log(`[fetchBuildingLoadProfile] Fetching data with params: ${params.toString()}`);
    
    // Call the new aggregated endpoint
    const response = await apiClient.get<AggregatedReadingsResponse[]>('/data/readings/aggregated/', { params });
    
    // Transform API response to the LoadProfileData format expected by the chart
    const loadProfileData: LoadProfileData[] = response.map(item => {
      // Extract hour from the ISO datetime string
      const timestamp = new Date(item.timestamp);
      const hour = timestamp.getHours();
      const formattedHour = `${hour}:00`;
      
      // Find the value for our parameter
      // The values object contains entries like "Main Meter - Power": 16.4
      // We need to extract the correct value based on the parameter we requested
      let value = 0;
      
      // Since we don't know the exact key name (it depends on device and parameter names),
      // we'll take the first value if there's only one, or try to find a key containing "Power" or "Demand"
      const valueEntries = Object.entries(item.values);
      
      if (valueEntries.length === 1) {
        // If there's only one value, use it
        value = valueEntries[0][1];
      } else {
        // Try to find a key containing "Power" or "Demand" (case insensitive)
        const powerEntry = valueEntries.find(([key]) => 
          key.toLowerCase().includes('power') || key.toLowerCase().includes('demand')
        );
        
        if (powerEntry) {
          value = powerEntry[1];
        } else {
          // Fallback: use the first value
          value = valueEntries[0]?.[1] || 0;
        }
      }
      
      return {
        time: formattedHour,
        demand: value,
      };
    });
    
    // Sort by hour to ensure correct order
    loadProfileData.sort((a, b) => {
      const hourA = parseInt(a.time.split(':')[0]);
      const hourB = parseInt(b.time.split(':')[0]);
      return hourA - hourB;
    });
    
    console.log('[fetchBuildingLoadProfile] Transformed data:', loadProfileData);
    return loadProfileData;
    
  } catch (error) {
    console.error('[fetchBuildingLoadProfile] Error fetching building load profile:', error);
    return []; // Return empty array on error to prevent crashes
  }
}

// Function to fetch comparison data (e.g., yesterday's data)
export async function fetchComparisonLoadProfile({
  parameterId,
  comparisonDate,
  deviceIds,
}: {
  parameterId?: number;
  comparisonDate?: Date; // The date to compare with (e.g., yesterday)
  deviceIds?: number[];
}): Promise<LoadProfileData[]> {
  try {
    // If no comparison date is provided, use yesterday
    const compareDate = comparisonDate || subDays(new Date(), 1);
    const startOfDay = new Date(compareDate);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(compareDate);
    endOfDay.setHours(23, 59, 59, 999);
    
    const params = new URLSearchParams();
    
    // Add parameters to the query for the new aggregated endpoint
    if (parameterId !== undefined) {
      params.append('parameter_id', parameterId.toString());
    }
    
    // For device filtering, we can use parameter__device_id
    if (deviceIds && deviceIds.length > 0) {
      deviceIds.forEach(id => {
        params.append('parameter__device_id', id.toString());
      });
    }
    
    // Set the interval to hourly for load profile
    params.append('interval', 'hour');
    
    // Use avg aggregation for power/demand values
    params.append('aggregation', 'avg');
    
    // Convert dates to ISO format for the new endpoint
    params.append('start_time', startOfDay.toISOString());
    params.append('end_time', endOfDay.toISOString());
    
    console.log(`[fetchComparisonLoadProfile] Fetching comparison data with params: ${params.toString()}`);
    
    // Call the new aggregated endpoint
    const response = await apiClient.get<AggregatedReadingsResponse[]>('/data/readings/aggregated/', { params });
    
    // Transform API response to the LoadProfileData format
    const comparisonData: LoadProfileData[] = response.map(item => {
      const timestamp = new Date(item.timestamp);
      const hour = timestamp.getHours();
      const formattedHour = `${hour}:00`;
      
      // Find the value for our parameter
      // The values object contains entries like "Main Meter - Power": 16.4
      let value = 0;
      
      // Since we don't know the exact key name (it depends on device and parameter names),
      // we'll take the first value if there's only one, or try to find a key containing "Power" or "Demand"
      const valueEntries = Object.entries(item.values);
      
      if (valueEntries.length === 1) {
        // If there's only one value, use it
        value = valueEntries[0][1];
      } else {
        // Try to find a key containing "Power" or "Demand" (case insensitive)
        const powerEntry = valueEntries.find(([key]) => 
          key.toLowerCase().includes('power') || key.toLowerCase().includes('demand')
        );
        
        if (powerEntry) {
          value = powerEntry[1];
        } else {
          // Fallback: use the first value
          value = valueEntries[0]?.[1] || 0;
        }
      }
      
      return {
        time: formattedHour,
        demand: value,
      };
    });
    
    // Sort by hour to ensure correct order
    comparisonData.sort((a, b) => {
      const hourA = parseInt(a.time.split(':')[0]);
      const hourB = parseInt(b.time.split(':')[0]);
      return hourA - hourB;
    });
    
    console.log('[fetchComparisonLoadProfile] Transformed comparison data:', comparisonData);
    return comparisonData;
    
  } catch (error) {
    console.error('[fetchComparisonLoadProfile] Error fetching comparison load profile:', error);
    return []; // Return empty array on error to prevent crashes
  }
}
