// Compare API with Mock Data Support
import { apiClient } from './enhancedApiClient';

export interface CompareDataRequest {
  systems: string[];
  metric: string;
  dateRange: {
    startDate: Date;
    endDate: Date;
  };
}

export interface CompareDataPoint {
  timestamp: string;
  value: number;
}

export interface CompareResult {
  system: string;
  name: string;
  type: string;
  data: CompareDataPoint[];
  summary: {
    average: number;
    peak: number;
    minimum: number;
    total: number;
  };
}

export const getCompareData = async (params: CompareDataRequest): Promise<CompareResult[]> => {
  try {
    const response = await apiClient.get<CompareResult[]>('/compare', {
      systems: params.systems,
      metric: params.metric,
      dateRange: {
        startDate: params.dateRange.startDate.toISOString(),
        endDate: params.dateRange.endDate.toISOString()
      }
    });
    return response;
  } catch (error) {
    console.error('Error fetching compare data:', error);
    throw error;
  }
};

export const getSystemsList = async () => {
  try {
    // Get available systems for comparison
    const response = await apiClient.get('/systems');
    return response;
  } catch (error) {
    console.error('Error fetching systems list:', error);
    
    // Return mock systems list for SET building
    return [
      { id: 'chiller-1', name: 'Chiller Plant 1', type: 'chiller' },
      { id: 'chiller-2', name: 'Chiller Plant 2', type: 'chiller' },
      { id: 'tower-a-hvac', name: 'Tower A HVAC', type: 'hvac' },
      { id: 'tower-b-hvac', name: 'Tower B HVAC', type: 'hvac' },
      { id: 'tower-c-hvac', name: 'Tower C HVAC', type: 'hvac' },
      { id: 'datacenter-cooling', name: 'Data Center Cooling', type: 'critical' },
      { id: 'tower-a', name: 'Tower A Total', type: 'building' },
      { id: 'tower-b', name: 'Tower B Total', type: 'building' },
      { id: 'tower-c', name: 'Tower C Total', type: 'building' },
    ];
  }
};

export const getMetricsList = async () => {
  try {
    const response = await apiClient.get('/metrics');
    return response;
  } catch (error) {
    console.error('Error fetching metrics list:', error);
    
    // Return available metrics
    return [
      { id: 'power', name: 'Power (kW)', unit: 'kW' },
      { id: 'energy', name: 'Energy (kWh)', unit: 'kWh' },
      { id: 'efficiency', name: 'Efficiency', unit: 'kW/RT' },
      { id: 'cop', name: 'COP', unit: '' },
      { id: 'power_factor', name: 'Power Factor', unit: '' },
    ];
  }
};