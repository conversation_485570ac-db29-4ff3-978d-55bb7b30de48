import { apiClient } from './enhancedApiClient';
import type { 
  DeviceRelation, 
  DeviceDetails, 
  Zone 
} from '../../services/deviceService';

/**
 * Enhanced device service that uses the apiClient with mock data support
 */

export const fetchChildDeviceRelationsByModel = async (modelType: string): Promise<DeviceRelation[]> => {
  try {
    const response = await apiClient.get('/device_relations/child/', {
      model: modelType
    });
    // Handle both direct array response and wrapped response
    return Array.isArray(response) ? response : (response?.data || []);
  } catch (error) {
    console.error('Error fetching child device relations by model:', error);
    return [];
  }
};

export const fetchChildDeviceRelationsById = async (deviceId: string): Promise<DeviceRelation[]> => {
  try {
    const response = await apiClient.get(`/device_relations/child/${deviceId}`);
    // Handle both direct array response and wrapped response
    return Array.isArray(response) ? response : (response?.data || []);
  } catch (error) {
    // Return empty array for 404 (no relations found)
    return [];
  }
};

export const fetchAllDevices = async (expandOptions?: string[]): Promise<DeviceDetails[]> => {
  try {
    const params = expandOptions?.length ? { expand: expandOptions.join(',') } : {};
    const response = await apiClient.get('/devices/', params);
    // Handle both direct array response and wrapped response
    return Array.isArray(response) ? response : (response?.data || []);
  } catch (error) {
    console.error('Error fetching all devices:', error);
    return [];
  }
};

export const fetchDeviceById = async (
  deviceId: string, 
  expandOptions?: string[]
): Promise<DeviceDetails> => {
  try {
    const params = expandOptions?.length ? { expand: expandOptions.join(',') } : {};
    const response = await apiClient.get(`/devices/${deviceId}/`, params);
    
    if (!response) {
      throw new Error(`Device ${deviceId} not found`);
    }
    
    return response;
  } catch (error) {
    console.error(`Error fetching device ${deviceId}:`, error);
    throw error;
  }
};

export const fetchZonesWithDevices = async (): Promise<Zone[]> => {
  try {
    const response = await apiClient.get('/zones/', { expand: 'devices' });
    // Handle both direct array response and wrapped response
    return Array.isArray(response) ? response : (response?.data || []);
  } catch (error) {
    console.error('Error fetching zones with devices:', error);
    return [];
  }
};