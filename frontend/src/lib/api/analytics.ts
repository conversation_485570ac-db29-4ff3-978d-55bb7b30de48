import type { ViewType as ApiViewType, BuildingType, SystemBreakdownItem, PerformanceData, PerformanceDataItem, PerformanceMetrics } from '../../types';
import { ChartData, ChartDataPoint } from '../../types/analytics';
import { apiClient } from './enhancedApiClient';

// API endpoints - Commented out as unused for now
// const ENDPOINTS = {
//   chartData: '/api/analytics/chart-data',
//   systemBreakdown: '/api/analytics/system-breakdown'
// } as const;

// Existing function (if any)
export async function fetchChartData(
  view: ApiViewType,
  date: Date,
  building: BuildingType
): Promise<ChartData> {
  console.log(`API: Fetching chart data - View: ${view}, Date: ${date.toISOString()}, Building: ${building}`);
  await new Promise(resolve => setTimeout(resolve, 600)); // Simulate network latency

  // Mock data generation based on view
  const generateData = (offset = 0): ChartDataPoint[] => {
    let points: ChartDataPoint[] = [];
    const baseDemand = 50 + Math.random() * 20 + offset * 5;
    const baseConsumption = 10 + Math.random() * 5 + offset * 2;

    switch (view) {
      case 'daily':
        points = Array.from({ length: 24 }, (_, i) => ({
          time: `${i}:00`,
          demand: Math.max(0, baseDemand + Math.sin(i / 3) * 15 + Math.random() * 5),
          consumption: Math.max(0, baseConsumption + Math.cos(i/4) * 3 + Math.random() * 2)
        }));
        break;
      case 'weekly':
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        points = days.map((day, i) => ({
          time: day,
          demand: Math.max(0, baseDemand * 1.5 + Math.sin(i) * 20 + Math.random() * 8),
          consumption: Math.max(0, baseConsumption * 1.8 + Math.cos(i) * 5 + Math.random() * 3)
        }));
        break;
      case 'monthly':
        const daysInMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
        points = Array.from({ length: daysInMonth }, (_, i) => ({
          time: String(i + 1),
          demand: Math.max(0, baseDemand * 2 + Math.sin(i / 5) * 25 + Math.random() * 10),
          consumption: Math.max(0, baseConsumption * 2.5 + Math.cos(i/6) * 8 + Math.random() * 4)
        }));
        break;
      case 'yearly':
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        points = months.map((month, i) => ({
          time: month,
          demand: Math.max(0, baseDemand * 20 + Math.sin(i / 2) * 300 + Math.random() * 100),
          consumption: Math.max(0, baseConsumption * 25 + Math.cos(i/3) * 200 + Math.random() * 50)
        }));
        break;
      default:
        points = Array.from({ length: 24 }, (_, i) => ({
          time: `${i}:00`,
          demand: Math.max(0, baseDemand + Math.random() * 10),
          consumption: Math.max(0, baseConsumption + Math.random() * 5)
        }));
    }
    return points;
  };

  return { 
    current: generateData(0), 
    comparison: generateData(1) // Use slightly offset data for comparison
  };
}

// Analytics data interface
export interface AnalyticsData {
  hourlyConsumption: Array<{ time: string; value: number }>;
  totalConsumption: number;
  peakDemand: { value: number; time: string };
  averageLoad: number;
}

// Function for fetching analytics data using enhanced API client
export async function fetchAnalyticsData(
  view: ApiViewType,
  date: Date,
  building: BuildingType
): Promise<AnalyticsData> {
  try {
    // Convert date to string format
    const dateStr = date.toISOString().split('T')[0];
    const endDate = new Date(date);
    endDate.setDate(endDate.getDate() + 1);
    
    // Fetch analytics data through API client
    const response = await apiClient.get('/analytics', {
      startDate: dateStr,
      endDate: endDate.toISOString().split('T')[0],
      metric: 'energy',
      system: building === 'all' ? undefined : building,
      interval: view === 'daily' ? 'hourly' : view === 'weekly' ? 'daily' : 'monthly'
    });
    
    // Transform the response to match AnalyticsData interface
    if (response && response.data) {
      // Remove duplicates and ensure unique time entries
      const timeMap = new Map<string, number>();
      
      response.data.forEach((item: any) => {
        const date = new Date(item.timestamp);
        const hours = date.getHours();
        // Format as HH:00 for hourly data (no minutes for hourly view)
        const time = `${hours.toString().padStart(2, '0')}:00`;
        
        // Ensure value is a number
        const itemValue = typeof item.value === 'number' ? item.value : (parseFloat(item.value) || 0);
        
        // If we already have this time, add the values
        if (timeMap.has(time)) {
          const existingValue = timeMap.get(time) || 0;
          timeMap.set(time, existingValue + itemValue);
        } else {
          timeMap.set(time, itemValue);
        }
      });
      
      // Convert map to array and sort by time
      const hourlyConsumption = Array.from(timeMap.entries())
        .map(([time, value]) => ({ time, value }))
        .sort((a, b) => a.time.localeCompare(b.time));
      
      const totalConsumption = response.summary?.total || hourlyConsumption.reduce((sum: number, item: any) => {
        const value = typeof item?.value === 'number' ? item.value : (parseFloat(item?.value) || 0);
        return sum + value;
      }, 0);
      
      const values = hourlyConsumption.map((item: any) => {
        const value = typeof item?.value === 'number' ? item.value : (parseFloat(item?.value) || 0);
        return value;
      });
      
      const maxValue = values.length > 0 ? Math.max(...values) : 0;
      const maxIndex = values.length > 0 ? values.indexOf(maxValue) : -1;
      
      return {
        hourlyConsumption,
        totalConsumption,
        peakDemand: {
          value: maxValue,
          time: hourlyConsumption[maxIndex]?.time || '10:30'
        },
        averageLoad: response.summary?.average || totalConsumption / hourlyConsumption.length
      };
    }
    
    // Fallback to mock data if needed
    throw new Error('Invalid response format');
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    
    // Return mock data as fallback
    const totalConsumption = 28000;
    const peakDemand = { value: 3200, time: '10:30' }; 
    const averageLoad = 2400;
    
    // Generate SET building data pattern - complete 24 hours
    const hourlyConsumption = [
      { time: '00:00', value: 1750 }, { time: '1:00', value: 1800 }, { time: '2:00', value: 1700 }, 
      { time: '3:00', value: 1700 }, { time: '4:00', value: 1750 }, { time: '5:00', value: 1900 }, 
      { time: '6:00', value: 2100 }, { time: '7:00', value: 2400 }, { time: '8:00', value: 2800 }, 
      { time: '9:00', value: 3000 }, { time: '10:00', value: 3200 }, { time: '11:00', value: 3100 }, 
      { time: '12:00', value: 2900 }, { time: '13:00', value: 2800 }, { time: '14:00', value: 3100 }, 
      { time: '15:00', value: 3150 }, { time: '16:00', value: 3050 }, { time: '17:00', value: 2600 }, 
      { time: '18:00', value: 2300 }, { time: '19:00', value: 2000 }, { time: '20:00', value: 1900 }, 
      { time: '21:00', value: 1850 }, { time: '22:00', value: 1800 }, { time: '23:00', value: 1750 }
    ];

    return {
      hourlyConsumption,
      totalConsumption,
      peakDemand,
      averageLoad
    };
  }
}

// Fetch System Breakdown Data using enhanced API client
export const fetchSystemBreakdown = async (
  view: ApiViewType,
  date: Date,
  building: BuildingType = 'all'
): Promise<{ current: SystemBreakdownItem[], previous: SystemBreakdownItem[] }> => {
  try {
    const dateStr = date.toISOString().split('T')[0];
    
    // Fetch system breakdown data through API client
    const response = await apiClient.get('/analytics', {
      startDate: dateStr,
      endDate: dateStr,
      metric: 'energy',
      system: 'all',
      breakdown: true
    });
    
    // If API returns data, use it
    if (response && response.breakdown) {
      return {
        current: response.breakdown.current,
        previous: response.breakdown.previous
      };
    }
  } catch (error) {
    console.error('Error fetching system breakdown:', error);
  }
  
  // Fallback to mock data for SET building
  const systems = ['Chiller Plant', 'Air Side', 'Data Center', 'Light & Power', 'EV Charger', 'Others'];
  const colors = ['#0284C7', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#6B7280'];
  
  // SET building realistic percentages
  const setPercentages = [45, 25, 10, 12, 3, 5]; // Chiller is dominant in Bangkok
  
  const generateBreakdown = (baseMultiplier: number): SystemBreakdownItem[] => {
    return systems.map((name, index) => {
      const percentage = setPercentages[index];
      const value = Math.round((2350000 * baseMultiplier * percentage) / 100); // Monthly total for SET
      
      return {
        id: name.toLowerCase().replace(' & ', '-').replace(' ', '-'),
        name: name,
        value: value,
        percentage: percentage,
        color: colors[index]
      };
    });
  };

  const currentBreakdown = generateBreakdown(1.0);
  const previousBreakdown = generateBreakdown(0.95); // Previous month slightly lower

  return { current: currentBreakdown, previous: previousBreakdown };
};

// Fetch Performance Data (Mock Implementation) - Prefix unused params
export const fetchPerformanceData = async (
  _view: ApiViewType,      // Use ApiViewType from ../../types
  _building: BuildingType = 'all', // Prefixed
  // Add date/year range parameters as needed later
): Promise<PerformanceData> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 700)); 

  // Mock Metrics
  const mockMetrics: PerformanceMetrics = {
    energySavings: Math.round(5000 + Math.random() * 15000),
    carbonReduction: Math.round(2000 + Math.random() * 6000),
  };

  // Mock Chart Data
  let mockChartData: PerformanceDataItem[] = [];
  const currentYear = new Date().getFullYear();

  if (_view === 'yearly' || _view === 'monthly' || _view === 'weekly' || _view === 'daily') { // Treat all these as monthly for mock
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    mockChartData = months.map(month => {
      const seasonalFactor = ['Jun', 'Jul', 'Aug'].includes(month) ? 1.3 : ['Dec', 'Jan', 'Feb'].includes(month) ? 0.8 : 1.0;
      return {
        time: month,
        actual: Math.round((8000 + Math.random() * 5000) * seasonalFactor),
        baseline: Math.round((10000 + Math.random() * 5000) * seasonalFactor),
        target: Math.round((9000 + Math.random() * 4000) * seasonalFactor),
      };
    });
  } else if (_view === 'multi-year') {
    const years = [currentYear - 2, currentYear - 1, currentYear];
    mockChartData = years.map(year => {
      const yearFactor = 1 - (currentYear - year) * 0.05;
      return {
        time: year.toString(),
        actual: Math.round((100000 + Math.random() * 20000) * yearFactor),
        baseline: Math.round((120000 + Math.random() * 20000) * yearFactor),
        target: Math.round((110000 + Math.random() * 15000) * yearFactor),
      };
    });
  }

  return {
    chartData: mockChartData,
    metrics: mockMetrics,
  };
};
