/**
 * Email API Client for Alto CERO EMS
 *
 * This file provides a mock implementation for development and a real implementation for production.
 * The mock implementation logs to the console instead of making actual API calls.
 */

// For production, we would use a real API client like this:
/*
import type { ApiError, ApiRequestConfig } from './types';

class EmailApiClient {
  private baseUrl: string;
  private apiKey: string;
  private defaultConfig: RequestInit;

  constructor(baseUrl: string, apiKey: string = '') {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
    this.defaultConfig = {
      headers: {
        'Content-Type': 'application/json',
        'X-Server-API-Key': this.apiKey
      },
    };
  }

  async post<T>(endpoint: string, data?: unknown, config?: ApiRequestConfig): Promise<T> {
    return this.request<T>('POST', endpoint, data, config);
  }

  private async request<T>(
    method: string,
    endpoint: string,
    data?: unknown,
    config?: ApiRequestConfig
  ): Promise<T> {
    try {
      // Performance optimization: Use AbortController to set a timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...this.defaultConfig,
        ...config,
        method,
        body: data ? JSON.stringify(data) : undefined,
        signal: controller.signal
      });

      // Clear the timeout once the request completes
      clearTimeout(timeoutId);

      if (!response.ok) {
        const error = await response.json();
        throw this.handleError(error);
      }

      return response.json();
    } catch (error) {
      throw this.handleError(error);
    }
  }

  private handleError(error: unknown): ApiError {
    if (error instanceof Error) {
      return {
        code: error.name === 'AbortError' ? 'TIMEOUT_ERROR' : 'UNKNOWN_ERROR',
        message: error.name === 'AbortError' ? 'Request timed out' : error.message,
      };
    }
    return {
      code: 'NETWORK_ERROR',
      message: 'An unexpected error occurred',
    };
  }

  async sendEmail(to: string, subject: string, body: string): Promise<any> {
    return this.post('/send/message', {
      to,
      from: '<EMAIL>',
      subject,
      html_body: body
    });
  }
}

// Use Postal server with API key
// In Vite, we use import.meta.env instead of process.env
const POSTAL_API_KEY = import.meta.env.VITE_POSTAL_API_KEY || '';
const emailApiClient = new EmailApiClient('http://localhost:5002/api/v1', POSTAL_API_KEY);
*/
// For development, let's create a mock email API that logs to console instead of making actual API calls
export const emailApi = {
  sendEmail: async (to: string, subject: string, body: string): Promise<any> => {
    console.log('MOCK EMAIL API: Sending email to', to);
    console.log('MOCK EMAIL API: Subject:', subject);
    console.log('MOCK EMAIL API: HTML content:', body);

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // In development mode, always return success
    return {
      success: true,
      message: `Email sent successfully to ${to}`
    };
  }
};

// Uncomment this to use the real email API in production
// export const emailApi = new EmailApiClient('http://localhost:5002/api/v1', POSTAL_API_KEY);

// Helper functions for common email operations
export const sendTestEmail = async (email: string): Promise<any> => {
  return emailApi.sendEmail(
    email,
    'Test Email from Alto CERO EMS',
    '<h1>Test Email</h1><p>This is a test email from your Alto CERO Energy Management System.</p>'
  );
};

export const sendAlertEmail = async (email: string, alertData: any): Promise<any> => {
  const subject = `Alert: ${alertData.type} - ${alertData.deviceName}`;
  const html = `
    <h1>Energy Alert</h1>
    <p>Type: ${alertData.type}</p>
    <p>Device: ${alertData.deviceName}</p>
    <p>Time: ${new Date(alertData.timestamp).toLocaleString()}</p>
    <p>Details: ${alertData.message}</p>
  `;
  return emailApi.sendEmail(email, subject, html);
};
