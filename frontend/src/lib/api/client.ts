import type { ApiError, ApiRequestConfig } from './types';

class ApiClient {
  private baseUrl: string;
  private defaultConfig: RequestInit;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
    this.defaultConfig = {
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }

  async get<T>(endpoint: string, config?: ApiRequestConfig): Promise<T> {
    return this.request<T>('GET', endpoint, undefined, config);
  }

  async post<T>(endpoint: string, data?: unknown, config?: ApiRequestConfig): Promise<T> {
    return this.request<T>('POST', endpoint, data, config);
  }

  private async request<T>(
    method: string,
    endpoint: string,
    data?: unknown,
    config?: ApiRequestConfig
  ): Promise<T> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...this.defaultConfig,
        ...config,
        method,
        body: data ? JSON.stringify(data) : undefined,
      });

      if (!response.ok) {
        const error = await response.json();
        throw this.handleError(error);
      }

      return response.json();
    } catch (error) {
      throw this.handleError(error);
    }
  }

  private handleError(error: unknown): ApiError {
    if (error instanceof Error) {
      return {
        code: 'UNKNOWN_ERROR',
        message: error.message,
      };
    }
    return {
      code: 'NETWORK_ERROR',
      message: 'An unexpected error occurred',
    };
  }
}

export const api = new ApiClient(import.meta.env.VITE_API_BASE_URL || '');