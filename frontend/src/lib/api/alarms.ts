import { apiClient } from '@/lib/api/enhancedApiClient';
import { AlarmRule, ActiveAlarm, AlarmHistory, AlarmRulePayload, AFDDLog, AFDDApiResponse } from '@/types/alarms';
import {
  getFilteredAndPaginatedActiveAlarms,
  getFilteredAndPaginatedAlarmRules,
  getFilteredAndPaginatedAlarmHistory,
  getMockAlarmRule
} from '@/utils/mockAlarms';

// Type for paginated responses (adjust if your backend pagination differs)
interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Type for Alarm Log converted to paginated format
interface AFDDLogsPaginatedResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: AFDDLog[];
}

// === Alarm Rules ===

export const getAlarmRules = async (params?: Record<string, string>): Promise<PaginatedResponse<AlarmRule>> => {
  if (apiClient.isUsingMockData()) {
    return getFilteredAndPaginatedAlarmRules(params || {});
  }
  const response = await apiClient.get<PaginatedResponse<AlarmRule>>('/alarms/definitions/', params);
  return response;
};

export const getAFDDFault = async (id: number): Promise<AlarmRule> => {
  if (apiClient.isUsingMockData()) {
    const rule = getMockAlarmRule(id);
    if (!rule) {
      throw new Error(`Alarm rule with ID ${id} not found`);
    }
    return rule;
  }
  const response = await apiClient.get<AlarmRule>(`/afdd/faults/${id}/`);
  return response;
};

export const getAlarmRule = getAFDDFault; // Alias for backward compatibility

export const createAFDDFault = async (payload: AlarmRulePayload): Promise<AlarmRule> => {
    if (apiClient.isUsingMockData()) {
      // In mock mode, just return a fake success response
      return {
        id: Math.floor(Math.random() * 1000) + 100,
        name: payload.name || '',
        metric: payload.metric || '',
        threshold: payload.threshold || 0,
        operator: payload.operator || '>',
        severity: payload.severity || 'INFO',
        message: payload.message || null,
        is_active: payload.is_active !== undefined ? payload.is_active : true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    }
    const response = await apiClient.post<AlarmRule>('/afdd/faults/', payload);
    return response;
};

export const createAlarmRule = createAFDDFault; // Alias for backward compatibility

export const updateAFDDFault = async (id: number, payload: AlarmRulePayload): Promise<AlarmRule> => {
    if (apiClient.isUsingMockData()) {
      // In mock mode, just return a fake success response
      const existingRule = getMockAlarmRule(id);
      if (!existingRule) {
        throw new Error(`Alarm rule with ID ${id} not found`);
      }

      return {
        ...existingRule,
        ...payload,
        updated_at: new Date().toISOString()
      };
    }
    const response = await apiClient.put<AlarmRule>(`/afdd/faults/${id}/`, payload);
    return response;
};

export const updateAlarmRule = updateAFDDFault; // Alias for backward compatibility

export const deleteAFDDFault = async (id: number): Promise<void> => {
    if (apiClient.isUsingMockData()) {
      // In mock mode, just pretend it was successful
      return Promise.resolve();
    }
    await apiClient.delete(`/afdd/faults/${id}/`);
};

export const deleteAlarmRule = deleteAFDDFault; // Alias for backward compatibility

// === Active Alarms ===

export const getActiveAlarms = async (params?: Record<string, string>): Promise<PaginatedResponse<ActiveAlarm>> => {
  if (apiClient.isUsingMockData()) {
    return getFilteredAndPaginatedActiveAlarms(params || {});
  }
  const response = await apiClient.get<PaginatedResponse<ActiveAlarm>>('/alarms/instances/', params);
  return response;
};

// === Alarm History ===

export const getAlarmHistory = async (params?: Record<string, string>): Promise<PaginatedResponse<AlarmHistory>> => {
  if (apiClient.isUsingMockData()) {
    return getFilteredAndPaginatedAlarmHistory(params || {});
  }
  const response = await apiClient.get<PaginatedResponse<AlarmHistory>>('/alarms/history/', params);
  return response;
};

// === AFDD Faults (Rules) ===

export const getAFDDFaults = async (params?: Record<string, string>): Promise<PaginatedResponse<AlarmRule>> => {
  if (apiClient.isUsingMockData()) {
    return getFilteredAndPaginatedAlarmRules(params || {});
  }
  
  try {
    const response = await apiClient.get('/afdd/faults/', params);
    console.log('Raw AFDD faults response:', response);
    
    // Check if response has the AFDD wrapper format
    if (response && response.metadata && response.metadata.pagination) {
      const afddResponse = response as AFDDApiResponse<any>;
      console.log('Processing AFDD wrapper format, data count:', afddResponse.data.length);
      return {
        count: afddResponse.metadata.pagination.count,
        next: afddResponse.metadata.pagination.next,
        previous: afddResponse.metadata.pagination.previous,
        results: afddResponse.data.map((fault: any) => ({
          id: fault.id,
          name: fault.name,
          category: fault.category || null,
          severity: fault.severity.toLowerCase(),
          is_active: fault.enabled,
          created_at: fault.created_at,
          updated_at: fault.updated_at,
          site_name: fault.site.name,
          site_id: fault.site.id,
          // Additional fields for compatibility with AlarmRule type
          metric: fault.name,
          threshold: 0,
          operator: '>',
          notification_message: `${fault.name.replace(/_/g, ' ')} fault detected`,
          meters: []
        }))
      };
    }
    
    // If response is already in paginated format
    if (response && 'results' in response) {
      return response as PaginatedResponse<AlarmRule>;
    }
    
    // Fallback
    return {
      count: 0,
      next: null,
      previous: null,
      results: []
    };
  } catch (error) {
    console.error('Error fetching AFDD faults:', error);
    throw error;
  }
};

// === Alarm Log ===

export const getAFDDLogs = async (params?: Record<string, string>): Promise<AFDDLogsPaginatedResponse> => {
  if (apiClient.isUsingMockData()) {
    // For now, return empty data when using mock
    return {
      count: 0,
      next: null,
      previous: null,
      results: []
    };
  }
  
  try {
    const response = await apiClient.get('/afdd/logs/', params);
    
    // Check if response is an array (raw data)
    if (Array.isArray(response)) {
      return {
        count: response.length,
        next: null,
        previous: null,
        results: response as AFDDLog[]
      };
    }
    
    // Check if response has the AFDD wrapper format
    if (response && response.metadata && response.metadata.pagination) {
      const afddResponse = response as AFDDApiResponse<any>;
      console.log('AFDD API Response:', afddResponse);
      const results = afddResponse.data.map((log: any) => ({
        id: log.id,
        fault: log.fault.id,
        fault_name: log.fault.name,
        alarm_state: log.alarm_state,
        ack_state: log.ack_state,
        severity: log.severity,
        active_at: log.active_at,
        normal_at: log.normal_at,
        acknowledged_at: log.acknowledged_at,
        acknowledged_by: log.acknowledged_by,
        is_active: log.is_active,
        is_cleared: log.is_cleared,
        duration: log.duration,
        message: log.message,
        site_name: log.site.name,
        site: log.site.id
      }));
      console.log('Mapped results:', results);
      return {
        count: afddResponse.metadata.pagination.count,
        next: afddResponse.metadata.pagination.next,
        previous: afddResponse.metadata.pagination.previous,
        results
      };
    }
    
    // If response is already in paginated format
    if (response && 'results' in response) {
      return response as AFDDLogsPaginatedResponse;
    }
    
    // Fallback for unexpected response format
    console.warn('Unexpected Alarm Log response format:', response);
    return {
      count: 0,
      next: null,
      previous: null,
      results: []
    };
  } catch (error) {
    console.error('Error fetching Alarm Log:', error);
    throw error;
  }
};

export const updateAFDDLogState = async (logId: number, ackState: string): Promise<boolean> => {
  if (apiClient.isUsingMockData()) {
    return true;
  }
  
  try {
    const response = await apiClient.patch(`/afdd/logs/${logId}/`, { ack_state: ackState });
    console.log('PATCH response:', response);
    return true;
  } catch (error) {
    console.error('Error updating AFDD log state:', error);
    throw error;
  }
};

// Get AFDD log detail
export const getAFDDLogDetail = async (logId: number): Promise<any> => {
  try {
    const response = await apiClient.get(`/afdd/logs/${logId}/`);
    // Handle AFDD wrapper format if needed
    if (response && response.data) {
      return response.data;
    }
    return response;
  } catch (error) {
    console.error('Error fetching AFDD log detail:', error);
    throw error;
  }
};

// === Acknowledgment and Resolution ===
export const acknowledgeAndResolveAlarm = async (alarmId: number | string): Promise<boolean> => {
  if (apiClient.isUsingMockData()) {
    // Use the mock function from mockAlarms.ts
    const { acknowledgeAndResolveAlarm } = await import('@/utils/mockAlarms');
    return acknowledgeAndResolveAlarm(alarmId);
  }

  // In a real implementation, this would call the API
  const response = await apiClient.post<{success: boolean}>(`/alarms/active/${alarmId}/acknowledge-resolve/`);
  return response.success;
};
