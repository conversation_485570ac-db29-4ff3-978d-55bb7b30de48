import type { BuildingId } from '../../types';
import { apiClient } from './enhancedApiClient'; // Use enhanced API client with mock support

// Types
export type PerformanceView = 'daily' | 'weekly' | 'monthly' | 'yearly';

// Performance data types
export interface PerformanceDataItem {
  time: string;
  actual: number;
  baseline: number;
  target: number;
}

export interface BenchmarkDataItem {
  category: string;
  value: number;
  average: number;
  best: number;
}

export interface PerformanceMetrics {
  efficiencyRatio?: number; // Made optional as not all metrics endpoints provide these
  energySavings?: number;
  carbonReduction?: number;
  costSavings?: number;
  performanceIndex?: number;
  // Fields from the actual EnergyPerformanceMetric model
  id?: number;
  period?: string;
  metric_type?: string;
  timestamp?: string;
  value: number; // Assuming value is always present
  unit?: string;
  building?: number;
  floor?: number | null;
  zone?: number | null;
  target_value?: number | null;
  benchmark_value?: number | null;
  calculation_method?: string;
  notes?: string;
}

// Updated Response Type for fetchBuildingMetrics
export interface BuildingMetricsResponse {
  building: {
    id: number;
    name: string;
    address?: string;
    total_area?: number;
  };
  metrics: PerformanceMetrics[];
}

// Updated function to fetch specific building metrics
export async function fetchBuildingMetrics({
  building_id,
  metric_type,
  period,
  start_date,
  end_date,
}: {
  building_id: number;
  metric_type: string;
  period: PerformanceView | 'hourly'; // Allow specific period strings
  start_date?: string; // YYYY-MM-DD
  end_date?: string; // YYYY-MM-DD
}): Promise<BuildingMetricsResponse> {
  console.log(`[fetchBuildingMetrics] Fetching for building: ${building_id}, period: ${period}, metric: ${metric_type}, start: ${start_date}, end: ${end_date}`);
  try {
    const params = new URLSearchParams();
    params.append('building_id', building_id.toString());
    params.append('metric_type', metric_type);
    params.append('period', period);
    if (start_date) params.append('start_date', start_date);
    if (end_date) params.append('end_date', end_date);

    console.log(`[fetchBuildingMetrics] Making GET request to /data/performance/building_metrics/ with params: ${params.toString()}`);

    // Use apiClient to make the actual request
    const response = await apiClient.get<BuildingMetricsResponse>(
      `/data/performance/building_metrics/`, 
      { params }
    );
    
    console.log(`[fetchBuildingMetrics] Received response for building ${building_id}:`, response);
    return response;
    
  } catch (error) {
    console.error(`[fetchBuildingMetrics] Error fetching building metrics for building ${building_id}:`, error);
    // Consider re-throwing the error for the caller to handle
    // throw error; 
    
    // Return a default structure on error to prevent crashes downstream
    return { building: { id: building_id, name: 'Unknown' }, metrics: [] };
  }
}

export interface PerformanceData {
  current: PerformanceDataItem[];
  metrics: PerformanceMetrics;
}

export interface BenchmarkData {
  categories: BenchmarkDataItem[];
  rating: number;
  certifications: {
    name: string;
    level: string;
    score: number;
    maxScore: number;
  }[];
}

// --- Mock Benchmark Data Function (Unchanged) ---
// Generate benchmark data
export function fetchBenchmarkData(
  selectedBuilding: BuildingId | 'all' = 'all'
): Promise<BenchmarkData> {
  // Building adjustment factor
  const adjustFactor = selectedBuilding === 'all' ? 1 : 
    selectedBuilding === 'A' ? 1.1 : 
    selectedBuilding === 'B' ? 0.95 : 0.85;
  
  // Mock benchmark categories
  const categories: BenchmarkDataItem[] = [
    {
      category: 'Energy Use Intensity',
      value: Math.round(78 * adjustFactor), // kWh/m²/year
      average: 100, // Industry average
      best: 65 // Best in class
    },
    {
      category: 'Peak Demand Ratio',
      value: parseFloat((0.67 * adjustFactor).toFixed(2)),
      average: 0.75,
      best: 0.55
    },
    {
      category: 'Load Factor',
      value: parseFloat((0.62 * adjustFactor).toFixed(2)),
      average: 0.55,
      best: 0.75
    },
    {
      category: 'Carbon Intensity',
      value: Math.round(32 * adjustFactor), // kgCO2/m²/year
      average: 42,
      best: 25
    },
    {
      category: 'Renewable Ratio',
      value: Math.round(15 * adjustFactor), // %
      average: 8,
      best: 30
    },
    {
      category: 'Energy Cost Index',
      value: Math.round(14 * adjustFactor), // $/m²/year
      average: 18,
      best: 10
    }
  ];
  
  // Calculate overall rating (1-5 scale)
  const categoryRatings = categories.map(cat => {
    // Normalize so lower values are better for all metrics
    const normalizedValue = cat.category === 'Renewable Ratio' || cat.category === 'Load Factor'
      ? (cat.value - cat.average) / (cat.best - cat.average) // Higher is better
      : (cat.average - cat.value) / (cat.average - cat.best); // Lower is better
    
    return Math.max(0, Math.min(1, normalizedValue)) * 5; // Scale to 0-5
  });
  
  const rating = categoryRatings.reduce((sum, val) => sum + val, 0) / categoryRatings.length;
  
  // Mock certifications
  const certifications = [
    {
      name: "LEED",
      level: "Gold",
      score: 65,
      maxScore: 80
    },
    {
      name: "ENERGY STAR",
      level: "Certified",
      score: 82,
      maxScore: 100
    },
    {
      name: "WELL",
      level: "Silver",
      score: 40,
      maxScore: 80
    }
  ];
  
  return Promise.resolve({
    categories,
    rating,
    certifications
  });
}

// New implementation of fetchPerformanceData
export function fetchPerformanceData(
  view: PerformanceView,
  selectedDate: Date = new Date(),
  selectedBuilding: BuildingId | 'all' = 'all'
): Promise<PerformanceData> {
  // Generate mock data based on view and building type
  const data: PerformanceDataItem[] = [];
  const currentYear = selectedDate.getFullYear();
  const currentMonth = selectedDate.getMonth();
  
  // Building adjustment factor
  const buildingMultiplier = selectedBuilding === 'all' ? 1 : 
    selectedBuilding === 'A' ? 1.2 : 
    selectedBuilding === 'B' ? 0.9 : 0.75;
  
  // Generate different data based on the selected view
  if (view === 'daily') {
    // Generate 24 hours of data
    for (let hour = 0; hour < 24; hour++) {
      const baseValue = generateBaseValue(hour, buildingMultiplier);
      const efficiencyFactor = 0.85 + (Math.random() * 0.15); // Between 0.85 and 1.0
      const targetFactor = 0.8; // 20% reduction target

      data.push({
        time: `${hour}:00`,
        baseline: baseValue,
        actual: generateActualValue(baseValue, efficiencyFactor, hour),
        target: generateTargetValue(baseValue, targetFactor)
      });
    }
  } else if (view === 'weekly') {
    // Generate 7 days of data
    const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    for (let day = 0; day < 7; day++) {
      const baseValue = generateBaseValue((day < 5 ? day + 9 : day + 4), buildingMultiplier) * 24 * 0.7;
      const efficiencyFactor = 0.85 + (Math.random() * 0.15);
      const targetFactor = 0.8;

      data.push({
        time: daysOfWeek[day],
        baseline: baseValue,
        actual: generateActualValue(baseValue, efficiencyFactor, day),
        target: generateTargetValue(baseValue, targetFactor)
      });
    }
  } else if (view === 'monthly') {
    // Generate monthly data for the year
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    for (let month = 0; month < 12; month++) {
      // Seasonal adjustment
      let seasonalFactor = 1.0;
      if (month >= 5 && month <= 7) seasonalFactor = 1.3; // Summer
      else if (month >= 11 || month <= 1) seasonalFactor = 1.2; // Winter
      
      const baseValue = generateBaseValue(month + 10, buildingMultiplier) * 30 * seasonalFactor;
      const efficiencyFactor = 0.85 + (Math.random() * 0.15);
      const targetFactor = 0.8;

      data.push({
        time: months[month],
        baseline: baseValue,
        actual: generateActualValue(baseValue, efficiencyFactor, month),
        target: generateTargetValue(baseValue, targetFactor)
      });
    }
  } else if (view === 'yearly') {
    // Generate yearly data for the past 5 years
    for (let yearOffset = 4; yearOffset >= 0; yearOffset--) {
      const year = currentYear - yearOffset;
      // Yearly trend: efficiency improves in more recent years
      const yearlyTrendFactor = 1 - (yearOffset * 0.03);
      
      const baseValue = generateBaseValue(14, buildingMultiplier) * 365 * yearlyTrendFactor;
      const efficiencyFactor = (0.85 + (4 - yearOffset) * 0.03) * yearlyTrendFactor;
      const targetFactor = 0.8;

      data.push({
        time: year.toString(),
        baseline: baseValue,
        actual: generateActualValue(baseValue, efficiencyFactor, yearOffset),
        target: generateTargetValue(baseValue, targetFactor)
      });
    }
  }

  // Calculate metrics
  const totalActual = data.reduce((sum, item) => sum + item.actual, 0);
  const totalBaseline = data.reduce((sum, item) => sum + item.baseline, 0);
  const efficiencyRatio = totalActual / totalBaseline;
  const energySavings = totalBaseline - totalActual;
  
  // Calculate metrics based on the data
  const metrics: PerformanceMetrics = {
    value: totalActual, // Using total actual value as the base metric value
    efficiencyRatio: parseFloat((efficiencyRatio * 100).toFixed(1)),
    energySavings: Math.round(energySavings),
    carbonReduction: Math.round(energySavings * 0.42), // CO2 kg per kWh
    costSavings: Math.round(energySavings * 0.18), // $ per kWh
    performanceIndex: parseFloat((Math.min(1, 2 - efficiencyRatio) * 100).toFixed(1))
  };
  
  return Promise.resolve({ current: data, metrics });
}

// --- Old Mock fetchPerformanceData (keep for reference or remove) ---
/*
export function fetchPerformanceData(
  view: PerformanceView,
  selectedDate: Date,
  selectedBuilding: BuildingId | 'all' = 'all'
): Promise<PerformanceData> {
  // Mock implementation for performance data
  const data: PerformanceDataItem[] = [];
  // ... (mock data generation logic) ...

  // Calculate metrics
  const totalActual = data.reduce((sum, item) => sum + item.actual, 0);
  const totalBaseline = data.reduce((sum, item) => sum + item.baseline, 0);
  const efficiencyRatio = totalActual / totalBaseline;
  const energySavings = totalBaseline - totalActual;
  
  // Mock metrics
  const metrics: PerformanceMetrics = {
    value: 0, // Added default value to satisfy type
    efficiencyRatio: parseFloat((efficiencyRatio * 100).toFixed(1)),
    energySavings: Math.round(energySavings),
    carbonReduction: Math.round(energySavings * 0.42), // CO2 kg per kWh
    costSavings: Math.round(energySavings * 0.18), // $ per kWh
    performanceIndex: parseFloat((Math.min(1, 2 - efficiencyRatio) * 100).toFixed(1))
  };
  
  return Promise.resolve({ current: data, metrics });
}
*/

// -- Helper functions (kept in case old mock is uncommented) --
function generateBaseValue(index: number, multiplier: number): number {
  // Create a realistic baseline pattern
  let baseValue: number;
  
  // Pattern based on index (hour, day, month)
  if (index < 6 || index >= 21) { // Early morning or late night
    baseValue = 100 + (index % 6) * 20;
  } else if (index < 9 || (index >= 18 && index < 21)) { // Morning or evening
    baseValue = 200 + (index % 3) * 30;
  } else { // Day time (9-18)
    baseValue = 300 + (index % 9) * 25;
  }
  
  // Apply building multiplier
  baseValue = baseValue * multiplier;
  
  // Add some randomness (±10%)
  const randomFactor = 0.9 + (Math.random() * 0.2);
  
  return Math.round(baseValue * randomFactor);
}

function generateActualValue(baseline: number, efficiencyFactor: number, index: number): number {
  // Generate actual consumption that's typically lower than baseline (energy savings)
  // but has some variability based on the hour/day/month
  const variabilityFactor = 0.95 + (Math.random() * 0.1);
  
  // Actual is generally lower than baseline due to efficiency
  const actualValue = baseline * efficiencyFactor * variabilityFactor;
  
  return Math.round(actualValue);
}

function generateTargetValue(baseline: number, targetFactor: number): number {
  // Target is a certain percentage of baseline (usually lower)
  const randomFactor = 0.95 + (Math.random() * 0.1);
  return Math.round(baseline * targetFactor * randomFactor);
}
