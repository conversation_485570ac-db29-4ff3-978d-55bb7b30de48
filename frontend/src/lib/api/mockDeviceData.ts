import type { DeviceRelation, DeviceDetails, Zone } from '@/services/deviceService';
import { calculatePowerConsumption } from '../utils/powerConsumptionPatterns';
import { refreshAllDeviceData, startMockDataRefresh } from './mockDataRefresher';
import { 
  MAIN_METER_LIST,
  TOWER_BUILDING_METERS,
  PODIUM_BUILDING_METERS,
  CAR_PARK_BUILDING_METERS
} from '@/lib/config/building/meters';

// Helper functions
const randomValue = (base: number, variance: number = 0.1) => {
  return base * (1 + (Math.random() - 0.5) * variance);
};

const currentTimestamp = () => new Date().toISOString();

// Counter for unique IDs
let deviceIdCounter = 1;

// Generate professional 3-phase power meter data for Thai commercial building
const generateDeviceDetails = (
  deviceId: string, 
  name: string, 
  model: string, 
  location: string, 
  ratedPower: number,
  isOnline: boolean = true
): DeviceDetails => {
  const timestamp = currentTimestamp();
  
  if (!isOnline) {
    // Offline meter - all values zero
    return {
      id: deviceIdCounter++,
      device_id: deviceId,
      name,
      model,
      metadata: {
        location,
        type: model,
        building: location.includes('Tower Building') ? 'A' : 
                  location.includes('Podium Building') ? 'B' : 
                  location.includes('Car Park Building') ? 'C' : 'main'
      },
      autopilot: {},
      latest_data: {
        status: { value: 0, updated_at: timestamp },
        // All parameters zero when offline - include both real API and backward compatibility fields
        power: { value: 0, updated_at: timestamp },
        power_total: { value: 0, updated_at: timestamp },
        power_l1: { value: 0, updated_at: timestamp },
        power_l2: { value: 0, updated_at: timestamp },
        power_l3: { value: 0, updated_at: timestamp },
        power_factor: { value: 0, updated_at: timestamp }, // Real API field
        power_factor_total: { value: 0, updated_at: timestamp },
        voltage_l1l2: { value: 0, updated_at: timestamp }, // Real API field
        voltage_l2l3: { value: 0, updated_at: timestamp }, // Real API field
        voltage_l3l1: { value: 0, updated_at: timestamp }, // Real API field
        voltage_l1_l2: { value: 0, updated_at: timestamp },
        voltage_l2_l3: { value: 0, updated_at: timestamp },
        voltage_l3_l1: { value: 0, updated_at: timestamp },
        voltage_l1: { value: 0, updated_at: timestamp }, // Real API field
        voltage_l2: { value: 0, updated_at: timestamp }, // Real API field
        voltage_l3: { value: 0, updated_at: timestamp }, // Real API field
        voltage_l1_n: { value: 0, updated_at: timestamp },
        voltage_l2_n: { value: 0, updated_at: timestamp },
        voltage_l3_n: { value: 0, updated_at: timestamp },
        voltage_ln_average: { value: 0, updated_at: timestamp },
        voltage_ll_average: { value: 0, updated_at: timestamp },
        current_l1: { value: 0, updated_at: timestamp },
        current_l2: { value: 0, updated_at: timestamp },
        current_l3: { value: 0, updated_at: timestamp },
        current: { value: 0, updated_at: timestamp },
        current_total: { value: 0, updated_at: timestamp },
        frequency: { value: 0, updated_at: timestamp },
        cumulative_energy: { value: 0, updated_at: timestamp },
        energy_import: { value: 0, updated_at: timestamp },
        reactive_power: { value: 0, updated_at: timestamp }, // Real API field
        reactive_power_total: { value: 0, updated_at: timestamp },
        apparent_power: { value: 0, updated_at: timestamp }, // Real API field
        apparent_power_total: { value: 0, updated_at: timestamp },
        voltage_thd_l1l2: { value: 0, updated_at: timestamp }, // Real API field
        voltage_thd_l2l3: { value: 0, updated_at: timestamp }, // Real API field
        voltage_thd_l3l1: { value: 0, updated_at: timestamp }, // Real API field
      },
    };
  }

  // Online meter - generate realistic 3-phase data
  
  // Thailand electrical system: 380V line-to-line, 220V line-to-neutral, 50Hz
  const nominalVoltageLL = 380; // Line-to-line voltage
  const nominalVoltageLN = 220; // Line-to-neutral voltage
  const nominalFrequency = 50;  // 50Hz in Thailand
  
  // Generate intelligent power consumption based on current time, equipment type, etc.
  const now = new Date();
  const buildingType = location.includes('Tower') ? 'office' : 'mixed';
  const totalPower = calculatePowerConsumption(now, ratedPower, model, buildingType);
  
  // Simulate load imbalance (typical in real buildings)
  const loadImbalance = randomValue(1.0, 0.15); // ±15% imbalance
  const powerL1 = totalPower * 0.33 * randomValue(loadImbalance, 0.1);
  const powerL2 = totalPower * 0.33 * randomValue(loadImbalance, 0.1);
  const powerL3 = totalPower - powerL1 - powerL2; // Ensure total adds up
  
  // Generate line-to-line voltages (slight variations)
  const voltageL1L2 = randomValue(nominalVoltageLL, 0.02); // ±2% variation
  const voltageL2L3 = randomValue(nominalVoltageLL, 0.02);
  const voltageL3L1 = randomValue(nominalVoltageLL, 0.02);
  
  // Generate line-to-neutral voltages
  const voltageL1N = randomValue(nominalVoltageLN, 0.02);
  const voltageL2N = randomValue(nominalVoltageLN, 0.02);
  const voltageL3N = randomValue(nominalVoltageLN, 0.02);
  
  // Calculate currents from power and voltage (P = V * I * cos(φ))
  const powerFactorL1 = randomValue(0.92, 0.05); // Typical office building PF
  const powerFactorL2 = randomValue(0.92, 0.05);
  const powerFactorL3 = randomValue(0.92, 0.05);
  const powerFactorTotal = (powerFactorL1 + powerFactorL2 + powerFactorL3) / 3;
  
  const currentL1 = powerL1 > 0 ? (powerL1 * 1000) / (voltageL1N * powerFactorL1) : 0;
  const currentL2 = powerL2 > 0 ? (powerL2 * 1000) / (voltageL2N * powerFactorL2) : 0;
  const currentL3 = powerL3 > 0 ? (powerL3 * 1000) / (voltageL3N * powerFactorL3) : 0;
  
  // Neutral current (due to imbalance)
  const currentNeutral = Math.abs(currentL1 - currentL2) * randomValue(0.1, 0.5);
  
  // Reactive and apparent power calculations
  const reactivePowerL1 = powerL1 * Math.tan(Math.acos(powerFactorL1));
  const reactivePowerL2 = powerL2 * Math.tan(Math.acos(powerFactorL2));
  const reactivePowerL3 = powerL3 * Math.tan(Math.acos(powerFactorL3));
  const reactivePowerTotal = reactivePowerL1 + reactivePowerL2 + reactivePowerL3;
  
  const apparentPowerTotal = Math.sqrt(totalPower * totalPower + reactivePowerTotal * reactivePowerTotal);
  
  // Power quality parameters
  const voltageTHD = randomValue(2.5, 0.4); // Typical 1-4% THD in commercial buildings
  const currentTHD = randomValue(8.0, 0.3); // Higher THD for current due to electronic loads
  const voltageUnbalance = Math.abs(voltageL1N - voltageL2N) / voltageL1N * 100; // %
  const currentUnbalance = Math.abs(currentL1 - currentL2) / currentL1 * 100; // %
  
  // Frequency with slight variation
  const frequency = randomValue(nominalFrequency, 0.001); // Very stable in grid
  
  // Energy accumulation (for billing)
  const energyBase = Math.floor(Math.random() * 500000) + 100000; // kWh
  const energyImport = energyBase + randomValue(1000, 0.1);
  const energyExport = Math.random() > 0.8 ? randomValue(50, 0.5) : 0; // Sometimes export (solar)
  const reactiveEnergyImport = energyImport * 0.3; // Typical reactive energy
  
  // Demand values (important for Thai utility billing)
  const maxDemand = totalPower * randomValue(1.2, 0.1); // Historical max
  const avgDemand = totalPower * randomValue(0.8, 0.1); // Average demand
  
  return {
    id: deviceIdCounter++,
    device_id: deviceId,
    name,
    model,
    metadata: {
      location,
      type: model,
      building: location.includes('Tower Building') ? 'A' : 
                location.includes('Podium Building') ? 'B' : 
                location.includes('Car Park Building') ? 'C' : 'main',
      meter_type: '3-phase',
      rated_voltage: '380/220V',
      rated_frequency: '50Hz',
      accuracy_class: '0.2S', // Professional meter accuracy
    },
    autopilot: {},
    latest_data: {
      // System status
      status: { value: 1, updated_at: timestamp },
      
      // Total Power Parameters - Using real API field names
      power: { value: Math.round(totalPower * 10) / 10, updated_at: timestamp }, // Real API uses 'power'
      power_total: { value: Math.round(totalPower * 10) / 10, updated_at: timestamp }, // Keep for backward compatibility
      reactive_power: { value: Math.round(reactivePowerTotal * 10) / 10, updated_at: timestamp }, // Real API uses 'reactive_power'
      reactive_power_total: { value: Math.round(reactivePowerTotal * 10) / 10, updated_at: timestamp }, // Keep for backward compatibility
      apparent_power: { value: Math.round(apparentPowerTotal * 10) / 10, updated_at: timestamp }, // Real API uses 'apparent_power'
      apparent_power_total: { value: Math.round(apparentPowerTotal * 10) / 10, updated_at: timestamp }, // Keep for backward compatibility
      power_factor: { value: Math.round(powerFactorTotal * 1000) / 1000, updated_at: timestamp }, // Real API uses 'power_factor'
      power_factor_total: { value: Math.round(powerFactorTotal * 1000) / 1000, updated_at: timestamp }, // Keep for backward compatibility
      
      // Per-Phase Real Power (kW)
      power_l1: { value: Math.round(powerL1 * 10) / 10, updated_at: timestamp },
      power_l2: { value: Math.round(powerL2 * 10) / 10, updated_at: timestamp },
      power_l3: { value: Math.round(powerL3 * 10) / 10, updated_at: timestamp },
      
      // Per-Phase Reactive Power (kVAR)
      reactive_power_l1: { value: Math.round(reactivePowerL1 * 10) / 10, updated_at: timestamp },
      reactive_power_l2: { value: Math.round(reactivePowerL2 * 10) / 10, updated_at: timestamp },
      reactive_power_l3: { value: Math.round(reactivePowerL3 * 10) / 10, updated_at: timestamp },
      
      // Per-Phase Power Factor
      power_factor_l1: { value: Math.round(powerFactorL1 * 1000) / 1000, updated_at: timestamp },
      power_factor_l2: { value: Math.round(powerFactorL2 * 1000) / 1000, updated_at: timestamp },
      power_factor_l3: { value: Math.round(powerFactorL3 * 1000) / 1000, updated_at: timestamp },
      
      // Line-to-Line Voltages (V) - Real API format (no underscores between phases)
      voltage_l1l2: { value: Math.round(voltageL1L2 * 10) / 10, updated_at: timestamp }, // Real API uses 'voltage_l1l2'
      voltage_l2l3: { value: Math.round(voltageL2L3 * 10) / 10, updated_at: timestamp }, // Real API uses 'voltage_l2l3'
      voltage_l3l1: { value: Math.round(voltageL3L1 * 10) / 10, updated_at: timestamp }, // Real API uses 'voltage_l3l1'
      voltage_l1_l2: { value: Math.round(voltageL1L2 * 10) / 10, updated_at: timestamp }, // Keep for backward compatibility
      voltage_l2_l3: { value: Math.round(voltageL2L3 * 10) / 10, updated_at: timestamp }, // Keep for backward compatibility
      voltage_l3_l1: { value: Math.round(voltageL3L1 * 10) / 10, updated_at: timestamp }, // Keep for backward compatibility
      
      // Line-to-Neutral Voltages (V) - Real API format
      voltage_l1: { value: Math.round(voltageL1N * 10) / 10, updated_at: timestamp }, // Real API uses 'voltage_l1'
      voltage_l2: { value: Math.round(voltageL2N * 10) / 10, updated_at: timestamp }, // Real API uses 'voltage_l2'
      voltage_l3: { value: Math.round(voltageL3N * 10) / 10, updated_at: timestamp }, // Real API uses 'voltage_l3'
      voltage_l1_n: { value: Math.round(voltageL1N * 10) / 10, updated_at: timestamp }, // Keep for backward compatibility
      voltage_l2_n: { value: Math.round(voltageL2N * 10) / 10, updated_at: timestamp }, // Keep for backward compatibility
      voltage_l3_n: { value: Math.round(voltageL3N * 10) / 10, updated_at: timestamp }, // Keep for backward compatibility
      
      // Average voltages (for backward compatibility)
      voltage_ln_average: { value: Math.round((voltageL1N + voltageL2N + voltageL3N) / 3 * 10) / 10, updated_at: timestamp },
      voltage_ll_average: { value: Math.round((voltageL1L2 + voltageL2L3 + voltageL3L1) / 3 * 10) / 10, updated_at: timestamp },
      
      // Phase Currents (A)
      current_l1: { value: Math.round(currentL1 * 10) / 10, updated_at: timestamp },
      current_l2: { value: Math.round(currentL2 * 10) / 10, updated_at: timestamp },
      current_l3: { value: Math.round(currentL3 * 10) / 10, updated_at: timestamp },
      current_neutral: { value: Math.round(currentNeutral * 10) / 10, updated_at: timestamp },
      
      // Average current (for backward compatibility)
      current: { value: Math.round((currentL1 + currentL2 + currentL3) / 3 * 10) / 10, updated_at: timestamp },
      // Total current (sum of all phases)
      current_total: { value: Math.round((currentL1 + currentL2 + currentL3) * 10) / 10, updated_at: timestamp },
      
      // System Frequency
      frequency: { value: Math.round(frequency * 100) / 100, updated_at: timestamp },
      
      // Power Quality Parameters - Real API format
      voltage_thd_l1l2: { value: Math.round(voltageTHD * 100) / 100, updated_at: timestamp }, // Real API uses line-to-line THD
      voltage_thd_l2l3: { value: Math.round(voltageTHD * 1.1 * 100) / 100, updated_at: timestamp },
      voltage_thd_l3l1: { value: Math.round(voltageTHD * 0.9 * 100) / 100, updated_at: timestamp },
      voltage_thd_l1: { value: Math.round(voltageTHD * 100) / 100, updated_at: timestamp }, // Keep for backward compatibility
      voltage_thd_l2: { value: Math.round(voltageTHD * 1.1 * 100) / 100, updated_at: timestamp },
      voltage_thd_l3: { value: Math.round(voltageTHD * 0.9 * 100) / 100, updated_at: timestamp },
      current_thd_l1: { value: Math.round(currentTHD * 100) / 100, updated_at: timestamp },
      current_thd_l2: { value: Math.round(currentTHD * 1.2 * 100) / 100, updated_at: timestamp },
      current_thd_l3: { value: Math.round(currentTHD * 0.8 * 100) / 100, updated_at: timestamp },
      voltage_unbalance: { value: Math.round(voltageUnbalance * 100) / 100, updated_at: timestamp },
      current_unbalance: { value: Math.round(currentUnbalance * 100) / 100, updated_at: timestamp },
      
      // Energy Measurements (kWh, kVARh)
      energy_import: { value: Math.round(energyImport * 10) / 10, updated_at: timestamp },
      energy_export: { value: Math.round(energyExport * 10) / 10, updated_at: timestamp },
      reactive_energy_import: { value: Math.round(reactiveEnergyImport * 10) / 10, updated_at: timestamp },
      reactive_energy_export: { value: Math.round(reactiveEnergyImport * 0.1 * 10) / 10, updated_at: timestamp },
      
      // Demand Parameters (important for Thai utility billing)
      demand_max: { value: Math.round(maxDemand * 10) / 10, updated_at: timestamp },
      demand_avg: { value: Math.round(avgDemand * 10) / 10, updated_at: timestamp },
      demand_current: { value: Math.round(totalPower * 10) / 10, updated_at: timestamp },
      
      // Temperature monitoring
      temperature_internal: { value: Math.round(randomValue(35, 0.1) * 10) / 10, updated_at: timestamp },
      
      // Legacy fields for backward compatibility
      cumulative_energy: { value: Math.round(energyImport), updated_at: timestamp },
    },
  };
};

// Generate mock devices from meter configurations
const generateMockDevicesFromMeterConfig = (): Record<string, DeviceDetails> => {
  const devices: Record<string, DeviceDetails> = {};
  let idCounter = 1;

  // Helper to generate device
  const generateDevice = (deviceId: string, name: string, model: string, location: string, power: number = 0) => {
    const timestamp = currentTimestamp();
    const isOnline = Math.random() > 0.05; // 95% online
    const actualPower = isOnline ? power : 0;
    
    return generateDeviceDetails(deviceId, name, model, location, actualPower);
  };

  // Add main meter
  devices['main'] = generateDevice('main', 'Main Meter', 'power_meter', 'Main Electrical Room', 2800);
  
  // Add devices from MAIN_METER_LIST
  Object.entries(MAIN_METER_LIST).forEach(([groupKey, group]) => {
    // Create group device
    devices[groupKey] = generateDevice(groupKey, group.name, 'meter_group', 'Main Distribution', 
      Math.random() * 500 + 100);
    
    // Add all meters in the group
    group.meters.forEach(meter => {
      const power = meter.type === 'chillerPlant' ? Math.random() * 400 + 100 :
                   meter.type === 'airSide' ? Math.random() * 150 + 50 :
                   meter.type === 'lightPower' ? Math.random() * 80 + 20 :
                   meter.type === 'tenant' ? Math.random() * 50 + 10 :
                   Math.random() * 100 + 50;
                   
      devices[meter.id] = generateDevice(meter.id, meter.name, meter.type, group.name, power);
    });
  });
  
  // Add tower main meters
  devices['tower_a'] = generateDevice('tower_a', 'Tower Building Main', 'power_meter', 'Tower Building - B1', 1200);
  devices['tower_b'] = generateDevice('tower_b', 'Podium Building Main', 'power_meter', 'Podium Building - B1', 950);
  devices['tower_c'] = generateDevice('tower_c', 'Car Park Building Main', 'power_meter', 'Car Park Building - B1', 450);
  
  // Add tower floor devices
  Object.entries(TOWER_BUILDING_METERS).forEach(([floorKey, meters]) => {
    const floorName = floorKey === 'floorB' ? 'Floor B' : 
                     floorKey === 'dataCenter' ? 'Data Center' :
                     floorKey === 'roof' ? 'Roof' :
                     `Floor ${floorKey.replace('floor', '')}`;
    
    const floorId = `tower_a_${floorKey}`;
    devices[floorId] = generateDevice(floorId, `Tower Building - ${floorName}`, 'floor_group', 'Tower Building', 
      Math.random() * 100 + 50);
    
    meters.forEach(meter => {
      devices[meter.id] = generateDevice(meter.id, meter.name, meter.type, 
        `Tower Building - ${floorName}`, Math.random() * 50 + 10);
    });
  });
  
  // Add podium (Tower B) devices
  Object.entries(PODIUM_BUILDING_METERS).forEach(([floorKey, meters]) => {
    const floorName = `Floor ${floorKey.replace('floor', '')}`;
    const floorId = `tower_b_${floorKey}`;
    
    devices[floorId] = generateDevice(floorId, `Podium Building - ${floorName}`, 'floor_group', 'Podium Building',
      Math.random() * 100 + 50);
    
    meters.forEach(meter => {
      devices[meter.id] = generateDevice(meter.id, meter.name, meter.type,
        `Podium Building - ${floorName}`, Math.random() * 50 + 10);
    });
  });
  
  // Add car park (Tower C) devices
  Object.entries(CAR_PARK_BUILDING_METERS).forEach(([floorKey, meters]) => {
    const floorName = floorKey === 'floorB' ? 'Floor B' : `Floor ${floorKey.replace('floor', '')}`;
    const floorId = `tower_c_${floorKey}`;
    
    devices[floorId] = generateDevice(floorId, `Car Park Building - ${floorName}`, 'floor_group', 'Car Park Building',
      Math.random() * 100 + 50);
    
    meters.forEach(meter => {
      devices[meter.id] = generateDevice(meter.id, meter.name, meter.type,
        `Car Park Building - ${floorName}`, Math.random() * 50 + 10);
    });
  });
  
  return devices;
};

// Mock devices data
export const mockDeviceDetailsMap: Record<string, DeviceDetails> = generateMockDevicesFromMeterConfig();

// Original mock devices (kept for reference but not used)
const oldMockDeviceDetailsMap: Record<string, DeviceDetails> = {
  // Main meters
  'main': generateDeviceDetails('main', 'Main Meter', 'power_meter', 'Main Electrical Room', 2800),
  'tower_a': generateDeviceDetails('tower_a', 'Tower Building Main', 'power_meter', 'Tower Building - B1', 1200),
  'tower_b': generateDeviceDetails('tower_b', 'Podium Building Main', 'power_meter', 'Podium Building - B1', 950),
  'tower_c': generateDeviceDetails('tower_c', 'Car Park Building Main', 'power_meter', 'Car Park Building - B1', 450),
  
  // Chiller Plant meters
  'chiller_plant': generateDeviceDetails('chiller_plant', 'Chiller Plant', 'power_meter', 'B3 - Central Plant', 850),
  'chiller-1': generateDeviceDetails('chiller-1', 'Chiller 1 (450RT)', 'chiller', 'B3 - Chiller Room', 420),
  'chiller-2': generateDeviceDetails('chiller-2', 'Chiller 2 (450RT)', 'chiller', 'B3 - Chiller Room', 430),
  'cooling-tower-1': generateDeviceDetails('cooling-tower-1', 'Cooling Tower 1', 'cooling_tower', 'Roof', 45),
  'cooling-tower-2': generateDeviceDetails('cooling-tower-2', 'Cooling Tower 2', 'cooling_tower', 'Roof', 45, false), // Offline
  'chilled-water-pump-1': generateDeviceDetails('chilled-water-pump-1', 'CHW Pump 1', 'pump', 'B3 - Pump Room', 30),
  'chilled-water-pump-2': generateDeviceDetails('chilled-water-pump-2', 'CHW Pump 2', 'pump', 'B3 - Pump Room', 30),
  
  // Air Distribution System
  'air_distribution_system': generateDeviceDetails('air_distribution_system', 'Air Distribution System', 'power_meter', 'Multiple Locations', 620),
  
  // Tower A AHU meters
  'tower-a-ahu': generateDeviceDetails('tower-a-ahu', 'Tower Building AHU Main', 'power_meter', 'Tower Building - Multiple Floors', 280),
  'tower-a-ahu-floor-1': generateDeviceDetails('tower-a-ahu-floor-1', 'Tower A - Floor 1 AHU', 'ahu', 'Tower A - Floor 1', 25),
  'tower-a-ahu-floor-2': generateDeviceDetails('tower-a-ahu-floor-2', 'Tower A - Floor 2 AHU', 'ahu', 'Tower A - Floor 2', 25),
  'tower-a-ahu-floor-3': generateDeviceDetails('tower-a-ahu-floor-3', 'Tower A - Floor 3 AHU', 'ahu', 'Tower A - Floor 3', 25),
  'tower-a-ahu-floor-4': generateDeviceDetails('tower-a-ahu-floor-4', 'Tower A - Floor 4 AHU', 'ahu', 'Tower A - Floor 4', 25),
  'tower-a-ahu-floor-5': generateDeviceDetails('tower-a-ahu-floor-5', 'Tower A - Floor 5 AHU', 'ahu', 'Tower A - Floor 5', 25),
  'tower-a-ahu-floor-6': generateDeviceDetails('tower-a-ahu-floor-6', 'Tower A - Floor 6 AHU', 'ahu', 'Tower A - Floor 6', 25),
  'tower-a-ahu-floor-7': generateDeviceDetails('tower-a-ahu-floor-7', 'Tower A - Floor 7 AHU', 'ahu', 'Tower A - Floor 7', 25),
  'tower-a-ahu-floor-8': generateDeviceDetails('tower-a-ahu-floor-8', 'Tower A - Floor 8 AHU', 'ahu', 'Tower A - Floor 8', 25),
  'tower-a-ahu-floor-9': generateDeviceDetails('tower-a-ahu-floor-9', 'Tower A - Floor 9 AHU', 'ahu', 'Tower A - Floor 9', 25),
  'tower-a-ahu-floor-10': generateDeviceDetails('tower-a-ahu-floor-10', 'Tower A - Floor 10 AHU', 'ahu', 'Tower A - Floor 10', 25, false), // Offline
  
  // Tower B AHU meters
  'tower-b-ahu': generateDeviceDetails('tower-b-ahu', 'Podium Building AHU Main', 'power_meter', 'Podium Building - Multiple Floors', 200),
  'tower-b-ahu-floor-1': generateDeviceDetails('tower-b-ahu-floor-1', 'Tower B - Floor 1 AHU', 'ahu', 'Tower B - Floor 1', 22),
  'tower-b-ahu-floor-2': generateDeviceDetails('tower-b-ahu-floor-2', 'Tower B - Floor 2 AHU', 'ahu', 'Tower B - Floor 2', 22),
  'tower-b-ahu-floor-3': generateDeviceDetails('tower-b-ahu-floor-3', 'Tower B - Floor 3 AHU', 'ahu', 'Tower B - Floor 3', 22),
  'tower-b-ahu-floor-4': generateDeviceDetails('tower-b-ahu-floor-4', 'Tower B - Floor 4 AHU', 'ahu', 'Tower B - Floor 4', 22),
  'tower-b-ahu-floor-5': generateDeviceDetails('tower-b-ahu-floor-5', 'Tower B - Floor 5 AHU', 'ahu', 'Tower B - Floor 5', 22),
  'tower-b-ahu-floor-6': generateDeviceDetails('tower-b-ahu-floor-6', 'Tower B - Floor 6 AHU', 'ahu', 'Tower B - Floor 6', 22),
  'tower-b-ahu-floor-7': generateDeviceDetails('tower-b-ahu-floor-7', 'Tower B - Floor 7 AHU', 'ahu', 'Tower B - Floor 7', 22),
  'tower-b-ahu-floor-8': generateDeviceDetails('tower-b-ahu-floor-8', 'Tower B - Floor 8 AHU', 'ahu', 'Tower B - Floor 8', 22),
  
  // Tower C AHU meters
  'tower-c-ahu': generateDeviceDetails('tower-c-ahu', 'Car Park Building AHU Main', 'power_meter', 'Car Park Building - Multiple Floors', 140),
  'tower-c-ahu-floor-b': generateDeviceDetails('tower-c-ahu-floor-b', 'Tower C - Floor B AHU', 'ahu', 'Tower C - Floor B', 20),
  'tower-c-ahu-floor-1': generateDeviceDetails('tower-c-ahu-floor-1', 'Tower C - Floor 1 AHU', 'ahu', 'Tower C - Floor 1', 20),
  'tower-c-ahu-floor-2': generateDeviceDetails('tower-c-ahu-floor-2', 'Tower C - Floor 2 AHU', 'ahu', 'Tower C - Floor 2', 20),
  'tower-c-ahu-floor-3': generateDeviceDetails('tower-c-ahu-floor-3', 'Tower C - Floor 3 AHU', 'ahu', 'Tower C - Floor 3', 20),
  'tower-c-ahu-floor-4': generateDeviceDetails('tower-c-ahu-floor-4', 'Tower C - Floor 4 AHU', 'ahu', 'Tower C - Floor 4', 20),
  'tower-c-ahu-floor-5': generateDeviceDetails('tower-c-ahu-floor-5', 'Tower C - Floor 5 AHU', 'ahu', 'Tower C - Floor 5', 20),
  'tower-c-ahu-floor-6': generateDeviceDetails('tower-c-ahu-floor-6', 'Tower C - Floor 6 AHU', 'ahu', 'Tower C - Floor 6', 20),
  
  // Lighting & Power
  'light_and_power': generateDeviceDetails('light_and_power', 'Lighting & Power Main', 'power_meter', 'All Buildings', 340),
  
  // Tower A floor lighting
  'tower-a-floor-1-lighting': generateDeviceDetails('tower-a-floor-1-lighting', 'Tower A - Floor 1 Lighting', 'lighting', 'Tower A - Floor 1', 15),
  'tower-a-floor-2-lighting': generateDeviceDetails('tower-a-floor-2-lighting', 'Tower A - Floor 2 Lighting', 'lighting', 'Tower A - Floor 2', 15),
  'tower-a-floor-3-lighting': generateDeviceDetails('tower-a-floor-3-lighting', 'Tower A - Floor 3 Lighting', 'lighting', 'Tower A - Floor 3', 15),
  'tower-a-floor-4-lighting': generateDeviceDetails('tower-a-floor-4-lighting', 'Tower A - Floor 4 Lighting', 'lighting', 'Tower A - Floor 4', 15),
  'tower-a-floor-5-lighting': generateDeviceDetails('tower-a-floor-5-lighting', 'Tower A - Floor 5 Lighting', 'lighting', 'Tower A - Floor 5', 15),
  
  // Others (Elevators, Escalators, etc.)
  'elevator-escalator': generateDeviceDetails('elevator-escalator', 'Elevators & Escalators', 'power_meter', 'All Buildings', 60),
  
  // EV Chargers
  'ev_charger': generateDeviceDetails('ev_charger', 'EV Chargers', 'power_meter', 'B2 Parking', 45),
  'ev-charger-1': generateDeviceDetails('ev-charger-1', 'EV Charger Bay 1', 'ev_charger', 'B2 Parking - Bay 1', 11),
  'ev-charger-2': generateDeviceDetails('ev-charger-2', 'EV Charger Bay 2', 'ev_charger', 'B2 Parking - Bay 2', 11),
  'ev-charger-3': generateDeviceDetails('ev-charger-3', 'EV Charger Bay 3', 'ev_charger', 'B2 Parking - Bay 3', 11),
  'ev-charger-4': generateDeviceDetails('ev-charger-4', 'EV Charger Bay 4', 'ev_charger', 'B2 Parking - Bay 4', 11, false), // Offline
  
  // Tenant meters (sample)
  'tenant-t1-f5-001': generateDeviceDetails('tenant-t1-f5-001', 'SET Member 001 - Tower A Floor 5', 'power_meter', 'Tower A - Floor 5', 25),
  'tenant-t1-f5-002': generateDeviceDetails('tenant-t1-f5-002', 'SET Member 002 - Tower A Floor 5', 'power_meter', 'Tower A - Floor 5', 30),
  'tenant-t2-f3-001': generateDeviceDetails('tenant-t2-f3-001', 'SET Member 003 - Tower B Floor 3', 'power_meter', 'Tower B - Floor 3', 20),
  'tenant-t2-f3-002': generateDeviceDetails('tenant-t2-f3-002', 'SET Member 004 - Tower B Floor 3', 'power_meter', 'Tower B - Floor 3', 15),
};

// Relation ID counter
let relationIdCounter = 1;

// Helper to create a device relation
const createRelation = (parentId: string, childId: string): DeviceRelation => ({
  id: relationIdCounter++,
  tag: null,
  parent_device_id: parentId,
  child_device_id: childId,
});

// Helper to create device relations
const createRelations = (parentId: string, childIds: string[]): DeviceRelation[] => {
  return childIds.map(childId => createRelation(parentId, childId));
};

// Generate relations from meter configurations
const generateMockRelationsFromMeterConfig = (): Record<string, DeviceRelation[]> => {
  const relations: Record<string, DeviceRelation[]> = {};
  let relationId = 1;
  
  // Main -> Groups
  relations['main'] = Object.keys(MAIN_METER_LIST).map(groupKey => ({
    id: relationId++,
    tag: null,
    parent_device_id: 'main',
    child_device_id: groupKey
  }));
  
  // Groups -> Meters
  Object.entries(MAIN_METER_LIST).forEach(([groupKey, group]) => {
    relations[groupKey] = group.meters.map(meter => ({
      id: relationId++,
      tag: null,
      parent_device_id: groupKey,
      child_device_id: meter.id
    }));
  });
  
  // Tower A relations
  relations['tower_a'] = Object.keys(TOWER_BUILDING_METERS).map(floorKey => ({
    id: relationId++,
    tag: null,
    parent_device_id: 'tower_a',
    child_device_id: `tower_a_${floorKey}`
  }));
  
  Object.entries(TOWER_BUILDING_METERS).forEach(([floorKey, meters]) => {
    const floorId = `tower_a_${floorKey}`;
    relations[floorId] = meters.map(meter => ({
      id: relationId++,
      tag: null,
      parent_device_id: floorId,
      child_device_id: meter.id
    }));
  });
  
  // Tower B relations
  relations['tower_b'] = Object.keys(PODIUM_BUILDING_METERS).map(floorKey => ({
    id: relationId++,
    tag: null,
    parent_device_id: 'tower_b',
    child_device_id: `tower_b_${floorKey}`
  }));
  
  Object.entries(PODIUM_BUILDING_METERS).forEach(([floorKey, meters]) => {
    const floorId = `tower_b_${floorKey}`;
    relations[floorId] = meters.map(meter => ({
      id: relationId++,
      tag: null,
      parent_device_id: floorId,
      child_device_id: meter.id
    }));
  });
  
  // Tower C relations
  relations['tower_c'] = Object.keys(CAR_PARK_BUILDING_METERS).map(floorKey => ({
    id: relationId++,
    tag: null,
    parent_device_id: 'tower_c',
    child_device_id: `tower_c_${floorKey}`
  }));
  
  Object.entries(CAR_PARK_BUILDING_METERS).forEach(([floorKey, meters]) => {
    const floorId = `tower_c_${floorKey}`;
    relations[floorId] = meters.map(meter => ({
      id: relationId++,
      tag: null,
      parent_device_id: floorId,
      child_device_id: meter.id
    }));
  });
  
  return relations;
};

// Device relations (parent-child hierarchy)
export const mockDeviceRelations: Record<string, DeviceRelation[]> = generateMockRelationsFromMeterConfig();

// Original relations (kept for reference but not used)
const oldMockDeviceRelations: Record<string, DeviceRelation[]> = {
  'main': createRelations('main', ['tower_a', 'tower_b', 'tower_c', 'chiller_plant', 'light_and_power', 'elevator-escalator', 'ev_charger', 'data_center_and_others']),
  'tower_a': createRelations('tower_a', ['tower-a-ahu', 'tenant-t1-f5-001', 'tenant-t1-f5-002']),
  'tower_b': createRelations('tower_b', ['tower-b-ahu', 'tenant-t2-f3-001', 'tenant-t2-f3-002']),
  'tower_c': createRelations('tower_c', ['tower-c-ahu']),
  'chiller_plant': createRelations('chiller_plant', ['chiller-1', 'chiller-2', 'cooling-tower-1', 'cooling-tower-2', 'chilled-water-pump-1', 'chilled-water-pump-2']),
  'air_distribution_system': createRelations('air_distribution_system', ['tower-a-ahu', 'tower-b-ahu', 'tower-c-ahu']),
  'tower-a-ahu': createRelations('tower-a-ahu', [
    'tower-a-ahu-floor-1', 'tower-a-ahu-floor-2', 'tower-a-ahu-floor-3', 'tower-a-ahu-floor-4', 'tower-a-ahu-floor-5',
    'tower-a-ahu-floor-6', 'tower-a-ahu-floor-7', 'tower-a-ahu-floor-8', 'tower-a-ahu-floor-9', 'tower-a-ahu-floor-10'
  ]),
  'tower-b-ahu': createRelations('tower-b-ahu', [
    'tower-b-ahu-floor-1', 'tower-b-ahu-floor-2', 'tower-b-ahu-floor-3', 'tower-b-ahu-floor-4',
    'tower-b-ahu-floor-5', 'tower-b-ahu-floor-6', 'tower-b-ahu-floor-7', 'tower-b-ahu-floor-8'
  ]),
  'tower-c-ahu': createRelations('tower-c-ahu', [
    'tower-c-ahu-floor-b', 'tower-c-ahu-floor-1', 'tower-c-ahu-floor-2', 'tower-c-ahu-floor-3',
    'tower-c-ahu-floor-4', 'tower-c-ahu-floor-5', 'tower-c-ahu-floor-6'
  ]),
  'light_and_power': createRelations('light_and_power', [
    'tower-a-floor-1-lighting', 'tower-a-floor-2-lighting', 'tower-a-floor-3-lighting', 
    'tower-a-floor-4-lighting', 'tower-a-floor-5-lighting'
  ]),
  'ev_charger': createRelations('ev_charger', ['ev-charger-1', 'ev-charger-2', 'ev-charger-3', 'ev-charger-4']),
};

// Zone ID counter
let zoneIdCounter = 1;

// Generate zones from meter configurations
const generateMockZones = (): Zone[] => {
  const zones: Zone[] = [];
  
  // Tower A zones
  Object.keys(TOWER_BUILDING_METERS).forEach(floorKey => {
    zones.push({
      id: zoneIdCounter++,
      device_id: `tower_a_${floorKey}`,
      name: `Tower A ${floorKey === 'floorB' ? 'Floor B' : 
                       floorKey === 'dataCenter' ? 'Data Center' : 
                       floorKey === 'roof' ? 'Roof' :
                       `Floor ${floorKey.replace('floor', '')}`}`,
      metadata: { floor: floorKey, tower: 'A' },
      devices: []
    });
  });
  
  // Tower B zones  
  Object.keys(PODIUM_BUILDING_METERS).forEach(floorKey => {
    zones.push({
      id: zoneIdCounter++,
      device_id: `tower_b_${floorKey}`,
      name: `Tower B Floor ${floorKey.replace('floor', '')}`,
      metadata: { floor: floorKey, tower: 'B' },
      devices: []
    });
  });
  
  // Tower C zones
  Object.keys(CAR_PARK_BUILDING_METERS).forEach(floorKey => {
    zones.push({
      id: zoneIdCounter++,
      device_id: `tower_c_${floorKey}`,
      name: `Tower C ${floorKey === 'floorB' ? 'Floor B' : `Floor ${floorKey.replace('floor', '')}`}`,
      metadata: { floor: floorKey, tower: 'C' },
      devices: []
    });
  });
  
  return zones;
};

// Mock zones
export const mockZones: Zone[] = generateMockZones();

// Helper to refresh timestamps for a device
const refreshDeviceTimestamps = (device: DeviceDetails): DeviceDetails => {
  const freshTimestamp = currentTimestamp();
  
  // Create a deep copy and update all timestamps
  const refreshedDevice = JSON.parse(JSON.stringify(device));
  
  // Update all timestamps in latest_data
  if (refreshedDevice.latest_data) {
    Object.keys(refreshedDevice.latest_data).forEach(key => {
      if (refreshedDevice.latest_data[key] && refreshedDevice.latest_data[key].updated_at) {
        refreshedDevice.latest_data[key].updated_at = freshTimestamp;
      }
    });
  }
  
  return refreshedDevice;
};

// Get all devices
export const getAllMockDevices = (): DeviceDetails[] => {
  // Trigger refresh of all device data (power values, timestamps, etc.)
  refreshAllDeviceData(mockDeviceDetailsMap);
  
  // Return all devices (already refreshed by refreshAllDeviceData)
  const allDevices = Object.values(mockDeviceDetailsMap);
  
  // Calculate data center consumption as virtual meter
  const totalMainPower = mockDeviceDetailsMap['main']?.latest_data?.power?.value || 2800;
  
  // Sum all sub-meters (excluding main meter itself)
  const sumOfSubMeters = allDevices
    .filter(d => d.device_id !== 'main' && d.device_id !== 'data_center_and_others')
    .reduce((sum, device) => sum + (device.latest_data?.power?.value || 0), 0);
  
  // Data center consumption = total - sum of all other meters
  const dataCenterPower = Math.max(0, totalMainPower - sumOfSubMeters);
  
  // Create virtual data center meter
  const dataCenterMeter: DeviceDetails = {
    id: 999,
    device_id: 'data_center_and_others',
    name: 'Data Center & Others (Calculated)',
    model: 'virtual_meter',
    metadata: {
      location: 'Multiple Locations',
      type: 'virtual',
      calculation: 'total - sum_of_meters',
      building: 'main'
    },
    autopilot: {},
    latest_data: {
      status: { value: 1, updated_at: currentTimestamp() },
      power: { value: Math.round(dataCenterPower * 10) / 10, updated_at: currentTimestamp() },
      power_total: { value: Math.round(dataCenterPower * 10) / 10, updated_at: currentTimestamp() }, // Backward compatibility
      voltage_l1: { value: 220, updated_at: currentTimestamp() }, // Real API field
      voltage_l2: { value: 220, updated_at: currentTimestamp() }, // Real API field
      voltage_l3: { value: 220, updated_at: currentTimestamp() }, // Real API field
      voltage_ln_average: { value: 220, updated_at: currentTimestamp() },
      current_l1: { value: Math.round((dataCenterPower / 3 / 220 / 0.9) * 10) / 10, updated_at: currentTimestamp() },
      current_l2: { value: Math.round((dataCenterPower / 3 / 220 / 0.9) * 10) / 10, updated_at: currentTimestamp() },
      current_l3: { value: Math.round((dataCenterPower / 3 / 220 / 0.9) * 10) / 10, updated_at: currentTimestamp() },
      current: { value: Math.round((dataCenterPower / (380 * Math.sqrt(3) * 0.9)) * 10) / 10, updated_at: currentTimestamp() },
      current_total: { value: Math.round((dataCenterPower / 220 / 0.9) * 10) / 10, updated_at: currentTimestamp() },
      power_factor: { value: 0.9, updated_at: currentTimestamp() },
      power_factor_total: { value: 0.9, updated_at: currentTimestamp() }, // Backward compatibility
      reactive_power: { value: Math.round(dataCenterPower * Math.tan(Math.acos(0.9)) * 10) / 10, updated_at: currentTimestamp() },
      reactive_power_total: { value: Math.round(dataCenterPower * Math.tan(Math.acos(0.9)) * 10) / 10, updated_at: currentTimestamp() },
      apparent_power: { value: Math.round((dataCenterPower / 0.9) * 10) / 10, updated_at: currentTimestamp() },
      apparent_power_total: { value: Math.round((dataCenterPower / 0.9) * 10) / 10, updated_at: currentTimestamp() },
      cumulative_energy: { value: Math.round(randomValue(50000, 0.5)), updated_at: currentTimestamp() },
      energy_import: { value: Math.round(randomValue(50000, 0.5)), updated_at: currentTimestamp() },
    },
  };
  
  return [...allDevices, dataCenterMeter];
};

// Get devices by model
export const getMockDevicesByModel = (model: string): DeviceRelation[] => {
  // For power_meter model, return all top-level relations
  if (model === 'power_meter') {
    // Return relations where the child is a top-level meter (main, tower_a, tower_b, tower_c)
    const topLevelMeters = ['main', 'tower_a', 'tower_b', 'tower_c'];
    const relations: DeviceRelation[] = [];
    let id = 1;
    
    // Add main as root with no parent (or self-parent)
    relations.push({
      id: id++,
      tag: null,
      parent_device_id: '',
      child_device_id: 'main'
    });
    
    // Add towers as children of main for navigation
    topLevelMeters.slice(1).forEach(meterId => {
      relations.push({
        id: id++,
        tag: null,
        parent_device_id: 'main',
        child_device_id: meterId
      });
    });
    
    return relations;
  }
  
  const devices = Object.values(mockDeviceDetailsMap).filter(d => d.model === model);
  const relations: DeviceRelation[] = [];
  
  // Find relations for these devices
  devices.forEach(device => {
    // Search through all relations to find where this device is a child
    Object.entries(mockDeviceRelations).forEach(([parentId, childRelations]) => {
      childRelations.forEach(relation => {
        if (relation.child_device_id === device.device_id) {
          relations.push({
            ...relation,
            id: relation.id || Math.random()
          });
        }
      });
    });
  });
  
  return relations;
};

// Start automatic refresh of mock data when module loads
if (typeof window !== 'undefined') {
  // Start refresh every 30 seconds to keep data fresh
  startMockDataRefresh(mockDeviceDetailsMap, 30000);
}