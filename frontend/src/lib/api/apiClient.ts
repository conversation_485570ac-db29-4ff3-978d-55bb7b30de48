import axios from 'axios';

// Create a base axios instance with common configuration
export const apiClient = axios.create({
  baseURL: '/api', // This should match the proxy configured in vite.config.ts
  headers: {
    'Content-Type': 'application/json',
  },
});

// Response interceptor for handling common error cases
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // You can implement global error handling here
    // For example, handle authentication errors, server errors, etc.
    
    // For now, just log and pass the error to the caller
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// Request interceptor for adding authentication tokens
apiClient.interceptors.request.use(
  (config) => {
    // TODO: Implement proper authentication for Django API
    // Example: Get token from local storage/state management
    // const token = localStorage.getItem('djangoAuthToken'); 
    // if (token && config.headers) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
