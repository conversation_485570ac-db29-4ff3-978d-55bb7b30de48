import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import type { BuildingId } from '../../types';

interface AppState {
  selectedBuilding: BuildingId;
  theme: 'light' | 'dark';
  notifications: number;
}

type AppAction =
  | { type: 'SET_BUILDING'; payload: BuildingId }
  | { type: 'SET_THEME'; payload: 'light' | 'dark' }
  | { type: 'SET_NOTIFICATIONS'; payload: number };

const initialState: AppState = {
  selectedBuilding: 'A',
  theme: 'light',
  notifications: 0,
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
}>({
  state: initialState,
  dispatch: () => null,
});

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_BUILDING':
      return { ...state, selectedBuilding: action.payload };
    case 'SET_THEME':
      return { ...state, theme: action.payload };
    case 'SET_NOTIFICATIONS':
      return { ...state, notifications: action.payload };
    default:
      return state;
  }
}

export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}