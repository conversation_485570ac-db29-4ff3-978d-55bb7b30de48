import React, { createContext, useContext, useState, ReactNode } from 'react';
import { REFRESH_INTERVALS } from '../constants/refresh';

interface AutoRefreshContextType {
  isAutoRefreshEnabled: boolean;
  toggleAutoRefresh: () => void;
  refreshInterval: number;
}

const AutoRefreshContext = createContext<AutoRefreshContextType | undefined>(undefined);

export function AutoRefreshProvider({ children }: { children: ReactNode }) {
  const [isAutoRefreshEnabled, setIsAutoRefreshEnabled] = useState(true);
  const refreshInterval = REFRESH_INTERVALS.default;

  const toggleAutoRefresh = () => {
    setIsAutoRefreshEnabled(prev => !prev);
  };

  return (
    <AutoRefreshContext.Provider value={{ isAutoRefreshEnabled, toggleAutoRefresh, refreshInterval }}>
      {children}
    </AutoRefreshContext.Provider>
  );
}

export function useAutoRefreshContext() {
  const context = useContext(AutoRefreshContext);
  if (context === undefined) {
    throw new Error('useAutoRefreshContext must be used within an AutoRefreshProvider');
  }
  return context;
}
