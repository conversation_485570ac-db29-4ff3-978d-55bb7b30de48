import { useState, useEffect, useRef } from 'react';
import { MeterType } from '@/types';
import {
  generatePowerDemandSeries,
  generateComparisonSeries,
  generateMonthlyEnergy,
  generatePowerQuality,
  generateMeterStatus,
} from './meterData';

// Import refresh intervals from constants
import { REFRESH_INTERVALS } from '../constants/refresh';

// Default refresh interval in milliseconds (15 seconds)
const DEFAULT_REFRESH_INTERVAL = REFRESH_INTERVALS.charts;

// Reduced simulated API latency (was 500ms)
const SIMULATED_API_LATENCY = 150;

export function useMockMeterTimeSeries(meterId: string, date: Date, type: MeterType | string, refreshInterval: number = DEFAULT_REFRESH_INTERVAL) {
  const meterType = typeof type === 'string' ? (type as MeterType) : type;
  const [data, setData] = useState<{ time: string; demand: number }[]>([]);
  const [comparison, setComparison] = useState<{ time: string; demand: number }[]>([]);
  const [loading, setLoading] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<number>(0);

  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Initial data load with simulated API latency
    const loadData = () => {
      // Only update if enough time has passed or it's the first load
      const now = Date.now();
      if (now - lastUpdateRef.current >= refreshInterval || lastUpdateRef.current === 0) {
        setLoading(true);
        // Generate data immediately but delay state update to simulate API call
        const newData = generatePowerDemandSeries(meterId, date, meterType);
        const newComparison = generateComparisonSeries(meterId, date, meterType);

        setTimeout(() => {
          setData(newData);
          setComparison(newComparison);
          setLoading(false);
          lastUpdateRef.current = now;
        }, SIMULATED_API_LATENCY);
      }
    };

    // Initial load
    loadData();

    // Set up refresh interval if enabled
    if (refreshInterval > 0) {
      intervalRef.current = setInterval(loadData, refreshInterval);
    }

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [meterId, date, type, refreshInterval, meterType]);

  return { data, comparison, loading };
}

export function useMockMonthlyEnergy(meterId: string, year: number, type: MeterType | string, refreshInterval: number = DEFAULT_REFRESH_INTERVAL) {
  const meterType = typeof type === 'string' ? (type as MeterType) : type;
  const [monthly, setMonthly] = useState<number[]>([]);
  const [loading, setLoading] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<number>(0);

  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Initial data load with simulated API latency
    const loadData = () => {
      // Only update if enough time has passed or it's the first load
      const now = Date.now();
      if (now - lastUpdateRef.current >= refreshInterval || lastUpdateRef.current === 0) {
        setLoading(true);
        // Generate data immediately but delay state update to simulate API call
        const newMonthly = generateMonthlyEnergy(year, meterId, meterType);

        setTimeout(() => {
          setMonthly(newMonthly);
          setLoading(false);
          lastUpdateRef.current = now;
        }, SIMULATED_API_LATENCY);
      }
    };

    // Initial load
    loadData();

    // Set up refresh interval if enabled
    if (refreshInterval > 0) {
      intervalRef.current = setInterval(loadData, refreshInterval);
    }

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [meterId, year, type, refreshInterval, meterType]);

  return { monthly, loading };
}

export function useMockPowerQuality(meterId: string, refreshInterval: number = DEFAULT_REFRESH_INTERVAL) {
  const [pq, setPQ] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<number>(0);

  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Initial data load
    const loadData = () => {
      // Only update if enough time has passed or it's the first load
      const now = Date.now();
      if (now - lastUpdateRef.current >= refreshInterval || lastUpdateRef.current === 0) {
        setLoading(true);
        // Generate data immediately but delay state update to simulate API call
        const newPQ = generatePowerQuality(meterId);

        setTimeout(() => {
          setPQ(newPQ);
          setLoading(false);
          lastUpdateRef.current = now;
        }, SIMULATED_API_LATENCY);
      }
    };

    // Initial load
    loadData();

    // Set up refresh interval if enabled
    if (refreshInterval > 0) {
      intervalRef.current = setInterval(loadData, refreshInterval);
    }

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [meterId, refreshInterval]);

  return { pq, loading };
}

export function useMockMeterStatus(meterId: string, refreshInterval: number = DEFAULT_REFRESH_INTERVAL) {
  const [status, setStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastUpdateRef = useRef<number>(0);

  useEffect(() => {
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Initial data load
    const loadData = () => {
      // Only update if enough time has passed or it's the first load
      const now = Date.now();
      if (now - lastUpdateRef.current >= refreshInterval || lastUpdateRef.current === 0) {
        setLoading(true);
        // Generate data immediately but delay state update to simulate API call
        const newStatus = generateMeterStatus(meterId);

        setTimeout(() => {
          setStatus(newStatus);
          setLoading(false);
          lastUpdateRef.current = now;
        }, SIMULATED_API_LATENCY);
      }
    };

    // Initial load
    loadData();

    // Set up refresh interval if enabled
    if (refreshInterval > 0) {
      intervalRef.current = setInterval(loadData, refreshInterval);
    }

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [meterId, refreshInterval]);

  return { status, loading };
}
