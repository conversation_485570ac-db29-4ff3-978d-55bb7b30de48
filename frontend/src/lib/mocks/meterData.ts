import { MeterType } from '../../types/meters';

// Deterministic pseudo-random number generator
function seededRandom(seed: string) {
  let h = 2166136261 >>> 0;
  for (let i = 0; i < seed.length; i++) {
    h ^= seed.charCodeAt(i);
    h += (h << 1) + (h << 4) + (h << 7) + (h << 8) + (h << 24);
  }
  return () => {
    h ^= h << 13;
    h ^= h >> 17;
    h ^= h << 5;
    return ((h >>> 0) / 4294967295);
  };
}

const PEAKS: Record<string, number> = {
  lighting: 80,
  airSide: 140,
  chillerPlant: 220,
  equipment: 60,
  evCharger: 90,
  others: 40,
};

export function generatePowerDemandSeries(meterId: string, date: Date, type: MeterType) {
  const rand = seededRandom(meterId + date.toISOString().slice(0, 10));
  const peak = PEAKS[type] || 60;
  const series = [];
  for (let i = 0; i < 96; i++) {
    const hour = Math.floor(i / 4);
    let base = 0.3;
    if (hour >= 6 && hour < 10) base = 0.3 + 0.175 * (hour - 5); // morning ramp
    else if (hour >= 10 && hour < 17) base = 0.9 + 0.1 * rand(); // workday plateau
    else if (hour >= 17 && hour < 21) base = 0.9 - 0.1 * (hour - 16) / 4; // evening decline
    else if (hour >= 21 && hour < 24) base = 0.4;
    const noise = (rand() - 0.5) * 0.06;
    const value = Math.max(0, peak * (base + noise));
    series.push({
      time: `${String(hour).padStart(2, '0')}:${String((i % 4) * 15).padStart(2, '0')}`,
      demand: parseFloat(value.toFixed(2)),
    });
  }
  return series;
}

export function generateComparisonSeries(meterId: string, date: Date, type: MeterType) {
  const prev = new Date(date);
  prev.setDate(date.getDate() - 1);
  const baseSeries = generatePowerDemandSeries(meterId, prev, type);
  const rand = seededRandom(meterId + prev.toISOString().slice(0, 10) + 'cmp');
  return baseSeries.map(pt => ({
    ...pt,
    demand: parseFloat((pt.demand * (0.97 + rand() * 0.06)).toFixed(2)),
  }));
}

export function generateMonthlyEnergy(year: number, meterId: string, type: MeterType) {
  const base = PEAKS[type] * 24 * 30 * 0.8; // base monthly kWh
  const rand = seededRandom(meterId + year);
  return Array.from({ length: 12 }, (_, m) => {
    let season = 1;
    if (m === 3 || m === 4) season = 1.15; // Apr/May hotter
    if (m === 11 || m === 0) season = 0.9; // Dec/Jan lower
    const noise = 0.92 + rand() * 0.16;
    return parseFloat((base * season * noise).toFixed(0));
  });
}

export function generatePowerQuality(meterId: string) {
  const rand = seededRandom(meterId + 'pq');
  return {
    voltage: parseFloat((215 + rand() * 20).toFixed(1)),
    current: parseFloat((rand() * 50).toFixed(1)),
    powerFactor: parseFloat((0.85 + rand() * 0.14).toFixed(2)),
    frequency: parseFloat((49.9 + rand() * 0.2).toFixed(2)),
    thd: parseFloat((2 + rand() * 3).toFixed(1)),
  };
}

export function generateMeterStatus(meterId: string) {
  const rand = seededRandom(meterId + 'status');
  const online = rand() > 0.06;

  // Generate a stable reading based on meterId and current hour
  // This ensures the value doesn't change too frequently but still varies over time
  const hour = new Date().getHours();
  const readingRand = seededRandom(meterId + hour.toString());
  const baseReading = 300 + readingRand() * 600; // Between 300 and 900

  return {
    online,
    lastUpdate: online ? new Date().toISOString() : new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    alarm: online && rand() < 0.01,
    lastReading: parseFloat(baseReading.toFixed(1)),
  };
}
