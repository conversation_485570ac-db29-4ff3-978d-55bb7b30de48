import { Snowflake, Wind, Server, Lightbulb, Plug, ArrowUpDown, Droplets, Fan, PlugZap, Layers, Building2, Users, Zap } from 'lucide-react';

export const SYSTEM_ICONS = {
  chillerPlant: Snowflake,
  airSide: Wind,
  light_power: Lightbulb,
  data_center_others: Server,
  evCharger: Plug,
  escalator_elevator: ArrowUpDown,
  tenant: Users,
  main: Zap,
  building: Building2,
  water: Droplets,
  fan: Fan,
  power: PlugZap,
  other: Layers
} as const;

export const COLORS = {
  primary: {
    blue: '#065BA9',
    white: '#FFFFFF',
    darkGrey: '#788796',
    black: '#212529',
  },
  background: {
    main: '#F9FAFF',
    lightGrey: '#EDEFF9',
    whiteBlue: '#DBE4FF',
  },
  status: {
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  consumption: {
    'no-data': '#9CA3AF',
    'low': '#10B981',    // <20 kWh
    'medium': '#F59E0B', // 20-40 kWh
    'high': '#EF4444',   // 40-60 kWh
    'critical': '#7F1D1D', // >60 kWh
  },
} as const;

export const BREAKPOINTS = {
  mobile: 768,
  tablet: 1199,
  desktop: 1200,
} as const;

export const UPDATE_INTERVALS = {
  energy: 5 * 60 * 1000,    // 5 minutes
  environmental: 15 * 60 * 1000,  // 15 minutes
  billing: 24 * 60 * 60 * 1000,  // 24 hours
} as const;

import { TOWER_A_CONFIG } from './config/buildings';

export const BUILDINGS = {
  A: {
    name: TOWER_A_CONFIG.name,
    floors: TOWER_A_CONFIG.floors,
    hasBasement: TOWER_A_CONFIG.hasBasement,
    floorUsage: Object.entries(TOWER_A_CONFIG.floorUsage).reduce((acc, [floor, { type }]) => ({
      ...acc,
      [floor]: type
    }), {})
  },
  B: {
    name: 'Podium Building',
    floors: 8,
    hasBasement: false,
    floorUsage: {
      7: 'Meeting Room',
      6: 'Meeting Room',
      5: 'Waiting Room',
      4: 'Empty Office',
      3: 'Sukri',
      2: 'Amazon',
      1: 'Counter TSD',
      B: 'Maruay Library'
    }
  },
  C: {
    name: 'Car Park Building',
    floors: 8,
    hasBasement: false,
    floorUsage: {
      7: 'Arena/Boxing Ring',
      6: 'Office',
      5: 'Office',
      4: 'Office',
      3: 'Office',
      2: 'Parking Area',
      1: 'Canteen',
      B: 'Inventory'
    }
  }
} as const;

export const CONSUMPTION_THRESHOLDS = {
  low: 20,     // 0-20 kWh
  medium: 40,  // 20-40 kWh
  high: 60,    // 40-60 kWh
  critical: 60, // >60 kWh
} as const;