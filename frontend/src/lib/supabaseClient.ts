import { createClient } from '@supabase/supabase-js'

// Configuration for self-hosted Supabase instance
// For local development and production (offline environment)
const supabaseUrl = 'http://localhost:8000'; // Self-hosted Supabase URL
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJld2VsYmZkaHdla2V5bmpkdnN5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM5MzM0NzYsImV4cCI6MjA1OTUwOTQ3Nn0.QXtFn9YE4c9BOaat7nqxUfY3XiW5NFTfu-ij3deh7og';

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Supabase URL and Anon Key are required.');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Enable Realtime subscriptions globally for the client
// This ensures all Realtime subscriptions use the same configuration
supabase.realtime.setAuth(supabaseAnonKey);

// Export the configured client
export default supabase;
