// Mock data for UI presentation - Focus on visual completeness
// No backend connectivity assumed.

import { Node, Edge } from 'reactflow';
import { LoadProfileData } from './config/load-profile'; // Assuming this type exists
import type { BuildingId } from '../types'; // Assuming this type exists

// --- Dashboard Metrics ---
export const mockDashboardMetrics = {
  totalConsumption: 328571.43, // kWh (based on 1.38M THB at 4.2 THB/kWh)
  peakDemand: 620, // kW (Matches Analytics Peak for consistency)
  energyCost: 4567.89, // Currency (e.g., USD, THB) - Example Value
  carbonFootprint: 328571.43 * 0.6, // kg CO2e (using 0.6 kg CO2e/kWh emissions factor)
};

export const mockTowerData: Record<BuildingId, { consumption: number; lastYearConsumption: number; percentChange: number; timeFrame: string }> = {
  A: { consumption: 5200, lastYearConsumption: 5000, percentChange: 4.0, timeFrame: 'weekly' },
  B: { consumption: 6100, lastYearConsumption: 6300, percentChange: -3.2, timeFrame: 'weekly' },
  C: { consumption: 4300, lastYearConsumption: 4150, percentChange: 3.6, timeFrame: 'weekly' },
};

// Use 'demand' as per LoadProfileData type
export const mockBuildingLoadProfile: LoadProfileData[] = [
  // Added more realistic hourly load profile data
  { time: '00:00', demand: 50 }, { time: '01:00', demand: 48 }, { time: '02:00', demand: 45 },
  { time: '03:00', demand: 46 }, { time: '04:00', demand: 55 }, { time: '05:00', demand: 70 },
  { time: '06:00', demand: 120 }, { time: '07:00', demand: 250 }, { time: '08:00', demand: 400 },
  { time: '09:00', demand: 550 }, { time: '10:00', demand: 600 }, { time: '11:00', demand: 620 }, // Peak demand
  { time: '12:00', demand: 610 }, { time: '13:00', demand: 590 }, { time: '14:00', demand: 580 },
  { time: '15:00', demand: 560 }, { time: '16:00', demand: 500 }, { time: '17:00', demand: 450 },
  { time: '18:00', demand: 350 }, { time: '19:00', demand: 280 }, { time: '20:00', demand: 200 },
  { time: '21:00', demand: 150 }, { time: '22:00', demand: 100 }, { time: '23:00', demand: 70 },
];

// --- Performance Analytics Data ---
// Specific structures for different views

// For Standard Bar Chart (e.g., Daily/Hourly Consumption - kWh)
interface StandardBarDataPoint { time: string; consumption: number; }
export const mockAnalyticsStandardBar: StandardBarDataPoint[] = Array.from({ length: 24 }, (_, i) => ({
  time: `${i}:00`,
  consumption: Math.max(10, 50 + Math.sin(i / 3) * 40 + Math.random() * 20), // Example kWh values
}));

// For Comparison Bar Chart (e.g., This Period vs Last Period - kWh)
interface ComparisonBarDataPoint { time: string; primaryValue: number; comparisonValue: number; } // Use keys from MEMORY[8354960a-229a-4a71-9861-482e4edf6df6]
export const mockAnalyticsComparisonBar: ComparisonBarDataPoint[] = Array.from({ length: 30 }, (_, i) => ({ // e.g., 30 days
  time: `Apr ${i + 1}`,
  primaryValue: Math.max(100, 200 + Math.sin(i / 5) * 50 + Math.random() * 30), // Example kWh
  comparisonValue: Math.max(90, 190 + Math.sin(i / 5) * 45 + Math.random() * 25), // Example kWh last period
}));

// For Cumulative Area Chart (e.g., Accumulated Consumption - kWh)
interface CumulativeAreaDataPoint { time: string; accumulatedValue: number; } // Use key from MEMORY[722be7f4-3259-4966-aee0-d71944579119]
let runningTotal = 0;
export const mockAnalyticsCumulativeArea: CumulativeAreaDataPoint[] = Array.from({ length: 30 }, (_, i) => { // e.g., 30 days
  runningTotal += Math.max(100, 200 + Math.sin(i / 5) * 50 + Math.random() * 30);
  return {
    time: `Apr ${i + 1}`,
    accumulatedValue: runningTotal,
  };
});

// Summary remains similar, using peak from load profile
export const mockAnalyticsSummary = {
  totalConsumption: { value: Math.round(runningTotal), unit: 'kWh', change: 5.2, trend: 'up' }, // Use calculated total
  peakDemand: { value: 620, unit: 'kW', time: '11:00', change: -2.1, trend: 'down' }, // Matches load profile peak
  averageLoad: { value: Math.round(mockBuildingLoadProfile.reduce((sum, p) => sum + p.demand, 0) / 24), unit: 'kW', change: 1.5, trend: 'up' }, // Calculated average
};

// --- Meter Diagram Data ---
// Based on MEMORY[27ce2567-9ebf-469f-99ee-746a7e3b405b] and MEMORY[e10d4f4b-affa-489f-a4e4-4a27afd5f9e5]

// Node positions will be calculated by Dagre layout, setting to 0,0 is fine initially.
// Adjusting power values slightly for presentation.
export const mockMeterNodes: Node[] = [
  { id: 'input', type: 'meterNode', position: { x: 0, y: 0 }, data: { label: 'Power Input', status: 'active', power: 1354, energy: 123456, isRoot: true } },
  { id: 'main', type: 'meterNode', position: { x: 0, y: 0 }, data: { label: 'Main Unit', status: 'active', power: 1354, energy: 123456 } },
  { id: 'tower_a', type: 'meterNode', position: { x: 0, y: 0 }, data: { label: 'Tower Building', status: 'active', power: 420, meterCount: 112 } }, // Adjusted power
  { id: 'tower_b', type: 'meterNode', position: { x: 0, y: 0 }, data: { label: 'Podium Building', status: 'active', power: 480, meterCount: 138 } }, // Adjusted power
  { id: 'tower_c', type: 'meterNode', position: { x: 0, y: 0 }, data: { label: 'Car Park Building', status: 'active', power: 330, meterCount: 95 } }, // Adjusted power
  { id: 'tenant', type: 'meterNode', position: { x: 0, y: 0 }, data: { label: 'Tenant', status: 'warning', power: 90, meterCount: 68 } }, // Adjusted power, Warning status
  { id: 'plant', type: 'meterNode', position: { x: 0, y: 0 }, data: { label: 'Plant', status: 'offline', power: 0, meterCount: 42 } }, // Disconnected status
  { id: 'datacenter', type: 'meterNode', position: { x: 0, y: 0 }, data: { label: 'Data Center', status: 'active', power: 34, meterCount: 0 } }, // Calculated power
];

export const mockMeterEdges: Edge[] = [
  { id: 'e_input_main', source: 'input', target: 'main', animated: true },
  { id: 'e_main_tower_a', source: 'main', target: 'tower_a', animated: true },
  { id: 'e_main_tower_b', source: 'main', target: 'tower_b', animated: true },
  { id: 'e_main_tower_c', source: 'main', target: 'tower_c', animated: true },
  { id: 'e_main_tenant', source: 'main', target: 'tenant', animated: true }, // Should be animated even if warning
  { id: 'e_main_plant', source: 'main', target: 'plant', animated: false }, // Not animated if offline
  { id: 'e_main_datacenter', source: 'main', target: 'datacenter', animated: true },
];

// Add any other necessary mock data structures below

// Mock data for System Breakdown Donut Chart - aligned with SystemBreakdownItem type
export const mockSystemBreakdownData = [
  { id: 'chillerPlant', name: 'Chiller Plant', value: 4200, color: '#0284C7', percentage: 42 }, // Adjusted value/percentage
  { id: 'airSide', name: 'Air Side', value: 2300, color: '#10B981', percentage: 23 }, // Adjusted value/percentage
  { id: 'equipment', name: 'Light & Power', value: 1800, color: '#8B5CF6', percentage: 18 }, // Adjusted value/percentage
  { id: 'dataCenterOthers', name: 'Data Center & Others', value: 1400, color: '#F59E0B', percentage: 14 }, // Combined Data Center and Others
  { id: 'evCharger', name: 'EV Charger', value: 300, color: '#EC4899', percentage: 3 },   // Added EV Charger
  { id: 'escalator', name: 'Escalator / Elevator', value: 200, color: '#6B7280', percentage: 2 },     // Added Escalator/Elevator
];

// Mock data for Performance Analytics page
export const mockPerformanceData = {
  // Yearly view data (monthly breakdown)
  yearly: {
    all: {
      current: [
        { time: 'Jan', value: 12500 },
        { time: 'Feb', value: 11800 },
        { time: 'Mar', value: 13200 },
        { time: 'Apr', value: 14500 },
        { time: 'May', value: 16800 },
        { time: 'Jun', value: 18200 },
        { time: 'Jul', value: 19500 },
        { time: 'Aug', value: 18700 },
        { time: 'Sep', value: 16400 },
        { time: 'Oct', value: 14800 },
        { time: 'Nov', value: 13500 },
        { time: 'Dec', value: 12900 }
      ],
      comparison: [
        { time: 'Jan', value: 11800 },
        { time: 'Feb', value: 11200 },
        { time: 'Mar', value: 12500 },
        { time: 'Apr', value: 13800 },
        { time: 'May', value: 15900 },
        { time: 'Jun', value: 17300 },
        { time: 'Jul', value: 18600 },
        { time: 'Aug', value: 17800 },
        { time: 'Sep', value: 15600 },
        { time: 'Oct', value: 14100 },
        { time: 'Nov', value: 12900 },
        { time: 'Dec', value: 12300 }
      ],
      target: [
        { time: 'Jan', value: 11000 },
        { time: 'Feb', value: 10500 },
        { time: 'Mar', value: 11800 },
        { time: 'Apr', value: 13000 },
        { time: 'May', value: 15000 },
        { time: 'Jun', value: 16500 },
        { time: 'Jul', value: 17500 },
        { time: 'Aug', value: 16800 },
        { time: 'Sep', value: 14700 },
        { time: 'Oct', value: 13200 },
        { time: 'Nov', value: 12000 },
        { time: 'Dec', value: 11500 }
      ],
      hourlyConsumption: Array.from({ length: 24 }, (_, i) => ({
        hour: i,
        value: 200 + Math.sin(i * Math.PI / 12) * 150
      })),
      totalConsumption: 182800,
      peakDemand: { value: 2450, time: '15:30' },
      averageLoad: 1520
    },
    // Building A data (slightly higher values)
    A: {
      current: [
        { time: 'Jan', value: 15000 },
        { time: 'Feb', value: 14200 },
        { time: 'Mar', value: 15800 },
        { time: 'Apr', value: 17400 },
        { time: 'May', value: 20200 },
        { time: 'Jun', value: 21800 },
        { time: 'Jul', value: 23400 },
        { time: 'Aug', value: 22400 },
        { time: 'Sep', value: 19700 },
        { time: 'Oct', value: 17800 },
        { time: 'Nov', value: 16200 },
        { time: 'Dec', value: 15500 }
      ],
      comparison: [
        { time: 'Jan', value: 14200 },
        { time: 'Feb', value: 13400 },
        { time: 'Mar', value: 15000 },
        { time: 'Apr', value: 16600 },
        { time: 'May', value: 19100 },
        { time: 'Jun', value: 20800 },
        { time: 'Jul', value: 22300 },
        { time: 'Aug', value: 21400 },
        { time: 'Sep', value: 18700 },
        { time: 'Oct', value: 16900 },
        { time: 'Nov', value: 15500 },
        { time: 'Dec', value: 14800 }
      ],
      target: [
        { time: 'Jan', value: 13200 },
        { time: 'Feb', value: 12600 },
        { time: 'Mar', value: 14200 },
        { time: 'Apr', value: 15600 },
        { time: 'May', value: 18000 },
        { time: 'Jun', value: 19800 },
        { time: 'Jul', value: 21000 },
        { time: 'Aug', value: 20200 },
        { time: 'Sep', value: 17600 },
        { time: 'Oct', value: 15800 },
        { time: 'Nov', value: 14400 },
        { time: 'Dec', value: 13800 }
      ],
      hourlyConsumption: Array.from({ length: 24 }, (_, i) => ({
        hour: i,
        value: 240 + Math.sin(i * Math.PI / 12) * 180
      })),
      totalConsumption: 219400,
      peakDemand: { value: 2940, time: '14:45' },
      averageLoad: 1820
    },
    // Building B data (slightly lower values)
    B: {
      current: [
        { time: 'Jan', value: 10000 },
        { time: 'Feb', value: 9400 },
        { time: 'Mar', value: 10600 },
        { time: 'Apr', value: 11600 },
        { time: 'May', value: 13400 },
        { time: 'Jun', value: 14600 },
        { time: 'Jul', value: 15600 },
        { time: 'Aug', value: 15000 },
        { time: 'Sep', value: 13100 },
        { time: 'Oct', value: 11800 },
        { time: 'Nov', value: 10800 },
        { time: 'Dec', value: 10300 }
      ],
      comparison: [
        { time: 'Jan', value: 9400 },
        { time: 'Feb', value: 9000 },
        { time: 'Mar', value: 10000 },
        { time: 'Apr', value: 11000 },
        { time: 'May', value: 12700 },
        { time: 'Jun', value: 13800 },
        { time: 'Jul', value: 14900 },
        { time: 'Aug', value: 14200 },
        { time: 'Sep', value: 12500 },
        { time: 'Oct', value: 11300 },
        { time: 'Nov', value: 10300 },
        { time: 'Dec', value: 9800 }
      ],
      target: [
        { time: 'Jan', value: 8800 },
        { time: 'Feb', value: 8400 },
        { time: 'Mar', value: 9400 },
        { time: 'Apr', value: 10400 },
        { time: 'May', value: 12000 },
        { time: 'Jun', value: 13200 },
        { time: 'Jul', value: 14000 },
        { time: 'Aug', value: 13400 },
        { time: 'Sep', value: 11800 },
        { time: 'Oct', value: 10600 },
        { time: 'Nov', value: 9600 },
        { time: 'Dec', value: 9200 }
      ],
      hourlyConsumption: Array.from({ length: 24 }, (_, i) => ({
        hour: i,
        value: 160 + Math.sin(i * Math.PI / 12) * 120
      })),
      totalConsumption: 146200,
      peakDemand: { value: 1960, time: '16:15' },
      averageLoad: 1220
    }
  },

  // Multi-year view data
  multiYear: {
    all: {
      current: [
        { time: '2016', value: 150000 },
        { time: '2017', value: 162000 },
        { time: '2018', value: 175000 },
        { time: '2019', value: 168000 },
        { time: '2020', value: 145000 },
        { time: '2021', value: 158000 },
        { time: '2022', value: 172000 },
        { time: '2023', value: 180000 },
        { time: '2024', value: 186000 },
        { time: '2025', value: 182800 }
      ],
      comparison: [
        { time: '2016', value: 142500 },
        { time: '2017', value: 153900 },
        { time: '2018', value: 166250 },
        { time: '2019', value: 159600 },
        { time: '2020', value: 137750 },
        { time: '2021', value: 150100 },
        { time: '2022', value: 163400 },
        { time: '2023', value: 171000 },
        { time: '2024', value: 176700 },
        { time: '2025', value: 173660 }
      ],
      target: [
        { time: '2016', value: 135000 },
        { time: '2017', value: 145800 },
        { time: '2018', value: 157500 },
        { time: '2019', value: 151200 },
        { time: '2020', value: 130500 },
        { time: '2021', value: 142200 },
        { time: '2022', value: 154800 },
        { time: '2023', value: 162000 },
        { time: '2024', value: 167400 },
        { time: '2025', value: 164520 }
      ],
      hourlyConsumption: Array.from({ length: 24 }, (_, i) => ({
        hour: i,
        value: 200 + Math.sin(i * Math.PI / 12) * 150
      })),
      totalConsumption: 1678800,
      peakDemand: { value: 2450, time: '15:30' },
      averageLoad: 1520
    },
    // Building A data (slightly higher values)
    A: {
      current: [
        { time: '2016', value: 180000 },
        { time: '2017', value: 194400 },
        { time: '2018', value: 210000 },
        { time: '2019', value: 201600 },
        { time: '2020', value: 174000 },
        { time: '2021', value: 189600 },
        { time: '2022', value: 206400 },
        { time: '2023', value: 216000 },
        { time: '2024', value: 223200 },
        { time: '2025', value: 219400 }
      ],
      comparison: [
        { time: '2016', value: 171000 },
        { time: '2017', value: 184680 },
        { time: '2018', value: 199500 },
        { time: '2019', value: 191520 },
        { time: '2020', value: 165300 },
        { time: '2021', value: 180120 },
        { time: '2022', value: 196080 },
        { time: '2023', value: 205200 },
        { time: '2024', value: 212040 },
        { time: '2025', value: 208430 }
      ],
      target: [
        { time: '2016', value: 162000 },
        { time: '2017', value: 174960 },
        { time: '2018', value: 189000 },
        { time: '2019', value: 181440 },
        { time: '2020', value: 156600 },
        { time: '2021', value: 170640 },
        { time: '2022', value: 185760 },
        { time: '2023', value: 194400 },
        { time: '2024', value: 200880 },
        { time: '2025', value: 197460 }
      ],
      hourlyConsumption: Array.from({ length: 24 }, (_, i) => ({
        hour: i,
        value: 240 + Math.sin(i * Math.PI / 12) * 180
      })),
      totalConsumption: 2014600,
      peakDemand: { value: 2940, time: '14:45' },
      averageLoad: 1820
    },
    // Building B data (slightly lower values)
    B: {
      current: [
        { time: '2016', value: 120000 },
        { time: '2017', value: 129600 },
        { time: '2018', value: 140000 },
        { time: '2019', value: 134400 },
        { time: '2020', value: 116000 },
        { time: '2021', value: 126400 },
        { time: '2022', value: 137600 },
        { time: '2023', value: 144000 },
        { time: '2024', value: 148800 },
        { time: '2025', value: 146200 }
      ],
      comparison: [
        { time: '2016', value: 114000 },
        { time: '2017', value: 123120 },
        { time: '2018', value: 133000 },
        { time: '2019', value: 127680 },
        { time: '2020', value: 110200 },
        { time: '2021', value: 120080 },
        { time: '2022', value: 130720 },
        { time: '2023', value: 136800 },
        { time: '2024', value: 141360 },
        { time: '2025', value: 138890 }
      ],
      target: [
        { time: '2016', value: 108000 },
        { time: '2017', value: 116640 },
        { time: '2018', value: 126000 },
        { time: '2019', value: 120960 },
        { time: '2020', value: 104400 },
        { time: '2021', value: 113760 },
        { time: '2022', value: 123840 },
        { time: '2023', value: 129600 },
        { time: '2024', value: 133920 },
        { time: '2025', value: 131580 }
      ],
      hourlyConsumption: Array.from({ length: 24 }, (_, i) => ({
        hour: i,
        value: 160 + Math.sin(i * Math.PI / 12) * 120
      })),
      totalConsumption: 1343000,
      peakDemand: { value: 1960, time: '16:15' },
      averageLoad: 1220
    }
  }
};
