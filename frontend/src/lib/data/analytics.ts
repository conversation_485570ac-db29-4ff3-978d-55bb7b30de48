import { WORKDAY_LOAD_PROFILE } from '../config/load-profile';
import { powerToEnergy, getUnitForView } from '../config/units';
import type { ChartView } from '../../components/charts/types';
import type { BuildingId } from '../types';

// Types
export interface ChartData {
  current: Array<{ time: string; demand: number }>;
  comparison?: Array<{ time: string; demand: number }>;
}

// Helper function to generate random but consistent demand values
export const generateRandomDemand = (baseValue: number, seed: number = 1) => {
  // Use seed to generate different but consistent random values
  const random = Math.sin(seed) * 10000;
  return baseValue * (0.8 + (random - Math.floor(random)) * 0.4);
};

// Base consumption values for different time periods (kWh)
export const BASE_CONSUMPTION = {
  daily: 500,     // Daily base power (kW) - instantaneous
  weekly: powerToEnergy(500, 24 * 7),  // Weekly energy (kWh)
  monthly: powerToEnergy(500, 24 * 30), // Monthly energy (kWh)
  yearly: powerToEnergy(500, 24 * 365)  // Yearly energy (kWh)
};

// Building distribution percentages
export const BUILDING_DISTRIBUTION = {
  A: 0.6, // 60% of total consumption
  B: 0.25, // 25% of total consumption
  C: 0.15  // 15% of total consumption
};

// Helper to get building-specific consumption
export const getBuildingConsumption = (baseValue: number, building: BuildingId | 'all') => {
  if (building === 'all') return baseValue;
  return baseValue * BUILDING_DISTRIBUTION[building];
};

// Mock data generators
export function generateChartData(
  view: ChartView, 
  selectedDate: Date,
  selectedBuilding: BuildingId | 'all' = 'all'
): ChartData {
  const baseConsumption = getBuildingConsumption(BASE_CONSUMPTION[view === 'multi-year' ? 'yearly' : view], selectedBuilding);
  
  // Generate a seed based on selections to get different but consistent random values
  const seed = Date.now() + 
    (selectedBuilding === 'all' ? 0 : selectedBuilding.charCodeAt(0)) + 
    view.length;

  switch (view) {
    case 'daily':
      return {
        current: WORKDAY_LOAD_PROFILE.map(d => ({ ...d })),
        comparison: WORKDAY_LOAD_PROFILE.map(d => ({
          time: d.time,
          // Generate yesterday's data - between 95-105% of today's value
          demand: d.demand * (0.95 + Math.random() * 0.1)
        }))
      };

    case 'weekly':
      const weeklyBase = baseConsumption / 7; // Divide by days in week
      return {
        current: Array.from({ length: 7 }, (_, i) => ({
          time: `Day ${i + 1}`,
          demand: powerToEnergy(weeklyBase, 24) * (0.8 + Math.random() * 0.4) // Convert power to daily energy
        })),
        comparison: Array.from({ length: 7 }, (_, i) => ({
          time: `Day ${i + 1}`,
          demand: powerToEnergy(weeklyBase, 24) * 0.95 * (0.8 + Math.random() * 0.4) // Convert power to daily energy
        }))
      };

    case 'monthly':
      const monthlyBase = baseConsumption / 31; // Divide by max days in month
      const daysInMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0).getDate();
      const daysInPrevMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 0).getDate();
      
      return {
        current: Array.from({ length: daysInMonth }, (_, i) => ({
          time: `${i + 1}`,
          demand: generateRandomDemand(monthlyBase, seed + i)
        })),
        comparison: Array.from({ length: daysInPrevMonth }, (_, i) => ({
          time: `${i + 1}`,
          demand: generateRandomDemand(monthlyBase * 0.95, seed + i + daysInPrevMonth)
        }))
      };

    case 'yearly':
      const yearlyBase = baseConsumption / 12; // Divide by months in year
      return {
        current: Array.from({ length: 12 }, (_, i) => ({
          time: new Date(2024, i).toLocaleString('en-US', { month: 'short' }),
          demand: generateRandomDemand(yearlyBase, seed + i)
        })),
        comparison: Array.from({ length: 12 }, (_, i) => ({
          time: new Date(2023, i).toLocaleString('en-US', { month: 'short' }),
          demand: generateRandomDemand(yearlyBase * 0.95, seed + i + 12)
        }))
      };

    case 'multi-year':
      const multiYearBase = baseConsumption;
      const years = [2020, 2021, 2022, 2023, 2024];
      return {
        current: years.map((year, i) => ({
          time: year.toString(),
          // Show increasing trend over years
          demand: generateRandomDemand(multiYearBase * (0.8 + (year - 2020) * 0.05), seed + i)
        }))
      };

    default:
      return {
        current: WORKDAY_LOAD_PROFILE.map(d => ({ ...d })),
        comparison: WORKDAY_LOAD_PROFILE.map(d => ({
          ...d,
          demand: generateRandomDemand(d.demand, seed)
        }))
      };
  }
}

// Chart title helpers
export function getChartTitle(view: ChartView, selectedDate: Date): string {
  const weekNum = Math.ceil((selectedDate.getDate() - selectedDate.getDay() + 1) / 7);
  const weekStart = new Date(selectedDate);
  weekStart.setDate(selectedDate.getDate() - selectedDate.getDay());
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 6);
  
  const formatComparisonLabel = (date: Date, comparisonDate: Date): string => {
    if (date.getFullYear() !== comparisonDate.getFullYear()) {
      return `${date.getFullYear()} vs ${comparisonDate.getFullYear()}`;
    }
    if (date.getMonth() !== comparisonDate.getMonth()) {
      return `${date.toLocaleString('en-US', { month: 'long', year: 'numeric' })} vs ${comparisonDate.toLocaleString('en-US', { month: 'long', year: 'numeric' })}`;
    }
    return `${date.toLocaleDateString()} vs ${comparisonDate.toLocaleDateString()}`;
  };

  switch (view) {
    case 'daily':
      return `${selectedDate.toLocaleDateString('en-US', { 
        weekday: 'long',
        month: 'long',
        day: 'numeric', 
        year: 'numeric'
      })} (Power Demand, kW)`;
    case 'weekly':
      const weekStart = new Date(selectedDate);
      weekStart.setDate(selectedDate.getDate() - selectedDate.getDay());
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);
      return `${weekStart.toLocaleDateString('en-US', { 
        month: 'long',
        day: 'numeric'
      })} - ${weekEnd.toLocaleDateString('en-US', { 
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      })} (Energy Consumption, kWh)`;
    case 'monthly':
      return `${selectedDate.toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      })} (Energy Consumption, kWh)`;
    case 'yearly':
      return `${selectedDate.getFullYear()} Annual Energy Consumption (kWh)`;
    case 'multi-year':
      return `Annual Energy Consumption Trend (kWh)`;
  }
}

export function formatDisplayDate(view: ChartView, date: Date): string {
  switch (view) {
    case 'daily':
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short', 
        day: 'numeric',
        year: 'numeric'
      });
    case 'weekly':
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      return `Week of ${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;
    case 'monthly':
      return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    case 'yearly':
    case 'multi-year':
      return date.getFullYear().toString();
    default:
      return date.toLocaleDateString();
  }
}
