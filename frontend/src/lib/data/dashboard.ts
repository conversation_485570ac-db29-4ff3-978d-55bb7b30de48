import { WORKDAY_LOAD_PROFILE } from '../config/load-profile';
import { ENERGY_CONFIG, calculateCO2Emissions } from '../config/energy';
import type { BuildingId } from '../../types';

// Calculate building data using config values
export const buildingData = {
  A: {
    consumption: 143003,
    meters: 0,
    offlineMeters: 0,  
    distribution: {},
    anomalies: 0
  },
  B: {
    consumption: 57201,
    meters: 0,
    offlineMeters: 0,  
    distribution: {},
    anomalies: 0
  },
  C: {
    consumption: 38134,
    meters: 0,
    offlineMeters: 0,  
    distribution: {},
    anomalies: 0
  }
};

// Mock meter stats
const meterStats = {
  total: 455, 
  active: 425,
  disconnected: 20,
  warning: 10,
  byBuilding: {
    A: 112, 
    B: 138, 
    C: 95   
  }
};

// Update meter counts
if (meterStats.byBuilding) {
  Object.entries(meterStats.byBuilding).forEach(([building, count]) => {
    if (building in buildingData) {
      buildingData[building as BuildingId].meters = count;
      buildingData[building as BuildingId].offlineMeters = Math.floor(count * (0.05 + Math.random() * 0.05));
      buildingData[building as BuildingId].anomalies = Math.floor(count * 0.05); 
    }
  });
}

// Calculate totals
const totalConsumption = Object.values(buildingData).reduce((sum, data) => sum + (data.consumption || 0), 0);
const totalMeters = Object.values(buildingData).reduce((sum, data) => sum + (data.meters || 0), 0);
const totalOfflineMeters = Object.values(buildingData).reduce((sum, data) => sum + (data.offlineMeters || 0), 0);
const totalAnomalies = Object.values(buildingData).reduce((acc, curr) => acc + curr.anomalies, 0);

export const energyData = {
  total: totalConsumption,
  peak: Math.max(...WORKDAY_LOAD_PROFILE.map(d => d.demand)),
  co2: calculateCO2Emissions(totalConsumption),
  cost: totalConsumption * 5.2, 
  previousCost: totalConsumption * 5.2 * 0.95, 
};