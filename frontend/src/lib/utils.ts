/**
 * Utility functions for formatting and data manipulation
 */

import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Combines class names with conditional logic and merges Tailwind classes
 * Used by shadcn/ui components for conditional classes
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format a number with thousands separators and specified decimal places
 * @param value Number to format
 * @param decimalPlaces Number of decimal places to show (default: 1)
 * @returns Formatted number as string
 */
export const formatNumber = (value: number, decimalPlaces: number = 1): string => {
  return value.toLocaleString('en-US', {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces
  });
};

/**
 * Format a percentage value with + sign for positive values
 * @param value Percentage value
 * @param decimalPlaces Number of decimal places to show (default: 1)
 * @returns Formatted percentage as string with sign
 */
export const formatPercentage = (value: number, decimalPlaces: number = 1): string => {
  const sign = value > 0 ? '+' : '';
  return `${sign}${value.toLocaleString('en-US', {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces
  })}%`;
};

/**
 * Truncate a string to a maximum length with ellipsis
 * @param str String to truncate
 * @param maxLength Maximum length before truncation (default: 50)
 * @returns Truncated string
 */
export const truncateString = (str: string, maxLength: number = 50): string => {
  if (str.length <= maxLength) return str;
  return str.substring(0, maxLength - 3) + '...';
};

/**
 * Convert a value from one unit to another
 * @param value Value to convert
 * @param fromUnit Original unit
 * @param toUnit Target unit
 * @returns Converted value
 */
export const convertUnit = (
  value: number, 
  fromUnit: 'kWh' | 'MWh' | 'kW' | 'MW', 
  toUnit: 'kWh' | 'MWh' | 'kW' | 'MW'
): number => {
  // Same unit, no conversion needed
  if (fromUnit === toUnit) return value;
  
  // kWh to MWh
  if (fromUnit === 'kWh' && toUnit === 'MWh') return value / 1000;
  
  // MWh to kWh
  if (fromUnit === 'MWh' && toUnit === 'kWh') return value * 1000;
  
  // kW to MW
  if (fromUnit === 'kW' && toUnit === 'MW') return value / 1000;
  
  // MW to kW
  if (fromUnit === 'MW' && toUnit === 'kW') return value * 1000;
  
  // Invalid conversion
  console.error(`Invalid unit conversion from ${fromUnit} to ${toUnit}`);
  return value;
};

/**
 * Gets the appropriate unit for a given smart meter metric identifier.
 * @param metric - The metric identifier (e.g., 'active_power').
 * @returns The unit string (e.g., 'kW') or an empty string if unknown.
 */
export function getMetricUnit(metric: string): string {
  const lowerMetric = metric.toLowerCase();

  if (lowerMetric.includes('power') && !lowerMetric.includes('factor')) {
    return 'kW'; // Active, Reactive, Apparent Power
  }
  if (lowerMetric.includes('energy')) {
    return 'kWh';
  }
  if (lowerMetric.includes('voltage')) {
    return 'V';
  }
  if (lowerMetric.includes('current')) {
    return 'A';
  }
  if (lowerMetric.includes('frequency')) {
    return 'Hz';
  }
  if (lowerMetric.includes('factor')) {
    return ''; // Power factor is unitless (or sometimes %)
  }
  // Add more specific cases as needed

  return ''; // Default to empty string if unit is unknown
}
