/**
 * Safe blue color definitions to replace bg-blue-500 and similar classes
 * These use inline styles to ensure they work in all contexts (modals, slides, overlays)
 */

export const SAFE_BLUE_COLORS = {
  // Replace bg-blue-500 with this
  blue500: { backgroundColor: '#3b82f6', color: '#ffffff' },
  
  // Replace bg-blue-600 with this
  blue600: { backgroundColor: '#2563eb', color: '#ffffff' },
  
  // Replace bg-blue-700 with this
  blue700: { backgroundColor: '#1d4ed8', color: '#ffffff' },
  
  // Replace bg-blue-800 with this
  blue800: { backgroundColor: '#1e40af', color: '#ffffff' },
  
  // Light blues for backgrounds (with dark text)
  blue50: { backgroundColor: '#eff6ff', color: '#1e40af' },
  blue100: { backgroundColor: '#dbeafe', color: '#1e40af' },
  blue200: { backgroundColor: '#bfdbfe', color: '#1e40af' },
  
  // For hover states
  blueHover: { backgroundColor: '#1d4ed8', color: '#ffffff' },
  
  // For primary brand blue
  primaryBlue: { backgroundColor: '#065BA9', color: '#ffffff' },
} as const;

// Helper function to apply safe blue styles
export const getSafeBlueStyle = (variant: keyof typeof SAFE_BLUE_COLORS) => {
  return SAFE_BLUE_COLORS[variant];
};

// Button style helpers
export const blueButtonStyle = {
  base: {
    ...SAFE_BLUE_COLORS.blue600,
    transition: 'all 0.2s',
  },
  hover: {
    ...SAFE_BLUE_COLORS.blue700,
  }
};

// Export for use in className when we know Tailwind is available
export const SAFE_BLUE_CLASSES = {
  blue500: 'bg-[#3b82f6] text-white',
  blue600: 'bg-[#2563eb] text-white', 
  blue700: 'bg-[#1d4ed8] text-white',
  blue800: 'bg-[#1e40af] text-white',
  primaryBlue: 'bg-[#065BA9] text-white',
} as const;