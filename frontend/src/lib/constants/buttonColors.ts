/**
 * Button Color Constants
 * Ensures all buttons have proper contrast between background and text
 */

export const BUTTON_COLORS = {
  // Primary buttons - always use dark backgrounds with white text
  primary: {
    blue: 'bg-blue-600 text-white hover:bg-blue-700',
    blueInline: { backgroundColor: '#2563eb', color: '#ffffff' },
  },
  green: {
    default: 'bg-green-600 text-white hover:bg-green-700',
    inline: { backgroundColor: '#059669', color: '#ffffff' },
  },
  purple: {
    default: 'bg-purple-600 text-white hover:bg-purple-700',
    inline: { backgroundColor: '#7c3aed', color: '#ffffff' },
  },
  red: {
    default: 'bg-red-600 text-white hover:bg-red-700',
    inline: { backgroundColor: '#dc2626', color: '#ffffff' },
  },
  orange: {
    default: 'bg-orange-600 text-white hover:bg-orange-700',
    inline: { backgroundColor: '#ea580c', color: '#ffffff' },
  },
  yellow: {
    // Yellow/amber need dark text for contrast
    default: 'bg-yellow-500 text-gray-900 hover:bg-yellow-600',
    inline: { backgroundColor: '#eab308', color: '#111827' },
  },
  
  // Secondary buttons
  secondary: 'bg-gray-100 text-gray-700 hover:bg-gray-200',
  secondaryOutline: 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50',
  
  // Special states
  disabled: 'bg-gray-100 text-gray-400 cursor-not-allowed',
  
  // Light backgrounds - NEVER use with white text
  lightBlue: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
  lightGreen: 'bg-green-100 text-green-800 hover:bg-green-200',
  lightRed: 'bg-red-100 text-red-800 hover:bg-red-200',
  lightPurple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
  lightYellow: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
} as const;

// Box/container colors for slides
export const BOX_COLORS = {
  // Dark boxes for white text
  darkBlue: { backgroundColor: '#1e40af', color: '#ffffff' },
  darkGreen: { backgroundColor: '#14532d', color: '#ffffff' },
  darkPurple: { backgroundColor: '#581c87', color: '#ffffff' },
  darkRed: { backgroundColor: '#7f1d1d', color: '#ffffff' },
  
  // Light boxes - MUST use dark text
  lightBlue: { backgroundColor: '#dbeafe', color: '#1e40af' },
  lightGreen: { backgroundColor: '#d1fae5', color: '#064e3b' },
  lightPurple: { backgroundColor: '#e9d5ff', color: '#581c87' },
  lightRed: { backgroundColor: '#fee2e2', color: '#7f1d1d' },
  lightYellow: { backgroundColor: '#fef3c7', color: '#78350f' },
  
  // Transparent overlays on dark backgrounds
  whiteOverlay: 'bg-white/10 backdrop-blur-md text-white',
  darkOverlay: 'bg-black/10 backdrop-blur-md text-gray-900',
} as const;