/**
 * Color Constants and Contrast Guidelines
 * 
 * This file defines safe color combinations to ensure proper text contrast
 * throughout the application. Always use these predefined combinations
 * to prevent accessibility issues.
 */

// Safe text colors for different backgrounds
export const TEXT_COLORS = {
  // For dark backgrounds (gray-700 and darker, all primary colors)
  onDark: 'text-white',
  
  // For light backgrounds (gray-50 to gray-300, white)
  onLight: 'text-gray-700',
  onLightMuted: 'text-gray-600',
  onLightStrong: 'text-gray-900',
  
  // For medium backgrounds (gray-400 to gray-600)
  onMedium: 'text-gray-900',
  
  // For colored backgrounds
  onPrimary: 'text-white',
  onSuccess: 'text-white',
  onWarning: 'text-gray-900', // Yellow/amber backgrounds need dark text
  onDanger: 'text-white',
} as const;

// Background and text color combinations
export const COLOR_COMBINATIONS = {
  // Primary actions
  primaryButton: 'bg-blue-600 text-white hover:bg-blue-700',
  primaryButtonOutline: 'bg-white text-blue-600 border-blue-600 hover:bg-blue-50',
  
  // Secondary actions
  secondaryButton: 'bg-gray-100 text-gray-700 hover:bg-gray-200',
  secondaryButtonOutline: 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50',
  
  // Status indicators
  success: 'bg-green-100 text-green-800',
  warning: 'bg-yellow-100 text-yellow-800',
  error: 'bg-red-100 text-red-800',
  info: 'bg-blue-100 text-blue-800',
  
  // Badges
  badgeDefault: 'bg-gray-100 text-gray-700',
  badgePrimary: 'bg-blue-600 text-white',
  badgeSuccess: 'bg-green-600 text-white',
  badgeWarning: 'bg-amber-500 text-white',
  badgeDanger: 'bg-red-600 text-white',
  
  // Cards and surfaces
  cardDefault: 'bg-white text-gray-700',
  cardHover: 'bg-gray-50 text-gray-700',
  cardSelected: 'bg-blue-50 text-gray-900',
  
  // Tooltips
  tooltip: 'bg-gray-900 text-white',
  
  // Disabled states
  disabled: 'bg-gray-100 text-gray-400',
} as const;

// Forbidden combinations (for linting)
export const FORBIDDEN_COMBINATIONS = [
  // White text on light backgrounds
  { text: 'text-white', backgrounds: ['bg-white', 'bg-gray-50', 'bg-gray-100', 'bg-gray-200', 'bg-gray-300', 'bg-gray-400'] },
  { text: 'text-gray-100', backgrounds: ['bg-white', 'bg-gray-50', 'bg-gray-100', 'bg-gray-200'] },
  { text: 'text-gray-200', backgrounds: ['bg-white', 'bg-gray-50', 'bg-gray-100', 'bg-gray-200', 'bg-gray-300'] },
  { text: 'text-gray-300', backgrounds: ['bg-white', 'bg-gray-50', 'bg-gray-100', 'bg-gray-200', 'bg-gray-300', 'bg-gray-400'] },
  
  // Light text on yellow/amber backgrounds
  { text: 'text-white', backgrounds: ['bg-yellow-100', 'bg-yellow-200', 'bg-yellow-300', 'bg-amber-100', 'bg-amber-200', 'bg-amber-300'] },
  
  // Dark text on dark backgrounds
  { text: 'text-gray-900', backgrounds: ['bg-gray-900', 'bg-gray-800', 'bg-black'] },
  { text: 'text-gray-800', backgrounds: ['bg-gray-900', 'bg-gray-800', 'bg-gray-700', 'bg-black'] },
];

// Contrast ratio requirements (WCAG AA)
export const CONTRAST_REQUIREMENTS = {
  normal: 4.5, // Normal text
  large: 3, // Large text (18pt or 14pt bold)
  nonText: 3, // UI components and graphical objects
} as const;

// Utility function to check if a combination is safe
export function isContrastSafe(textClass: string, bgClass: string): boolean {
  return !FORBIDDEN_COMBINATIONS.some(
    combo => combo.text === textClass && combo.backgrounds.includes(bgClass)
  );
}