import { useState, useCallback, useRef } from 'react';

interface UseRetryOptions {
  maxRetries?: number;
  backoffMultiplier?: number;
  initialDelay?: number;
  maxDelay?: number;
}

interface UseRetryReturn {
  retry: () => Promise<void>;
  isRetrying: boolean;
  retryCount: number;
  reset: () => void;
}

export function useRetry(
  asyncFunction: () => Promise<void>,
  options: UseRetryOptions = {}
): UseRetryReturn {
  const {
    maxRetries = 3,
    backoffMultiplier = 2,
    initialDelay = 1000,
    maxDelay = 10000
  } = options;

  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const calculateDelay = useCallback((attempt: number): number => {
    const delay = initialDelay * Math.pow(backoffMultiplier, attempt);
    return Math.min(delay, maxDelay);
  }, [initialDelay, backoffMultiplier, maxDelay]);

  const retry = useCallback(async () => {
    if (retryCount >= maxRetries) {
      console.warn('Maximum retry attempts reached');
      return;
    }

    setIsRetrying(true);
    setRetryCount(prev => prev + 1);

    try {
      // Add delay for exponential backoff (except for first retry)
      if (retryCount > 0) {
        const delay = calculateDelay(retryCount);
        await new Promise(resolve => {
          timeoutRef.current = setTimeout(resolve, delay);
        });
      }

      await asyncFunction();
    } catch (error) {
      console.error(`Retry attempt ${retryCount + 1} failed:`, error);
      // Error is handled by the calling component
    } finally {
      setIsRetrying(false);
    }
  }, [asyncFunction, retryCount, maxRetries, calculateDelay]);

  const reset = useCallback(() => {
    setIsRetrying(false);
    setRetryCount(0);
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  return {
    retry,
    isRetrying,
    retryCount,
    reset
  };
}