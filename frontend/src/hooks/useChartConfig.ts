import { useMemo } from 'react';
import * as echarts from 'echarts';
import { CHART_STYLES } from '../lib/config/ui';
import { LoadProfileData, isPeakHour } from '../lib/config/load-profile';
import { getChartTooltipFormatter } from '../utils/chartFormatters';
import type { ChartConfig } from '../components/BuildingLoad/types';

export function useChartConfig(data: LoadProfileData[]): ChartConfig {
  return useMemo(() => {
    const POWER_LINE_COLOR = CHART_STYLES.colors.primary;
    const POWER_AREA_COLOR = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      ...CHART_STYLES.gradients.area
    ]);

    const visualMap = new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: 'rgba(96, 165, 250, 0.25)' },
      { offset: 1, color: 'rgba(96, 165, 250, 0.01)' }
    ]);

    const theme = {
      colors: [POWER_LINE_COLOR],
      backgroundColor: 'transparent',
      gradients: {
        primary: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: POWER_AREA_COLOR.colorStops[0].color },
          { offset: 1, color: POWER_AREA_COLOR.colorStops[1].color }
        ]),
        secondary: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          ...CHART_STYLES.gradients.secondaryArea
        ])
      }
    };

    const option = {
      // ... (existing chart options)
      tooltip: {
        ...CHART_STYLES.tooltip,
        formatter: getChartTooltipFormatter(CHART_STYLES)
      },
      series: [
        {
          name: 'Power Consumption',
          type: 'line',
          z: 2,
          zlevel: 1,
          smooth: true,
          data: data.map(d => d.power),
          symbolSize: 0,
          ...CHART_STYLES.series.primary,
          symbol: 'circle',
          showSymbol: false,
          silent: false,
          emphasis: {
            focus: 'series',
            ...CHART_STYLES.series.primary.emphasis,
            scale: 1.2,
            symbolSize: 5,
            itemStyle: {
              borderWidth: 3,
              shadowBlur: 8,
              shadowColor: 'rgba(59, 130, 246, 0.25)'
            },
            lineStyle: { 
              width: 2,
              shadowBlur: 4,
              shadowColor: 'rgba(59, 130, 246, 0.15)'
            }
          },
          lineStyle: {
            width: CHART_STYLES.series.primary.lineWidth,
            color: CHART_STYLES.colors.secondary,
            borderWidth: 2
          }
        }
      ]
    };

    return { option, theme };
  }, [data]);
}