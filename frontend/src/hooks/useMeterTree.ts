import { useState, useEffect, useMemo } from 'react';
import { MeterType } from '@/types';
import { TreeNode } from '@/components/meters/types';
import { 
    fetchChildDeviceRelationsByModel, 
    fetchChildDeviceRelationsById,
    fetchAllDevices, 
    fetchZonesWithDevices,
} from '@/lib/api/devices';
import type {
    DeviceRelation, 
    DeviceDetails,
    Zone,
} from '@/services/deviceService';

// Helper function to map API model string to MeterType (simplified example)
function mapModelToMeterType(model: string): MeterType {
  const lowerModel = model.toLowerCase();
  // Basic mapping, refine as needed based on actual models
  if (lowerModel.includes('power')) return 'equipment'; 
  if (lowerModel.includes('light')) return 'lighting';
  if (lowerModel.includes('ahu')) return 'airSide';
  if (lowerModel.includes('chiller')) return 'chillerPlant';
  if (lowerModel.includes('ev')) return 'evCharger';
  if (lowerModel.includes('elevator') || lowerModel.includes('escalator')) return 'others';
  // Add more specific mappings
  return 'others';
}

// Helper to flatten all meters from a node recursively
function getAllMetersRecursive(node: TreeNode): TreeNode[] {
  const result: TreeNode[] = [];
  
  // If this node has status, it's a meter (regardless of whether it has children)
  if (node.status) {
    result.push(node);
  }
  
  // If this node has children, also count all child meters recursively
  if (node.children && node.children.length > 0) {
    result.push(...node.children.flatMap(getAllMetersRecursive));
  }
  
  return result;
}

// Create meter node from device details
function createMeterNode(deviceDetail: DeviceDetails): TreeNode {
  // Determine online status from the API response
  // status.value of 1 means the device is online, 0 means offline
  // If status is missing, default to offline
  const isOnline = deviceDetail.latest_data?.status?.value === 1;
  
  // Get latest reading timestamp if available
  const lastUpdateTime = deviceDetail.latest_data?.power?.updated_at || 
                        deviceDetail.latest_data?.value?.updated_at || 
                        deviceDetail.latest_data?.cumulative_energy?.updated_at ||
                        deviceDetail.latest_data?.status?.updated_at ||
                        new Date().toISOString();

  // Get power or energy value as the last reading
  const lastReadingValue = deviceDetail.latest_data?.power?.value || deviceDetail.latest_data?.power_total?.value;

  // Get voltage and current values
  const voltageValue = deviceDetail.latest_data?.voltage_ln_average?.value;
  const currentValue = deviceDetail.latest_data?.current?.value || deviceDetail.latest_data?.current_total?.value;
  const powerFactorValue = deviceDetail.latest_data?.power_factor?.value || deviceDetail.latest_data?.power_factor_total?.value;

  return {
    id: deviceDetail.device_id,
    name: deviceDetail.name,
    type: mapModelToMeterType(deviceDetail.model),
    status: {
      online: isOnline,
      lastReading: lastReadingValue,
      lastUpdate: lastUpdateTime,
      voltage: voltageValue,
      current: currentValue,
      powerFactor: powerFactorValue,
    },
    children: [],
  };
}

// Extract floor number/name from device name (e.g. "Tower A Floor 1" -> "Floor 1")
function extractFloorName(deviceName: string, towerPrefix: string): string {
  // Replace tower prefix (e.g., "Tower A - ") with empty string
  const selectedTowerDisplayName = towerPrefix.replace('_', ' ');
  const towerNameRegexPattern = new RegExp(`^${selectedTowerDisplayName.replace(/[-\s_]+/g, '[-\\s_]+')}\\s*-\\s*`, 'i');
  let floorName = deviceName.replace(towerNameRegexPattern, '');
  
  // If the replacement didn't work (no match), return original name
  return floorName.trim() || deviceName;
}

// Helper to sort floor nodes properly (Floor B first, then numbered floors, then others)
function sortFloorNodes(nodes: TreeNode[]): TreeNode[] {
  return [...nodes].sort((a, b) => {
    const nameA = a.name.toUpperCase();
    const nameB = b.name.toUpperCase();

    const isBasementA = nameA === 'FLOOR B' || nameA === 'B';
    const isBasementB = nameB === 'FLOOR B' || nameB === 'B';

    if (isBasementA && !isBasementB) return -1;
    if (!isBasementA && isBasementB) return 1;
    if (isBasementA && isBasementB) return 0;

    // Try to parse numbers, e.g., from "Floor 1", "Floor 10"
    const numA = parseInt(nameA.replace(/[^0-9]/g, ''), 10);
    const numB = parseInt(nameB.replace(/[^0-9]/g, ''), 10);

    if (!isNaN(numA) && !isNaN(numB)) {
      return numA - numB;
    }
    if (!isNaN(numA)) return -1; // Numbers before non-numbers (like Lobby)
    if (!isNaN(numB)) return 1;  // Non-numbers after numbers
    
    // Fallback to alphabetical for non-numeric, non-basement floors (e.g. Lobby, Mezzanine)
    return nameA.localeCompare(nameB);
  });
}

export function useMeterTree(selectedMeterView: 'main' | 'tower_a' | 'tower_b' | 'tower_c') {
  const [relations, setRelations] = useState<DeviceRelation[]>([]);
  const [devicesMap, setDevicesMap] = useState<Map<string, DeviceDetails>>(new Map());
  const [zones, setZones] = useState<Zone[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [towerFloorDevices, setTowerFloorDevices] = useState<Record<string, DeviceDetails[]>>({
    tower_a: [],
    tower_b: [],
    tower_c: []
  });

  // --- Fetch Data Effect ---
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Fetch data concurrently for better performance
        const [relationsData, devicesData, zonesData] = await Promise.all([
          fetchChildDeviceRelationsByModel('power_meter'),
          fetchAllDevices(['latest_data']),
          fetchZonesWithDevices()
        ]);

        // Debug logs removed for performance

        // Set up devices map for quick access
        const newDevicesMap = new Map<string, DeviceDetails>();
        devicesData.forEach(device => {
          newDevicesMap.set(device.device_id, device);
        });
        setDevicesMap(newDevicesMap);

        // Verify we have all necessary child relations for key devices
        // This ensures we don't miss important relationships like MDB3 -> P-BDB1
        const keyParentIds = ['MDB1', 'MDB2', 'MDB3', 'MDB4', 'EMDB1', 'EMDB2', 'tower_a', 'tower_b', 'tower_c'];
        const additionalRelationsPromises = keyParentIds.map(id => fetchChildDeviceRelationsById(id));
        const additionalRelationsResults = await Promise.all(additionalRelationsPromises);
        
        // Combine all relations and remove duplicates
        const allRelations = [...relationsData];
        
        // Add relations from direct device queries if they don't already exist
        additionalRelationsResults.forEach(deviceRelations => {
          deviceRelations.forEach(relation => {
            const exists = allRelations.some(r => 
              r.parent_device_id === relation.parent_device_id && 
              r.child_device_id === relation.child_device_id
            );
            if (!exists) {
              allRelations.push(relation);
            }
          });
        });

        // Process tower floor children relations
        const towerPrefixes = ['tower_a_floor_', 'tower_b_floor_', 'tower_c_floor_'];
        const towerFloorIds = devicesData
          .filter(device => towerPrefixes.some(prefix => device.device_id.startsWith(prefix)))
          .map(device => device.device_id);
        
        // Helper function to recursively fetch all child relations
        const fetchedDeviceIds = new Set<string>(); // Track already processed devices to avoid cycles
        
        async function fetchAllChildrenRecursively(deviceId: string): Promise<DeviceRelation[]> {
          // If already processed this device, return empty to avoid cycles
          if (fetchedDeviceIds.has(deviceId)) {
            return [];
          }
          
          fetchedDeviceIds.add(deviceId);
          
          // Fetch direct children
          const directRelations = await fetchChildDeviceRelationsById(deviceId);
          const results = [...directRelations];
          
          // For each child, recursively fetch its children too
          const childIds = directRelations.map(rel => rel.child_device_id);
          const childrenPromises = childIds.map(id => fetchAllChildrenRecursively(id));
          const childrenResults = await Promise.all(childrenPromises);
          
          // Combine all results
          childrenResults.forEach(relations => {
            relations.forEach(relation => {
              const exists = results.some(r => 
                r.parent_device_id === relation.parent_device_id && 
                r.child_device_id === relation.child_device_id
              );
              if (!exists) {
                results.push(relation);
              }
            });
          });
          
          return results;
        }
        
        // Fetch all child relations recursively for tower floor devices
        const towerFloorRecursiveRelationsPromises = towerFloorIds.map(id => fetchAllChildrenRecursively(id));
        const towerFloorRecursiveRelationsResults = await Promise.all(towerFloorRecursiveRelationsPromises);
        
        // Add all recursive tower floor relations to allRelations
        towerFloorRecursiveRelationsResults.forEach(relationSet => {
          relationSet.forEach(relation => {
            const exists = allRelations.some(r => 
              r.parent_device_id === relation.parent_device_id && 
              r.child_device_id === relation.child_device_id
            );
            if (!exists) {
              allRelations.push(relation);
            }
          });
        });
        
        // Organize tower floor devices by tower
        const towerA: DeviceDetails[] = [];
        const towerB: DeviceDetails[] = [];
        const towerC: DeviceDetails[] = [];
        
        devicesData.forEach(device => {
          const deviceId = device.device_id;
          if (deviceId.startsWith('tower_a_floor_')) {
            towerA.push(device);
          } else if (deviceId.startsWith('tower_b_floor_')) {
            towerB.push(device);
          } else if (deviceId.startsWith('tower_c_floor_')) {
            towerC.push(device);
          }
        });
        
        setTowerFloorDevices({
          tower_a: towerA,
          tower_b: towerB,
          tower_c: towerC
        });

        setRelations(allRelations);
        setZones(zonesData);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch data'));
        console.error('Error in useMeterTree:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []); 

  // --- Build Base Tree Structure ---
  const baseTree = useMemo(() => {
    if (isLoading) return [];
    if (error) return [];
    if (devicesMap.size === 0) return [];

    const lowerSelectedMeterView = selectedMeterView.toLowerCase();

    if (selectedMeterView === 'main') {
        const rootNodesForMain: TreeNode[] = [];

        // 1. Identify devices in tower zones to exclude them
        const towerZoneDeviceIds = new Set<string>();
        zones.forEach(zone => {
            const lowerZoneDeviceId = zone.device_id.toLowerCase();
            if (lowerZoneDeviceId.startsWith('tower_a_') ||
                lowerZoneDeviceId.startsWith('tower_b_') ||
                lowerZoneDeviceId.startsWith('tower_c_')) {
                zone.devices.forEach(d => towerZoneDeviceIds.add(d.device_id));
            }
        });

        // 2. Create the "Main Distribution" summary node
        const mainDeviceId = 'main';
        const mainDeviceDetails = devicesMap.get(mainDeviceId);
        
        if (mainDeviceDetails) {
            // Get direct children of main using relations
            const directChildrenOfMainIds = new Set<string>();
            relations.forEach(rel => {
                if (rel.parent_device_id === mainDeviceId) {
                    directChildrenOfMainIds.add(rel.child_device_id);
                }
            });

            const summaryChildren: TreeNode[] = [];
            directChildrenOfMainIds.forEach(childId => {
                const childDeviceDetails = devicesMap.get(childId);
                if (childDeviceDetails && childDeviceDetails.model === 'power_meter') {
                    summaryChildren.push(createMeterNode(childDeviceDetails));
                }
            });
            summaryChildren.sort((a, b) => a.name.localeCompare(b.name));

            const mainDistributionSummaryNode: TreeNode = {
                id: 'main-distribution-summary', 
                name: mainDeviceDetails.name || 'Main Distribution', 
                type: 'summary' as any, 
                children: summaryChildren,
            };
            rootNodesForMain.push(mainDistributionSummaryNode);
        }

        // 3. Build a comprehensive map of all non-tower power meters
        const nodesMap = new Map<string, TreeNode>();
        
        // First create all nodes from devices
        devicesMap.forEach(device => {
            if (device.model === 'power_meter' && !towerZoneDeviceIds.has(device.device_id)) {
                nodesMap.set(device.device_id, createMeterNode(device));
            }
        });
        
        // Then establish parent-child relationships from relations data
        relations.forEach(rel => {
            const parentNode = nodesMap.get(rel.parent_device_id);
            const childNode = nodesMap.get(rel.child_device_id);
            
            if (parentNode && childNode) {
                parentNode.children = parentNode.children || [];
                // Avoid duplicate children
                if (!parentNode.children.some(child => child.id === childNode.id)) {
                    parentNode.children.push(childNode);
                }
            }
        });
        
        // Sort all children alphabetically
        nodesMap.forEach(node => {
            if (node.children && node.children.length > 0) {
                node.children.sort((a, b) => a.name.localeCompare(b.name));
            }
        });

        // Define device ID lists for specific handling
        const individualSystemRootIds = [
            'EMDB1', 'MDB1', 'MDB2', 'MDB3', 'MDB4', 'EMDB2',
            'air_distribution_system', 'data_center_and_others', 'light_and_power', 
            'ev_charger', 'chiller_plant', 'chiller', 'chiller_plant_pump', 
            'cooling_tower', 'elevator_escalator', 'pump_station', 
            'waste_water_treatment_plant'
        ];
        
        // 4. Add all individual top-level systems (hierarchical)
        individualSystemRootIds.forEach(id => {
            const deviceNode = nodesMap.get(id);
            if (deviceNode) {
                if (!rootNodesForMain.some(node => node.id === deviceNode.id)) {
                    rootNodesForMain.push(deviceNode);
                }
            }
        });
        
        // Sort the final list of top-level nodes with custom order
        if (rootNodesForMain.length > 1) {
            const mainSummaryNode = rootNodesForMain.find(node => node.id === 'main-distribution-summary');
            let remainingNodes = rootNodesForMain.filter(node => node.id !== 'main-distribution-summary');

            const finalSortedNodes: TreeNode[] = [];
            if (mainSummaryNode) {
                finalSortedNodes.push(mainSummaryNode);
            }

            const mdbOrder = ['MDB1', 'MDB2', 'MDB3', 'MDB4'];
            const emdbOrder = ['EMDB1', 'EMDB2'];
            const chillerRelatedIds = ['chiller_plant', 'chiller', 'chiller_plant_pump', 'cooling_tower']; 

            // Add MDBs in specified order
            mdbOrder.forEach(id => {
                const nodeIndex = remainingNodes.findIndex(n => n.id === id);
                if (nodeIndex > -1) {
                    const node = remainingNodes.splice(nodeIndex, 1)[0];
                    // Verify node has all expected children based on relations
                    const expectedChildIds = relations
                        .filter(rel => rel.parent_device_id === id)
                        .map(rel => rel.child_device_id);
                        
                    // Ensure all expected children are included
                    expectedChildIds.forEach(childId => {
                        const childDevice = devicesMap.get(childId);
                        if (childDevice && node.children && !node.children.some(c => c.id === childId)) {
                            node.children.push(createMeterNode(childDevice));
                        }
                    });
                    
                    // Re-sort children after additions
                    if (node.children && node.children.length > 0) {
                        node.children.sort((a, b) => a.name.localeCompare(b.name));
                    }
                    
                    finalSortedNodes.push(node);
                }
            });

            // Add EMDBs in specified order
            emdbOrder.forEach(id => {
                const nodeIndex = remainingNodes.findIndex(n => n.id === id);
                if (nodeIndex > -1) {
                    finalSortedNodes.push(remainingNodes.splice(nodeIndex, 1)[0]);
                }
            });

            // Add Chiller-related, sorted alphabetically
            const chillerNodes: TreeNode[] = [];
            chillerRelatedIds.forEach(id => {
                const nodeIndex = remainingNodes.findIndex(n => n.id === id);
                if (nodeIndex > -1) {
                    chillerNodes.push(remainingNodes.splice(nodeIndex, 1)[0]);
                }
            });
            chillerNodes.sort((a, b) => a.name.localeCompare(b.name));
            finalSortedNodes.push(...chillerNodes);

            // Add any other remaining nodes, sorted alphabetically
            remainingNodes.sort((a, b) => a.name.localeCompare(b.name));
            finalSortedNodes.push(...remainingNodes);
            
            return finalSortedNodes;
        }
        return rootNodesForMain;

    } else { // Tower A, B, or C
        // Use the tower floor devices that we've fetched directly
        const floorDevices = towerFloorDevices[lowerSelectedMeterView] || [];
        const rootFloorNodes: TreeNode[] = [];
        
        // If no floor devices (shouldn't happen after proper fetching), return empty array
        if (floorDevices.length === 0) {
            return [];
        }
        
        // Helper function to build device tree recursively
        const buildDeviceTreeRecursively = (
            parentId: string, 
            deviceMap: Map<string, DeviceDetails>,
            processedIds: Set<string> = new Set()
        ): TreeNode[] => {
            // Prevent infinite recursion
            if (processedIds.has(parentId)) {
                return [];
            }
            processedIds.add(parentId);
            
            // Find all direct child relations for this parent
            const childRelations = relations.filter(rel => rel.parent_device_id === parentId);
            
            // Create nodes for all child devices
            return childRelations.map(relation => {
                const childDevice = deviceMap.get(relation.child_device_id);
                
                if (!childDevice) {
                    return null; // Skip if device not found
                }
                
                // Create node for this device
                const deviceNode = createMeterNode(childDevice);
                
                // Recursively get this device's children
                const grandchildren = buildDeviceTreeRecursively(
                    relation.child_device_id, 
                    deviceMap,
                    new Set(processedIds) // Create a new set to avoid modifying the original
                );
                
                // Add non-null grandchildren
                const validGrandchildren = grandchildren.filter(Boolean) as TreeNode[];
                if (validGrandchildren.length > 0) {
                    deviceNode.children = validGrandchildren;
                    // Sort children alphabetically
                    deviceNode.children.sort((a, b) => a.name.localeCompare(b.name));
                }
                
                return deviceNode;
            }).filter(Boolean) as TreeNode[]; // Filter out null nodes
        };
        
        // Create nodes for each floor device
        floorDevices.forEach(floorDevice => {
            const floorName = extractFloorName(floorDevice.name, lowerSelectedMeterView);
            
            // Create floor node
            const floorNode: TreeNode = {
                id: floorDevice.device_id,
                name: floorName,
                type: 'floor' as any,
                children: [],
            };
            
            // Build recursive tree for this floor
            const childDevices = buildDeviceTreeRecursively(floorDevice.device_id, devicesMap);
            
            // Add children to floor node
            if (childDevices.length > 0) {
                floorNode.children = childDevices;
                
                // Sort children alphabetically
                floorNode.children.sort((a, b) => a.name.localeCompare(b.name));
            }
            
            rootFloorNodes.push(floorNode);
        });
        
        // Sort floor nodes by floor number/name
        return sortFloorNodes(rootFloorNodes);
    }
  }, [selectedMeterView, isLoading, error, relations, devicesMap, zones, towerFloorDevices]);

  // --- Flattened List of All Meters ---
  const allMeters = useMemo(() => baseTree.flatMap(getAllMetersRecursive), [baseTree]);

  // --- Calculate Stats ---
  const stats = useMemo(() => ({
    total: allMeters.length,
    online: allMeters.filter((m: TreeNode) => m.status?.online).length,
    offline: allMeters.filter((m: TreeNode) => !m.status?.online).length,
    alerts: 0, // Placeholder
  }), [allMeters]);

  // --- Function to Filter Tree by View and Type ---
  const getFilteredTree = (view: 'all' | 'online' | 'offline' | 'alerts', type: MeterType | null): TreeNode[] => {
    // This function operates on baseTree, which is now dynamically built per selectedMeterView
    const filterNode = (node: TreeNode): TreeNode | null => {
      if (node.status) { 
        const typeMatch = !type || node.type === type;
        const statusMatch = view === 'all' ||
                            (view === 'online' && node.status.online) ||
                            (view === 'offline' && !node.status.online);
        return typeMatch && statusMatch ? node : null;
      }

      if (node.children && node.children.length > 0) {
        const filteredChildren = node.children
          .map(child => filterNode(child))
          .filter((child): child is TreeNode => child !== null); 

        return filteredChildren.length > 0
          ? { ...node, children: filteredChildren }
          : null;
      }
      return null; 
    };

    return baseTree // baseTree is already filtered by selectedMeterView (tower_a, main, etc.)
      .map(node => filterNode(node))
      .filter((node): node is TreeNode => node !== null);
  };

  // --- Function to get nodes containing offline meters ---
  const getNodesWithOfflineMeters = (node: TreeNode): string[] => {
      if (node.status) {
        return !node.status.online ? [node.id] : [];
      }
      if (node.children) {
        const childrenWithOffline = node.children.flatMap((child: TreeNode) => getNodesWithOfflineMeters(child));
        return childrenWithOffline.length > 0 ? [node.id, ...childrenWithOffline] : [];
      }
      return [];
    };

  const offlineNodesToExpand = useMemo(() => baseTree.flatMap(getNodesWithOfflineMeters), [baseTree]);

  // --- Return values ---
  return {
    stats,
    getFilteredTree,
    offlineNodesToExpand,
    allMeters,
    isLoading,
    error,
    baseTree
  };
}
