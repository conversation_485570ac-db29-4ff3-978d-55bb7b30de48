import { useCallback } from 'react';

interface ToastOptions {
  title?: string;
  description: string;
}

/**
 * Temporary placeholder hook for toast notifications.
 * Replaces planned Radix Toast implementation so Storybook compiles.
 */
export function useToast() {
  const toast = useCallback(({ title, description }: ToastOptions) => {
    // Use browser alert as a simple stand‑in until Radix Toast is wired.
    window.alert(`${title ? `${title}: ` : ''}${description}`);
  }, []);

  return { toast };
} 