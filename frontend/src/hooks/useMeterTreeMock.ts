import { useState, useMemo } from 'react';
import { MeterType } from '@/types';
import { TreeNode } from '@/components/meters/types';
import {
  MAIN_METER_LIST,
  TOWER_BUILDING_METERS,
  PODIUM_BUILDING_METERS,
  CAR_PARK_BUILDING_METERS
} from '@/lib/config/building/meters';

// Helper function to map various meter types to valid MeterType enum values
function mapToValidMeterType(type: string): MeterType {
  // Map the various types used in meter configurations to valid MeterType enum values
  const typeMap: Record<string, MeterType> = {
    // Direct matches
    'chillerPlant': 'chillerPlant',
    'airSide': 'airSide',
    'lightPower': 'lightPower',
    'light_power': 'light_power',
    'data_center_others': 'data_center_others',
    'data_center_it': 'data_center_it',
    'evCharger': 'evCharger',
    'escalator_elevator': 'escalator_elevator',
    'tenant': 'tenant',
    'equipment': 'equipment',
    'others': 'others',
    
    // Map various lighting/power types
    'Lighting': 'lightPower',
    'lighting': 'lightPower',
    'Power': 'lightPower',
    'power': 'lightPower',
    'Light&Power': 'lightPower',
    'Receptacle': 'lightPower',
    
    // Map infrastructure types
    'CCTV': 'data_center_others',
    'Landscape': 'others',
    'Emergency': 'others',
    'MDF': 'data_center_it',
    'Load Center': 'others',
    'Drainage Pumps': 'equipment',
    'Water Treatment': 'equipment',
    'Obstruction light': 'others',
    'Sanitary': 'others'
  };
  
  return typeMap[type] || 'others';
}

// Helper function to generate consistent mock status data matching BuildingMetersManagement
function generateMockStatus(meterId: string) {
  // Same logic as BuildingMetersManagement's getMeterStatus
  const random = meterId.charCodeAt(0) + meterId.charCodeAt(1);
  const isOffline = random % 10 === 0;
  const hasWarning = random % 7 === 0;
  
  return {
    online: !isOffline && !hasWarning,
    lastReading: Math.floor(Math.random() * 1000),
    lastUpdate: new Date().toISOString(),
    voltage: 220 + Math.random() * 10,
    current: Math.random() * 100,
    powerFactor: 0.85 + Math.random() * 0.15,
    status: isOffline ? 'offline' : hasWarning ? 'warning' : 'online'
  };
}

// Helper function to format floor names consistently
function formatFloorName(floorKey: string): string {
  if (floorKey === 'floorB') return 'Floor B';
  if (floorKey === 'dataCenter') return 'Data Center';
  if (floorKey === 'roof') return 'Roof';
  if (floorKey.startsWith('floor')) {
    const floorNumber = floorKey.replace('floor', '');
    return `Floor ${floorNumber}`;
  }
  return floorKey;
}

export function useMeterTreeMock(selectedMeterView: 'main' | 'tower_a' | 'tower_b' | 'tower_c') {
  // No loading state for mock data - it's all synchronous
  const [isLoading] = useState(false);
  const [error] = useState<Error | null>(null);

  const baseTree = useMemo(() => {
    // Don't check isLoading here - we want to generate the tree immediately
    if (error) return [];


    if (selectedMeterView === 'main') {
      // Main meters view - groups as top-level nodes with meters as children
      return Object.entries(MAIN_METER_LIST).map(([key, group]) => ({
        id: key,
        name: group.name,
        type: 'group' as any, // Use 'group' to match the expected type
        children: group.meters.map(meter => ({
          id: meter.id,
          name: meter.name,
          type: mapToValidMeterType(meter.type),
          status: generateMockStatus(meter.id),
          children: []
        }))
      }));
    } else if (selectedMeterView === 'tower_a') {
      // Tower Building (Tower A) - floors as top-level nodes with meters as children
      return Object.entries(TOWER_BUILDING_METERS).map(([floorKey, meters]) => ({
        id: `tower_a_${floorKey}`,
        name: formatFloorName(floorKey),
        type: 'floor' as any,
        children: meters.map(meter => ({
          id: meter.id,
          name: meter.name,
          type: mapToValidMeterType(meter.type),
          status: generateMockStatus(meter.id),
          children: []
        }))
      }));
    } else if (selectedMeterView === 'tower_b') {
      // Podium Building (Tower B) - floors as top-level nodes with meters as children
      return Object.entries(PODIUM_BUILDING_METERS).map(([floorKey, meters]) => ({
        id: `tower_b_${floorKey}`,
        name: formatFloorName(floorKey),
        type: 'floor' as any,
        children: meters.map(meter => ({
          id: meter.id,
          name: meter.name,
          type: mapToValidMeterType(meter.type),
          status: generateMockStatus(meter.id),
          children: []
        }))
      }));
    } else {
      // Car Park Building (Tower C) - floors as top-level nodes with meters as children
      return Object.entries(CAR_PARK_BUILDING_METERS).map(([floorKey, meters]) => ({
        id: `tower_c_${floorKey}`,
        name: formatFloorName(floorKey),
        type: 'floor' as any,
        children: meters.map(meter => ({
          id: meter.id,
          name: meter.name,
          type: mapToValidMeterType(meter.type),
          status: generateMockStatus(meter.id),
          children: []
        }))
      }));
    }
  }, [selectedMeterView, error]);

  const allMeters = useMemo(() => {
    const meters: TreeNode[] = [];

    function collectMeters(nodes: TreeNode[]) {
      nodes.forEach(node => {
        if (node.status) {
          meters.push(node);
        }
        if (node.children) {
          collectMeters(node.children);
        }
      });
    }

    collectMeters(baseTree);
    return meters;
  }, [baseTree]);

  const stats = useMemo(() => ({
    total: allMeters.length,
    online: allMeters.filter(m => m.status?.online).length,
    offline: allMeters.filter(m => !m.status?.online).length,
    alerts: Math.floor(allMeters.length * 0.05), // Mock 5% alerts
  }), [allMeters]);

  const getFilteredTree = (view: 'all' | 'online' | 'offline' | 'alerts', type: MeterType | null): TreeNode[] => {
    if (view === 'all' && !type) return baseTree;

    const filterNode = (node: TreeNode): TreeNode | null => {
      if (node.status) {
        // This is a meter node
        const typeMatch = !type || node.type === type;
        const statusMatch = view === 'all' ||
                            (view === 'online' && node.status.online) ||
                            (view === 'offline' && !node.status.online) ||
                            (view === 'alerts' && Math.random() < 0.05); // Mock alerts
        return typeMatch && statusMatch ? node : null;
      }

      if (node.children) {
        // This is a group/floor node - filter its children
        const filteredChildren = node.children
          .map(child => filterNode(child))
          .filter((child): child is TreeNode => child !== null);

        return filteredChildren.length > 0
          ? { ...node, children: filteredChildren }
          : null;
      }
      return null;
    };

    return baseTree
      .map(node => filterNode(node))
      .filter((node): node is TreeNode => node !== null);
  };

  // Calculate nodes that should be expanded for offline view
  const offlineNodesToExpand = useMemo(() => {
    const nodesToExpand: string[] = [];

    function findOfflineNodes(nodes: TreeNode[]) {
      nodes.forEach(node => {
        if (node.children) {
          const hasOfflineChildren = node.children.some(child =>
            child.status && !child.status.online
          );
          if (hasOfflineChildren) {
            nodesToExpand.push(node.id);
          }
          findOfflineNodes(node.children);
        }
      });
    }

    findOfflineNodes(baseTree);
    return nodesToExpand;
  }, [baseTree]);

  return {
    stats,
    getFilteredTree,
    offlineNodesToExpand,
    allMeters,
    isLoading,
    error,
    baseTree
  };
}