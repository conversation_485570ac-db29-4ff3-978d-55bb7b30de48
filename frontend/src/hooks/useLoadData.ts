import { useState, useCallback } from 'react';
import { WORKDAY_LOAD_PROFILE, calculateDailyTotal } from '../lib/config/load-profile';
import type { LoadDataHookResult } from '../components/BuildingLoad/types';

export function useLoadData(): LoadDataHookResult {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [data] = useState(WORKDAY_LOAD_PROFILE);

  const todayTotal = calculateDailyTotal(data);
  const lastWeekTotal = data.reduce((sum, hour) => sum + (hour.lastWeek || 0), 0);
  const totalDiff = ((todayTotal - lastWeekTotal) / lastWeekTotal * 100).toFixed(1);

  const refetch = useCallback(async () => {
    setIsLoading(true);
    try {
      // Implement actual data fetching here
      setError(null);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    todayTotal,
    lastWeekTotal,
    totalDiff,
    data,
    isLoading,
    error,
    refetch
  };
}