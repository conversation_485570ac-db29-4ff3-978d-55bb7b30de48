import { useState, useCallback, useEffect } from 'react';
import { 
  ViewType, 
  BuildingType, 
  ChartData, 
  SystemBreakdownItem,
  AnalyticsState
} from '../types/analytics';
import { fetchChartData, ChartView } from '../lib/api/analytics';
import { fetchSystemBreakdown as fetchApiSystemBreakdown } from '../lib/api/analytics';

// Generate dates for previous period
const getPreviousDate = (date: Date, view: ViewType): Date => {
  const newDate = new Date(date);
  switch (view) {
    case 'day':
      newDate.setDate(newDate.getDate() - 1);
      break;
    case 'week':
      newDate.setDate(newDate.getDate() - 7);
      break;
    case 'month':
      newDate.setMonth(newDate.getMonth() - 1);
      break;
    case 'year':
      newDate.setFullYear(newDate.getFullYear() - 1);
      break;
    case 'multi-year':
      newDate.setFullYear(newDate.getFullYear() - 5); // For multi-year, go back 5 years
      break;
  }
  return newDate;
};

// Helper to generate time points based on view
const generateTimePoints = (date: Date, view: ViewType): string[] => {
  const result: string[] = [];
  const startDate = new Date(date);
  
  // Reset hours for consistent starting points
  startDate.setHours(0, 0, 0, 0);
  
  switch (view) {
    case 'day':
      // 24 hours
      for (let i = 0; i < 24; i++) {
        result.push(`${i}:00`);
      }
      break;
    case 'week':
      // 7 days
      const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
      const weekStart = new Date(startDate);
      weekStart.setDate(weekStart.getDate() - weekStart.getDay()); // Start from Sunday
      
      for (let i = 0; i < 7; i++) {
        const day = new Date(weekStart);
        day.setDate(day.getDate() + i);
        result.push(days[day.getDay()]);
      }
      break;
    case 'month':
      // Days in month
      const daysInMonth = new Date(
        startDate.getFullYear(),
        startDate.getMonth() + 1,
        0
      ).getDate();
      
      for (let i = 1; i <= daysInMonth; i++) {
        result.push(i.toString());
      }
      break;
    case 'year':
      // 12 months
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      for (let i = 0; i < 12; i++) {
        result.push(months[i]);
      }
      break;
    case 'multi-year':
      // 5 years
      const currentYear = new Date().getFullYear();
      const startYear = currentYear - 4; // Show current year and 4 previous years
      for (let i = 0; i < 5; i++) {
        result.push((startYear + i).toString());
      }
      break;
    default:
      break;
  }
  
  return result;
};

// Format display date based on view
export const formatDisplayDate = (date: Date, view: ViewType): string => {
  const options: Intl.DateTimeFormatOptions = {};
  
  switch (view) {
    case 'day':
      options.month = 'short';
      options.day = 'numeric';
      options.year = 'numeric';
      break;
    case 'week':
      const startOfWeek = new Date(date);
      startOfWeek.setDate(date.getDate() - date.getDay()); // Start from Sunday
      const endOfWeek = new Date(startOfWeek);
      endOfWeek.setDate(startOfWeek.getDate() + 6);
      
      return `${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
    case 'month':
      options.month = 'long';
      options.year = 'numeric';
      break;
    case 'year':
      options.year = 'numeric';
      break;
    case 'multi-year':
      const currentYear = new Date().getFullYear();
      const startYear = currentYear - 4;
      return `${startYear} - ${currentYear}`;
    default:
      break;
  }
  
  return new Date(date).toLocaleDateString('en-US', options);
};

// Get label for previous period based on view
export const getPreviousPeriodLabel = (view: ViewType): string => {
  switch (view) {
    case 'day':
      return 'Yesterday';
    case 'week':
      return 'Last Week';
    case 'month':
      return 'Last Month';
    case 'year':
      return 'Last Year';
    case 'multi-year':
      return 'Previous 5 Years';
    default:
      return 'Previous Period';
  }
};

const useAnalyticsData = (
  selectedView: ViewType,
  selectedDate: Date,
  selectedBuilding: BuildingType
) => {
  const [state, setState] = useState<AnalyticsState>({
    loading: true,
    chartData: { current: [], comparison: [] },
    systemBreakdown: [],
    totalConsumption: 0,
    peakDemand: 0,
    averageLoad: 0
  });

  const loadData = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      // Fetch both data sets in parallel for better performance
      const [chartResponse, breakdownData] = await Promise.all([
        fetchChartData(selectedView as ChartView, selectedDate, selectedBuilding),
        fetchApiSystemBreakdown(selectedDate, selectedBuilding)
      ]);
      
      // Calculate metrics from the data
      const currentData = chartResponse.current || [];
      const totalDemand = currentData.reduce((sum, item) => sum + (item?.demand || 0), 0);
      const peakDemand = Math.max(...currentData.map(item => item?.demand || 0), 0);
      const averageLoad = currentData.length > 0 ? totalDemand / currentData.length : 0;
      
      setState({
        loading: false,
        chartData: chartResponse,
        systemBreakdown: breakdownData,
        totalConsumption: totalDemand,
        peakDemand,
        averageLoad
      });
    } catch (error) {
      console.error('Error loading analytics data:', error);
      setState(prev => ({ 
        ...prev, 
        loading: false 
      }));
    }
  }, [selectedView, selectedDate, selectedBuilding]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return state;
};

export default useAnalyticsData;
