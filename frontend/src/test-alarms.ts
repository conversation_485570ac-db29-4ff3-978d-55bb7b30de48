// Quick test script to verify alarms API functionality
import { getActiveAlarms, getAlarmHistory, getAlarmRules, acknowledgeAndResolveAlarm } from './lib/api/alarms';

async function testAlarmsAPI() {
  console.log('🧪 Testing Alarms API...');
  
  try {
    // Test Active Alarms
    console.log('📋 Testing Active Alarms...');
    const activeAlarms = await getActiveAlarms({ page: '1', limit: '5' });
    console.log(`✅ Active Alarms: ${activeAlarms.count} total, ${activeAlarms.results.length} fetched`);
    console.log('First active alarm:', activeAlarms.results[0]);

    // Test Alarm Rules
    console.log('📋 Testing Alarm Rules...');
    const alarmRules = await getAlarmRules({ page: '1', limit: '5' });
    console.log(`✅ Alarm Rules: ${alarmRules.count} total, ${alarmRules.results.length} fetched`);
    console.log('First alarm rule:', alarmRules.results[0]);

    // Test Alarm History
    console.log('📋 Testing Alarm History...');
    const alarmHistory = await getAlarmHistory({ page: '1', limit: '5' });
    console.log(`✅ Alarm History: ${alarmHistory.count} total, ${alarmHistory.results.length} fetched`);
    console.log('First alarm history:', alarmHistory.results[0]);

    // Test Acknowledge & Resolve
    if (activeAlarms.results.length > 0) {
      console.log('📋 Testing Acknowledge & Resolve...');
      const firstAlarm = activeAlarms.results[0];
      const success = await acknowledgeAndResolveAlarm(firstAlarm.id);
      console.log(`✅ Acknowledge & Resolve: ${success ? 'SUCCESS' : 'FAILED'}`);
      
      // Verify it moved to history
      const updatedActive = await getActiveAlarms({ page: '1', limit: '5' });
      console.log(`Updated active count: ${updatedActive.count}`);
    }

    console.log('🎉 All Alarms API tests passed!');
    
  } catch (error) {
    console.error('❌ Alarms API test failed:', error);
  }
}

// Export for manual testing in browser console
(window as any).testAlarmsAPI = testAlarmsAPI;

export { testAlarmsAPI };