import React, { useState } from 'react';
import EmailPreviewModal from '@/components/alarms/EmailPreviewModal';
import { Link } from 'react-router-dom';
import { ArrowLeft, Mail, AlertTriangle, Server, User, Plus, X, CheckCircle, Loader2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/Card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/Button';
import { ToggleSwitch } from '@/components/ui/ToggleSwitch';

// Mock data
const mockRecipients = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>', initials: 'JS', enabled: true },
  { id: '2', name: '<PERSON>', email: '<EMAIL>', initials: 'SP', enabled: true },
  { id: '3', name: '<PERSON>', email: '<EMAIL>', initials: 'DC', enabled: false }
];

const AlarmEmailSettingsNew: React.FC = () => {
  const [emailEnabled, setEmailEnabled] = useState(true);
  const [email, setEmail] = useState('<EMAIL>');
  const [recipients, setRecipients] = useState(mockRecipients);
  const [newRecipient, setNewRecipient] = useState({ name: '', email: '' });
  const [showAddForm, setShowAddForm] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isSendingTest, setIsSendingTest] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Toggle email notifications
  const handleToggleEmail = () => {
    setEmailEnabled(!emailEnabled);
  };

  // Toggle recipient status
  const handleToggleRecipient = (id: string) => {
    setRecipients(
      recipients.map(recipient =>
        recipient.id === id ? { ...recipient, enabled: !recipient.enabled } : recipient
      )
    );
  };

  // Handle email input change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  // Handle new recipient form changes
  const handleNewRecipientChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewRecipient(prev => ({ ...prev, [name]: value }));
  };

  // Add new recipient
  const handleAddRecipient = () => {
    if (!newRecipient.name || !newRecipient.email) return;

    const initials = newRecipient.name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);

    const newRecipientObj = {
      id: Date.now().toString(),
      name: newRecipient.name,
      email: newRecipient.email,
      initials,
      enabled: true
    };

    setRecipients([...recipients, newRecipientObj]);
    setNewRecipient({ name: '', email: '' });
    setShowAddForm(false);
  };

  // Save settings
  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Email notification settings saved successfully');
    } catch (error) {
      console.error('Error saving email settings:', error);
      alert('Failed to save email notification settings');
    } finally {
      setIsSaving(false);
    }
  };

  // Send test email
  const handleSendTestEmail = async () => {
    setIsSendingTest(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Test email sent successfully');
    } catch (error) {
      console.error('Error sending test email:', error);
      alert('Failed to send test email');
    } finally {
      setIsSendingTest(false);
    }
  };

  // Open email preview modal
  const handleOpenPreview = () => {
    setIsPreviewOpen(true);
  };

  return (
    <main className="h-[calc(100vh-56px)] px-4 pb-4 pt-0 relative bg-gradient-to-br from-[#F9FAFF] via-white to-blue-50/30 overflow-auto">
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center gap-2 mb-2">
          <Link
            to="/alarms"
            className="flex items-center gap-1 text-sm text-gray-500 hover:text-primary-blue transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Alarm Management
          </Link>
        </div>

        <div>
          <h1 className="text-2xl font-bold tracking-tight">Critical Alarm Email Notifications</h1>
          <p className="text-gray-500 mt-1">
            Configure email notifications for critical alarms only
          </p>
        </div>

        <div className="space-y-6">
          {/* Critical Alarm Notifications Card */}
          <Card className="w-full">
            <CardHeader>
              <CardTitle className="text-xl flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                Critical Alarm Email Notifications
              </CardTitle>
              <CardDescription>
                Configure email notifications for critical alarms only
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Enable/Disable Email Notifications */}
                <div className={`flex items-center justify-between rounded-lg p-3 ${emailEnabled ? 'bg-blue-100 border-l-4 border-blue-600' : 'border border-gray-100'}`}>
                  <div>
                    <Label htmlFor="email-notifications" className="text-base font-medium">
                      Email Notifications for Critical Alarms Only
                    </Label>
                    <p className="text-sm text-gray-500 mt-1">
                      Receive email alerts when critical alarms are triggered (Warning and Info alarms do not send emails)
                    </p>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className={`flex-shrink-0 w-20 text-center text-xs font-medium px-2 py-0.5 rounded ${emailEnabled ? 'text-blue-700 bg-blue-50' : 'text-gray-400 bg-gray-50'}`}>
                      {emailEnabled ? 'Enabled' : 'Disabled'}
                    </span>
                    <ToggleSwitch size="md" checked={emailEnabled} onChange={handleToggleEmail} />
                  </div>
                </div>

                {/* Email Address */}
                <div className="space-y-2">
                  <Label htmlFor="email-address" className="text-base font-medium">
                    Notification Email Address
                  </Label>
                  <div className="flex gap-2">
                    <div className="relative flex-1">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="email-address"
                        type="email"
                        placeholder="<EMAIL>"
                        value={email}
                        onChange={handleEmailChange}
                        className="pl-10"
                        disabled={!emailEnabled}
                      />
                    </div>
                    <Button
                      variant="outline"
                      onClick={handleSendTestEmail}
                      disabled={!emailEnabled || isSendingTest || !email}
                    >
                      {isSendingTest ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        <>
                          <Mail className="mr-2 h-4 w-4" />
                          Send Test
                        </>
                      )}
                    </Button>
                  </div>
                </div>

                {/* Email Notification Details */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-gray-500" />
                      <h3 className="text-base font-medium">Email Notification Details</h3>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleOpenPreview}
                        className="text-xs"
                      >
                        <Mail className="h-3.5 w-3.5 mr-1" />
                        Preview Email
                      </Button>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500 ml-7">
                    Alarm emails include detailed information about the alarm, including the meter name, metric, trigger value, and timestamp. They also include a direct link to the alarm management page. <strong>Note: Email notifications are only sent for Critical alarms.</strong>
                  </p>
                </div>

                {/* Recipients Section */}
                <div className="space-y-4 mt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <User className="h-5 w-5 text-gray-500" />
                      <h3 className="text-base font-medium">Recipients</h3>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowAddForm(true)}
                      className="text-xs"
                      disabled={showAddForm}
                    >
                      <Plus className="h-3.5 w-3.5 mr-1" />
                      Add Recipient
                    </Button>
                  </div>

                  {/* Add Recipient Form */}
                  {showAddForm && (
                    <div className="border rounded-md p-3 space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium">Add New Recipient</h4>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowAddForm(false)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <Label htmlFor="recipient-name" className="text-xs">
                            Name
                          </Label>
                          <Input
                            id="recipient-name"
                            name="name"
                            value={newRecipient.name}
                            onChange={handleNewRecipientChange}
                            placeholder="John Doe"
                            className="h-9 text-sm"
                          />
                        </div>
                        <div>
                          <Label htmlFor="recipient-email" className="text-xs">
                            Email
                          </Label>
                          <Input
                            id="recipient-email"
                            name="email"
                            type="email"
                            value={newRecipient.email}
                            onChange={handleNewRecipientChange}
                            placeholder="<EMAIL>"
                            className="h-9 text-sm"
                          />
                        </div>
                      </div>
                      <div className="flex justify-end">
                        <Button
                          size="sm"
                          onClick={handleAddRecipient}
                          disabled={!newRecipient.name || !newRecipient.email}
                          className="text-xs"
                        >
                          Add Recipient
                        </Button>
                      </div>
                    </div>
                  )}

                  <div className="space-y-3 max-h-[300px] overflow-y-auto">
                    {recipients.map((recipient) => (
                      <div key={recipient.id} className="relative border-b pb-3">
                        {recipient.enabled && (
                          <div className="absolute inset-0 bg-blue-50 rounded-md"></div>
                        )}
                        {recipient.enabled && (
                          <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-600 rounded-l"></div>
                        )}
                        <div className="flex items-center justify-between relative z-10 px-2 py-1">
                          <div className="flex items-center gap-3">
                            <div className={`h-7 w-7 rounded bg-blue-50 border border-blue-200 flex items-center justify-center text-blue-700 font-medium text-xs`}>
                              {recipient.initials}
                            </div>
                            <div>
                              <p className="text-sm font-medium">{recipient.name}</p>
                              <p className="text-xs text-gray-500">{recipient.email}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className={`flex-shrink-0 w-20 text-center text-xs font-medium px-2 py-0.5 rounded ${recipient.enabled ? 'text-blue-700 bg-blue-50' : 'text-gray-400 bg-gray-50'}`}>
                              {recipient.enabled ? 'Active' : 'Inactive'}
                            </span>
                            <ToggleSwitch size="md" checked={recipient.enabled} onChange={() => handleToggleRecipient(recipient.id)} />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAddForm(true)}
                    className="w-full text-sm py-5"
                    disabled={showAddForm}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Recipient
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Email Server Settings Card */}
          <Card className="w-full">
            <CardHeader className="py-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Server className="h-5 w-5 text-gray-500" />
                Email Server Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">SMTP Server:</span>
                  <span className="ml-2 font-medium">smtp.alto-cero.com</span>
                </div>
                <div>
                  <span className="text-gray-500">Port:</span>
                  <span className="ml-2 font-medium">587</span>
                </div>
                <div>
                  <span className="text-gray-500">Sender Email:</span>
                  <span className="ml-2 font-medium"><EMAIL></span>
                </div>
                <div>
                  <Button variant="outline" size="sm" className="text-xs">
                    Edit Server Settings
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button onClick={handleSaveSettings} disabled={isSaving} className="px-8">
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Settings'
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Email Preview Modal */}
      <EmailPreviewModal
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        recipientEmail={email}
      />
    </main>
  );
};

export default AlarmEmailSettingsNew;
