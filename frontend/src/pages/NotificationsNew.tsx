import React, { useState, useEffect } from 'react';
import { Al<PERSON><PERSON>riangle, WifiOff, TrendingUp, Bell, CheckCircle2, Search, Filter, Calendar, RefreshCw, Trash2, CheckCheck, Info, X, ChevronRight } from 'lucide-react';
import { formatDate } from '../lib/utils/formatters';
import { NotificationsPageSkeleton } from '@/components/ui/notifications-skeletons';
import { EnhancedErrorDisplay } from '@/components/ui/error-display';
import { useRetry } from '@/hooks/useRetry';

interface Notification {
  id: string;
  type: 'alert' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  source: {
    type: 'system' | 'meter' | 'consumption' | 'device';
    id: string;
    name: string;
  };
  priority?: 'high' | 'medium' | 'low';
}

const NOTIFICATION_ICONS = {
  alert: AlertTriangle,
  warning: TrendingUp,
  info: Info,
  success: CheckCircle2,
} as const;

const NOTIFICATION_COLORS = {
  alert: 'bg-red-50 border-red-200 text-red-800',
  warning: 'bg-amber-50 border-amber-200 text-amber-800',
  info: 'bg-blue-50 border-blue-200 text-blue-800',
  success: 'bg-emerald-50 border-emerald-200 text-emerald-800',
} as const;

// Generate more realistic mock notifications
const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'alert',
    title: 'High Power Consumption Alert',
    message: 'Tower A has exceeded the power threshold of 1,500 kW. Current consumption: 1,745 kW',
    timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    isRead: false,
    source: { type: 'meter', id: 'METER-A-001', name: 'Tower A Main Meter' },
    priority: 'high'
  },
  {
    id: '2',
    type: 'warning',
    title: 'Meter Communication Lost',
    message: 'Unable to communicate with Chiller Plant meter for the past 15 minutes',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    isRead: false,
    source: { type: 'device', id: 'CHILLER-001', name: 'Chiller Plant' },
    priority: 'medium'
  },
  {
    id: '3',
    type: 'success',
    title: 'Monthly Report Generated',
    message: 'Energy consumption report for October 2024 has been successfully generated',
    timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    isRead: true,
    source: { type: 'system', id: 'REPORT-001', name: 'Reporting System' },
    priority: 'low'
  },
  {
    id: '4',
    type: 'info',
    title: 'Scheduled Maintenance',
    message: 'Meter calibration scheduled for Tower B on Nov 15, 2024 at 10:00 AM',
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    isRead: true,
    source: { type: 'system', id: 'MAINT-001', name: 'Maintenance System' },
    priority: 'medium'
  },
  {
    id: '5',
    type: 'alert',
    title: 'Abnormal Consumption Pattern',
    message: 'Unusual energy spike detected in Data Center during off-hours',
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    isRead: true,
    source: { type: 'consumption', id: 'DC-001', name: 'Data Center Analytics' },
    priority: 'high'
  }
];

export default function NotificationsNew() {
  const [selectedType, setSelectedType] = useState<'all' | Notification['type']>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [showOnlyUnread, setShowOnlyUnread] = useState(false);

  const fetchNotifications = async () => {
    await new Promise(resolve => setTimeout(resolve, 800));
    return mockNotifications;
  };

  const { retry, retryCount, isRetrying } = useRetry(fetchNotifications);

  useEffect(() => {
    const loadNotifications = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await fetchNotifications();
        setNotifications(data);
        setIsLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load notifications'));
        setIsLoading(false);
      }
    };

    loadNotifications();
  }, []);

  const filteredNotifications = notifications.filter(notification => {
    if (selectedType !== 'all' && notification.type !== selectedType) return false;
    if (showOnlyUnread && notification.isRead) return false;
    if (searchQuery && !notification.title.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !notification.message.toLowerCase().includes(searchQuery.toLowerCase())) return false;
    return true;
  });

  const stats = {
    total: notifications.length,
    unread: notifications.filter(n => !n.isRead).length,
    alerts: notifications.filter(n => n.type === 'alert').length,
    warnings: notifications.filter(n => n.type === 'warning').length,
  };

  const handleMarkAsRead = (ids: string[]) => {
    setNotifications(prev => 
      prev.map(n => ids.includes(n.id) ? { ...n, isRead: true } : n)
    );
    setSelectedNotifications([]);
  };

  const handleDelete = (ids: string[]) => {
    setNotifications(prev => prev.filter(n => !ids.includes(n.id)));
    setSelectedNotifications([]);
  };

  const handleMarkAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
  };

  if (isLoading) {
    return <NotificationsPageSkeleton />;
  }

  if (error) {
    return (
      <div className="h-[calc(100vh-56px)] flex items-center justify-center p-4">
        <EnhancedErrorDisplay
          error={error}
          onRetry={retry}
          retryCount={retryCount}
          isRetrying={isRetrying}
          context="notifications"
        />
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-56px)] bg-gradient-to-br from-[#F9FAFF] via-white to-blue-50/30 p-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Notifications</h1>
        <p className="text-gray-600">Monitor system alerts and important updates</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Total</p>
              <p className="text-3xl font-bold text-gray-900">{stats.total}</p>
            </div>
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
              <Bell size={24} className="text-gray-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Unread</p>
              <p className="text-3xl font-bold text-blue-600">{stats.unread}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Info size={24} className="text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Alerts</p>
              <p className="text-3xl font-bold text-red-600">{stats.alerts}</p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle size={24} className="text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600 mb-1">Warnings</p>
              <p className="text-3xl font-bold text-amber-600">{stats.warnings}</p>
            </div>
            <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
              <TrendingUp size={24} className="text-amber-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Actions */}
      <div className="bg-white rounded-xl border border-gray-100 shadow-sm p-4 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Search */}
            <div className="relative">
              <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search notifications..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm w-64"
              />
            </div>

            {/* Type Filter */}
            <div className="flex items-center gap-2">
              {(['all', 'alert', 'warning', 'info', 'success'] as const).map((type) => (
                <button
                  key={type}
                  onClick={() => setSelectedType(type)}
                  className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-all ${
                    selectedType === type
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {type === 'all' ? 'All' : type.charAt(0).toUpperCase() + type.slice(1)}
                </button>
              ))}
            </div>

            {/* Unread Filter */}
            <label className="flex items-center gap-2 ml-4">
              <input
                type="checkbox"
                checked={showOnlyUnread}
                onChange={(e) => setShowOnlyUnread(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700">Unread only</span>
            </label>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {selectedNotifications.length > 0 ? (
              <>
                <button
                  onClick={() => handleMarkAsRead(selectedNotifications)}
                  className="px-3 py-1.5 text-sm font-medium text-blue-600 hover:bg-blue-50 rounded-lg transition-colors flex items-center gap-1"
                >
                  <CheckCheck size={16} />
                  Mark as Read ({selectedNotifications.length})
                </button>
                <button
                  onClick={() => handleDelete(selectedNotifications)}
                  className="px-3 py-1.5 text-sm font-medium text-red-600 hover:bg-red-50 rounded-lg transition-colors flex items-center gap-1"
                >
                  <Trash2 size={16} />
                  Delete ({selectedNotifications.length})
                </button>
              </>
            ) : (
              <button
                onClick={handleMarkAllAsRead}
                className="px-3 py-1.5 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Mark all as read
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Notifications List */}
      <div className="bg-white rounded-xl border border-gray-100 shadow-sm overflow-hidden">
        <div className="divide-y divide-gray-100">
          {filteredNotifications.length === 0 ? (
            <div className="p-12 text-center">
              <Bell size={48} className="mx-auto text-gray-300 mb-4" />
              <p className="text-gray-500">No notifications found</p>
            </div>
          ) : (
            filteredNotifications.map((notification) => {
              const Icon = NOTIFICATION_ICONS[notification.type];
              return (
                <div
                  key={notification.id}
                  className={`p-4 hover:bg-gray-50 transition-colors cursor-pointer ${
                    !notification.isRead ? 'bg-blue-50/30 border-l-4 border-blue-500' : ''
                  }`}
                  onClick={() => handleMarkAsRead([notification.id])}
                >
                  <div className="flex items-start gap-4">
                    {/* Checkbox */}
                    <input
                      type="checkbox"
                      checked={selectedNotifications.includes(notification.id)}
                      onChange={(e) => {
                        e.stopPropagation();
                        if (e.target.checked) {
                          setSelectedNotifications([...selectedNotifications, notification.id]);
                        } else {
                          setSelectedNotifications(selectedNotifications.filter(id => id !== notification.id));
                        }
                      }}
                      onClick={(e) => e.stopPropagation()}
                      className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />

                    {/* Icon */}
                    <div className={`p-2 rounded-lg ${NOTIFICATION_COLORS[notification.type]}`}>
                      <Icon size={20} />
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-sm font-semibold text-gray-900 mb-1">
                            {notification.title}
                            {!notification.isRead && (
                              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                New
                              </span>
                            )}
                          </h3>
                          <p className="text-sm text-gray-600 mb-2">{notification.message}</p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>{notification.source.name}</span>
                            <span>•</span>
                            <span>{formatDate(new Date(notification.timestamp))}</span>
                            {notification.priority && (
                              <>
                                <span>•</span>
                                <span className={`font-medium ${
                                  notification.priority === 'high' ? 'text-red-600' :
                                  notification.priority === 'medium' ? 'text-amber-600' :
                                  'text-gray-600'
                                }`}>
                                  {notification.priority.toUpperCase()} Priority
                                </span>
                              </>
                            )}
                          </div>
                        </div>
                        <ChevronRight size={18} className="text-gray-400 flex-shrink-0" />
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
}