import React, { useCallback, useState, useEffect, useRef } from 'react';
import { DASHBOARD_CONFIG } from '../lib/config/dashboard';
import { SECTION_TITLES } from '../lib/config/content';
import { BUILDING_CONFIG, SCALE_CATEGORIES } from '../lib/config/building';
import { useAutoRefresh } from '../lib/hooks/useAutoRefresh';
import { useRetry } from '../hooks/useRetry';
import { LoadProfileData } from '../lib/config/load-profile';
import type { BuildingId } from '../types';
import { EnergyBenchmarkChart } from '../components/overview/EnergyBenchmarkChart';
import { getBenchmarkSettings } from '../components/settings/EnergyBenchmarkSettings';
import { LineChart, BarChart3, Activity, Zap, PieChart, Download } from 'lucide-react';
import { SystemBreakdownDonut } from '../components/charts/SystemBreakdownDonut';
import { ErrorBoundary } from '../lib/hooks/useErrorBoundary';
import { BuildingLoadChart } from '../components/overview/BuildingLoadChart';
import { MonthlyOverview } from '../components/dashboard/MonthlyOverview';
import { EnergyEfficiencyCard } from '../components/overview/EnergyEfficiencyCard';
import { TowerOverview } from '../components/overview/TowerOverview';
import { DashboardSection } from '../components/dashboard/DashboardSection';
import { DashboardLayout } from '../components/dashboard/DashboardLayout';
import { formatTimestamp } from '../utils/formatting';
import { format } from 'date-fns';
import { ErrorDisplay } from '../components/ui/error-display';
import { DatePresetSelector } from '../components/common/DatePresetSelector';
import {
  TowerSkeleton,
  ChartSkeleton,
  PerformanceCardSkeleton,
  MonthlyOverviewSkeleton,
  PieChartSkeleton
} from '../components/ui/dashboard-skeletons';
import {
  fetchEnergyData,
  type HistoricalDataQuery,
  type HistoricalDataRecord,
  type EnergyRecord,
  type StatisticalRecord,
  fetchStatisticalData
} from '../services/timescaleServiceMock';
import {
  fetchSiteData,
  type SiteData,
  type SiteMetadata
} from '../services/siteServiceMock';

// Constants used for data fetching
const SITE_ID = "set";
const MAIN_DEVICE_ID = "Main";
const DEVICE_ID_TO_NAME_MAP: Record<string, string> = {
  "chiller_plant": "Chiller Plant",
  "air_distribution_system": "Air Side",
  "light_and_power": "Light & Power",
  "data_center_and_others": "Data Center & Others",
  "ev_charger": "EV Charger",
  "elevator_escalator": "Escalator / Elevator"
};

const TOWER_DEVICE_IDS: Record<BuildingId, string> = {
  A: 'tower_a',
  B: 'tower_b',
  C: 'tower_c',
};

// Helper functions for data fetching
function getCurrentDateRanges() {
  const now = new Date();
  const today = new Date(now);
  const todayStart = new Date(today.setHours(0, 0, 0, 0));
  const todayEnd = new Date(now);

  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  const yesterdayStart = new Date(yesterday);
  yesterdayStart.setHours(0, 0, 0, 0);
  const yesterdayEnd = new Date(yesterday);
  yesterdayEnd.setHours(23, 59, 59, 999);

  const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

  const currentYear = now.getFullYear();
  const startOfYear = new Date(Date.UTC(currentYear, 0, 1, 0, 0, 0, 0));
  const endOfYear = new Date(Date.UTC(currentYear, 11, 31, 23, 59, 59, 999));

  const lastYear = currentYear - 1;
  const startOfLastYear = new Date(Date.UTC(lastYear, 0, 1, 0, 0, 0, 0));
  const endOfLastYear = new Date(Date.UTC(lastYear, 11, 31, 23, 59, 59, 999));

  const weekEnd = new Date(now);
  const weekStart = new Date(now);
  weekStart.setDate(now.getDate() - 7);
  weekStart.setHours(0, 0, 0, 0);

  const lastYearWeekEnd = new Date(weekEnd);
  lastYearWeekEnd.setFullYear(lastYearWeekEnd.getFullYear() - 1);
  const lastYearWeekStart = new Date(weekStart);
  lastYearWeekStart.setFullYear(lastYearWeekStart.getFullYear() - 1);

  return {
    today: { start: todayStart, end: todayEnd },
    yesterday: { start: yesterdayStart, end: yesterdayEnd },
    currentMonth: { start: firstDayOfMonth, end: lastDayOfMonth },
    currentYear: { start: startOfYear, end: endOfYear },
    lastYear: { start: startOfLastYear, end: endOfLastYear },
    currentWeek: { start: weekStart, end: weekEnd },
    lastYearWeek: { start: lastYearWeekStart, end: lastYearWeekEnd }
  };
}

// Add this function before PerformanceSection
// Helper function to get EUI color class based on value
function getEuiColorClass(value: number): string {
  if (value <= SCALE_CATEGORIES[4].value) {
    return 'text-teal-900';
  }
  if (value <= SCALE_CATEGORIES[3].value) {
    return 'text-teal-800';
  }
  if (value <= SCALE_CATEGORIES[2].value) {
    return 'text-teal-700';
  }
  if (value <= SCALE_CATEGORIES[1].value) {
    return 'text-teal-600';
  }
  if (value <= SCALE_CATEGORIES[0].value) {
    return 'text-teal-500';
  }
  return 'text-teal-500';
}

// Helper function to generate CSV data from dashboard
const generateDashboardCSV = (dashboardData: DashboardData): string => {
  const rows: string[] = [];

  // Add headers
  rows.push('Metric,Value,Unit,Period');

  // Add monthly overview data
  if (dashboardData.monthlyOverview) {
    rows.push(`Total Consumption,${dashboardData.monthlyOverview.totalConsumption || 0},MWh,Month-to-date`);
    rows.push(`CO2 Emissions,${dashboardData.monthlyOverview.totalCO2Emissions || 0},tCO2e,Month-to-date`);
    rows.push(`Electricity Cost,${dashboardData.monthlyOverview.totalCost || 0},THB,Month-to-date`);
  }

  // Add tower data
  if (dashboardData.towerData) {
    Object.entries(dashboardData.towerData).forEach(([tower, data]) => {
      rows.push(`${tower} Consumption,${data.consumption || 0},MWh,Current`);
      rows.push(`${tower} YoY Change,${data.percentChange || 0},%,Year-over-year`);
    });
  }

  // Add environmental data
  if (dashboardData.environmentalData) {
    rows.push(`Power Usage Effectiveness,${dashboardData.environmentalData.powerUsageEffectiveness || 0},PUE,Current`);
    rows.push(`Renewable Energy,${dashboardData.environmentalData.renewableEnergyPercentage || 0},%,Current`);
  }

  return rows.join('\n');
};

// Helper function to download CSV
const downloadCSV = (csvData: string, filename: string) => {
  const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
  URL.revokeObjectURL(link.href);
};

// Export dropdown component with better UX
function ExportDropdown({ dashboardData }: { dashboardData: any }) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div ref={dropdownRef} className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200"
        title="Export dashboard data"
      >
        <Download size={18} />
      </button>
      {isOpen && (
        <div className="absolute bottom-full right-0 mb-1 animate-in fade-in slide-in-from-bottom-1 duration-200">
          <div className="bg-white border border-gray-200 rounded-lg shadow-lg py-1 min-w-[140px]">
            <button
              onClick={() => {
                const csvData = generateDashboardCSV(dashboardData);
                downloadCSV(csvData, `dashboard-export-${format(new Date(), 'yyyy-MM-dd')}.csv`);
                setIsOpen(false);
              }}
              className="w-full px-4 py-2 text-xs text-gray-700 hover:bg-gray-50 text-left flex items-center gap-2"
            >
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
                <line x1="16" y1="13" x2="8" y2="13" />
                <line x1="16" y1="17" x2="8" y2="17" />
                <polyline points="10 9 9 9 8 9" />
              </svg>
              Export as CSV
            </button>
            <button
              onClick={() => {
                window.print();
                setIsOpen(false);
              }}
              className="w-full px-4 py-2 text-xs text-gray-700 hover:bg-gray-50 text-left flex items-center gap-2"
            >
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                <polyline points="14 2 14 8 20 8" />
                <path d="M12 18v-6" />
                <path d="m9 15 3 3 3-3" />
              </svg>
              Export as PDF
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default function Dashboard() {
  // Date picker state for demonstration
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedView, setSelectedView] = useState<'day' | 'week' | 'month' | 'year' | 'multi-year'>('day');

  const [isAutoRefresh] = useState(true);
  const [benchmarkRefreshKey, setBenchmarkRefreshKey] = useState(0);
  const lastUpdatedTimestamp = formatTimestamp(new Date());
  const formattedTimestamp = <span className="text-[10px] text-gray-400">
    Last updated: {lastUpdatedTimestamp}
  </span>;

  // Handle date preset selection
  const handlePresetSelect = (preset: any) => {
    setSelectedDate(preset.getDate());
    setSelectedView(preset.view);
  };

  // Centralized state for all fetched data
  const [dashboardData, setDashboardData] = useState({
    // Site metadata
    siteMetadata: null as SiteMetadata | null,
    isLoadingSiteMetadata: true,
    errorSiteMetadata: null as string | null,

    // Tower data
    towerData: {
      A: { consumption: null, lastYearConsumption: null, percentChange: null },
      B: { consumption: null, lastYearConsumption: null, percentChange: null },
      C: { consumption: null, lastYearConsumption: null, percentChange: null }
    } as Record<BuildingId, TowerDisplayData>,
    isLoadingTowerData: true,
    errorTowerData: null as string | null,

    // Load profile data
    loadProfileData: [] as LoadProfileData[],
    comparisonData: [] as LoadProfileData[],
    isLoadingProfile: true,
    errorProfile: null as string | null,

    // Daily kWh
    todayDailyKWh: null as number | null,
    yesterdayDailyKWh: null as number | null,
    isLoadingDailyKWh: true,
    errorDailyKWh: null as string | null,

    // Benchmark data
    benchmarkApiData: undefined as number[] | undefined,
    previousYearBenchmarkApiData: undefined as number[] | undefined,
    isLoadingBenchmark: true,
    errorBenchmark: null as string | null,

    // Monthly/yearly performance data
    monthlyConsumption: null as number | null,
    peakDemand: null as number | null,
    energyCost: null as number | null,
    yearlyConsumptionTotal: null as number | null,
    yearlyCO2Emissions: null as number | null,
    yearlyElectricityCost: null as number | null,
    isLoadingPerformance: true,
    errorPerformance: null as string | null,

    // Pie chart data
    pieChartData: [] as any[],
    isLoadingPieChart: true,
    errorPieChart: null as string | null,
  });

  // Centralized data fetching function
  const fetchAllDashboardData = useCallback(async () => {
    console.log('Fetching all dashboard data...');

    // Get date ranges for queries
    const dateRanges = getCurrentDateRanges();

    // Initialize loading states
    setDashboardData(prev => ({
      ...prev,
      isLoadingSiteMetadata: true,
      isLoadingTowerData: true,
      isLoadingProfile: true,
      isLoadingDailyKWh: true,
      isLoadingBenchmark: true,
      isLoadingPerformance: true,
      isLoadingPieChart: true,

      // Reset errors
      errorSiteMetadata: null,
      errorTowerData: null,
      errorProfile: null,
      errorDailyKWh: null,
      errorBenchmark: null,
      errorPerformance: null,
      errorPieChart: null,
    }));

    // Fetch site metadata
    let siteMetadata: SiteMetadata | null = null;
    let siteError: string | null = null;

    try {
      const siteDataResponse = await fetchSiteData(SITE_ID);
      if (siteDataResponse && siteDataResponse.metadata) {
        siteMetadata = siteDataResponse.metadata;
      } else {
        throw new Error("Site metadata not found in response");
      }
    } catch (err) {
      console.error("Error fetching site metadata:", err);
      siteError = err instanceof Error ? err.message : "Failed to fetch site metadata.";
    }

    // Update site metadata state
    setDashboardData(prev => ({
      ...prev,
      siteMetadata,
      isLoadingSiteMetadata: false,
      errorSiteMetadata: siteError
    }));

    // If we failed to get site metadata, some calculations might not work
    // but we can still try to fetch other data

    // 1. Fetch Tower Data
    const fetchTowerData = async () => {
      const buildingIds: BuildingId[] = ['A', 'B', 'C'];
      const towerDataResults = {} as Record<BuildingId, TowerDisplayData>;
      let towerError: string | null = null;

      try {
        // Create promises for all towers
        const towerPromises = buildingIds.map(async (buildingId) => {
          try {
            const device_id = TOWER_DEVICE_IDS[buildingId];

            const currentWeekQuery: HistoricalDataQuery = {
              table_name: "energy_data_1day",
              site_id: SITE_ID,
              device_id,
              datapoints: ["daily_energy"],
              start_timestamp: dateRanges.currentWeek.start.toISOString(),
              end_timestamp: dateRanges.currentWeek.end.toISOString(),
            };

            const lastYearWeekQuery: HistoricalDataQuery = {
              ...currentWeekQuery,
              start_timestamp: dateRanges.lastYearWeek.start.toISOString(),
              end_timestamp: dateRanges.lastYearWeek.end.toISOString(),
            };

            // Fetch data in parallel
            const [currentDataResponse, lastYearDataResponse] = await Promise.all([
              fetchEnergyData(currentWeekQuery),
              fetchEnergyData(lastYearWeekQuery)
            ]);

            // Sum energy values - update the function to use absolute values
            const sumEnergy = (response: HistoricalDataRecord<EnergyRecord>[]): number | null => {
              if (!response || response.length === 0) return null;

              // Sum all records from all devices in the response
              let totalEnergy = 0;
              for (const deviceData of response) {
                if (deviceData.records && Array.isArray(deviceData.records)) {
                  totalEnergy += deviceData.records.reduce((acc, record) => {
                    // Use absolute value as negative values can represent energy flow direction
                    return acc + Math.abs(record?.value || 0);
                  }, 0);
                }
              }

              return totalEnergy > 0 ? totalEnergy : null;
            };

            // Add debug logging
            console.log(`Tower ${buildingId} data:`, {
              device_id,
              currentWeekQuery,
              currentDataResponse,
              currentSumEnergy: sumEnergy(currentDataResponse),
              lastYearDataResponse,
              lastYearSumEnergy: sumEnergy(lastYearDataResponse)
            });

            const currentConsumption = sumEnergy(currentDataResponse);
            const lastYearConsumption = sumEnergy(lastYearDataResponse);

            // Calculate percent change
            let percentChange: number | null = null;
            if (currentConsumption !== null && lastYearConsumption !== null && lastYearConsumption !== 0) {
              percentChange = ((currentConsumption - lastYearConsumption) / lastYearConsumption) * 100;
            } else if (currentConsumption !== null && lastYearConsumption === 0) {
              percentChange = currentConsumption > 0 ? 100 : (currentConsumption < 0 ? -100 : 0);
            }

            return {
              buildingId,
              data: {
                consumption: currentConsumption,
                lastYearConsumption: lastYearConsumption,
                percentChange: percentChange,
              }
            };
          } catch (err) {
            console.error(`Failed to fetch data for Tower ${buildingId}:`, err);
            return {
              buildingId,
              data: { consumption: null, lastYearConsumption: null, percentChange: null },
              error: `Failed to load data for Tower ${buildingId}`
            };
          }
        });

        // Wait for all tower data
        const results = await Promise.all(towerPromises);
        let hasPartialError = false;

        // Process results
        for (const result of results) {
          towerDataResults[result.buildingId] = result.data;
          if ('error' in result) {
            hasPartialError = true;
          }
        }

        if (hasPartialError) {
          towerError = 'Failed to load data for some towers. Displaying available data.';
        }
      } catch (err) {
        console.error("Error fetching all tower data:", err);
        towerError = 'Failed to load all tower overview data.';
      }

      // Update tower data state
      setDashboardData(prev => ({
        ...prev,
        towerData: towerDataResults,
        isLoadingTowerData: false,
        errorTowerData: towerError
      }));
    };

    // 2. Fetch Load Profile Data
    const fetchLoadProfileData = async () => {
      let loadData: LoadProfileData[] = [];
      let comparisonData: LoadProfileData[] = [];
      let profileError: string | null = null;

      try {
        const commonQueryProps = {
          table_name: "statistic_data_15min",
          site_id: SITE_ID,
          device_id: MAIN_DEVICE_ID,
          datapoints: ["power"],
        };

        const queryToday: HistoricalDataQuery = {
          ...commonQueryProps,
          start_timestamp: dateRanges.today.start.toISOString(),
          end_timestamp: dateRanges.today.end.toISOString(),
        };

        const queryYesterday: HistoricalDataQuery = {
          ...commonQueryProps,
          start_timestamp: dateRanges.yesterday.start.toISOString(),
          end_timestamp: dateRanges.yesterday.end.toISOString(),
        };

        // Fetch data in parallel
        const [responseToday, responseYesterday] = await Promise.all([
          fetchStatisticalData(queryToday),
          fetchStatisticalData(queryYesterday)
        ]);

        // Process today's data
        if (responseToday && responseToday.length > 0 && responseToday[0].records) {
          loadData = responseToday[0].records.map((record: StatisticalRecord): LoadProfileData => {
            const recordDate = new Date(record.timestamp);
            const hours = recordDate.getHours().toString().padStart(2, '0');
            const minutes = recordDate.getMinutes().toString().padStart(2, '0');
            return {
              time: `${hours}:${minutes}`,
              demand: record.mean_value,
            };
          });
        }

        // Process yesterday's data
        if (responseYesterday && responseYesterday.length > 0 && responseYesterday[0].records) {
          comparisonData = responseYesterday[0].records.map((record: StatisticalRecord): LoadProfileData => {
            const recordDate = new Date(record.timestamp);
            const hours = recordDate.getHours().toString().padStart(2, '0');
            const minutes = recordDate.getMinutes().toString().padStart(2, '0');
            return {
              time: `${hours}:${minutes}`,
              demand: record.mean_value,
            };
          });
        }
      } catch (err) {
        console.error("Error fetching building load profile data:", err);
        profileError = err instanceof Error ? err.message : "Failed to fetch building load profile data.";
      }

      // Update load profile state
      setDashboardData(prev => ({
        ...prev,
        loadProfileData: loadData,
        comparisonData: comparisonData,
        isLoadingProfile: false,
        errorProfile: profileError
      }));
    };

    // 3. Fetch Daily KWh Totals
    const fetchDailyKWhTotals = async () => {
      let todayKWh: number | null = null;
      let yesterdayKWh: number | null = null;
      let dailyKWhError: string | null = null;

      try {
        const commonEnergyQueryProps = {
          table_name: "energy_data_1day",
          site_id: SITE_ID,
          device_id: MAIN_DEVICE_ID,
          datapoints: ["daily_energy"],
        };

        const queryTodayKWh: HistoricalDataQuery = {
          ...commonEnergyQueryProps,
          start_timestamp: dateRanges.today.start.toISOString(),
          end_timestamp: dateRanges.today.end.toISOString(),
        };

        const queryYesterdayKWh: HistoricalDataQuery = {
          ...commonEnergyQueryProps,
          start_timestamp: dateRanges.yesterday.start.toISOString(),
          end_timestamp: dateRanges.yesterday.end.toISOString(),
        };

        const getEnergyValue = (response: HistoricalDataRecord<EnergyRecord>[]): number | null => {
          if (response && response.length > 0 && response[0].records && response[0].records.length > 0) {
            return response[0].records[0].value;
          }
          return null;
        };

        // Fetch data in parallel
        const [todayResponse, yesterdayResponse] = await Promise.all([
          fetchEnergyData(queryTodayKWh),
          fetchEnergyData(queryYesterdayKWh),
        ]);

        todayKWh = getEnergyValue(todayResponse);
        yesterdayKWh = getEnergyValue(yesterdayResponse);
      } catch (err) {
        console.error("Error fetching daily kWh totals:", err);
        dailyKWhError = err instanceof Error ? err.message : "Failed to fetch daily kWh totals.";
      }

      // Update daily KWh state
      setDashboardData(prev => ({
        ...prev,
        todayDailyKWh: todayKWh,
        yesterdayDailyKWh: yesterdayKWh,
        isLoadingDailyKWh: false,
        errorDailyKWh: dailyKWhError
      }));
    };

    // 4. Fetch Benchmark Data
    const fetchBenchmarkData = async () => {
      let currentYearData: number[] | undefined = undefined;
      let previousYearData: number[] | undefined = undefined;
      let benchmarkError: string | null = null;

      const fetchDataForYear = async (year: number): Promise<number[] | null> => {
        const startOfYear = new Date(Date.UTC(year, 0, 1, 0, 0, 0, 0)).toISOString();
        const endOfYear = new Date(Date.UTC(year, 11, 31, 23, 59, 59, 999)).toISOString();
        const query: HistoricalDataQuery = {
          table_name: "energy_data_1day",
          site_id: SITE_ID,
          device_id: MAIN_DEVICE_ID,
          datapoints: ["daily_energy"],
          start_timestamp: startOfYear,
          end_timestamp: endOfYear,
        };

        try {
          const response = await fetchEnergyData(query);
          if (response && response.length > 0 && response[0].records) {
            const monthlyData = new Array(12).fill(0);
            response[0].records.forEach((record: EnergyRecord) => {
              const recordDate = new Date(record.timestamp);
              const monthIndex = recordDate.getUTCMonth();
              monthlyData[monthIndex] += record.value;
            });
            // Divide by 2 to match other consumption values
            return monthlyData.map(value => value / 2);
          }
          return new Array(12).fill(0);
        } catch (err) {
          console.error(`Error fetching energy benchmark data for ${year}:`, err);
          return null;
        }
      };

      try {
        const currentYear = new Date().getFullYear();
        const previousYear = currentYear - 1;

        // Fetch data for both years in parallel
        const [currentYearResults, previousYearResults] = await Promise.all([
          fetchDataForYear(currentYear),
          fetchDataForYear(previousYear),
        ]);

        if (currentYearResults) {
          currentYearData = currentYearResults;
        } else {
          currentYearData = new Array(12).fill(0);
          benchmarkError = `Failed to fetch data for ${currentYear}.`;
        }

        if (previousYearResults) {
          previousYearData = previousYearResults;
        } else {
          previousYearData = new Array(12).fill(0);
          benchmarkError = benchmarkError
            ? `${benchmarkError}, Failed for ${previousYear}`
            : `Failed to fetch data for ${previousYear}.`;
        }
      } catch (err) {
        console.error("Error fetching all benchmark data:", err);
        benchmarkError = err instanceof Error ? err.message : "Failed to fetch all energy benchmark data.";
        currentYearData = new Array(12).fill(0);
        previousYearData = new Array(12).fill(0);
      }

      // Update benchmark data state
      setDashboardData(prev => ({
        ...prev,
        benchmarkApiData: currentYearData,
        previousYearBenchmarkApiData: previousYearData,
        isLoadingBenchmark: false,
        errorBenchmark: benchmarkError
      }));
    };

    // 5. Fetch Performance Data (monthly and yearly)
    const fetchPerformanceData = async () => {
      let monthlyConsumption: number | null = null;
      let peakDemand: number | null = null;
      let energyCost: number | null = null;
      let yearlyConsumptionTotal: number | null = null;
      let yearlyCO2Emissions: number | null = null;
      let yearlyElectricityCost: number | null = null;
      let performanceError: string | null = null;

      // Skip if site metadata is not available and show error
      if (!siteMetadata) {
        performanceError = "Site metadata unavailable. Some calculations could not be performed.";

        // Update performance data state with error
        setDashboardData(prev => ({
          ...prev,
          isLoadingPerformance: false,
          errorPerformance: performanceError
        }));
        return;
      }

      try {
        const yearlyEnergyQuery: HistoricalDataQuery = {
          table_name: "energy_data_1day",
          site_id: SITE_ID,
          device_id: MAIN_DEVICE_ID,
          datapoints: ["daily_energy"],
          start_timestamp: dateRanges.currentYear.start.toISOString(),
          end_timestamp: dateRanges.currentYear.end.toISOString(),
        };

        const yearlyEnergyResponse = await fetchEnergyData(yearlyEnergyQuery);

        let totalEnergyForYear = 0;
        let hasYearlyEnergyData = false;
        let sumForCurrentMonth = 0;

        if (yearlyEnergyResponse && yearlyEnergyResponse.length > 0 && yearlyEnergyResponse[0].records) {
          const records = yearlyEnergyResponse[0].records;
          hasYearlyEnergyData = records.length > 0;

          const currentMonthEpochStart = dateRanges.currentMonth.start.getTime();
          const currentMonthEpochEnd = dateRanges.currentMonth.end.getTime();

          records.forEach(record => {
            totalEnergyForYear += record.value;
            const recordDate = new Date(record.timestamp);
            if (recordDate.getTime() >= currentMonthEpochStart && recordDate.getTime() <= currentMonthEpochEnd) {
              sumForCurrentMonth += record.value;
            }
          });

          yearlyConsumptionTotal = hasYearlyEnergyData ? totalEnergyForYear / 2 : null;

          monthlyConsumption = hasYearlyEnergyData && sumForCurrentMonth > 0
            ? sumForCurrentMonth / 2
            : (records.find(r =>
                new Date(r.timestamp).getTime() >= currentMonthEpochStart &&
                new Date(r.timestamp).getTime() <= currentMonthEpochEnd
              ) ? 0 : null);
        }

        // Calculate CO2 emissions
        if (hasYearlyEnergyData && siteMetadata.co2_kg_per_kwh != null && totalEnergyForYear > 0) {
          yearlyCO2Emissions = totalEnergyForYear * siteMetadata.co2_kg_per_kwh;
        }

        // Calculate yearly electricity cost
        if (hasYearlyEnergyData && siteMetadata.electricity_cost_per_kwh != null && totalEnergyForYear > 0) {
          yearlyElectricityCost = totalEnergyForYear * siteMetadata.electricity_cost_per_kwh;
        }

        // Calculate monthly electricity cost
        if (monthlyConsumption !== null && siteMetadata.electricity_cost_per_kwh != null) {
          energyCost = monthlyConsumption * siteMetadata.electricity_cost_per_kwh;
        }

        // Fetch peak demand data
        const statsQuery: HistoricalDataQuery = {
          table_name: "statistic_data_1hour",
          site_id: SITE_ID,
          device_id: MAIN_DEVICE_ID,
          datapoints: ["power"],
          start_timestamp: dateRanges.currentMonth.start.toISOString(),
          end_timestamp: dateRanges.currentMonth.end.toISOString(),
        };

        const statsResponse = await fetchStatisticalData(statsQuery);

        if (statsResponse && statsResponse.length > 0 && statsResponse[0].records && statsResponse[0].records.length > 0) {
          const maxPeak = statsResponse[0].records.reduce((max, record) => Math.max(max, record.max_value), -Infinity);
          peakDemand = maxPeak === -Infinity ? null : maxPeak;
        }

        // Mock data fallback for peak demand
        if (peakDemand === null) {
          peakDemand = 756.2; // Mock peak demand value in kW
        }
      } catch (err) {
        console.error("Error fetching performance data:", err);
        performanceError = err instanceof Error ? err.message : "Failed to fetch performance data.";
      }

      // Update performance data state
      setDashboardData(prev => ({
        ...prev,
        monthlyConsumption,
        peakDemand,
        energyCost,
        yearlyConsumptionTotal,
        yearlyCO2Emissions,
        yearlyElectricityCost,
        isLoadingPerformance: false,
        errorPerformance: performanceError
      }));
    };

    // 6. Fetch Pie Chart Data
    const fetchPieChartData = async () => {
      let pieData: any[] = [];
      let pieChartError: string | null = null;

      try {
        const deviceIds = Object.keys(DEVICE_ID_TO_NAME_MAP);

        const query: HistoricalDataQuery = {
          table_name: "energy_data_1day",
          site_id: SITE_ID,
          device_id: deviceIds,
          datapoints: ["daily_energy"],
          start_timestamp: dateRanges.currentMonth.start.toISOString(),
          end_timestamp: dateRanges.currentMonth.end.toISOString(),
        };

        const response = await fetchEnergyData(query);

        if (response && response.length > 0) {
          pieData = response.map((deviceData: HistoricalDataRecord<EnergyRecord>) => {
            const totalEnergyForDevice = deviceData.records && Array.isArray(deviceData.records)
              ? Math.abs(deviceData.records.reduce((acc, record) => acc + (record?.value || 0), 0))
              : 0;
            // Map device_id to correct name format for pie chart
            let displayName = DEVICE_ID_TO_NAME_MAP[deviceData.device_id] || deviceData.device_id;

            return {
              id: deviceData.device_id,
              name: displayName,
              value: totalEnergyForDevice / 2, // Divide by 2 to match monthly consumption
            };
          }).filter(item => item.value >= 0);
        }
      } catch (err) {
        console.error("Error fetching consumption distribution data:", err);
        pieChartError = err instanceof Error ? err.message : "Failed to fetch consumption distribution.";
      }

      // Update pie chart data state
      setDashboardData(prev => ({
        ...prev,
        pieChartData: pieData,
        isLoadingPieChart: false,
        errorPieChart: pieChartError
      }));
    };

    // Run all fetch operations in parallel
    await Promise.all([
      fetchTowerData(),
      fetchLoadProfileData(),
      fetchDailyKWhTotals(),
      fetchBenchmarkData(),
      fetchPerformanceData(),
      fetchPieChartData()
    ]);

  }, []); // No dependencies for initial fetch

  // Auto-refresh handler
  const handleRefresh = useCallback(async () => {
    console.log('Refreshing dashboard data...');
    await fetchAllDashboardData();
  }, [fetchAllDashboardData]);

  // Add retry functionality
  const { retry: retryFetch, isRetrying, retryCount } = useRetry(fetchAllDashboardData, {
    maxRetries: 3,
    initialDelay: 1000
  });

  // Initialize data fetching
  useEffect(() => {
    fetchAllDashboardData();
  }, [fetchAllDashboardData]);

  // Listen for benchmark settings updates
  useEffect(() => {
    const handleBenchmarkUpdate = () => {
      setBenchmarkRefreshKey(prev => prev + 1);
    };

    window.addEventListener('benchmarkSettingsUpdated', handleBenchmarkUpdate);
    return () => {
      window.removeEventListener('benchmarkSettingsUpdated', handleBenchmarkUpdate);
    };
  }, []);

  // Set up auto-refresh
  useAutoRefresh({
    enabled: isAutoRefresh,
    interval: DASHBOARD_CONFIG.refreshInterval,
    onRefresh: handleRefresh
  });

  return (
    <ErrorBoundary fallback={
      <div className="h-[calc(100vh-56px)] p-4 relative bg-gradient-to-br from-[#F9FAFF] via-white to-blue-50/30 overflow-x-hidden overflow-y-auto">
        {/* Date Picker Demo - positioned at top-right */}
        <div className="absolute top-4 right-4 z-[9999]">
          <DatePresetSelector
            selectedDate={selectedDate}
            selectedView={selectedView}
            onPresetSelect={handlePresetSelect}
            label="Time Period"
          />
        </div>

        <DashboardLayout
          mainColumn={<MainContent lastUpdatedTimestamp={formattedTimestamp} dashboardData={dashboardData} />}
          sideColumn={<SideContent lastUpdatedTimestamp={formattedTimestamp} dashboardData={dashboardData} />}
        />
      </div>
    }>
      <div className="h-[calc(100vh-56px)] p-4 relative bg-gradient-to-br from-[#F9FAFF] via-white to-blue-50/30 overflow-x-hidden overflow-y-auto">
        {/* Date Picker Demo - positioned at top-right */}
        <div className="absolute top-4 right-4 z-[9999]">
          <DatePresetSelector
            selectedDate={selectedDate}
            selectedView={selectedView}
            onPresetSelect={handlePresetSelect}
            label="Time Period"
          />
        </div>

        <DashboardLayout
          mainColumn={
            <MainContent
              key={benchmarkRefreshKey}
              lastUpdatedTimestamp={formattedTimestamp}
              dashboardData={dashboardData}
            />
          }
          sideColumn={
            <SideContent
              lastUpdatedTimestamp={formattedTimestamp}
              dashboardData={dashboardData}
            />
          }
        />
      </div>
    </ErrorBoundary>
  );
}

interface TowerDisplayData {
  consumption: number | null;
  lastYearConsumption: number | null;
  percentChange: number | null;
}

// Define prop types for MainContent with centralized data
interface MainContentProps {
  lastUpdatedTimestamp: React.ReactNode;
  dashboardData: any; // Using any for brevity, could be properly typed
}

function MainContent({ lastUpdatedTimestamp, dashboardData }: MainContentProps) {
  // Get benchmark settings
  const benchmarkSettings = getBenchmarkSettings();

  // Extract the data from props
  const {
    towerData,
    isLoadingTowerData,
    errorTowerData,

    loadProfileData,
    comparisonData,
    isLoadingProfile,
    errorProfile,

    todayDailyKWh,
    yesterdayDailyKWh,
    isLoadingDailyKWh,
    errorDailyKWh,

    benchmarkApiData,
    previousYearBenchmarkApiData,
    isLoadingBenchmark,
    errorBenchmark,

    siteMetadata,
    isLoadingSiteMetadata,
    errorSiteMetadata
  } = dashboardData;

  // TowerOverviewsSection as a local component using props data
  const TowerOverviewsSection = () => {
    if (isLoadingTowerData) {
      return (
        <div className="grid grid-cols-3 gap-3">
          {(['A', 'B', 'C'] as BuildingId[]).map(building => (
            <TowerSkeleton key={building} />
          ))}
        </div>
      );
    }

    // If there's a general error and no data at all for any tower, show empty towers
    if (errorTowerData && Object.values(towerData as Record<BuildingId, TowerDisplayData>).every(d => d.consumption === null)) {
      return (
        <div className="grid grid-cols-3 gap-3">
          {(['A', 'B', 'C'] as BuildingId[]).map(building => (
            <TowerOverview
              key={building}
              building={building}
              customData={{
                consumption: null,
                lastYearConsumption: null,
                percentChange: null,
                timeFrame: 'weekly'
              }}
            />
          ))}
        </div>
      );
    }

    // No partial error display - just show the data
    const partialErrorDisplay = null;

    return (
      <>
        {partialErrorDisplay}
        <div className="grid grid-cols-3 gap-3">
          {(['A', 'B', 'C'] as BuildingId[]).map(building => {
            // Ensure there's always a data object, even if it's all nulls (e.g., if a specific tower's fetch failed)
            const data = towerData[building] || { consumption: null, lastYearConsumption: null, percentChange: null };
            return (
              <TowerOverview
                key={building}
                building={building}
                customData={{
                  consumption: data.consumption,
                  lastYearConsumption: data.lastYearConsumption,
                  percentChange: data.percentChange,
                  timeFrame: 'weekly'
                }}
              />
            );
          })}
        </div>
      </>
    );
  };

  return (
    <>

      <div>
        <TowerOverviewsSection />
      </div>

      <DashboardSection
        title="Real-time Power Demand"
        icon={LineChart}
        className="flex flex-col h-[280px]"
        compact={true}
        rightContent={lastUpdatedTimestamp}
      >
        {isLoadingProfile ? (
          <ChartSkeleton height={240} />
        ) : (
          <div className="flex-1 min-h-0">
            <BuildingLoadChart
              data={loadProfileData}
              comparisonData={comparisonData}
              todayTotalKWh={todayDailyKWh}
              yesterdayTotalKWh={yesterdayDailyKWh}
              isLoadingTotals={isLoadingDailyKWh}
              errorTotals={errorDailyKWh}
              view="daily"
              showComparison={true}
            />
          </div>
        )}
      </DashboardSection>

      <DashboardSection
        title="Energy Benchmark"
        icon={BarChart3}
        className=""
        compact={true}
        rightContent={lastUpdatedTimestamp}
      >
        {isLoadingBenchmark || isLoadingSiteMetadata ? (
          <ChartSkeleton height={210} />
        ) : (
          <EnergyBenchmarkChart
            currentYearData={benchmarkApiData}
            previousYearData={previousYearBenchmarkApiData}
            sbtiTargetValue={benchmarkSettings.sbtiTarget}
            pfmTargetValue={benchmarkSettings.pfmTarget}
            baselineYear={benchmarkSettings.baselineYear}
          />
        )}
      </DashboardSection>
    </>
  );
}

// Define prop types for SideContent
interface SideContentProps {
  lastUpdatedTimestamp: React.ReactNode;
  dashboardData: any; // Using any for brevity, could be properly typed
}

function SideContent({ lastUpdatedTimestamp, dashboardData }: SideContentProps) {
  // Extract data from props
  const {
    siteMetadata,
    isLoadingSiteMetadata,
    errorSiteMetadata,

    monthlyConsumption,
    peakDemand,
    energyCost,

    yearlyConsumptionTotal,
    yearlyCO2Emissions,
    yearlyElectricityCost,

    isLoadingPerformance,
    errorPerformance,

    pieChartData,
    isLoadingPieChart,
    errorPieChart
  } = dashboardData;

  const month = new Date().toLocaleString('default', { month: 'long' });

  return (
    <div className="flex flex-col h-full space-y-3">
      <DashboardSection
        title={`${new Date().getFullYear()} Yearly Performance`}
        icon={Activity}
        rightContent={lastUpdatedTimestamp}
      >
        {isLoadingPerformance || isLoadingSiteMetadata ? (
          <PerformanceCardSkeleton />
        ) : (
          <PerformanceSection
            yearlyConsumption={yearlyConsumptionTotal}
            yearlyCO2={yearlyCO2Emissions}
            yearlyCost={yearlyElectricityCost}
            gfa={siteMetadata?.area}
          />
        )}
      </DashboardSection>

      <DashboardSection
        title={SECTION_TITLES.monthlyOverview()}
        icon={Zap}
        rightContent={lastUpdatedTimestamp}
      >
        {isLoadingPerformance || isLoadingSiteMetadata ? (
          <MonthlyOverviewSkeleton />
        ) : (
          <MonthlyOverview
            energy={{
              consumption: monthlyConsumption,
              peak: peakDemand,
              cost: energyCost
            }}
            billing={{
              currentAmount: 1234567.89,
              dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
              paidBills: 12
            }}
          />
        )}
      </DashboardSection>

      <DashboardSection
        title={`${month} Consumption Distribution `}
        icon={PieChart}
        rightContent={lastUpdatedTimestamp}
      >
        {isLoadingPieChart ? (
          <PieChartSkeleton />
        ) : (
          <div className="h-72 flex items-center justify-center">
            <SystemBreakdownDonut data={pieChartData} />
          </div>
        )}
      </DashboardSection>
    </div>
  );
}

// PerformanceSection component (moved from the bottom of the file)
function PerformanceSection({
  yearlyConsumption,
  yearlyCO2,
  yearlyCost,
  gfa
}: {
  yearlyConsumption: number | null;
  yearlyCO2: number | null;
  yearlyCost: number | null;
  gfa?: number | null;
}) {
  const euiValue = gfa && yearlyConsumption && gfa > 0 ? yearlyConsumption / gfa : null;
  const euiColorClass = euiValue !== null ? getEuiColorClass(euiValue) : 'text-gray-500';

  return (
    <div className="space-y-3">
      <div className="p-2 bg-gradient-to-br from-blue-50/60 via-blue-50/40 to-white rounded-lg border border-blue-100/80 hover:shadow-[0_2px_4px_rgba(14,126,228,0.1)] transition-all duration-200">
        <div className="flex items-center justify-between mb-1">
          <h3 className="text-xs font-medium text-gray-700">Energy Use Intensity</h3>
          <div className="text-center">
            <div className="flex items-baseline justify-center gap-1">
              <span className={`text-sm font-bold ${euiColorClass} transition-all duration-300`}>
                {euiValue !== null ? euiValue.toFixed(2) : '-'}
              </span>
              <span className="text-[9px] text-gray-500 whitespace-nowrap ml-0.5">
                kWh/m²·yr
              </span>
            </div>
          </div>
        </div>
        <div className="mt-0.5">
          <EnergyEfficiencyCard
            value={euiValue !== null ? euiValue : 0}
            gfa={gfa ?? BUILDING_CONFIG.gfa}
            hideValue={true}
          />
        </div>
      </div>

      <KeyPerformanceMetrics
        consumption={yearlyConsumption}
        co2Emissions={yearlyCO2}
        cost={yearlyCost}
      />
    </div>
  );
}

function KeyPerformanceMetrics({
  consumption,
  co2Emissions,
  cost
}: {
  consumption: number | null;
  co2Emissions: number | null;
  cost: number | null;
}) {
  const formatDisplay = (value: number | null, unit: string = '', prefix: string = '', decimals: number = 0): string => {
    if (value === null) return '-';
    return `${prefix}${value.toLocaleString(undefined, { minimumFractionDigits: decimals, maximumFractionDigits: decimals })} ${unit}`.trim();
  };

  const formatConsumption = (num: number | null): string => {
    if (num === null) return '-';
    return `${Math.round(num).toLocaleString()} kWh`;
  };

  const formatCO2Emissions = (num: number | null): string => {
    if (num === null) return '-';
    const tonsValue = num / 1000;
    return `${tonsValue.toLocaleString(undefined, {minimumFractionDigits: 0, maximumFractionDigits: 1})} tCO₂e`;
  };

  const formatCost = (num: number | null): string => {
    if (num === null) return '-';
    return `฿${(num / 1000000).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}M`;
  };

  return (
    <div className="grid grid-cols-3 gap-3">
      <div className="bg-gradient-to-br from-blue-50/80 via-blue-50/50 to-white rounded-lg p-2 border border-blue-100 hover:shadow-[0_2px_8px_rgba(14,126,228,0.15)] transition-all duration-300 group">
        <div className="flex items-start gap-1">
          <div className="flex-1">
            <p className="text-[10px] text-gray-500 font-medium mb-0.5">Consumption</p>
            <div className="flex items-baseline gap-1 truncate">
              <p className="text-xs font-bold text-primary-blue">{formatConsumption(consumption)}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-br from-blue-50/80 via-blue-50/50 to-white rounded-lg p-2 border border-blue-100 hover:shadow-[0_2px_8px_rgba(14,126,228,0.15)] transition-all duration-300 group">
        <div className="flex items-start gap-1">
          <div className="flex-1">
            <p className="text-[10px] text-gray-500 font-medium mb-0.5">CO₂ Emissions</p>
            <div className="flex items-baseline gap-1">
              <p className="text-xs font-bold text-primary-blue">{formatCO2Emissions(co2Emissions)}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-br from-blue-50/80 via-blue-50/50 to-white rounded-lg p-2 border border-blue-100 hover:shadow-[0_2px_8px_rgba(14,126,228,0.15)] transition-all duration-300 group">
        <div className="flex items-start gap-1">
          <div className="flex-1">
            <p className="text-[10px] text-gray-500 font-medium mb-0.5">Electricity Cost</p>
            <div className="flex items-baseline gap-1">
              <p className="text-xs font-bold text-primary-blue">{formatCost(cost)}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}