import React, { useState, useEffect } from 'react';
import { Alert<PERSON>riangle, WifiOff, TrendingUp, Bell, CheckCircle2, Search, Filter, Calendar, RefreshCw, Trash2, CheckCheck } from 'lucide-react';
import { formatDate } from '../lib/utils/formatters';
import { NotificationsPageSkeleton, NotificationStatsSkeleton, NotificationListSkeleton } from '@/components/ui/notifications-skeletons';
import { EnhancedErrorDisplay } from '@/components/ui/error-display';
import { useRetry } from '@/hooks/useRetry';

interface Notification {
  id: string;
  type: 'alert' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  source: {
    type: 'system' | 'meter' | 'consumption' | 'device';
    id: string;
    name: string;
  };
}

const NOTIFICATION_ICONS = {
  alert: AlertTriangle,
  warning: TrendingUp,
  info: Bell,
  success: CheckCircle2,
} as const;

const NOTIFICATION_STYLES = {
  alert: 'bg-red-50 border-red-200 hover:bg-red-100',
  warning: 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100',
  info: 'bg-blue-50 border-blue-200 hover:bg-blue-100',
  success: 'bg-green-50 border-green-200 hover:bg-green-100',
} as const;

const NOTIFICATION_ICON_STYLES = {
  alert: 'text-red-600',
  warning: 'text-yellow-600',
  info: 'text-blue-600',
  success: 'text-green-600',
} as const;

// Generate more mock notifications
const mockNotifications: Notification[] = Array.from({ length: 50 }, (_, i) => {
  const types: Notification['type'][] = ['alert', 'warning', 'info', 'success'];
  const sourceTypes: Notification['source']['type'][] = ['system', 'meter', 'consumption', 'device'];
  const type = types[Math.floor(Math.random() * types.length)];
  const sourceType = sourceTypes[Math.floor(Math.random() * sourceTypes.length)];
  
  return {
    id: `${i + 1}`,
    type,
    title: `${type.charAt(0).toUpperCase() + type.slice(1)} Notification ${i + 1}`,
    message: `This is a ${type} notification message for ${sourceType}`,
    timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    isRead: Math.random() > 0.3,
    source: {
      type: sourceType,
      id: `${sourceType.toUpperCase()}-${i + 1}`,
      name: `${sourceType.charAt(0).toUpperCase() + sourceType.slice(1)} ${i + 1}`
    }
  };
});

export default function Notifications() {
  const [selectedType, setSelectedType] = useState<'all' | Notification['type']>('all');
  const [selectedSource, setSelectedSource] = useState<'all' | Notification['source']['type']>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const fetchNotifications = async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1200));
    return mockNotifications;
  };

  const { retry, retryCount, isRetrying } = useRetry(fetchNotifications);

  useEffect(() => {
    const loadNotifications = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await fetchNotifications();
        setNotifications(data);
        setIsLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load notifications'));
        setIsLoading(false);
      }
    };

    loadNotifications();
  }, []);

  const filteredNotifications = notifications.filter(notification => {
    if (selectedType !== 'all' && notification.type !== selectedType) return false;
    if (selectedSource !== 'all' && notification.source.type !== selectedSource) return false;
    if (searchQuery && !notification.title.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !notification.message.toLowerCase().includes(searchQuery.toLowerCase())) return false;
    return true;
  });

  const stats = {
    total: filteredNotifications.length,
    unread: filteredNotifications.filter(n => !n.isRead).length,
    alerts: filteredNotifications.filter(n => n.type === 'alert').length,
    warnings: filteredNotifications.filter(n => n.type === 'warning').length,
  };

  const handleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map(n => n.id));
    }
  };

  const handleMarkAsRead = () => {
    // Implement mark as read functionality
    console.log('Marking selected notifications as read:', selectedNotifications);
  };

  const handleDelete = () => {
    // Implement delete functionality
    console.log('Deleting selected notifications:', selectedNotifications);
  };

  if (isLoading) {
    return <NotificationsPageSkeleton />;
  }

  if (error) {
    return (
      <div className="h-[calc(100vh-56px)] flex items-center justify-center p-4">
        <EnhancedErrorDisplay
          error={error}
          onRetry={retry}
          retryCount={retryCount}
          isRetrying={isRetrying}
          context="notifications"
        />
      </div>
    );
  }

  return (
    <div className="h-[calc(100vh-56px)] flex flex-col gap-4 p-4">
      {/* Stats */}
      <div className="grid grid-cols-4 gap-4 mt-4">
        <div className="bg-white p-4 rounded-xl border border-[#EDEFF9]">
          <div className="text-sm text-gray-500">Total Notifications</div>
          <div className="text-2xl font-semibold mt-1 text-transparent bg-clip-text bg-gradient-to-r from-primary-blue to-blue-600">{stats.total}</div>
        </div>
        <div className="bg-white p-4 rounded-xl border border-[#EDEFF9]">
          <div className="text-sm text-gray-500">Unread</div>
          <div className="text-2xl font-semibold mt-1 text-primary-blue">{stats.unread}</div>
        </div>
        <div className="bg-white p-4 rounded-xl border border-[#EDEFF9]">
          <div className="text-sm text-gray-500">Active Alerts</div>
          <div className="text-2xl font-semibold mt-1 text-red-600">{stats.alerts}</div>
        </div>
        <div className="bg-white p-4 rounded-xl border border-[#EDEFF9]">
          <div className="text-sm text-gray-500">Active Warnings</div>
          <div className="text-2xl font-semibold mt-1 text-yellow-600">{stats.warnings}</div>
        </div>
      </div>

      {/* Notifications List */}
      <div className="flex-1 bg-white rounded-xl border border-[#EDEFF9] overflow-hidden flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={selectedNotifications.length === filteredNotifications.length}
                  onChange={handleSelectAll}
                  className="rounded border-gray-300 text-primary-blue focus:ring-primary-blue"
                />
                <span className="text-sm text-gray-600">Select All</span>
              </label>
              {selectedNotifications.length > 0 && (
                <div className="flex items-center gap-2">
                  <button
                    onClick={handleMarkAsRead}
                    className="flex items-center gap-1 px-2 py-1 text-sm text-primary-blue hover:bg-blue-50 rounded-lg"
                  >
                    <CheckCheck size={16} />
                    <span>Mark as Read</span>
                  </button>
                  <button
                    onClick={handleDelete}
                    className="flex items-center gap-1 px-2 py-1 text-sm text-red-600 hover:bg-red-50 rounded-lg"
                  >
                    <Trash2 size={16} />
                    <span>Delete</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th scope="col" className="w-8 px-3 py-3">
                  <span className="sr-only">Select</span>
                </th>
                <th scope="col" className="w-8 px-3 py-3">
                  <span className="sr-only">Type</span>
                </th>
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Title & Message
                </th>
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Source
                </th>
                <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Time
                </th>
                <th scope="col" className="w-20 px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredNotifications.map((notification) => {
                const Icon = NOTIFICATION_ICONS[notification.type];
                return (
                  <tr
                    key={notification.id}
                    className={`group hover:bg-gray-50 ${!notification.isRead ? 'bg-blue-50/30' : ''}`}
                  >
                    <td className="px-3 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedNotifications.includes(notification.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedNotifications([...selectedNotifications, notification.id]);
                          } else {
                            setSelectedNotifications(selectedNotifications.filter(id => id !== notification.id));
                          }
                        }}
                        className="rounded border-gray-300 text-primary-blue focus:ring-primary-blue"
                      />
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap">
                      <Icon size={20} className={NOTIFICATION_ICON_STYLES[notification.type]} />
                    </td>
                    <td className="px-3 py-4">
                      <div className="flex flex-col">
                        <div className="text-sm font-medium text-gray-900">
                          {notification.title}
                        </div>
                        <div className="text-sm text-gray-500">
                          {notification.message}
                        </div>
                      </div>
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap">
                      <div className="flex flex-col">
                        <div className="text-sm text-gray-900">
                          {notification.source.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {notification.source.type}
                        </div>
                      </div>
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(new Date(notification.timestamp))}
                    </td>
                    <td className="px-3 py-4 whitespace-nowrap">
                      {!notification.isRead && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          New
                        </span>
                      )}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}