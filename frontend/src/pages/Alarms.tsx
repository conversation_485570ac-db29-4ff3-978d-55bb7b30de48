import React, { useState, useEffect } from 'react';
import { AlertCircle, Settings, Mail } from 'lucide-react';
import { useParams, useNavigate } from 'react-router-dom';
import AFDDLogsTab from '@/components/alarms/AFDDLogsTab';
import AlarmRulesTab from '@/components/alarms/AlarmRulesTab';
import EmailSettingsTab from '@/components/alarms/EmailSettingsTab';
import { formatTimestamp } from '@/utils/formatting';

const AlarmsPage: React.FC = () => {
  const { tab } = useParams<{ tab?: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(tab || 'logs');

  useEffect(() => {
    // Update active tab when route changes
    if (tab && ['logs', 'configuration', 'email'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [tab]);

  // Timestamp for consistent formatting like Dash<PERSON>
  const lastUpdatedTimestamp = formatTimestamp(new Date());
  const formattedTimestamp = (
    <span className="text-[10px] text-gray-400">
      Last updated: {lastUpdatedTimestamp}
    </span>
  );

  return (
    <div className="h-[calc(100vh-56px)] p-4 bg-gradient-to-br from-[#F9FAFF] via-white to-blue-50/30">
      {/* Tabs */}
      <div className="border-b border-gray-200 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex space-x-8">
            <button
              className={`py-2 px-1 inline-flex items-center gap-2 border-b-2 font-medium text-xs ${activeTab === 'logs'
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              onClick={() => {
                setActiveTab('logs');
                navigate('/alarms/logs');
              }}
            >
              <AlertCircle size={14} />
              Alarm Log
            </button>
            <button
              className={`py-2 px-1 inline-flex items-center gap-2 border-b-2 font-medium text-xs ${activeTab === 'configuration'
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              onClick={() => {
                setActiveTab('configuration');
                navigate('/alarms/configuration');
              }}
            >
              <Settings size={14} />
              Fault Configuration
            </button>
            <button
              className={`py-2 px-1 inline-flex items-center gap-2 border-b-2 font-medium text-xs ${activeTab === 'email'
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              onClick={() => {
                setActiveTab('email');
                navigate('/alarms/email');
              }}
            >
              <Mail size={14} />
              Email Settings
            </button>
          </div>
          <div className="pb-2">
            {formattedTimestamp}
          </div>
        </div>
      </div>

      {/* Content area */}
      <div className="bg-white rounded-lg shadow-sm h-[calc(100%-4rem)]">
        {activeTab === 'logs' && <AFDDLogsTab />}
        {activeTab === 'configuration' && <AlarmRulesTab />}
        {activeTab === 'email' && <EmailSettingsTab />}
      </div>
    </div>
  );
};

export default AlarmsPage;
