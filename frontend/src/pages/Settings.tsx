import React, { useState, useEffect, lazy, Suspense } from 'react';
import { useSearchParams } from 'react-router-dom';
import { MockDataToggle } from '../components/ui/mock-data-toggle';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import { SettingsPageSkeleton } from '@/components/ui/settings-skeletons';
import { EnhancedErrorDisplay } from '@/components/ui/error-display';
import { useRetry } from '@/hooks/useRetry';

// Lazy load heavy components
const UserManagement = lazy(() => import('../components/settings/UserManagement').then(module => ({ default: module.UserManagement })));
const BuildingMetersManagement = lazy(() => import('../components/settings/BuildingMetersManagement'));
const ScreenshotUploadManager = lazy(() => import('../components/admin/ScreenshotUploadManager'));
const EnergyBenchmarkSettings = lazy(() => import('../components/settings/EnergyBenchmarkSettings').then(module => ({ default: module.EnergyBenchmarkSettings })));
const ElectricityRateConfiguration = lazy(() => import('../components/settings/ElectricityRateConfiguration').then(module => ({ default: module.ElectricityRateConfiguration })));
import { 
  UserCog, 
  TrendingDown, 
  Layers, 
  Camera, 
  Code, 
  ChevronRight,
  ArrowLeft,
  DollarSign
} from 'lucide-react';

interface SettingsCard {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

const settingsCards: SettingsCard[] = [
  {
    id: 'users',
    title: 'User Management',
    description: 'Manage users, roles, and permissions',
    icon: <UserCog className="w-6 h-6" />,
    color: 'bg-indigo-500'
  },
  {
    id: 'electricity-rate',
    title: 'Electricity Rate',
    description: 'Configure electricity billing rates',
    icon: <DollarSign className="w-6 h-6" />,
    color: 'bg-blue-500'
  },
  {
    id: 'benchmarks',
    title: 'Energy Benchmarks',
    description: 'Configure SBTi, PFM targets and baseline year',
    icon: <TrendingDown className="w-6 h-6" />,
    color: 'bg-green-500'
  },
  {
    id: 'meters',
    title: 'Building Meters Management',
    description: 'View and manage all 520 meters across all buildings',
    icon: <Layers className="w-6 h-6" />,
    color: 'bg-purple-500'
  },
  {
    id: 'screenshots',
    title: 'Screenshot Manager',
    description: 'Upload and organize documentation screenshots',
    icon: <Camera className="w-6 h-6" />,
    color: 'bg-orange-500'
  },
  {
    id: 'development',
    title: 'Development',
    description: 'Configure development environment options',
    icon: <Code className="w-6 h-6" />,
    color: 'bg-gray-600'
  }
];

export default function Settings() {
  const [searchParams] = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [activeSection, setActiveSection] = useState<string | null>(null);

  const fetchSettings = async () => {
    // Load settings data
    return { success: true };
  };

  const { retry, retryCount, isRetrying } = useRetry(fetchSettings);

  useEffect(() => {
    const loadSettings = async () => {
      try {
        setIsLoading(true);
        setError(null);
        await fetchSettings();
        setIsLoading(false);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to load settings'));
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);
  
  // Check for section parameter in URL
  useEffect(() => {
    const section = searchParams.get('section');
    if (section && settingsCards.some(card => card.id === section)) {
      setActiveSection(section);
    }
  }, [searchParams]);

  if (isLoading) {
    return <SettingsPageSkeleton />;
  }

  if (error) {
    return (
      <div className="p-4">
        <EnhancedErrorDisplay
          error={error}
          onRetry={retry}
          retryCount={retryCount}
          isRetrying={isRetrying}
          context="settings"
        />
      </div>
    );
  }

  // Render the selected section content
  const renderSectionContent = () => {
    switch (activeSection) {
      case 'users':
        return (
          <Suspense fallback={<SettingsPageSkeleton />}>
            <UserManagement />
          </Suspense>
        );
      case 'electricity-rate':
        return (
          <Suspense fallback={<SettingsPageSkeleton />}>
            <ElectricityRateConfiguration onBack={() => setActiveSection(null)} />
          </Suspense>
        );
      case 'benchmarks':
        return (
          <Suspense fallback={<SettingsPageSkeleton />}>
            <EnergyBenchmarkSettings onBack={() => setActiveSection(null)} />
          </Suspense>
        );
      case 'meters':
        return (
          <Suspense fallback={<SettingsPageSkeleton />}>
            <BuildingMetersManagement />
          </Suspense>
        );
      case 'screenshots':
        return (
          <Suspense fallback={<SettingsPageSkeleton />}>
            <ScreenshotUploadManager />
          </Suspense>
        );
      case 'development':
        return (
          <Card>
            <CardHeader>
              <CardTitle>Development Settings</CardTitle>
              <CardDescription>Configure development environment options</CardDescription>
            </CardHeader>
            <CardContent>
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">Mock Data Mode</h3>
                <MockDataToggle />
                <p className="text-sm text-gray-500 mt-3">
                  When enabled, a floating toggle will appear in the bottom right corner for quick switching between mock and real API data. The floating toggle only temporarily switches data sources without changing this setting.
                </p>
              </div>
            </CardContent>
          </Card>
        );
      default:
        return null;
    }
  };

  return (
    <div className="p-4">
      {activeSection === null ? (
        // Card Navigation View
        <div>
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
            <p className="text-sm text-gray-600 mt-1">Configure system preferences and manage your account</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {settingsCards.map((card) => (
              <button
                key={card.id}
                onClick={() => setActiveSection(card.id)}
                className="group relative bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200 text-left"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className={`inline-flex p-3 rounded-lg ${card.color} text-white mb-4`}>
                      {card.icon}
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{card.title}</h3>
                    <p className="text-sm text-gray-600">{card.description}</p>
                  </div>
                  <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors mt-1" />
                </div>
              </button>
            ))}
          </div>
        </div>
      ) : (
        // Section Content View
        <div className="min-h-[calc(100vh-200px)] flex flex-col">
          <div className="flex-1">
            {renderSectionContent()}
          </div>
          
          {/* Only show back button for sections that don't handle it internally */}
          {activeSection !== 'benchmarks' && (
            <div className="mt-8 pt-6 border-t border-gray-200">
              <button
                onClick={() => setActiveSection(null)}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span className="text-sm font-medium">Back to Settings</span>
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}