import React, { useState } from 'react';
import { Book, Search, Printer, LayoutDashboard, Zap, BarChart3, FileText, Settings } from 'lucide-react';
import { ManualChapter, ManualSection } from '../components/Manual/ManualTypes'; // Import types

// Import content data
import introductionSections from '../components/Manual/data/introduction';
import dashboardSections from '../components/Manual/data/dashboard';
import analyticsSections from '../components/Manual/data/analytics';
// Add imports for other chapters as you create them

// --- Assemble the Full Manual Content --- 
const ALL_CHAPTERS: ManualChapter[] = [
  {
    id: 'introduction',
    title: 'Introduction',
    icon: <Book size={18} />,
    sections: introductionSections,
  },
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: <LayoutDashboard size={18} />,
    sections: dashboardSections,
  },
   {
    id: 'analytics',
    title: 'Analytics',
    icon: <BarChart3 size={18} />,
    sections: analyticsSections,
  },
  // Add other chapter objects here
];
// --- End Full Manual Content ---

const ManualSidebar: React.FC<{
  chapters: ManualChapter[];
  activeChapterId: string;
  activeSectionId: string;
  onSelectSection: (chapterId: string, sectionId: string) => void;
}> = ({ chapters, activeChapterId, activeSectionId, onSelectSection }) => {
  return (
    <nav className="w-64 h-full border-r border-gray-200 bg-white overflow-y-auto p-4 space-y-6 print:hidden">
      {chapters.map((chapter) => (
        <div key={chapter.id}>
          <h3 className="flex items-center gap-2 text-sm font-semibold text-gray-800 mb-3">
            {chapter.icon}
            {chapter.title}
          </h3>
          <ul className="space-y-1">
            {chapter.sections.map((section) => (
              <li key={section.id}>
                <button
                  onClick={() => onSelectSection(chapter.id, section.id)}
                  className={`w-full text-left px-3 py-1.5 text-xs rounded-md transition-colors ${
                    activeChapterId === chapter.id && activeSectionId === section.id
                      ? 'bg-blue-50 text-blue-700 font-medium'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  }`}
                >
                  {section.title}
                </button>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </nav>
  );
};

const ManualContentDisplay: React.FC<{ content: React.ReactNode }> = ({ content }) => {
  // Add print:block for printing
  return (
    <div className="flex-1 p-6 lg:p-8 overflow-y-auto print:block print:overflow-visible">
      <article className="prose prose-sm max-w-none">
         {content}
      </article>
    </div>
  );
};

const ManualHeader: React.FC<{ title: string; onExport: () => void }> = ({ title, onExport }) => {
  // Add print:hidden
  return (
    <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50 sticky top-0 z-10 print:hidden">
      <h2 className="text-lg font-semibold text-gray-800">{title}</h2>
      <div className="flex items-center gap-4">
        <div className="relative">
           <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
           <input
             type="text"
             placeholder="Search manual..."
             className="pl-8 pr-2 py-1.5 text-xs border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 w-48"
           />
        </div>
        <button
          onClick={onExport}
          className="flex items-center gap-1.5 px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-md hover:bg-blue-700 transition-colors"
        >
          <Printer size={14} />
          Export PDF
        </button>
      </div>
    </div>
  );
};


export default function Manual() {
  // Use the imported ALL_CHAPTERS data
  const [activeChapterId, setActiveChapterId] = useState<string>(ALL_CHAPTERS[0]?.id || '');
  const [activeSectionId, setActiveSectionId] = useState<string>(ALL_CHAPTERS[0]?.sections[0]?.id || '');

  const handleSelectSection = (chapterId: string, sectionId: string) => {
    setActiveChapterId(chapterId);
    setActiveSectionId(sectionId);
  };

  const handleExport = () => {
    console.log('Exporting manual to PDF...');
    window.print();
  };

  // Find content from ALL_CHAPTERS
  const currentChapter = ALL_CHAPTERS.find(c => c.id === activeChapterId);
  const currentSection = currentChapter?.sections.find(s => s.id === activeSectionId);
  const currentContent = currentSection?.content || <p>Select a section.</p>;
  const currentTitle = `${currentChapter?.title || ''} - ${currentSection?.title || ''}`;

  return (
    // Added print styles container if needed, or apply to body for print
    <div className="flex h-[calc(100vh-48px)] bg-gray-50 print:block print:h-auto">
        <ManualSidebar
            chapters={ALL_CHAPTERS}
            activeChapterId={activeChapterId}
            activeSectionId={activeSectionId}
            onSelectSection={handleSelectSection}
        />
        <div className="flex flex-col flex-1 overflow-hidden print:overflow-visible">
            <ManualHeader title={currentTitle} onExport={handleExport} />
            <ManualContentDisplay content={currentContent} />
        </div>
    </div>
  );
}
