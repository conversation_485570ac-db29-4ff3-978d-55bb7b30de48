import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Calendar, ChevronDown, TrendingUp, TrendingDown, ArrowLeft, ArrowRight, CheckSquare, Download, FileText, FileSpreadsheet } from 'lucide-react';
import {
  XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  BarChart, Bar
} from 'recharts';
import AnalyticsControls from '../components/analytics/AnalyticsControls';
import ConsumptionTab from '../components/analytics/ConsumptionTab';
import ComparisonTab from '../components/analytics/ComparisonTab';
// Import mock data
import { mockPerformanceData } from '../lib/mockData';
import { generateMockChartData as mockDataGenerator } from '../utils/mockDataGenerator';

// Ensure our types match the ones defined in AnalyticsControls.tsx
type BuildingType = 'all' | 'A' | 'B';
type ViewType = 'day' | 'week' | 'month' | 'year' | 'multi-year';
type AnalyticsTab = 'consumption' | 'comparison';

// Define types that match our mock data structure
interface DataPoint {
  time: string;
  value: number;
}

interface HourlyPoint {
  hour: number;
  value: number;
}

interface PeakDemand {
  value: number;
  time: string;
}

interface PerformanceData {
  current: DataPoint[];
  comparison: DataPoint[];
  target: DataPoint[];
  hourlyConsumption: HourlyPoint[];
  totalConsumption: number;
  peakDemand: PeakDemand;
  averageLoad: number;
}

// Comparison period options
export type ComparisonPeriod = 'day' | 'week' | 'month' | 'year' | 'custom' | 'none';

// Constants
const COLORS = {
  // Primary palette - Tesla-inspired blues
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  // Data visualization colors
  data: {
    actual: '#2563eb',     // Primary blue for actual data
    baseline: '#93c5fd',   // Lighter blue for baseline/previous
    target: '#10b981',     // Green for targets
    warning: '#f59e0b',    // Amber for warnings
    danger: '#ef4444',     // Red for negative/danger
    info: '#3b82f6',       // Blue for informational
    comparison: '#d1d5db', // Gray for comparison
  },
  // Text colors
  text: {
    primary: '#111827',   // Near black
    secondary: '#4b5563', // Dark gray
    tertiary: '#9ca3af',  // Medium gray
    light: '#f9fafb',     // Near white
  },
  // Background colors
  background: {
    primary: '#ffffff',   // White
    secondary: '#f3f4f6', // Light gray
    tertiary: '#e5e7eb',  // Medium gray
    highlight: '#f0f9ff', // Very light blue
  },
  // Semantic colors
  semantic: {
    success: '#10b981', // Green
    warning: '#f59e0b', // Amber
    danger: '#ef4444',  // Red
    info: '#3b82f6',    // Blue
  },
  // Border colors
  border: {
    light: '#e5e7eb',    // Light gray
    medium: '#d1d5db',   // Medium gray
    dark: '#9ca3af',     // Dark gray
    highlight: '#bae6fd', // Light blue
  },
  // Gradients
  gradients: {
    blueToGreen: 'linear-gradient(to right, #2563eb, #10b981)',
    lightToDark: 'linear-gradient(to bottom, #f9fafb, #e5e7eb)',
  }
};

// Define consistent typography styles
const TYPOGRAPHY = {
  heading: {
    h1: "text-2xl font-bold text-gray-900",
    h2: "text-xl font-semibold text-gray-800",
    h3: "text-lg font-semibold text-gray-800",
    h4: "text-base font-medium text-gray-800",
  },
  body: {
    large: "text-base text-gray-700",
    medium: "text-sm text-gray-700",
    small: "text-xs text-gray-600",
  },
  label: {
    large: "text-base font-medium text-gray-700",
    medium: "text-sm font-medium text-gray-700",
    small: "text-xs font-medium text-gray-600",
  },
  metric: {
    value: "text-2xl font-bold text-gray-900",
    unit: "text-sm font-medium text-gray-600",
    change: "text-sm font-medium",
  }
};

// Styles object for consistent typography and spacing
const STYLES = {
  // Typography
  heading: TYPOGRAPHY.heading.h1,
  subheading: TYPOGRAPHY.heading.h2,
  bodyText: TYPOGRAPHY.body.medium,
  label: TYPOGRAPHY.label.medium,
  value: TYPOGRAPHY.metric.value,
  smallValue: TYPOGRAPHY.metric.unit,

  // Layout
  container: "max-w-6xl mx-auto px-8 py-6",
  section: "mb-8",
  card: "bg-white rounded-lg shadow-sm p-6 mb-6",
  metricsGrid: "grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",
  metricCard: "bg-white rounded-lg shadow-sm p-6",
  metricTitle: "flex justify-between items-center mb-2",
  metricValue: "text-2xl font-bold text-gray-900 mb-2",
  metricSubtitle: "text-sm text-gray-600",

  // Controls
  controlGroup: "flex flex-wrap items-center justify-between mb-6 gap-3",
  controlItem: "flex items-center gap-2",

  // Buttons
  button: "px-4 py-2 text-sm font-medium rounded-md transition-all duration-200",
  buttonPrimary: "bg-blue-600 text-white hover:bg-blue-700",
  buttonSecondary: "bg-white text-gray-700 border border-gray-300 hover:bg-gray-50",

  // Tabs
  tabContainer: "flex border-b border-gray-200 mb-6",
  tab: "px-6 py-3 text-sm font-medium cursor-pointer",
  activeTab: "border-b-2 border-blue-600 text-blue-600 font-semibold",
  inactiveTab: "text-gray-500 hover:text-gray-700 hover:bg-gray-50",

  // Trends
  trendUp: "text-xs font-medium text-green-600 flex items-center gap-1",
  trendDown: "text-xs font-medium text-red-600 flex items-center gap-1",
};

// View options
const viewOptions = [
  { value: 'year' as ViewType, label: 'Yearly' },
  { value: 'multi-year' as ViewType, label: 'Multi-Year' }
];

// Building options
const buildingOptions = [
  { value: 'all' as BuildingType, label: 'All Buildings' },
  { value: 'A' as BuildingType, label: 'Building A' },
  { value: 'B' as BuildingType, label: 'Building B' }
];

const PerformanceAnalytics = () => {
  // Use imported types for state
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [selectedTab, setSelectedTab] = useState<AnalyticsTab>('consumption');
  const [selectedView, setSelectedView] = useState<ViewType>('year');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [showCumulative, setShowCumulative] = useState<boolean>(false);
  const [comparisonPeriod, setComparisonPeriod] = useState<ComparisonPeriod>('week');
  const [customComparisonStartDate, setCustomComparisonStartDate] = useState<Date | null>(null);
  const [customComparisonEndDate, setCustomComparisonEndDate] = useState<Date | null>(null);

  // For yearly view
  const currentYear = new Date().getFullYear();
  const [selectedYears, setSelectedYears] = useState<number[]>([currentYear]);
  const [selectedBuilding, setSelectedBuilding] = useState<BuildingType>('all');
  const [yearSelectorOpen, setYearSelectorOpen] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('Performance');

  // Refs
  const yearSelectorRef = useRef<HTMLDivElement>(null);
  const chartContainerRef = useRef<HTMLDivElement>(null);

  // For analytics tabs
  const handleTabChange = (tab: AnalyticsTab) => {
    setSelectedTab(tab);
  };

  // For view type
  const onViewChange = (view: ViewType) => {
    setSelectedView(view);
  };

  // For data
  const [analyticsData, setAnalyticsData] = useState<PerformanceData>({
    current: [],
    comparison: [],
    target: [],
    hourlyConsumption: [],
    totalConsumption: 0,
    peakDemand: { value: 0, time: '' },
    averageLoad: 0,
  });

  // Get available years for selection - compute once
  const availableYears = useMemo(() =>
    Array.from({ length: 10 }, (_, i) => currentYear - 9 + i),
    [currentYear]
  );

  // Implement lazy loading for the chart
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (chartContainerRef.current) {
      observer.observe(chartContainerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  // Load performance data when view, building, or years change
  useEffect(() => {
    // Show loading indicator
    setIsLoading(true);

    // Simulate API delay for a more realistic experience
    const loadingTimeout = setTimeout(() => {
      // Get the appropriate mock data based on view and building
      const viewType = selectedView === 'year' ? 'yearly' : 'multiYear';
      const buildingData = mockPerformanceData[viewType][selectedBuilding];

      // Set the data
      setAnalyticsData(buildingData);

      // Hide loading indicator
      setIsLoading(false);
    }, 500); // Simulate a short loading delay

    return () => {
      clearTimeout(loadingTimeout);
    };
  }, [selectedView, selectedBuilding, selectedYears]);

  // Handle view change
  const handleViewChange = useCallback((view: ViewType) => {
    setSelectedView(view);

    // Reset selected years when changing to/from multi-year view
    if (view === 'multi-year') {
      // Default to last 5 years when switching to multi-year
      setSelectedYears([currentYear - 4, currentYear - 3, currentYear - 2, currentYear - 1, currentYear]);
    } else {
      setSelectedYears([]);
    }
  }, [currentYear]);

  // Handle building change
  const handleBuildingChange = useCallback((building: BuildingType) => {
    setSelectedBuilding(building);
  }, []);

  // Handle year selection
  const handleYearSelection = useCallback((year: number) => {
    setSelectedYears(prev => {
      if (prev.includes(year)) {
        // Remove year if already selected
        return prev.filter(y => y !== year);
      } else {
        // Add year if not already selected (limit to 10 years)
        return prev.length < 10 ? [...prev, year].sort() : prev;
      }
    });
  }, []);

  // Handle custom comparison date range change
  const handleCustomComparisonRangeChange = useCallback((startDate: Date | null, endDate: Date | null) => {
    setCustomComparisonStartDate(startDate);
    setCustomComparisonEndDate(endDate);

    // Set period to custom only if both dates are valid
    if (startDate && endDate) {
      setComparisonPeriod('custom');
    } else {
      // If range is cleared while it was 'custom', revert to default period (e.g., 'week')
      if (comparisonPeriod === 'custom') {
         setComparisonPeriod('week');
      }
      // If the period wasn't 'custom' (e.g. user selected 'week' then opened date picker),
      // clearing the dates shouldn't change the period from 'week'.
    }
  }, [comparisonPeriod]); // Keep dependency

  // Transform the data for the ConsumptionTab component
  const transformedData = useMemo(() => {
    console.log("[PerformanceAnalytics] Generating transformedData", {
      analyticsData,
      selectedView,
      selectedTab
    });

    if (!analyticsData) {
      // Generate mock data for comparison tab when real data isn't available
      const mockData = mockDataGenerator(selectedView);
      console.log("[PerformanceAnalytics] Generated mock data:", mockData);

      // Return a default data structure with mock comparison data
      return {
        hourlyConsumption: [],
        totalConsumption: 0,
        peakDemand: { value: 0, time: '' },
        averageLoad: 0,
        current: mockData.current,
        comparison: mockData.comparison,
        target: []
      };
    }

    // Transform hourlyConsumption to match the expected format
    const transformedHourlyConsumption = analyticsData.hourlyConsumption.map(point => ({
      time: `${point.hour}:00`,
      actual: point.value,
      // Add any other required fields
    }));

    // If we have real data but no comparison data, generate mock comparison data
    if (!analyticsData.current || !analyticsData.comparison) {
      const mockData = mockDataGenerator(selectedView);
      console.log("[PerformanceAnalytics] Supplementing with mock data:", mockData);

      return {
        ...analyticsData,
        hourlyConsumption: transformedHourlyConsumption,
        current: analyticsData.current || mockData.current,
        comparison: analyticsData.comparison || mockData.comparison
      };
    }

    return {
      ...analyticsData,
      hourlyConsumption: transformedHourlyConsumption
    };
  }, [analyticsData, selectedView, selectedTab]);

  // State for export dropdown
  const [showExportOptions, setShowExportOptions] = useState(false);
  const exportRef = useRef<HTMLDivElement>(null);

  // Handle export options
  const handleExport = (format: 'pdf' | 'csv') => {
    // This would be connected to actual export functionality in the future
    console.log(`[PerformanceAnalytics] handleExport called with format: ${format}`);

    // Close the dropdown
    setShowExportOptions(false);

    // Show a toast or notification (simplified for now)
    alert(`Data exported as ${format.toUpperCase()}`);
  };

  // Close export dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (exportRef.current && !exportRef.current.contains(event.target as Node)) {
        setShowExportOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Render efficiency chart
  const renderEfficiencyChart = () => {
    if (!analyticsData || !analyticsData.current || analyticsData.current.length === 0) {
      return (
        <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
          <p className="text-gray-500">No data available</p>
        </div>
      );
    }

    return (
      <div className="relative">
        <div className="flex justify-between items-center mb-2">
          <h3 className={TYPOGRAPHY.heading.h2}>
            {selectedView === 'multi-year' ? 'Multi-Year Energy Consumption' : 'Annual Energy Consumption'}
          </h3>

          {/* Export Button with Dropdown */}
          <div className="relative" ref={exportRef}>
            <button
              onClick={() => {
                console.log("[PerformanceAnalytics] Export button clicked, toggling dropdown.");
                setShowExportOptions(!showExportOptions)
              }}
              className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-blue-50 hover:border-blue-300 transition-all duration-200"
            >
              <Download size={16} className={`text-blue-500 transition-transform duration-200 ${showExportOptions ? 'transform rotate-180' : ''}`} />
              Export
              <ChevronDown size={14} className={`text-gray-500 transition-transform duration-200 ${showExportOptions ? 'rotate-180' : ''}`} />
            </button>

            {showExportOptions && (
              <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-300 rounded-md shadow-lg z-10 animate-fadeIn">
                <div className="py-2">
                  <button
                    onClick={() => handleExport('pdf')}
                    className="flex items-center gap-2 w-full px-4 py-2.5 text-sm text-gray-700 hover:bg-red-50 transition-colors duration-150"
                  >
                    <FileText size={16} className="text-red-500 drop-shadow-sm" />
                    <span className={TYPOGRAPHY.label.medium}>Export as PDF</span>
                  </button>
                  <button
                    onClick={() => handleExport('csv')}
                    className="flex items-center gap-2 w-full px-4 py-2.5 text-sm text-gray-700 hover:bg-green-50 transition-colors duration-150"
                  >
                    <FileText size={16} className="text-green-500 drop-shadow-sm" />
                    <span className={TYPOGRAPHY.label.medium}>Export as CSV</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {isLoading ? (
          <div className="h-80 flex items-center justify-center my-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={analyticsData.current}
                margin={{ top: 10, right: 30, left: 20, bottom: 40 }}
                barGap={5}  // Increase gap for better readability
                barCategoryGap={10}  // Adjust category gap
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" vertical={false} />
                <XAxis
                  dataKey="time"
                  tick={{ fill: COLORS.text.secondary, fontSize: 12 }}
                  axisLine={{ stroke: COLORS.border.light }}
                  tickLine={{ stroke: COLORS.border.light }}
                  angle={-45}
                  textAnchor="end"
                  height={70}
                  padding={{ left: 16, right: 16 }}
                  dy={10}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fill: COLORS.text.secondary, fontSize: 13, fontWeight: 500 }}
                  width={60}
                  tickFormatter={(value) => value.toFixed(1)}
                  domain={[0, 'auto']}
                  label={{
                    value: 'GWh',
                    angle: -90,
                    position: 'insideLeft',
                    fill: COLORS.text.secondary,
                    fontSize: 14,
                    fontWeight: 500,
                    dx: -10
                  }}
                />
                <Tooltip content={<CustomTooltip selectedView={selectedView} />} />
                <Legend
                  content={<CustomLegend />}
                  verticalAlign="top"
                  height={50}
                />

                <Bar
                  dataKey="value"
                  name="Actual"
                  fill={COLORS.primary[600]} // Use primary brand blue
                  radius={[4, 4, 0, 0]}
                  maxBarSize={24}
                  opacity={1}
                  stroke={COLORS.primary[700]}
                  strokeWidth={1}
                />
                <Bar
                  dataKey="value"
                  name="Baseline"
                  fill={COLORS.data.baseline}
                  radius={[2, 2, 0, 0]}
                  maxBarSize={24}
                  opacity={0.8}
                />
                <Bar
                  dataKey="value"
                  name="Target"
                  fill={COLORS.data.target}
                  radius={[2, 2, 0, 0]}
                  maxBarSize={24}
                  opacity={1}
                  stroke={COLORS.semantic.success}
                  strokeWidth={1}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}
      </div>
    );
  };

  // Custom legend to provide better explanations
  const CustomLegend = React.memo((props: any) => {
    const { payload } = props;

    return (
      <div className="flex flex-wrap justify-center gap-6 mt-3 mb-4 pt-3">
        {payload.map((entry: any, index: number) => (
          <div key={`legend-${index}`} className="flex items-center gap-2">
            <div className="w-4 h-4 rounded-sm" style={{
              backgroundColor: entry.color,
              border: entry.value === 'Target'
                ? `1px solid ${COLORS.semantic.success}`
                : entry.value === 'Actual'
                  ? `1px solid ${COLORS.primary[700]}`
                  : 'none'
            }}>
              {entry.value === 'Target' && (
                <div className="w-2 h-2 bg-white rounded-sm"></div>
              )}
            </div>
            <span className={TYPOGRAPHY.label.medium}>{entry.value}</span>
          </div>
        ))}
      </div>
    );
  });

  // Custom tooltip component to provide better context
  const CustomTooltip = React.memo(({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      // Get all values from the payload
      const currentValue = payload.find((p: any) => p.dataKey === 'value')?.value || 0;
      const comparisonValue = payload.find((p: any) => p.dataKey === 'value')?.value || 0;
      const targetValue = payload.find((p: any) => p.dataKey === 'value')?.value || 0;

      // Calculate differences and percentage changes for context
      const vsComparison = currentValue - comparisonValue;
      const vsComparisonPercent = ((vsComparison / comparisonValue) * 100).toFixed(1);
      const vsTarget = currentValue - targetValue;
      const vsTargetPercent = ((vsTarget / targetValue) * 100).toFixed(1);

      return (
        <div className="custom-tooltip p-4 bg-white border border-gray-200 rounded-lg shadow-md text-sm">
          <p className={`${TYPOGRAPHY.heading.h4} mb-4 border-b pb-3`}>
            {selectedView === 'year' ? `Month: ${label}` : `Year: ${label}`}
          </p>

          <div className="space-y-4">
            <div className="flex items-center justify-between gap-3">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: COLORS.data.comparison }}></div>
                <span className={TYPOGRAPHY.label.medium}>Baseline</span>
              </div>
              <span className={TYPOGRAPHY.metric.value}>{comparisonValue.toFixed(2)} GWh</span>
            </div>

            <div className="flex items-center justify-between gap-3">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: COLORS.data.target }}></div>
                <span className={TYPOGRAPHY.label.medium}>Target</span>
              </div>
              <span className={TYPOGRAPHY.metric.value}>{targetValue.toFixed(2)} GWh</span>
            </div>

            <div className="flex items-center justify-between gap-3 border-t border-b py-2 my-1">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-sm" style={{ backgroundColor: COLORS.data.actual }}></div>
                <span className={TYPOGRAPHY.label.medium}>Actual</span>
              </div>
              <span className={`${TYPOGRAPHY.metric.value}`} style={{ color: COLORS.data.actual }}>{currentValue.toFixed(2)} GWh</span>
            </div>
          </div>

          {/* Comparison insights */}
          <div className="mt-4 pt-2 bg-gray-50 p-3 rounded-md">
            <div className={`${TYPOGRAPHY.label.small} mb-1`}>PERFORMANCE SUMMARY</div>
            <div className={`flex items-center gap-1 ${vsComparison < 0 ? 'text-green-600' : 'text-red-600'}`}>
              <span>{vsComparison < 0 ? '↓' : '↑'}</span>
              <span className={TYPOGRAPHY.metric.change}>{Math.abs(vsComparison).toFixed(2)} GWh ({Math.abs(Number(vsComparisonPercent))}%)</span>
              <span className={TYPOGRAPHY.body.small}>vs Baseline</span>
            </div>
            <div className={`flex items-center gap-1 ${vsTarget < 0 ? 'text-green-600' : 'text-red-600'}`}>
              <span>{vsTarget < 0 ? '↓' : '↑'}</span>
              <span className={TYPOGRAPHY.metric.change}>{Math.abs(vsTarget).toFixed(2)} GWh ({Math.abs(Number(vsTargetPercent))}%)</span>
              <span className={TYPOGRAPHY.body.small}>vs Target</span>
            </div>
          </div>
        </div>
      );
    }

    return null;
  });

  // Render metrics based on active tab
  const renderMetrics = () => {
    if (isLoading) {
      return (
        <div className={STYLES.metricsGrid}>
          {[...Array(3)].map((_, index) => (
            <div key={index} className={`${STYLES.metricCard} animate-pulse`}>
              <div className="flex justify-between items-start mb-2">
                <div className="h-4 bg-gray-200 w-24 rounded"></div>
                <div className="h-4 bg-gray-200 w-6 rounded-full"></div>
              </div>
              <div className="h-6 bg-gray-300 w-16 mb-2 rounded"></div>
              <div className="h-3 bg-gray-200 w-20 rounded"></div>
            </div>
          ))}
        </div>
      );
    }

    // If no data, don't render anything
    if (!analyticsData) {
      return (
        <div className={STYLES.metricsGrid}>
          <div className="col-span-full p-4 bg-white rounded-lg text-center text-gray-500">
            No performance data available
          </div>
        </div>
      );
    }

    const {
      current,
      comparison,
      target
    } = analyticsData;

    // Determine if the metrics are good or bad
    const savingsGood = current[current.length - 1].value < comparison[comparison.length - 1].value;
    const carbonGood = current[current.length - 1].value < target[target.length - 1].value;
    const consumptionChangeGood = current[current.length - 1].value <= target[target.length - 1].value;

    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Energy Savings */}
        <div className={STYLES.metricCard} style={{
          borderTop: `3px solid ${COLORS.data.target}`,
          background: `linear-gradient(to bottom, ${COLORS.primary[50]}, white 15%)`
        }}>
          <div className={STYLES.metricTitle}>
            <span className={TYPOGRAPHY.label.medium}>Energy Savings</span>
          </div>
          <div className={STYLES.metricValue}>
            {current[current.length - 1].value.toLocaleString()} kWh
          </div>
          <div className={savingsGood ? STYLES.trendUp : STYLES.trendDown}>
            {savingsGood ? <TrendingUp size={14} /> : <TrendingDown size={14} />}
            <span>vs baseline</span>
          </div>
        </div>

        {/* Carbon Reduction */}
        <div className={STYLES.metricCard} style={{
          borderTop: `3px solid ${COLORS.data.danger}`,
          background: `linear-gradient(to bottom, ${COLORS.primary[50]}, white 15%)`
        }}>
          <div className={STYLES.metricTitle}>
            <span className={TYPOGRAPHY.label.medium}>Carbon Reduction</span>
          </div>
          <div className={STYLES.metricValue}>
            {(current[current.length - 1].value * 0.429).toLocaleString()} kg
          </div>
          <div className={carbonGood ? STYLES.trendUp : STYLES.trendDown}>
            {carbonGood ? <TrendingUp size={14} /> : <TrendingDown size={14} />}
            <span>CO₂ avoided</span>
          </div>
        </div>

        {/* % Change vs Previous Period */}
        <div className={STYLES.metricCard} style={{
          borderTop: `3px solid ${consumptionChangeGood ? COLORS.data.target : COLORS.data.danger}`,
          background: `linear-gradient(to bottom, ${COLORS.primary[50]}, white 15%)`
        }}>
          <div className={STYLES.metricTitle}>
            <span className={TYPOGRAPHY.label.medium}>Period-over-Period</span>
          </div>
          <div className={STYLES.metricValue}>
            {Math.abs((current[current.length - 1].value - comparison[comparison.length - 1].value) / comparison[comparison.length - 1].value * 100).toFixed(1)}%
          </div>
          <div className={consumptionChangeGood ? STYLES.trendUp : STYLES.trendDown}>
            {consumptionChangeGood ? <TrendingUp size={14} /> : <TrendingDown size={14} />}
            <span>{consumptionChangeGood ? 'decrease' : 'increase'} vs previous period</span>
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    // Set default selected years when view changes
    if (selectedView === 'year') {
      setSelectedYears([currentYear]);
    } else if (selectedView === 'multi-year') {
      setSelectedYears([currentYear - 2, currentYear - 1, currentYear]);
    }
  }, [selectedView, currentYear]);

  // Tab selection and content
  return (
    <div className={STYLES.container}>
      {/* Page Header */}
      <div className={STYLES.section}>
        <h1 className={`${TYPOGRAPHY.heading.h1} mb-2`}>Energy Efficiency Analysis</h1>
        <p className={`${TYPOGRAPHY.body.medium} mb-6`}>
          Analyze energy consumption performance against baselines and targets to identify savings.
        </p>
      </div>

      {/* Controls */}
      <div className="flex flex-wrap items-center gap-x-5 gap-y-4 w-full md:w-auto">
        {/* View Selector */}
        <div className="flex items-center gap-2">
          <span className={TYPOGRAPHY.label.medium}>View:</span>
          <div className="inline-flex rounded-md shadow-sm">
            {viewOptions.map(option => (
              <button
                key={option.value}
                className={selectedView === option.value ? "px-3 py-1.5 text-xs font-medium bg-blue-600 text-white border border-gray-300 -ml-px first:ml-0 first:rounded-l-md" : "px-3 py-1.5 text-xs font-medium bg-white text-gray-600 hover:bg-gray-50 border border-gray-300 -ml-px first:ml-0"}
                onClick={() => handleViewChange(option.value)}
                aria-pressed={selectedView === option.value}
                aria-label={`Select ${option.label} view`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>

        {/* Building Selector */}
        <div className="flex items-center gap-2">
          <span className={TYPOGRAPHY.label.medium}>Building:</span>
          <select
            value={selectedBuilding}
            onChange={(e) => handleBuildingChange(e.target.value as BuildingType)}
            className="appearance-none bg-white border border-gray-300 rounded-md py-1 pl-2.5 pr-8 text-xs font-medium text-gray-700 focus:outline-none"
          >
            {buildingOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Unified Date Control */}
      <div className="mb-4">
        <UnifiedDateControl
          selectedView={selectedView}
          selectedDate={selectedDate}
          onViewChange={onViewChange}
          onDateChange={setSelectedDate}
        />
      </div>

      {/* Bottom Controls Row */}
      <div className="flex justify-between items-center mb-4">
        {/* Cumulative Toggle */}
        <div className="flex items-center gap-2">
          <span className={TYPOGRAPHY.label.medium}>Standard</span>
          <div className="relative inline-flex items-center cursor-pointer">
            <input
              id="cumulative-toggle"
              type="checkbox"
              checked={showCumulative}
              onChange={(e) => setShowCumulative(e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
          </div>
          <span className={TYPOGRAPHY.label.medium}>Cumulative</span>
        </div>

        {/* Comparison Period (only shown when needed) */}
        {comparisonPeriod !== 'none' && (
          <div className="flex items-center gap-2">
            <span className={TYPOGRAPHY.label.medium}>Comparison Period:</span>
            <select
              value={comparisonPeriod}
              onChange={(e) => setComparisonPeriod(e.target.value as ComparisonPeriod)}
              className="appearance-none bg-white border border-gray-300 rounded-md py-1 pl-2.5 pr-8 text-xs font-medium text-gray-700 focus:outline-none"
            >
              <option value="week">Week</option>
              <option value="month">Month</option>
              <option value="quarter">Quarter</option>
              <option value="year">Year</option>
              <option value="custom">Custom</option>
            </select>

            {comparisonPeriod === 'custom' && (
              <>
                <span className={TYPOGRAPHY.label.medium}>Custom Range:</span>
                <input
                  type="date"
                  value={customComparisonStartDate?.toISOString().split('T')[0] || ''}
                  onChange={(e) => setCustomComparisonStartDate(new Date(e.target.value))}
                  className="bg-white border border-gray-300 rounded-md py-1 pl-2.5 pr-8 text-xs font-medium text-gray-700 focus:outline-none"
                />
                <span className={TYPOGRAPHY.label.medium}>to</span>
                <input
                  type="date"
                  value={customComparisonEndDate?.toISOString().split('T')[0] || ''}
                  onChange={(e) => setCustomComparisonEndDate(new Date(e.target.value))}
                  className="bg-white border border-gray-300 rounded-md py-1 pl-2.5 pr-8 text-xs font-medium text-gray-700 focus:outline-none"
                />
              </>
            )}
          </div>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="mb-6 border-b border-gray-200">
        <nav className="-mb-px flex space-x-6" aria-label="Tabs">
          <button
            className={`whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'Performance'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-900 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('Performance')}
            aria-current={activeTab === 'Performance' ? 'page' : undefined}
          >
            Performance
          </button>
          <button
            className={`whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'Benchmarks'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-900 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('Benchmarks')}
            aria-current={activeTab === 'Benchmarks' ? 'page' : undefined}
          >
            Benchmarks
          </button>
          <button
            className={`whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'Reports'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-900 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('Reports')}
            aria-current={activeTab === 'Reports' ? 'page' : undefined}
          >
            Reports
          </button>
          <button
            className={`whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'Analytics'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-900 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('Analytics')}
            aria-current={activeTab === 'Analytics' ? 'page' : undefined}
          >
            Analytics
          </button>
        </nav>
      </div>

      {/* Tab Content Area */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 min-h-[300px]">
        {/* Render content based on active tab */}
        {activeTab === 'Performance' && (
          <div>
            {renderMetrics()}
            {renderEfficiencyChart()}
          </div>
        )}
        {activeTab === 'Benchmarks' && (
          <div className="p-8 bg-white rounded-lg shadow">
            <h3 className={`${TYPOGRAPHY.heading.h2} mb-4`}>Benchmarks</h3>
            <p className={TYPOGRAPHY.body.medium}>Benchmark data will be displayed here.</p>
          </div>
        )}
        {activeTab === 'Reports' && (
          <div className="p-8 bg-white rounded-lg shadow">
            <h3 className={`${TYPOGRAPHY.heading.h2} mb-4`}>Reports</h3>
            <p className={TYPOGRAPHY.body.medium}>Report data will be displayed here.</p>
          </div>
        )}
        {activeTab === 'Analytics' && (
          <div>
            {/* Analytics Controls Component */}
            <AnalyticsControls
              selectedTab={selectedTab}
              selectedView={selectedView}
              selectedDate={selectedDate}
              showCumulative={showCumulative}
              comparisonPeriod={comparisonPeriod}
              onTabChange={handleTabChange}
              onViewChange={onViewChange}
              onDateChange={setSelectedDate}
              onCumulativeToggle={setShowCumulative}
              onComparisonPeriodChange={(period) => setComparisonPeriod(period)}
            />

            {/* Analytics Content */}
            <div className="mt-4">
              {selectedTab === 'consumption' && (
                <ConsumptionTab
                  chartData={transformedData}
                  view={selectedView}
                  showCumulative={showCumulative}
                />
              )}

              {selectedTab === 'comparison' &&
                selectedView &&
                comparisonPeriod !== 'none' && (
                <ComparisonTab
                  selectedView={selectedView}
                  comparisonPeriod={comparisonPeriod}
                  customComparisonStartDate={customComparisonStartDate}
                  customComparisonEndDate={customComparisonEndDate}
                  onCustomRangeChange={handleCustomComparisonRangeChange}
                  chartData={{
                    current: transformedData.current || [],
                    comparison: transformedData.comparison || []
                  }}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceAnalytics;
