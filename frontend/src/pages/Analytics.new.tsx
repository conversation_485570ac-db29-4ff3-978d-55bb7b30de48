import React, { useState, useCallback } from 'react';
import { ViewType, BuildingType, AnalyticsTab } from '../types/analytics';
import useAnalyticsData from '../hooks/useAnalyticsData';
import ControlPanel from '../components/analytics/ControlPanel';
import MetricCards from '../components/analytics/MetricCards';
import ConsumptionTab from '../components/analytics/ConsumptionTab';
import ComparisonTab from '../components/analytics/ComparisonTab';
import PerformanceTab from '../components/analytics/PerformanceTab';
import SystemBreakdown from '../components/analytics/SystemBreakdown';

const Analytics = React.memo(() => {
  // State for the selected filters
  const [selectedView, setSelectedView] = useState<ViewType>('day');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedBuilding, setSelectedBuilding] = useState<BuildingType>('all');
  const [selectedTab, setSelectedTab] = useState<AnalyticsTab>('consumption');
  const [showCumulative, setShowCumulative] = useState<boolean>(false);

  // Use our custom hook to fetch and manage data
  const analyticsState = useAnalyticsData(selectedView, selectedDate, selectedBuilding);

  // Render the current tab based on selection
  const renderCurrentTab = useCallback(() => {
    switch (selectedTab) {
      case 'consumption':
        return (
          <ConsumptionTab
            loading={analyticsState.loading}
            chartData={analyticsState.chartData.current}
            selectedDate={selectedDate}
            selectedView={selectedView}
            showCumulative={showCumulative}
          />
        );
      case 'comparison':
        return (
          <ComparisonTab
            loading={analyticsState.loading}
            chartData={analyticsState.chartData}
            selectedDate={selectedDate}
            selectedView={selectedView}
          />
        );
      case 'performance':
        return (
          <PerformanceTab
            loading={analyticsState.loading}
            chartData={analyticsState.chartData.current}
            selectedView={selectedView}
            selectedDate={selectedDate}
          />
        );
      default:
        return null;
    }
  }, [
    selectedTab,
    analyticsState.loading,
    analyticsState.chartData,
    selectedDate,
    selectedView,
    showCumulative
  ]);

  return (
    <div className="bg-gray-50 p-6 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Page Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold">Analytics</h1>
          <p className="text-gray-500">Analyze your electricity performance data</p>
        </div>

        {/* Controls for filters */}
        <ControlPanel
          selectedView={selectedView}
          selectedDate={selectedDate}
          selectedBuilding={selectedBuilding}
          selectedTab={selectedTab}
          setSelectedView={setSelectedView}
          setSelectedDate={setSelectedDate}
          setSelectedBuilding={setSelectedBuilding}
          setSelectedTab={setSelectedTab}
          showCumulative={showCumulative}
          setShowCumulative={setShowCumulative}
        />

        {/* Key Metrics Cards */}
        <MetricCards
          loading={analyticsState.loading}
          totalConsumption={analyticsState.totalConsumption}
          peakDemand={analyticsState.peakDemand}
          averageLoad={analyticsState.averageLoad}
          selectedView={selectedView}
        />

        {/* Main Content Area */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column: Chart Data */}
          <div className="lg:col-span-2">
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
              {renderCurrentTab()}
            </div>
          </div>
          
          {/* Right Column: System Breakdown */}
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
            <SystemBreakdown
              loading={analyticsState.loading}
              systemBreakdown={analyticsState.systemBreakdown}
            />
          </div>
        </div>
      </div>
    </div>
  );
});

export default Analytics;
