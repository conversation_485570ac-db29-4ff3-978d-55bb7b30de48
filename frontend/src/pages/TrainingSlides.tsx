import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ChevronLeft, 
  Home, 
  BarChart3, 
  Gauge, 
  AlertCircle, 
  Settings, 
  Users,
  FileText,
  Zap,
  Building,
  GitCompare,
  PlayCircle,
  Layers
} from 'lucide-react';
import { ExportAllSlidesPDFButton } from '../components/training/ExportAllSlidesPDF';
import { ExportSlidesAsImagesPDF } from '../components/training/ExportSlidesAsImages';

interface PresentationModule {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  slides: number;
  roles: string[];
}

const presentations: PresentationModule[] = [
  {
    id: 'overview',
    title: 'Dashboard Overview',
    description: 'Learn how to navigate and use the main dashboard effectively',
    icon: <Home size={32} />,
    slides: 12,
    roles: ['All Users', 'Facility Manager', 'Energy Analyst']
  },
  {
    id: 'analytics',
    title: 'Analytics & Reporting',
    description: 'Master energy consumption analysis and report generation',
    icon: <BarChart3 size={32} />,
    slides: 15,
    roles: ['Energy Analyst', 'Facility Manager', 'Executive']
  },
  {
    id: 'meters',
    title: 'Meter Management',
    description: 'Understanding meter hierarchy and real-time monitoring',
    icon: <Gauge size={32} />,
    slides: 10,
    roles: ['Facility Manager', 'Technician', 'Energy Analyst']
  },
  {
    id: 'alarms',
    title: 'Alarms & Notifications',
    description: 'Configure and manage system alerts and email notifications',
    icon: <AlertCircle size={32} />,
    slides: 8,
    roles: ['Facility Manager', 'Technician', 'Administrator']
  },
  {
    id: 'comparison',
    title: 'Meter Comparison',
    description: 'Compare multiple meters and analyze performance differences',
    icon: <GitCompare size={32} />,
    slides: 6,
    roles: ['Energy Analyst', 'Facility Manager']
  },
  {
    id: 'diagram',
    title: 'Electricity Meter Diagram',
    description: 'Visualize power distribution across the building',
    icon: <Zap size={32} />,
    slides: 7,
    roles: ['All Users', 'Technician', 'Facility Manager']
  },
  {
    id: 'settings',
    title: 'System Settings',
    description: 'Configure system preferences and user management',
    icon: <Settings size={32} />,
    slides: 9,
    roles: ['Administrator', 'IT Manager']
  },
  {
    id: 'quick-start',
    title: 'Quick Start Guide',
    description: 'Get started with the Energy Management System in 5 minutes',
    icon: <Building size={32} />,
    slides: 5,
    roles: ['All Users', 'New Staff']
  }
];

export default function TrainingSlides() {
  const navigate = useNavigate();

  const handlePresentationClick = (id: string) => {
    navigate(`/training/${id}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F9FAFF] via-white to-blue-50/30">
      {/* Header */}
      <div className="px-8 py-6 border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-800">
                Choose a module to start your training session
              </h2>
            </div>
          </div>
          <div className="flex gap-3">
            <ExportAllSlidesPDFButton />
            <ExportSlidesAsImagesPDF />
          </div>
        </div>
      </div>

      {/* Presentation Grid */}
      <div className="p-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {/* Complete Training Card */}
          <div
            onClick={() => navigate('/training/all')}
            className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl border-2 border-green-300 p-6 hover:shadow-lg hover:scale-[1.02] transition-all cursor-pointer group"
          >
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform">
              <PlayCircle size={32} />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Complete Training Course
            </h3>
            <p className="text-sm text-gray-600 mb-4 line-clamp-2">
              Take all training modules in sequence for a comprehensive learning experience
            </p>
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <FileText size={14} />
                <span>{presentations.reduce((total, p) => total + p.slides, 0)} total slides</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                  Complete Course
                </span>
              </div>
            </div>
          </div>

          {/* Individual Presentation Cards */}
          {presentations.map((presentation) => (
            <div
              key={presentation.id}
              onClick={() => handlePresentationClick(presentation.id)}
              className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg hover:scale-[1.02] transition-all cursor-pointer group relative"
            >
              {/* Icon */}
              <div>
                <div className="w-16 h-16 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl flex items-center justify-center text-blue-600 mb-4 group-hover:scale-110 transition-transform">
                  {presentation.icon}
                </div>

                {/* Title */}
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {presentation.title}
                </h3>

                {/* Description */}
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {presentation.description}
                </p>
              </div>

              {/* Metadata */}
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <FileText size={14} />
                <span>{presentation.slides} slides</span>
              </div>

            </div>
          ))}
        </div>

      </div>
    </div>
  );
}