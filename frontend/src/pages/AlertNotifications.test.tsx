import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AlertNotifications from './AlertNotifications';

// Mock the API calls
jest.mock('../api/notifications', () => ({
  fetchNotifications: jest.fn(() => Promise.resolve([
    { 
      id: '1', 
      title: 'High Voltage Alert', 
      description: 'Voltage exceeded threshold on Meter A', 
      read: false, 
      type: 'alert',
      severity: 'high', 
      timestamp: '2025-03-22T09:30:00Z',
      source: 'Meter A'
    },
    { 
      id: '2', 
      title: 'Power Quality Issue', 
      description: 'Harmonic distortion detected', 
      read: true, 
      type: 'alert',
      severity: 'medium', 
      timestamp: '2025-03-22T08:45:00Z',
      source: 'Meter B'
    },
    { 
      id: '3', 
      title: 'System Update', 
      description: 'New features available', 
      read: false, 
      type: 'info',
      severity: 'low', 
      timestamp: '2025-03-21T14:20:00Z',
      source: 'System'
    }
  ])),
  markAsRead: jest.fn(() => Promise.resolve({ success: true })),
  deleteNotifications: jest.fn(() => Promise.resolve({ success: true }))
}));

describe('AlertNotifications Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the component with notifications', async () => {
    render(<AlertNotifications />);
    
    // Check for loading state initially
    expect(screen.getByText(/loading notifications/i)).toBeInTheDocument();
    
    // Wait for notifications to load
    await waitFor(() => {
      expect(screen.getByText('High Voltage Alert')).toBeInTheDocument();
    });
    
    // Verify all notifications are displayed
    expect(screen.getByText('Power Quality Issue')).toBeInTheDocument();
    expect(screen.getByText('System Update')).toBeInTheDocument();
  });

  test('allows selecting a notification', async () => {
    render(<AlertNotifications />);
    
    // Wait for notifications to load
    await waitFor(() => {
      expect(screen.getByText('High Voltage Alert')).toBeInTheDocument();
    });
    
    // Find first notification and select it
    const firstNotificationCheckbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(firstNotificationCheckbox);
    
    // Check if bulk actions appears (this depends on your UI implementation)
    expect(screen.getByText(/selected/i)).toBeInTheDocument();
  });

  test('shows notification details when clicking on a notification', async () => {
    render(<AlertNotifications />);
    
    // Wait for notifications to load
    await waitFor(() => {
      expect(screen.getByText('High Voltage Alert')).toBeInTheDocument();
    });
    
    // Click on a notification
    const firstNotification = screen.getByText('High Voltage Alert').closest('li');
    if (firstNotification) {
      fireEvent.click(firstNotification);
    }
    
    // Check if details panel is shown
    expect(screen.getByText(/details/i)).toBeInTheDocument();
    expect(screen.getByText(/overview/i)).toBeInTheDocument();
    expect(screen.getByText(/waveform/i)).toBeInTheDocument();
  });

  test('can mark notifications as read', async () => {
    const { fetchNotifications, markAsRead } = require('../api/notifications');
    render(<AlertNotifications />);
    
    // Wait for notifications to load
    await waitFor(() => {
      expect(screen.getByText('High Voltage Alert')).toBeInTheDocument();
    });
    
    // Select the first notification
    const firstNotificationCheckbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(firstNotificationCheckbox);
    
    // Click mark as read button
    const markAsReadButton = screen.getByLabelText(/mark as read/i);
    fireEvent.click(markAsReadButton);
    
    // Verify API was called
    expect(markAsRead).toHaveBeenCalledWith(['1']);
    
    // Verify UI updates after marking as read
    await waitFor(() => {
      expect(fetchNotifications).toHaveBeenCalledTimes(2); // Once on load, once after marking as read
    });
  });

  test('can delete notifications', async () => {
    const { fetchNotifications, deleteNotifications } = require('../api/notifications');
    render(<AlertNotifications />);
    
    // Wait for notifications to load
    await waitFor(() => {
      expect(screen.getByText('High Voltage Alert')).toBeInTheDocument();
    });
    
    // Select the first notification
    const firstNotificationCheckbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(firstNotificationCheckbox);
    
    // Click delete button
    const deleteButton = screen.getByLabelText(/delete/i);
    fireEvent.click(deleteButton);
    
    // Confirm deletion (assuming there's a confirmation dialog)
    const confirmButton = screen.getByText(/confirm/i);
    fireEvent.click(confirmButton);
    
    // Verify API was called
    expect(deleteNotifications).toHaveBeenCalledWith(['1']);
    
    // Verify UI updates after deletion
    await waitFor(() => {
      expect(fetchNotifications).toHaveBeenCalledTimes(2); // Once on load, once after deletion
    });
  });

  test('can filter notifications by tab', async () => {
    render(<AlertNotifications />);
    
    // Wait for notifications to load
    await waitFor(() => {
      expect(screen.getByText('High Voltage Alert')).toBeInTheDocument();
    });
    
    // Click on the Alerts tab
    const alertsTab = screen.getByText(/alerts/i, { selector: 'button' });
    fireEvent.click(alertsTab);
    
    // Verify only alert type notifications are visible
    expect(screen.getByText('High Voltage Alert')).toBeInTheDocument();
    expect(screen.getByText('Power Quality Issue')).toBeInTheDocument();
    expect(screen.queryByText('System Update')).not.toBeInTheDocument();
    
    // Click on the Unread tab
    const unreadTab = screen.getByText(/unread/i, { selector: 'button' });
    fireEvent.click(unreadTab);
    
    // Verify only unread notifications are visible
    expect(screen.getByText('High Voltage Alert')).toBeInTheDocument();
    expect(screen.queryByText('Power Quality Issue')).not.toBeInTheDocument();
    expect(screen.getByText('System Update')).toBeInTheDocument();
  });

  test('can search for notifications', async () => {
    render(<AlertNotifications />);
    
    // Wait for notifications to load
    await waitFor(() => {
      expect(screen.getByText('High Voltage Alert')).toBeInTheDocument();
    });
    
    // Find search input and type in it
    const searchInput = screen.getByPlaceholderText(/search/i);
    fireEvent.change(searchInput, { target: { value: 'voltage' } });
    
    // Verify filtered results
    expect(screen.getByText('High Voltage Alert')).toBeInTheDocument();
    expect(screen.queryByText('Power Quality Issue')).not.toBeInTheDocument();
    expect(screen.queryByText('System Update')).not.toBeInTheDocument();
  });
});
