/**
 * Electricity Meter Diagram Component
 *
 * This implementation provides a 2D visualization of the building's electrical distribution system
 * using React Flow for interactive node-based diagrams.
 *
 * Key features:
 * - Interactive React Flow implementation with clickable nodes for navigation
 * - Real-time power flow visualization
 * - Status-based visual indicators
 * - Hierarchical component structure
 */

import { memo, useState, useEffect } from 'react';
import { InfoIcon } from 'lucide-react';
import { MeterDiagramView, MeterInfoSidebar } from '../components/meters/MeterDiagramView';
import SimpleMeterDiagram from '../components/meters/SimpleMeterDiagram';
import { ViewMode } from '../components/common/ViewToggle';
import { fetchDeviceById, fetchChildDeviceRelationsById, isUsingMockData } from '../lib/services/meterDiagramService';
import { SoftwareVersion } from '../components/common/SoftwareVersion';
import { Activity, Zap } from 'lucide-react';
import { 
  getAllMainMetersFlat,
  getAllTowerBuildingMeters,
  getAllPodiumBuildingMeters,
  getAllCarParkBuildingMeters,
  getTowerAMeters,
  getTowerBMeters,
  getTowerCMeters
} from '../lib/config/building/meters';

const SIDEBAR_METER_GROUP_CONFIG = [
  {
    id: 'main',
    label: 'Main Unit',
    deviceId: 'main',
    powerDataKeyPath: ['power', 'value'],
  },
  {
    id: 'towerA',
    label: 'Tower Building',
    deviceId: 'tower_a', // Placeholder
    powerDataKeyPath: ['power', 'value'],
  },
  {
    id: 'towerB',
    label: 'Podium Building',
    deviceId: 'tower_b', // Placeholder
    powerDataKeyPath: ['power', 'value'],
  },
  {
    id: 'towerC',
    label: 'Car Park Building',
    deviceId: 'tower_c', // Placeholder
    powerDataKeyPath: ['power', 'value'],
  },
  {
    id: 'tenant',
    label: 'Tenant',
    deviceId: 'tower_a_6th_floor_tenants', // Placeholder
    powerDataKeyPath: ['power', 'value'],
  },
  {
    id: 'plant',
    label: 'Plant',
    deviceId: 'chiller_plant', // Placeholder
    powerDataKeyPath: ['power', 'value'],
  },
  {
    id: 'dataCenter',
    label: 'Data Center',
    deviceId: 'data_center_and_others', // Placeholder
    powerDataKeyPath: ['power', 'value'],
  },
];

// Helper to safely access nested properties
const getNestedValue = (obj: any, path: string[]): number | undefined | null => {
  return path.reduce((currentObject, key) => currentObject?.[key], obj) as number | undefined | null;
};

// Helper to determine status
const getStatusFromPower = (power: number | undefined | null): 'active' | 'warning' | 'disconnected' => {
  if (power === undefined || power === null) return 'disconnected';
  if (power === 0) return 'warning';
  return 'active';
};

interface MeterGroupData {
  id: string;
  label: string;
  meterCount: number;
  power: number;
  status: 'active' | 'warning' | 'disconnected';
}

function MeterDiagramPage() {
  const [viewMode, setViewMode] = useState<ViewMode>('2d');
  const [meterGroupsData, setMeterGroupsData] = useState<MeterGroupData[]>([]);
  const [totalMetersCount, setTotalMetersCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  // Add state for selected node
  const [selectedNode, setSelectedNode] = useState<string | null>(null);

  // Handle node selection
  const handleNodeSelect = (nodeId: string) => {
    setSelectedNode(nodeId);
    // Any other logic needed when a node is selected
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const groupDataPromises = SIDEBAR_METER_GROUP_CONFIG.map(async (groupConfig) => {
          let power: number | undefined | null = 0;
          let meterCount = 0;
          let status: 'active' | 'warning' | 'disconnected' = 'disconnected';

          try {
            const deviceDetails = await fetchDeviceById(groupConfig.deviceId, ['latest_data']);
            power = getNestedValue(deviceDetails.latest_data, groupConfig.powerDataKeyPath);

            const relations = await fetchChildDeviceRelationsById(groupConfig.deviceId);
            meterCount = relations.length;
            status = getStatusFromPower(power);
          } catch (e) {
            console.warn(`Sidebar: Failed to fetch data for group ${groupConfig.id} (device: ${groupConfig.deviceId}):`, e);
          }
          return {
            id: groupConfig.id,
            label: groupConfig.label,
            meterCount,
            power: power !== undefined && power !== null ? parseFloat(power.toFixed(2)) : 0,
            status,
          };
        });

        const resolvedGroupData = await Promise.all(groupDataPromises);
        setMeterGroupsData(resolvedGroupData);

        const mainGroupConfig = SIDEBAR_METER_GROUP_CONFIG.find(g => g.id === 'main');
        if (mainGroupConfig && mainGroupConfig.deviceId) {
          try {
            // For total meters in sidebar, count children of the *actual main physical meter*
            // The deviceId for 'main' in SIDEBAR_METER_GROUP_CONFIG should point to this.
            const mainRelations = await fetchChildDeviceRelationsById(mainGroupConfig.deviceId);
            setTotalMetersCount(296); // Total meters in our configuration
          } catch (e) {
            console.warn(`Sidebar: Failed to fetch total meter count for main device ${mainGroupConfig.deviceId}:`, e);
            setTotalMetersCount(296);
          }
        } else {
          console.warn('Sidebar: Main group deviceId not configured for total meter count.');
          setTotalMetersCount(296);
        }

      } catch (err) {
        console.error('Sidebar: Global error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred.');
        setMeterGroupsData(SIDEBAR_METER_GROUP_CONFIG.map(gc => ({
          id: gc.id, label: gc.label, meterCount: 0, power: 0, status: 'disconnected'
        })));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
    // Optional: Refresh interval
    // const intervalId = setInterval(fetchData, 30000);
    // return () => clearInterval(intervalId);
  }, []);

  return (
    <div className="h-[calc(100vh-56px)] bg-gradient-to-br from-[#F9FAFF] via-white to-blue-50/30 overflow-hidden">

      {/* Content area - Improved layout with better spacing */}
      <div className="flex flex-1 px-6 py-6 gap-6" style={{ height: 'calc(100vh - 56px)' }}>
        {/* Main diagram card */}
        <div className="flex-1 bg-white rounded-xl border border-[#EDEFF9] shadow-sm overflow-hidden">
          <div className="h-full flex flex-col">
            {/* Diagram header */}
            <div className="px-4 py-3 border-b border-gray-100 bg-gradient-to-r from-blue-50/50 to-transparent">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Zap size={18} className="text-blue-600" />
                  <h2 className="text-sm font-medium text-gray-700">Power Flow Diagram</h2>
                </div>
                <div className="text-xs text-gray-500">
                  Click on any node to view details
                </div>
              </div>
            </div>
            {/* Diagram content */}
            <div className="flex-1">
              {viewMode === '2d' ?
                <SimpleMeterDiagram
                  key="diagram-2d"
                  viewMode={viewMode}
                  onViewChange={setViewMode}
                /> :
                <MeterDiagramView
                  key="diagram-3d"
                  viewMode={viewMode}
                  onViewChange={setViewMode}
                  onNodeSelect={handleNodeSelect}
                />}
            </div>
          </div>
        </div>

        {/* System overview sidebar */}
        <div className="w-[380px] bg-white rounded-xl border border-[#EDEFF9] shadow-sm overflow-hidden p-4">
          <MeterInfoSidebar
            meterGroupsData={meterGroupsData}
            totalMetersCount={totalMetersCount}
            loading={loading}
            error={error}
          />
        </div>
      </div>
      
      {/* Software version */}
      <SoftwareVersion />
    </div>
  );
}

// Optimize rendering with React.memo
export default memo(MeterDiagramPage);
