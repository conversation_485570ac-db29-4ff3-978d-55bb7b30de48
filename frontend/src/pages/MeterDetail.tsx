import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Gauge, ArrowLeft, Activity, Clock, Zap, AlertTriangle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/Button';
import { FlatTabBar } from '@/components/ui/FlatTabBar';
import { StatCard } from '@/components/ui/StatCard';
import { PowerDemandChart } from '@/components/charts/PowerDemandChart';
import { format } from 'date-fns';
import { ActiveAlarm } from '@/types/alarms';
import { getActiveAlarms } from '@/lib/api/alarms';
import RuleDetailDrawer from '@/components/alarms/RuleDetailDrawer';
import { fetchDeviceById, DeviceDetails } from '@/services/deviceService';

interface PowerQualityMetrics {
  // Phase Power Distribution (Currents)
  currentL1: number;
  currentL2: number;
  currentL3: number;
  
  // Line-to-Line Voltages (380V nominal)
  voltageL1L2: number;
  voltageL2L3: number;
  voltageL3L1: number;
  
  // Line-to-Neutral Voltages (220V nominal)
  voltageL1N: number;
  voltageL2N: number;
  voltageL3N: number;
  
  // Other metrics
  powerFactor: number;
  frequency: number;
  power: number;
  reactivePower: number;
  currentThdL1: number;
  currentThdL2: number;
  currentThdL3: number;
  voltageThdL1L2: number;
  voltageThdL2L3: number;
  voltageThdL3L1: number;
}

interface EnergyData {
  daily: number;
  weekly: number;
  monthly: number;
  peak: number;
}

const MeterDetailPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const meterName = searchParams.get('meter');
  const [relatedAlarms, setRelatedAlarms] = useState<ActiveAlarm[]>([]);
  const [unreadAlarms, setUnreadAlarms] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [isRuleDrawerOpen, setIsRuleDrawerOpen] = useState(false);
  const [ruleDrawerMeterId, setRuleDrawerMeterId] = useState<number | null>(null);
  
  // Real data from API
  const [powerQuality, setPowerQuality] = useState<PowerQualityMetrics | null>(null);
  const [energyData, setEnergyData] = useState<EnergyData | null>(null);
  const [deviceData, setDeviceData] = useState<DeviceDetails | null>(null);

  // Fetch real-time device data
  useEffect(() => {
    const fetchMeterData = async () => {
      if (!meterName) return;
      
      setIsLoading(true);
      try {
        // Fetch real-time device data with latest_data expansion
        const device = await fetchDeviceById(meterName, ['latest_data']);
        setDeviceData(device);
        
        // Helper function to get latest value from device data
        const getLatestValue = (fieldName: string): number => {
          const fieldData = device.latest_data?.[fieldName];
          return typeof fieldData?.value === 'number' ? fieldData.value : 0;
        };

        // Map device API response to PowerQualityMetrics interface
        const processedPowerQuality: PowerQualityMetrics = {
          // Phase Power Distribution (Currents) - directly from API
          currentL1: getLatestValue('current_l1'),
          currentL2: getLatestValue('current_l2'),
          currentL3: getLatestValue('current_l3'),
          
          // Line-to-Line Voltages (380V nominal) - directly from API
          voltageL1L2: getLatestValue('voltage_l1l2'),
          voltageL2L3: getLatestValue('voltage_l2l3'),
          voltageL3L1: getLatestValue('voltage_l3l1'),
          
          // Line-to-Neutral Voltages (220V nominal) - directly from API
          voltageL1N: getLatestValue('voltage_l1'),
          voltageL2N: getLatestValue('voltage_l2'),
          voltageL3N: getLatestValue('voltage_l3'),
          
          // Other metrics - directly from API
          powerFactor: getLatestValue('power_factor'),
          frequency: getLatestValue('frequency'),
          currentThdL1: getLatestValue('current_thd_l1'),
          currentThdL2: getLatestValue('current_thd_l2'),
          currentThdL3: getLatestValue('current_thd_l3'),
          voltageThdL1L2: getLatestValue('voltage_thd_l1l2'),
          voltageThdL2L3: getLatestValue('voltage_thd_l2l3'),
          voltageThdL3L1: getLatestValue('voltage_thd_l3l1'),
          power: getLatestValue('power'),
          reactivePower: getLatestValue('reactive_power'),
        };

        setPowerQuality(processedPowerQuality);
        setEnergyData({
          daily: 20,
          weekly: 140,
          monthly: 600,
          peak: 20
        });

        console.log("TESTTTTTTESTTTTTTESTTTTTTESTTTTTTESTTTTTTESTTTTTTESTTTTTTESTTTTT", processedPowerQuality);
        // Also fetch alarms
        const response = await getActiveAlarms();
        const filteredAlarms = response.results.filter(
          alarm => alarm.meter_name === meterName
        );
        setRelatedAlarms(filteredAlarms);
        setUnreadAlarms(Math.min(Math.floor(Math.random() * 3) + 1, filteredAlarms.length));

      } catch (error) {
        console.error('Error fetching meter data:', error);
        // Fallback to default values on error
        const fallbackLineToNeutral = 220 + Math.random() * 10;
        const fallbackCurrent = 15 + Math.random() * 5;
        setPowerQuality({
          // Phase Power Distribution (Currents)
          currentL1: fallbackCurrent + Math.random() * 3 - 1.5,
          currentL2: fallbackCurrent + Math.random() * 3 - 1.5,
          currentL3: fallbackCurrent + Math.random() * 3 - 1.5,
          
          // Line-to-Line Voltages (380V nominal)
          voltageL1L2: 380 + Math.random() * 10 - 5,
          voltageL2L3: 380 + Math.random() * 10 - 5,
          voltageL3L1: 380 + Math.random() * 10 - 5,
          
          // Line-to-Neutral Voltages (220V nominal)
          voltageL1N: fallbackLineToNeutral + Math.random() * 5 - 2.5,
          voltageL2N: fallbackLineToNeutral + Math.random() * 5 - 2.5,
          voltageL3N: fallbackLineToNeutral + Math.random() * 5 - 2.5,
          
          // Other metrics
          powerFactor: 0.85 + Math.random() * 0.1,
          frequency: 49.8 + Math.random() * 0.4,
          currentThdL1: 2 + Math.random() * 3,
          currentThdL2: 2 + Math.random() * 3,
          currentThdL3: 2 + Math.random() * 3,
          voltageThdL1L2: 2 + Math.random() * 3,
          voltageThdL2L3: 2 + Math.random() * 3,
          voltageThdL3L1: 2 + Math.random() * 3,
          power: 45 + Math.random() * 15,
          reactivePower: 30 + Math.random() * 10
        });
        setEnergyData({
          daily: 240 + Math.random() * 60,
          weekly: 1680 + Math.random() * 320,
          monthly: 7200 + Math.random() * 1200,
          peak: 45 + Math.random() * 15
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchMeterData();
  }, [meterName]);

  // Add handler to open drawer
  const handleOpenCreateRule = () => {
    if (!meterName) return;
    // Convert meterName (string) to a numeric ID for demo/mock
    let numericId = Number(meterName);
    if (isNaN(numericId)) {
      // fallback: use char codes sum as mock unique number
      numericId = meterName.split('').reduce((acc, c) => acc + c.charCodeAt(0), 0);
    }
    setRuleDrawerMeterId(numericId);
    setIsRuleDrawerOpen(true);
  };

  // Add handler to update in-memory rules after creation
  const handleRuleCreated = (newRule: any) => {
    if (newRule.meter_ids && newRule.meter_ids.includes(ruleDrawerMeterId)) {
      setRelatedAlarms(prev => [...prev, newRule]);
    }
  };

  if (!meterName) {
    return (
      <div className="h-full flex flex-col items-center justify-center text-gray-500">
        <Gauge size={48} className="mb-4" />
        <p>No meter specified</p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => navigate('/alarms')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Alarms
        </Button>
      </div>
    );
  }

  return (
    <main className="h-[calc(100vh-56px)] px-4 pb-2 pt-0 relative bg-gradient-to-br from-[#F9FAFF] via-white to-blue-50/30 overflow-hidden">
      <div className="container mx-auto p-4 flex flex-col h-full overflow-auto">
        {/* Header with back button */}
        <div className="flex items-center mb-3">
          <Button
            variant="outline"
            size="sm"
            className="mr-3 h-8 px-2 text-xs"
            onClick={() => navigate('/alarms')}
          >
            <ArrowLeft className="mr-1 h-3 w-3" />
            Back to Alarms
          </Button>
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-gradient-to-br from-blue-100/80 via-blue-100/50 to-white border border-blue-200/50 shadow-[0_2px_8px_rgba(14,126,228,0.12)]">
              <Gauge size={16} className="text-primary-blue" />
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">{meterName || 'Unknown Meter'}</h1>
              <p className="text-xs text-gray-500">Last updated: {format(new Date(), 'MMM dd, yyyy HH:mm:ss')}</p>
            </div>
          </div>
          {/* Create Rule Button */}
          <Button
            variant="default"
            className="ml-auto px-4 py-2 text-sm font-medium bg-blue-600 text-white hover:bg-blue-700 rounded-md shadow-sm"
            onClick={handleOpenCreateRule}
          >
            + Create Rule
          </Button>
        </div>

        {/* Main content */}
        <div className="flex-1">
          {/* Sticky tabs container */}
          <div className="sticky top-0 z-10 bg-white shadow-sm">
            <FlatTabBar
              value={activeTab}
              onValueChange={setActiveTab}
              tabs={[
                {
                  value: 'overview',
                  label: 'Overview',
                  icon: <Zap className="h-4 w-4" />,
                  content: null
                },
                {
                  value: 'alarms',
                  label: (
                    <div className="flex items-center">
                      Alarms
                      {unreadAlarms > 0 && (
                        <span className="ml-1.5 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
                          {unreadAlarms}
                        </span>
                      )}
                    </div>
                  ),
                  icon: <AlertTriangle className="h-4 w-4" />,
                  content: null
                }
              ]}
            />

            {/* Badge is now integrated directly in the tab label */}
          </div>

          <div className="space-y-3 pt-3">
            {activeTab === 'overview' && (
              <>
                {/* Status Card */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
                  <StatCard
                    title="Status"
                    value="Online"
                    variant="success"
                    icon={<div className="w-2 h-2 rounded-full bg-green-500 animate-pulse" />}
                  />

                  <StatCard
                    title="Daily Energy"
                    value={energyData?.daily.toFixed(1) || '0.0'}
                    unit="kWh"
                    icon={<Zap size={16} className="text-primary-blue" />}
                  />

                  <StatCard
                    title="Weekly Energy"
                    value={energyData?.weekly.toFixed(1) || '0.0'}
                    unit="kWh"
                    icon={<Zap size={16} className="text-primary-blue" />}
                  />

                  <StatCard
                    title="Peak Demand (Today)"
                    value={energyData?.peak.toFixed(1) || '0.0'}
                    unit="kW"
                    icon={<Zap size={16} className="text-primary-blue" />}
                  />
                </div>

                {/* Power Quality Metrics */}
                <div className="p-3 rounded-xl border border-[#EDEFF9] bg-gradient-to-br from-blue-50/30 via-blue-50/20 to-transparent">
                  <div className="flex items-center gap-2 mb-2">
                    <Activity size={14} className="text-primary-blue" />
                    <h3 className="text-xs font-semibold">Power Quality Metrics</h3>
                  </div>
                  <div className="space-y-4">
                    {/* Basic Metrics */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      <StatCard
                        title="Power Factor"
                        value={powerQuality?.powerFactor.toFixed(2) || '0.00'}
                        variant="primary"
                        className="border-0 shadow-none p-0"
                      />
                      <StatCard
                        title="Frequency"
                        value={powerQuality?.frequency.toFixed(1) || '50.0'}
                        unit="Hz"
                        variant="primary"
                        className="border-0 shadow-none p-0"
                      />
                      <StatCard
                        title="THD"
                        value={powerQuality?.currentThdL1.toFixed(1) || '0.0'}
                        unit="%"
                        variant="primary"
                        className="border-0 shadow-none p-0"
                      />
                      <StatCard
                        title="Reactive Power"
                        value={powerQuality?.reactivePower.toFixed(1) || '0.0'}
                        unit="kVAR"
                        variant="primary"
                        className="border-0 shadow-none p-0"
                      />
                    </div>
                    
                    {/* 3-Phase Power Quality */}
                    <div className="space-y-3">
                      {/* Phase Power Distribution */}
                      <div>
                        <h4 className="text-xs font-semibold text-gray-600 mb-2">Phase Power Distribution</h4>
                        <div className="grid grid-cols-3 gap-2">
                          <StatCard
                            title="L1"
                            value={powerQuality?.currentL1.toFixed(1) || '0.0'}
                            unit="A"
                            variant="primary"
                            className="border-0 shadow-none p-0"
                          />
                          <StatCard
                            title="L2"
                            value={powerQuality?.currentL2.toFixed(1) || '0.0'}
                            unit="A"
                            variant="primary"
                            className="border-0 shadow-none p-0"
                          />
                          <StatCard
                            title="L3"
                            value={powerQuality?.currentL3.toFixed(1) || '0.0'}
                            unit="A"
                            variant="primary"
                            className="border-0 shadow-none p-0"
                          />
                        </div>
                      </div>
                      
                      {/* Voltage Measurements */}
                      <div>
                        <h4 className="text-xs font-semibold text-gray-600 mb-2">Voltage Measurements</h4>
                        
                        {/* Line-to-Line (380V nominal) */}
                        <div className="mb-3">
                          <h5 className="text-xs text-gray-500 mb-1">Line-to-Line (380V nominal)</h5>
                          <div className="grid grid-cols-3 gap-2">
                            <StatCard
                              title="L1-L2"
                              value={powerQuality?.voltageL1L2.toFixed(1) || '0.0'}
                              unit="V"
                              variant="primary"
                              className="border-0 shadow-none p-0"
                            />
                            <StatCard
                              title="L2-L3"
                              value={powerQuality?.voltageL2L3.toFixed(1) || '0.0'}
                              unit="V"
                              variant="primary"
                              className="border-0 shadow-none p-0"
                            />
                            <StatCard
                              title="L3-L1"
                              value={powerQuality?.voltageL3L1.toFixed(1) || '0.0'}
                              unit="V"
                              variant="primary"
                              className="border-0 shadow-none p-0"
                            />
                          </div>
                        </div>
                        
                        {/* Line-to-Neutral (220V nominal) */}
                        <div>
                          <h5 className="text-xs text-gray-500 mb-1">Line-to-Neutral (220V nominal)</h5>
                          <div className="grid grid-cols-3 gap-2">
                            <StatCard
                              title="L1-N"
                              value={powerQuality?.voltageL1N.toFixed(1) || '0.0'}
                              unit="V"
                              variant="primary"
                              className="border-0 shadow-none p-0"
                            />
                            <StatCard
                              title="L2-N"
                              value={powerQuality?.voltageL2N.toFixed(1) || '0.0'}
                              unit="V"
                              variant="primary"
                              className="border-0 shadow-none p-0"
                            />
                            <StatCard
                              title="L3-N"
                              value={powerQuality?.voltageL3N.toFixed(1) || '0.0'}
                              unit="V"
                              variant="primary"
                              className="border-0 shadow-none p-0"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Power Demand Chart */}
                <div className="p-3 rounded-xl border border-[#EDEFF9] bg-white">
                  <div className="flex items-center gap-2 mb-2">
                    <Zap size={14} className="text-primary-blue" />
                    <h3 className="text-xs font-semibold">Real-time Power Demand</h3>
                  </div>
                  <div className="h-[220px] bg-white rounded relative overflow-visible">
                    <PowerDemandChart 
                      height={220} 
                      data={deviceData ? [{
                        timestamp: new Date().toISOString(),
                        value: (deviceData.latest_data?.power?.value || 0)
                      }] : []}
                    />
                  </div>
                </div>
              </>
            )}

            {activeTab === 'alarms' && (
              <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                <div className="p-4 border-b border-gray-100 bg-gray-50">
                  <h3 className="font-medium flex items-center gap-2">
                    <AlertTriangle size={16} className="text-amber-500" />
                    Related Alarms
                    {unreadAlarms > 0 && (
                      <span className="ml-2 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white">
                        {unreadAlarms}
                      </span>
                    )}
                  </h3>
                </div>

                {isLoading ? (
                  <div className="p-8 flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
                  </div>
                ) : relatedAlarms.length === 0 ? (
                  <div className="p-8 text-center text-gray-500">
                    <AlertTriangle size={24} className="mx-auto mb-2 text-gray-300" />
                    <p>No alarms found for this meter.</p>
                  </div>
                ) : (
                  <div className="divide-y divide-gray-100">
                    {relatedAlarms.map((alarm, index) => (
                      <div key={alarm.id} className={`p-4 hover:bg-gray-50 ${index < unreadAlarms ? 'bg-blue-50/30' : ''}`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Badge className={alarm.severity === 'CRITICAL' ? 'bg-red-500 text-white' : alarm.severity === 'WARNING' ? 'bg-amber-500 text-white' : 'bg-blue-500 text-white'}>
                              {alarm.severity}
                            </Badge>
                            <span className="text-sm font-medium">{alarm.rule_name}</span>
                            {index < unreadAlarms && (
                              <span className="px-1.5 py-0.5 text-[10px] font-medium bg-blue-100 text-blue-800 rounded-full">New</span>
                            )}
                          </div>
                          <span className="text-xs text-gray-500">
                            {format(new Date(alarm.trigger_time), 'MMM d, yyyy HH:mm')}
                          </span>
                        </div>
                        <p className="mt-1 text-sm text-gray-600">{alarm.message}</p>
                        <div className="mt-2 flex items-center justify-between">
                          <div className="text-xs text-gray-500">
                            Value: {alarm.trigger_value} {alarm.metric === 'power_factor' ? '' : alarm.metric.includes('voltage') ? 'V' : alarm.metric.includes('current') ? 'A' : 'kW'}
                          </div>
                          <Button variant="outline" size="sm" className="text-xs h-7 px-2">
                            Acknowledge
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      {/* RuleDetailDrawer for creating rule */}
      <RuleDetailDrawer
        isOpen={isRuleDrawerOpen}
        onClose={() => setIsRuleDrawerOpen(false)}
        rule={null}
        isCreating={true}
        presetMeterIds={ruleDrawerMeterId !== null ? [ruleDrawerMeterId] : undefined}
        onCreate={handleRuleCreated}
      />
    </main>
  );
};

export default MeterDetailPage;
