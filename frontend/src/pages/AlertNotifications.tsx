import React, { useState, useCallback, memo, useEffect } from 'react';
import { 
  Zap,
  Gauge,
  XCircle,
  MailCheck
} from 'lucide-react';
import { sendTestEmail } from '../lib/api/email';

// Types
type AlertType = 'consumption_spike' | 'device_offline' | 'target_exceeded' | 'maintenance' | 'system';
type AlertSeverity = 'high' | 'medium' | 'low';

interface AlertRule {
  id: string;
  name: string;
  type: AlertType;
  enabled: boolean;
  severity: AlertSeverity;
  description: string;
  icon: React.ElementType;
}

interface EmailSettings {
  enabled: boolean;
  address: string;
}

// Default email settings
const defaultEmailSettings: EmailSettings = {
  enabled: true,
  address: '<EMAIL>'
};

// Alert rules configuration
const DEFAULT_ALERT_RULES: AlertRule[] = [
  { 
    id: 'consumption_spike', 
    name: 'Consumption Spike', 
    type: 'consumption_spike', 
    enabled: true, 
    severity: 'high',
    description: 'Alert when energy consumption suddenly increases beyond normal patterns',
    icon: Zap
  },
  { 
    id: 'device_offline', 
    name: 'Device Offline', 
    type: 'device_offline', 
    enabled: true, 
    severity: 'medium',
    description: 'Alert when a meter or device goes offline unexpectedly',
    icon: XCircle
  },
  { 
    id: 'target_exceeded', 
    name: 'Target Exceeded', 
    type: 'target_exceeded', 
    enabled: true, 
    severity: 'medium',
    description: 'Alert when consumption exceeds predefined targets',
    icon: Gauge
  }
];

// Card component with Tesla-inspired design
const Card = ({ children, className = '' }: { children: React.ReactNode, className?: string }) => (
  <div className={`bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden ${className}`}>
    {children}
  </div>
);

// Alert Rule Item component
const AlertRuleItem = memo(({ rule, onToggle }: { 
  rule: AlertRule; 
  onToggle: (id: string, enabled: boolean) => void;
}) => {
  const Icon = rule.icon;
  
  return (
    <div className="py-4 px-5 border-b border-gray-100 last:border-b-0">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className={`
            p-2 rounded-md 
            ${rule.severity === 'high' ? 'bg-red-50 text-red-600' : 
             rule.severity === 'medium' ? 'bg-orange-50 text-orange-600' : 
             'bg-blue-50 text-blue-600'}
          `}>
            <Icon size={18} />
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-900">{rule.name}</h3>
            <p className="text-xs text-gray-500 mt-0.5">{rule.description}</p>
          </div>
        </div>
        <div className="flex items-center">
          <label className="relative inline-flex items-center cursor-pointer">
            <input 
              type="checkbox" 
              className="sr-only peer" 
              checked={rule.enabled} 
              onChange={(e) => onToggle(rule.id, e.target.checked)}
            />
            <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none rounded-full peer 
                          peer-checked:after:translate-x-full peer-checked:after:border-white 
                          after:content-[''] after:absolute after:top-[2px] after:left-[2px] 
                          after:bg-white after:border-gray-300 after:border after:rounded-full 
                          after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>
    </div>
  );
});

// Email Settings Component
const EmailSettingsPanel = memo(({ settings, onUpdateSettings, onTestEmail }: {
  settings: EmailSettings;
  onUpdateSettings: (updatedSettings: Partial<EmailSettings>) => void;
  onTestEmail: () => Promise<boolean | void>;
}) => {
  const [isSending, setIsSending] = useState(false);
  
  const handleTestEmail = async () => {
    setIsSending(true);
    try {
      await onTestEmail();
    } finally {
      setIsSending(false);
    }
  };
  
  return (
    <Card className="mb-4">
      <div className="p-5">
        <div className="flex items-center justify-between mb-5">
          <h2 className="text-lg font-medium">Email Notifications</h2>
          <label className="relative inline-flex items-center cursor-pointer">
            <input 
              type="checkbox"
              className="sr-only peer"
              checked={settings.enabled}
              onChange={(e) => onUpdateSettings({ enabled: e.target.checked })}
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer 
                          peer-checked:after:translate-x-full peer-checked:after:border-white 
                          after:content-[''] after:absolute after:top-[2px] after:left-[2px] 
                          after:bg-white after:border-gray-300 after:border after:rounded-full 
                          after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            <span className="ml-3 text-sm font-medium text-gray-700">
              {settings.enabled ? 'Enabled' : 'Disabled'}
            </span>
          </label>
        </div>
        
        {settings.enabled && (
          <div className="space-y-5">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Recipient Email Address
              </label>
              <input 
                type="email"
                value={settings.address}
                onChange={(e) => onUpdateSettings({ address: e.target.value })}
                placeholder="<EMAIL>"
                className="w-full px-3 py-2.5 rounded-md border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="mt-2 text-xs text-gray-500">
                All alerts will be delivered directly to this email address
              </p>
            </div>
            
            <div className="border-t border-gray-100 pt-5 mt-5">
              <button
                onClick={handleTestEmail}
                disabled={!settings.address || isSending}
                className={`w-full flex items-center justify-center gap-2 rounded-md py-2.5 px-4 text-sm font-medium 
                  ${!settings.address || isSending 
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                    : 'bg-blue-600 text-white hover:bg-blue-700'}`}
              >
                {isSending ? (
                  <>
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
                    <span>Sending Test Email...</span>
                  </>
                ) : (
                  <>
                    <MailCheck size={16} />
                    <span>Send Test Email</span>
                  </>
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
});

// Alert Example component to show what alerts look like
const AlertExample = memo(({ rule }: { rule: AlertRule }) => {
  const Icon = rule.icon;
  const now = new Date();
  const formattedDate = now.toLocaleString('en-US', { 
    month: 'short', 
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: true
  });
  
  // Generate example content based on alert type
  let exampleContent = '';
  let exampleValue = '';
  
  switch(rule.type) {
    case 'consumption_spike':
      exampleValue = '37.4 kWh';
      exampleContent = 'Energy consumption has increased by 42% in the past hour compared to the same period yesterday.';
      break;
    case 'device_offline':
      exampleValue = 'Meter #A2104';
      exampleContent = 'Device has been offline for 25 minutes. Last data received at 2:25 PM.';
      break;
    case 'target_exceeded':
      exampleValue = '246 kWh';
      exampleContent = 'Daily consumption target of 200 kWh has been exceeded by 23%.';
      break;
    default:
      exampleContent = 'Alert notification example.';
  }
  
  return (
    <div className="border border-gray-200 rounded-md overflow-hidden mb-4">
      <div className={`px-4 py-2 flex items-center gap-2
        ${rule.severity === 'high' ? 'bg-red-50 text-red-700 border-b border-red-100' : 
         rule.severity === 'medium' ? 'bg-orange-50 text-orange-700 border-b border-orange-100' : 
         'bg-blue-50 text-blue-700 border-b border-blue-100'}`}>
        <Icon size={14} />
        <span className="text-xs font-medium">
          {rule.name} Alert
        </span>
        <span className="ml-auto text-[10px] text-gray-500">{formattedDate}</span>
      </div>
      <div className="px-4 py-3 bg-white">
        <div className="flex items-baseline mb-1">
          <span className="text-xs font-medium text-gray-700">Value:</span>
          <span className="ml-2 text-xs">{exampleValue}</span>
        </div>
        <p className="text-xs text-gray-600">{exampleContent}</p>
      </div>
    </div>
  );
});

// Alert Examples section
const AlertExampleSection = memo(({ rules }: { rules: AlertRule[] }) => {
  if (rules.length === 0) return null;
  
  return (
    <Card className="mb-4">
      <div className="p-4 border-b border-gray-100 bg-gray-50">
        <h2 className="text-base font-medium">Example Alerts</h2>
        <p className="text-xs text-gray-500 mt-1">Preview of alerts you'll receive by email</p>
      </div>
      <div className="p-4">
        {rules.filter(rule => rule.enabled).map(rule => (
          <AlertExample key={rule.id} rule={rule} />
        ))}
        {rules.filter(rule => rule.enabled).length === 0 && (
          <p className="text-sm text-gray-500 italic text-center py-3">
            No alerts enabled. Enable alert rules to see examples.
          </p>
        )}
      </div>
    </Card>
  );
});

// Main Alert Notifications Component
const AlertNotifications = () => {
  const [emailSettings, setEmailSettings] = useState<EmailSettings>(defaultEmailSettings);
  const [alertRules, setAlertRules] = useState<AlertRule[]>(DEFAULT_ALERT_RULES);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  
  // Update email settings
  const handleUpdateEmailSettings = useCallback((updatedSettings: Partial<EmailSettings>) => {
    setEmailSettings(prev => ({ ...prev, ...updatedSettings }));
  }, []);
  
  // Toggle alert rule
  const handleToggleRule = useCallback((id: string, enabled: boolean) => {
    setAlertRules(prev => 
      prev.map(rule => 
        rule.id === id ? { ...rule, enabled } : rule
      )
    );
  }, []);
  
  // Save settings
  const handleSaveSettings = useCallback(() => {
    setSaveStatus('saving');
    
    // Simulate API call
    setTimeout(() => {
      console.log('Saving settings:', { emailSettings, alertRules });
      setSaveStatus('saved');
      
      // Reset status after 2 seconds
      setTimeout(() => {
        setSaveStatus('idle');
      }, 2000);
    }, 800);
  }, [emailSettings, alertRules]);
  
  // Send test email
  const handleSendTestEmail = useCallback(async (): Promise<void> => {
    try {
      const response = await sendTestEmail({
        to: emailSettings.address,
        subject: 'Alto CERO EMS Test Alert',
        text: `This is a test alert email from the Alto CERO Energy Management System. If you're receiving this, your email alert configuration is working correctly.

Time sent: ${new Date().toLocaleString()}

Alerts will be delivered to this email address based on your configuration in the EMS.

Alta CERO Energy Management System`
      });
      
      if (!response.success) {
        throw new Error(response.error);
      }
      
      alert(`Test email sent successfully to ${emailSettings.address}`);
    } catch (error) {
      console.error('Failed to send test email:', error);
      alert(`Failed to send test email: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [emailSettings.address]);
  
  // Auto-save settings when changes are made
  useEffect(() => {
    const timer = setTimeout(() => {
      if (saveStatus === 'idle') {
        handleSaveSettings();
      }
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [emailSettings, alertRules, saveStatus, handleSaveSettings]);
  
  return (
    <div className="flex flex-col gap-4 max-w-4xl mx-auto">
      <div className="mb-2">
        <h1 className="text-2xl font-medium text-gray-900">Email Alert Settings</h1>
        <p className="text-gray-500 mt-1">
          Configure what alerts are sent to your email inbox
        </p>
      </div>
      
      <div className="grid md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <EmailSettingsPanel 
            settings={emailSettings}
            onUpdateSettings={handleUpdateEmailSettings}
            onTestEmail={handleSendTestEmail}
          />
          
          {/* Add Alert Examples section */}
          {emailSettings.enabled && (
            <AlertExampleSection rules={alertRules} />
          )}
          
          <div className="flex items-center justify-between mt-6">
            <div className="flex items-center text-sm text-gray-500">
              {saveStatus === 'saving' && (
                <div className="flex items-center">
                  <div className="animate-spin h-3 w-3 border-2 border-gray-400 border-t-transparent rounded-full mr-2"></div>
                  <span>Saving changes...</span>
                </div>
              )}
              {saveStatus === 'saved' && (
                <div className="text-green-600">Settings saved</div>
              )}
            </div>
            
            <button
              onClick={handleSaveSettings}
              disabled={saveStatus === 'saving'}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-md transition-colors"
            >
              Save Changes
            </button>
          </div>
        </div>
        
        <div className="md:col-span-1">
          <Card>
            <div className="p-4 border-b border-gray-100 bg-gray-50">
              <h2 className="text-base font-medium">Alert Rules</h2>
              <p className="text-xs text-gray-500 mt-1">Enable the alert types you want to receive</p>
            </div>
            <div>
              {alertRules.map((rule) => (
                <AlertRuleItem 
                  key={rule.id} 
                  rule={rule} 
                  onToggle={handleToggleRule} 
                />
              ))}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default memo(AlertNotifications);
