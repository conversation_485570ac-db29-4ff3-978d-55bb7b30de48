import React, { useState, useEffect, useMemo } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { MeterTree } from '@/components/meters/MeterTree';
import MeterDetails from '@/components/meters/MeterDetails';
import { MeterHeader } from '@/components/meters/MeterHeader';
import { TreeNode } from '@/components/meters/types';
import { SYSTEM_ICONS } from '@/lib/constants';
import { METER_TYPES } from '@/lib/config/meters';
import { Filter, ChevronDown, Wifi, WifiOff, AlertTriangle, Loader2, Table2 } from 'lucide-react';
import { useMeterTree } from '@/hooks/useMeterTree';
import { useMeterTreeMock } from '@/hooks/useMeterTreeMock';
import clsx from 'clsx';
import { useLocalStorage } from '@/lib/hooks/useLocalStorage';
import '../styles/text-selection.css';

// Import enhanced components
import { ErrorDisplay } from '@/components/ui/error-display';
import { useRetry } from '@/hooks/useRetry';
import {
  MetersPageSkeleton,
  TreeLoadingSkeleton,
  DetailsLoadingSkeleton,
  MeterTreeSkeleton
} from '@/components/ui/meters-skeletons';

// Default Filter icon if type icon not found
const DefaultIcon = Filter;

// Define a type for the keys of METER_TYPES
type MeterConfigKey = keyof typeof METER_TYPES;

// Type guard to check if a key is a valid MeterConfigKey
const isValidMeterConfigKey = (key: string | null): key is MeterConfigKey => {
  return key !== null && key in METER_TYPES;
};

export default function MetersPage() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // Get view from URL parameters or default to 'main'
  const viewParam = searchParams.get('view') as 'main' | 'tower-a' | 'tower-b' | 'tower-c' | 'chiller-plant' | 'data-center-others' | 'tenant' | null;

  // Set initial meter view based on URL parameter
  let initialMeterView: 'main' | 'tower_a' | 'tower_b' | 'tower_c' = 'main';
  let initialType: MeterConfigKey | null = null;

  if (viewParam) {
    if (['main', 'tower-a', 'tower-b', 'tower-c'].includes(viewParam)) {
      // Convert dash format to underscore format for consistency
      if (viewParam === 'tower-a') initialMeterView = 'tower_a';
      else if (viewParam === 'tower-b') initialMeterView = 'tower_b';
      else if (viewParam === 'tower-c') initialMeterView = 'tower_c';
      else initialMeterView = viewParam as 'main';
    } else if (viewParam === 'chiller-plant') {
      // For chiller-plant, we keep the main view but set the type filter
      initialMeterView = 'main';
      initialType = 'chillerPlant' as MeterConfigKey;
    } else if (viewParam === 'data-center-others') {
      // For data-center-others, we keep the main view but set the type filter to 'others'
      initialMeterView = 'main';
      initialType = 'others' as MeterConfigKey;
    } else if (viewParam === 'tenant') {
      // For tenant, we show a special view that includes tenant-specific meters
      // We'll use the main view but could potentially add a custom filter in the future
      // that specifically targets tenant meters across all towers
      initialMeterView = 'main';
      // We don't set a specific type filter, as tenant meters could be of various types
      // This will show all meters in the main view, which includes tenant meters
    }
  }

  const [selectedMeterView, setSelectedMeterView] = useState<'main' | 'tower_a' | 'tower_b' | 'tower_c'>(initialMeterView);
  const [selectedView, setSelectedView] = useState<'all' | 'online' | 'offline' | 'alerts'>('all');
  const [selectedType, setSelectedType] = useState<MeterConfigKey | null>(initialType);
  const [expandedNodes, setExpandedNodes] = useState<string[]>([]);
  const [autoExpandedNodes, setAutoExpandedNodes] = useState<string[]>([]);
  const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState(false);

  // Check if we're in mock data mode
  const [mockDataMode] = useLocalStorage('useMockData', false);
  
  // Enable mock data mode if URL has ?mock=true
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('mock') === 'true' && !mockDataMode) {
      localStorage.setItem('useMockData', 'true');
      window.location.reload();
    }
  }, [mockDataMode]);

  // --- Use the appropriate hook based on mock data mode ---
  const meterTreeHook = mockDataMode ? useMeterTreeMock : useMeterTree;
  const {
    stats,
    getFilteredTree,
    offlineNodesToExpand,
    allMeters,
    isLoading,
    error,
  } = meterTreeHook(selectedMeterView);

  // For now, create a simple refetch function that forces a reload
  // TODO: Update useMeterTree hook to expose a proper refetch function
  const refetchData = async () => {
    window.location.reload();
  };

  // Enhanced retry functionality
  const { retry: retryFetch, isRetrying, retryCount } = useRetry(refetchData, {
    maxRetries: 3,
    initialDelay: 1000
  });

  // --- Filtered Tree based on selections ---
  const filteredTree = useMemo(() => {
    if (isLoading || error) return [];
    const tree = getFilteredTree(selectedView, selectedType as any);
    return tree;
  }, [selectedView, selectedType, getFilteredTree, isLoading, error, mockDataMode, stats]);

  // --- Calculate counts per type for filter chips ---
  const countsByType = useMemo(() => {
    if (isLoading || error) return { all: 0 };
    const counts: Record<string, number> = {};
    Object.keys(METER_TYPES).forEach(typeKeyString => {
      // Ensure type is a valid MeterConfigKey before using it for filtering
      if (isValidMeterConfigKey(typeKeyString)) {
        counts[typeKeyString] = allMeters.filter(meter => meter.type === typeKeyString).length;
      }
    });
    counts['all'] = allMeters.length;
    return counts;
  }, [allMeters, isLoading, error]);

  // --- Handle Auto-Expansion for Offline View ---
  useEffect(() => {
    if (isLoading || error) return;
    if (selectedView === 'offline') {
      setAutoExpandedNodes(offlineNodesToExpand);
      setExpandedNodes(offlineNodesToExpand);
    } else {
      setAutoExpandedNodes([]);
    }
  }, [selectedView, offlineNodesToExpand, isLoading, error]);

  // --- Find the first selectable node from the filtered tree
  const initialNode = useMemo(() => {
    if (isLoading || error) return null;
    const tree = getFilteredTree(selectedView, selectedType as any);
    function findFirstMeter(nodes: TreeNode[]): TreeNode | null {
      for (const node of nodes) {
        if (!node.children || node.children.length === 0) return node;
        const found = findFirstMeter(node.children);
        if (found) return found;
      }
      return null;
    }
    return findFirstMeter(tree);
  }, [getFilteredTree, selectedView, selectedType, isLoading, error]);

  const [selectedNode, setSelectedNode] = useState<TreeNode | null>(null);

  // --- Node Selection and Toggle Logic ---
  const handleSelectNode = (node: TreeNode) => {
    setSelectedNode(node);
  };

  const toggleNode = (nodeId: string) => {
    if (selectedView === 'offline' && autoExpandedNodes.includes(nodeId) && expandedNodes.includes(nodeId)) {
      return;
    }
    setExpandedNodes(prev =>
      prev.includes(nodeId) ? prev.filter(id => id !== nodeId) : [...prev, nodeId]
    );
  };

  // --- Set initial selection on mount or when filtered tree changes
  useEffect(() => {
    if (isLoading || error) {
        setSelectedNode(null);
        return;
    }
    if (!selectedNode && initialNode) {
      setSelectedNode(initialNode);
    } else if (selectedNode && !initialNode && filteredTree.length > 0) {
      setSelectedNode(initialNode);
    } else if (filteredTree.length === 0) {
      setSelectedNode(null);
    }
  }, [initialNode, selectedNode, isLoading, error, filteredTree]);

  // --- Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isTypeDropdownOpen && !target.closest('[data-dropdown="type-filter"]')) {
        setIsTypeDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isTypeDropdownOpen]);

  // --- Empty State Check for the right pane ---
  const isRightPaneEmpty = isLoading || error || filteredTree.length === 0 || !selectedNode;

  // Show full page skeleton on initial load
  if (isLoading && !error && stats.total === 0) {
    return <MetersPageSkeleton />;
  }

  return (
    <div className="h-[calc(100vh-56px)] flex flex-col gap-6 p-4">
      {/* Sticky header - Combined MeterHeader and Filter Chips Bar into one row */}
      <div
        className={`sticky top-0 z-20 bg-white rounded-xl border border-borderLight p-2 shadow-sm animate-fade-in`}
        style={{ animationDuration: '400ms', top: '0px' }}
        data-component-name="MetersPage"
      >
        <div className="flex flex-row flex-nowrap items-center justify-between gap-3 w-full">
          {/* Left side: Building and System Type Filters */}
          <div className="flex items-center gap-2">
            {/* Building Selection - contains just the dropdown now */}
            <div className="bg-white rounded-lg border border-[#EDEFF9] p-1 flex-shrink-0">
              <MeterHeader
                stats={isLoading || error ? { total: 0, online: 0, offline: 0, alerts: 0 } : stats}
                selectedMeterView={selectedMeterView}
                onViewChange={setSelectedMeterView}
                selectedView={selectedView}
                onViewSelect={setSelectedView}
              />
            </div>

            {/* System Type Filter Dropdown */}
            <div className="relative" data-dropdown="type-filter">
              <button
                onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
                className="flex items-center gap-1 px-2 py-1 rounded-lg text-xs transition-all duration-200 text-primary-blue bg-blue-50 hover:bg-blue-100/70"
                aria-haspopup="listbox"
                aria-expanded={isTypeDropdownOpen}
                disabled={isLoading || !!error}
              >
                {/* Show icon for selected type */}
                {selectedType === null ? (
                  <DefaultIcon size={14} />
                ) : (
                  (() => {
                    const iconKey = selectedType as keyof typeof SYSTEM_ICONS;
                    const Icon = SYSTEM_ICONS[iconKey] || DefaultIcon;
                    return <Icon size={14} />;
                  })()
                )}
                <span>{selectedType === null ? 'All System Types' : METER_TYPES[selectedType]?.name || 'Unknown'}</span>
                <ChevronDown
                  size={12}
                  className={`transition-transform ${isTypeDropdownOpen ? 'rotate-180' : ''}`}
                />
              </button>

              {/* Dropdown Menu */}
              {isTypeDropdownOpen && (
                <div className="absolute z-10 mt-1 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <div className="py-0.5" role="listbox">
                    {/* All Types Option */}
                    <button
                      className={clsx(
                        'w-full text-left px-3 py-1.5 text-sm flex items-center gap-2',
                        selectedType === null ? 'bg-blue-50 text-primary-blue' : 'text-gray-700 hover:bg-gray-100'
                      )}
                      onClick={() => {
                        setSelectedType(null);
                        setIsTypeDropdownOpen(false);
                      }}
                      role="option"
                      aria-selected={selectedType === null}
                      disabled={isLoading || !!error}
                    >
                      <DefaultIcon size={14} className={selectedType === null ? 'text-primary-blue' : 'text-gray-500'} />
                      <span className="flex-1">All System Types</span>
                      <span className="px-1.5 py-0.5 rounded-full bg-gray-100 text-gray-600 text-[10px]">
                        {isLoading ? '-' : (countsByType['all'] ?? 0)}
                      </span>
                    </button>

                    {/* Meter Type Options */}
                    {Object.entries(METER_TYPES).map(([typeId, typeInfo]) => {
                      // Ensure typeId is a valid MeterConfigKey before proceeding
                      if (!isValidMeterConfigKey(typeId)) return null;
                      const iconKey = typeId as keyof typeof SYSTEM_ICONS;
                      const Icon = SYSTEM_ICONS[iconKey] || DefaultIcon;
                      const count = isLoading ? 0 : (countsByType[typeId] ?? 0);
                      const isSelected = typeId === selectedType;

                      return (
                        <button
                          key={typeId}
                          className={clsx(
                            'w-full text-left px-3 py-1.5 text-sm flex items-center gap-2',
                            isSelected ? 'bg-blue-50 text-primary-blue' : 'text-gray-700 hover:bg-gray-100',
                            (count === 0 || isLoading || !!error) && 'opacity-60 cursor-not-allowed'
                          )}
                          onClick={() => {
                            setSelectedType(isSelected ? null : typeId);
                            setIsTypeDropdownOpen(false);
                          }}
                          disabled={count === 0 || isLoading || !!error}
                          role="option"
                          aria-selected={isSelected}
                        >
                          <Icon size={14} className={isSelected ? 'text-primary-blue' : 'text-gray-500'} />
                          <span className="flex-1">{typeInfo.name}</span>
                          <span className="px-1.5 py-0.5 rounded-full bg-gray-100 text-gray-600 text-[10px]">
                            {isLoading ? '-' : count}
                          </span>
                        </button>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right side: Status Filter Buttons and Table View Button */}
          <div className="flex items-center gap-2">
            {/* Status Filter Buttons */}
            <div className="flex items-center gap-1 bg-white rounded-lg border border-[#EDEFF9] p-1">
            <button
              onClick={() => setSelectedView('all')}
              className={`flex items-center gap-1 px-2 py-1 rounded-lg text-xs transition-all duration-200 ${
                selectedView === 'all'
                  ? 'bg-blue-50 text-primary-blue'
                  : 'hover:bg-gray-50'
              }`}
            >
              <span>All Meters</span>
              <span className="px-1 py-0.5 rounded-full bg-gray-100 text-gray-600 text-[10px]">
                {stats.total}
              </span>
            </button>

            <button
              onClick={() => setSelectedView('online')}
              className={`flex items-center gap-1 px-2 py-1 rounded-lg text-xs transition-all duration-200 ${
                selectedView === 'online'
                  ? 'bg-green-50 text-green-600'
                  : 'hover:bg-gray-50'
              }`}
            >
              <Wifi size={12} />
              <span>Online</span>
              <span className="px-1 py-0.5 rounded-full bg-green-100 text-green-600 text-[10px]">
                {stats.online}
              </span>
            </button>

            <button
              onClick={() => setSelectedView('offline')}
              className={`flex items-center gap-1 px-2 py-1 rounded-lg text-xs transition-all duration-200 ${
                selectedView === 'offline'
                  ? 'bg-red-50 text-red-600'
                  : 'hover:bg-gray-50'
              }`}
            >
              <WifiOff size={12} />
              <span>Offline</span>
              <span className="px-1 py-0.5 rounded-full bg-red-100 text-red-600 text-[10px]">
                {stats.offline}
              </span>
            </button>

            <button
              onClick={() => setSelectedView('alerts')}
              className={`flex items-center gap-1 px-2 py-1 rounded-lg text-xs transition-all duration-200 ${
                selectedView === 'alerts'
                  ? 'bg-yellow-50 text-yellow-600'
                  : 'hover:bg-gray-50'
              }`}
            >
              <AlertTriangle size={12} />
              <span>Alerts</span>
              <span className="px-1 py-0.5 rounded-full bg-yellow-100 text-yellow-600 text-[10px]">
                {stats.alerts}
              </span>
            </button>
          </div>
          
          {/* Table View Button */}
          <button
            onClick={() => navigate('/settings?section=meters')}
            className="flex items-center gap-1.5 px-3 py-1.5 bg-white rounded-lg border border-[#EDEFF9] hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 text-xs font-medium text-gray-700 hover:text-blue-600"
            title="View meters in table format"
          >
            <Table2 size={14} />
            <span>Table View</span>
          </button>
        </div>
        </div>
      </div>

      {/* Main Content - Split Pane */}
      <div className="flex-1 flex gap-6 overflow-hidden">
        {/* Left Pane - Tree View */}
        <div className="w-64 bg-white rounded-xl border border-borderLight overflow-y-auto max-h-[calc(100vh-200px)] scrollbar-thin p-2">
          {isLoading ? (
            <TreeLoadingSkeleton />
          ) : error ? (
            <ErrorDisplay
              message={error.message}
              onRetry={retryFetch}
              isRetrying={isRetrying}
              retryCount={retryCount}
              variant="compact"
              errorType="data"
              className="h-full"
            />
          ) : filteredTree.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <Filter size={32} className="mb-2" />
                <span>No items match filters.</span>
            </div>
          ) : (
            <MeterTree
              nodes={filteredTree}
              expandedNodes={expandedNodes}
              selectedNodeId={selectedNode?.id ?? null}
              onSelectNode={handleSelectNode}
              onToggleNode={toggleNode}
              ariaLabel="Meter Hierarchy"
              selectedMeterView={selectedMeterView}
            />
          )}
        </div>

        {/* Right Pane - Details or Empty State */}
        <div className="flex-1 bg-white rounded-xl border border-borderLight p-4 overflow-y-auto">
          {isRightPaneEmpty ? (
             isLoading ? (
                <DetailsLoadingSkeleton />
            ) : error ? (
                <ErrorDisplay
                  message={error.message}
                  onRetry={retryFetch}
                  isRetrying={isRetrying}
                  retryCount={retryCount}
                  variant="compact"
                  errorType="data"
                  className="h-full"
                />
            ) : (
                <div className="h-full flex flex-col items-center justify-center text-center text-gray-500">
                    <Filter size={48} className="mb-4 text-gray-300" />
                    <p className="font-semibold">No Meter Selected or Data Available</p>
                    <p className="text-sm">Select a meter from the tree on the left, or adjust filters.</p>
                </div>
            )
          ) : selectedNode && (
            <MeterDetails node={selectedNode} />
          )}
        </div>
      </div>
    </div>
  );
}