import React, { useState } from 'react';
import LiveMeterReadings from '../components/meters/LiveMeterReadings';
import { TOWER_A_METERS } from '../lib/config/building/meters/tower-a-meters';

const LiveMeterDemo: React.FC = () => {
  // Get Floor 24 meters from the configuration
  const floor24Meters = TOWER_A_METERS[24].panels['T-24DB1'].meters;
  
  // State to track which meter is selected
  const [selectedMeterId, setSelectedMeterId] = useState<string>(floor24Meters[0].id);
  
  // Find the selected meter object
  const selectedMeter = floor24Meters.find(meter => meter.id === selectedMeterId);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Live Meter Data Demo</h1>
      
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <h2 className="text-lg font-semibold mb-4">Select a Meter</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {floor24Meters.map((meter) => (
            <button
              key={meter.id}
              className={`p-4 rounded-lg border ${
                selectedMeterId === meter.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:bg-gray-50'
              }`}
              onClick={() => setSelectedMeterId(meter.id)}
            >
              <div className="font-medium">{meter.name}</div>
              <div className="text-sm text-gray-500">{meter.type}</div>
            </button>
          ))}
        </div>
      </div>
      
      {selectedMeter && (
        <LiveMeterReadings
          meterId={selectedMeter.id}
          meterName={selectedMeter.name}
          meterType={selectedMeter.type}
        />
      )}
      
      <div className="bg-white rounded-lg shadow p-4 mt-6">
        <h2 className="text-lg font-semibold mb-4">About This Demo</h2>
        <p className="mb-2">
          This page demonstrates real-time meter data visualization using Supabase Realtime.
        </p>
        <p className="mb-2">
          Data flows from Tridium Niagara to a Supabase table via the integration script, then to this UI in real-time.
        </p>
        <p>
          <strong>Note:</strong> For this demo to work, you need to:
        </p>
        <ol className="list-decimal ml-6 mt-2">
          <li>Configure and run the <code>niagara_to_supabase.py</code> script to fetch data from Niagara</li>
          <li>Ensure your Supabase instance has the <code>meter_readings</code> table with Realtime enabled</li>
          <li>Verify the Supabase client in <code>supabaseClient.ts</code> is correctly configured</li>
        </ol>
      </div>
    </div>
  );
};

export default LiveMeterDemo;
