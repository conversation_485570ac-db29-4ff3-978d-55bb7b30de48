import React, { useState } from 'react';
import { FileText, Download, TrendingUp, DollarSign } from 'lucide-react';
import { WeeklyReportExport } from '../components/dashboard/WeeklyReportExport';
import { format } from 'date-fns';
import BillingTab from '../components/billing/BillingTab';
import { FlatTabBar } from '@/components/ui/FlatTabBar';

interface Report {
  id: number;
  name: string;
  description: string;
  date: string;
  period: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  size: string;
  format: 'PDF' | 'CSV' | 'Excel';
  status: 'ready' | 'generating';
}

const mockReports: Report[] = [ 
  // Weekly Reports
  {
    id: 7,
    name: 'Week 21 Energy Report',
    description: 'Weekly energy consumption and performance metrics',
    date: '2025-05-24',
    period: 'weekly',
    size: '1.2 MB',
    format: 'PDF',
    status: 'ready',
  },
  {
    id: 8,
    name: 'Week 20 Energy Report',
    description: 'Weekly energy consumption and performance metrics',
    date: '2025-05-17',
    period: 'weekly',
    size: '1.1 MB',
    format: 'PDF',
    status: 'ready',
  },
  // Monthly Reports
  {
    id: 1,
    name: 'April 2025 Energy Report',
    description: 'Monthly energy consumption analysis with cost breakdown',
    date: '2025-04-30',
    period: 'monthly',
    size: '2.4 MB',
    format: 'PDF',
    status: 'ready',
  },
  {
    id: 9,
    name: 'March 2025 Energy Report',
    description: 'Monthly energy consumption analysis with cost breakdown',
    date: '2025-03-31',
    period: 'monthly',
    size: '2.8 MB',
    format: 'PDF',
    status: 'ready',
  },
  // Quarterly Reports
  {
    id: 2,
    name: 'Q1 2025 Energy Report',
    description: 'Quarterly energy performance and cost analysis',
    date: '2025-03-31',
    period: 'quarterly',
    size: '5.8 MB',
    format: 'PDF',
    status: 'ready',
  },
  {
    id: 10,
    name: 'Q4 2024 Energy Report',
    description: 'Quarterly energy performance and cost analysis',
    date: '2024-12-31',
    period: 'quarterly',
    size: '6.2 MB',
    format: 'Excel',
    status: 'ready',
  },
  // Yearly Reports
  {
    id: 5,
    name: '2024 Annual Energy Report',
    description: 'Annual energy consumption and sustainability metrics',
    date: '2024-12-31',
    period: 'yearly',
    size: '12.8 MB',
    format: 'PDF',
    status: 'ready',
  },
];

const PERIOD_BADGES = {
  weekly: { color: 'bg-gray-100 text-gray-800', label: 'Weekly' },
  monthly: { color: 'bg-blue-100 text-blue-800', label: 'Monthly' },
  quarterly: { color: 'bg-purple-100 text-purple-800', label: 'Quarterly' },
  yearly: { color: 'bg-green-100 text-green-800', label: 'Yearly' }
} as const;

const FORMAT_BADGES = {
  PDF: 'bg-red-50 text-red-700',
  CSV: 'bg-green-50 text-green-700',
  Excel: 'bg-emerald-50 text-emerald-700'
} as const;

// Energy Reports Component (existing functionality)
const EnergyReports: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'all' | Report['period']>('all');

  const filteredReports = mockReports.filter(report => {
    if (selectedPeriod !== 'all' && report.period !== selectedPeriod) return false;
    return true;
  });

  // Group reports by period for quick stats
  const reportStats = {
    total: mockReports.length,
    byPeriod: {
      weekly: mockReports.filter(r => r.period === 'weekly').length,
      monthly: mockReports.filter(r => r.period === 'monthly').length,
      quarterly: mockReports.filter(r => r.period === 'quarterly').length,
      yearly: mockReports.filter(r => r.period === 'yearly').length,
    }
  };

  return (
    <div className="flex flex-col gap-4">
      {/* Simplified Header with Integrated Filter */}
      <div className="bg-white rounded-xl border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {Object.entries(PERIOD_BADGES).map(([period, { label }]) => (
              <button
                key={period}
                onClick={() => setSelectedPeriod(period as Report['period'])}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all ${
                  selectedPeriod === period
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'bg-white text-gray-600 border border-gray-200 hover:bg-gray-50'
                }`}
              >
                {label}
              </button>
            ))}
            <button
              onClick={() => setSelectedPeriod('all')}
              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all ${
                selectedPeriod === 'all'
                  ? 'bg-blue-50 text-blue-700 border border-blue-200'
                  : 'bg-white text-gray-600 border border-gray-200 hover:bg-gray-50'
              }`}
            >
              All
            </button>
          </div>
          <WeeklyReportExport />
        </div>
      </div>

      {/* Reports Table */}
      <div className="flex-1 bg-white rounded-xl border border-gray-200 overflow-hidden">
        <div className="overflow-auto h-full">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Period</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Format</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredReports.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-12 text-center">
                    <div className="text-gray-500">
                      <FileText size={48} className="mx-auto mb-4 text-gray-300" />
                      <p className="text-lg font-medium">No reports found</p>
                      <p className="text-sm mt-1">Try selecting a different period</p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredReports.map((report) => (
                  <tr key={report.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-6 py-4">
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-lg bg-blue-50">
                          <TrendingUp size={20} className="text-blue-600" />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{report.name}</div>
                          <div className="text-sm text-gray-500">{report.description}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${PERIOD_BADGES[report.period].color}`}>
                        {PERIOD_BADGES[report.period].label}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {format(new Date(report.date), 'MMM d, yyyy')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${FORMAT_BADGES[report.format]}`}>
                        {report.format}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {report.size}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center justify-center">
                        <button 
                          className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all"
                          title="Download Report"
                          disabled={report.status !== 'ready'}
                        >
                          <Download size={18} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Main Reports Component with Tabs
export default function Reports() {
  return (
    <div className="h-[calc(100vh-56px)] p-6 bg-gray-50/50">
      <FlatTabBar
        defaultValue="energy"
        tabs={[
          {
            value: 'energy',
            label: 'Energy Reports',
            icon: <FileText className="w-4 h-4" />,
            content: <EnergyReports />
          },
          {
            value: 'financial',
            label: 'Financial Reports',
            icon: <DollarSign className="w-4 h-4" />,
            content: <BillingTab />
          }
        ]}
      />
    </div>
  );
}