import { useState, useEffect, useCallback, useMemo } from 'react';

import {
  BuildingType,
  AnalyticsData,
  ViewType as AnalyticsViewType
} from '../types/analytics';
import { SystemType } from '../components/analytics/SystemSelector';
import { AnalyticsTab, ViewType as ControlsViewType } from '../components/analytics/AnalyticsControls';
import { getComparisonDate } from '../lib/utils/dates';
import { SYSTEM_TYPES } from '../lib/config/energy-systems';

// Import services
import {
  fetchEnergyData,
  fetchStatisticalData,
  HistoricalDataQuery,
  EnergyRecord,
  StatisticalRecord,
  HistoricalDataRecord
} from '../services/timescaleServiceMock';
import { fetchSiteData } from '../services/siteServiceMock';

// Import common components
import LoadingSpinner from '../components/common/LoadingSpinner';
import ErrorDisplay from '../components/common/ErrorDisplay';
import BuildingSelector from '../components/common/BuildingSelector';

// Import enhanced components
import { ErrorDisplay as EnhancedErrorDisplay } from '../components/ui/error-display';
import { useRetry } from '../hooks/useRetry';
import {
  AnalyticsPageSkeleton,
  AnalyticsChartSkeleton,
  ComparisonTabSkeleton,
  ChartDataSkeleton
} from '../components/ui/analytics-skeletons';

// Import analytics components
import ConsumptionTab from '../components/analytics/ConsumptionTab';
import AnalyticsControls from '../components/analytics/AnalyticsControls';
import ComparisonTab from '../components/analytics/ComparisonTab';
import SystemSelector from '../components/analytics/SystemSelector';
import { ComparisonPeriod } from '../components/analytics/ComparisonSelect';

// Constants
const SITE_ID = 'set'; // Default site ID

// Helper to get time range based on view and date
const getTimeRange = (view: ControlsViewType, date: Date): { start: Date, end: Date } => {
  const start = new Date(date);
  const end = new Date(date);

  // Reset hours for consistent time ranges
  start.setHours(0, 0, 0, 0);

  switch (view) {
    case 'day':
      end.setHours(23, 59, 59, 999);
      break;
    case 'week':
      // Set to start of week (Sunday)
      start.setDate(start.getDate() - start.getDay());
      // Set to end of week (Saturday)
      end.setDate(end.getDate() - end.getDay() + 6);
      end.setHours(23, 59, 59, 999);
      break;
    case 'month':
      // Set to start of month
      start.setDate(1);
      // Set to end of month
      end.setMonth(end.getMonth() + 1);
      end.setDate(0);
      end.setHours(23, 59, 59, 999);
      break;
    case 'year':
      // Set to start of year
      start.setMonth(0, 1);
      // Set to end of year
      end.setMonth(11, 31);
      end.setHours(23, 59, 59, 999);
      break;
    case 'multi-year':
      // Show 5 years of data
      start.setFullYear(start.getFullYear() - 4); // Go back 4 years
      start.setMonth(0, 1);
      start.setHours(0, 0, 0, 0);
      // End at current year end
      end.setMonth(11, 31);
      end.setHours(23, 59, 59, 999);
      break;
    default:
      // Default to day view
      end.setHours(23, 59, 59, 999);
      break;
  }

  return { start, end };
};

// Helper to map BuildingType to device IDs
const getDeviceIds = (building: BuildingType): string[] => {
  switch (building) {
    case 'A':
      return ['tower_a'];
    case 'B':
      return ['tower_b'];
    case 'C':
      return ['tower_c'];
    case 'all':
    default:
      return ['Main']; // Main meter covers all buildings
  }
};

// Helper to get table name based on view
const getTableName = (view: ControlsViewType): string => {
  switch (view) {
    case 'day':
      return 'energy_data_1hour';
    case 'week':
      return 'energy_data_1day';
    case 'month':
      return 'energy_data_1day';
    case 'year':
      return 'energy_data_1month';
    case 'multi-year':
      return 'energy_data_1year';
    default:
      return 'energy_data_1hour';
  }
};

// Helper to map system type to device IDs
const getSystemDeviceIds = (systemType: SystemType): string[] => {
  switch (systemType) {
    case 'chillerPlant':
      return ['chiller_plant'];
    case 'airSide':
      return ['air_distribution_system'];
    case 'evCharger':
      return ['ev_charger'];
    case 'escalator_elevator':
      return ['elevator_escalator'];
    case 'data_center_others':
      return ['data_center_and_others'];
    case 'light_power':
      return ['light_and_power'];
    case 'all':
    default:
      return []; // Empty array means no specific system filter
  }
};

export default function Analytics() {
  const [selectedTab, setSelectedTab] = useState<AnalyticsTab>('consumption');
  const [selectedView, setSelectedView] = useState<ControlsViewType>('day');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedBuilding, setSelectedBuilding] = useState<BuildingType>('all');
  const [selectedSystem, setSelectedSystem] = useState<SystemType>('all');
  const [showCumulative, setShowCumulative] = useState<boolean>(false);
  const [chartType, setChartType] = useState<'energy' | 'power'>('energy');

  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [powerData, setPowerData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [siteConfig, setSiteConfig] = useState<{
    co2_kg_per_kwh: number | undefined;
    electricity_cost_per_kwh: number | undefined;
  }>({
    co2_kg_per_kwh: undefined,
    electricity_cost_per_kwh: undefined,
  });

  // New state for comparison
  const [comparisonPeriod, setComparisonPeriod] = useState<ComparisonPeriod>('week');
  const [comparisonData, setComparisonData] = useState<AnalyticsData | null>(null);
  
  // Loading states for different data types
  const [energyLoading, setEnergyLoading] = useState<boolean>(false);
  const [powerLoading, setPowerLoading] = useState<boolean>(false);
  const [comparisonLoading, setComparisonLoading] = useState<boolean>(false);

  // Memoize the buildings data structure to prevent unnecessary re-renders
  const buildings = useMemo(() => [
    { id: 'all' as BuildingType, name: 'All Buildings' },
    { id: 'A' as BuildingType, name: 'Building A' },
    { id: 'B' as BuildingType, name: 'Building B' },
    { id: 'C' as BuildingType, name: 'Building C' },
  ], []);

  // Fetch site configuration once on component mount
  useEffect(() => {
    const loadSiteConfig = async () => {
      try {
        const siteData = await fetchSiteData(SITE_ID);
        if (siteData.metadata) {
          setSiteConfig({
            co2_kg_per_kwh: siteData.metadata.co2_kg_per_kwh,
            electricity_cost_per_kwh: siteData.metadata.electricity_cost_per_kwh,
          });
        }
      } catch (err) {
        console.error('Error loading site configuration:', err);
        // Use default values if site config fetch fails
      }
    };

    loadSiteConfig();
  }, []);

  // Process energy data into analytics format
  const processEnergyData = useCallback((
    energyData: HistoricalDataRecord<EnergyRecord>[],
    timeRange: { start: Date, end: Date },
    viewType: ViewType
  ): AnalyticsData => {
    // Default empty analytics data structure
    const defaultData: AnalyticsData = {
      hourlyConsumption: [],
      totalConsumption: 0,
      peakDemand: { value: 0, time: '' },
      averageLoad: 0,
    };

    if (!energyData || energyData.length === 0) {
      return defaultData;
    }

    // Combine data from all devices
    let allRecords: EnergyRecord[] = [];
    energyData.forEach(deviceData => {
      allRecords = [...allRecords, ...deviceData.records];
    });

    // Sort records by timestamp
    allRecords.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    // Map records to consumption format based on view type
    const hourlyConsumption = allRecords.map(record => {
      const date = new Date(record.timestamp);
      let timeLabel: string;
      
      switch(viewType) {
        case 'day':
          timeLabel = date.toLocaleTimeString('en-US', { hour: '2-digit', hour12: false }) + ':00';
          break;
        case 'week':
          timeLabel = date.toLocaleDateString('en-US', { weekday: 'short' }); // Mon, Tue, etc.
          break;
        case 'month':
          timeLabel = date.toLocaleDateString('en-US', { day: '2-digit' }); // 01, 02, etc.
          break;
        case 'year':
          timeLabel = date.toLocaleDateString('en-US', { month: 'short' }); // Jan, Feb, etc.
          break;
        case 'multi-year':
          timeLabel = date.getFullYear().toString(); // 2023, 2024, etc.
          break;
        default:
          timeLabel = date.toISOString();
      }
      
      return {
        time: timeLabel,
        actual: Math.abs(record.value), // Use absolute value for consistent display
        predicted: undefined, // No prediction data yet
        accumulatedValue: undefined, // Will be calculated if showCumulative is true
      };
    });

    // Calculate total consumption
    const totalConsumption = allRecords.reduce((sum, record) => sum + Math.abs(record.value), 0);

    // Find peak demand (highest consumption in a single record)
    const peakRecord = [...allRecords].sort((a, b) => Math.abs(b.value) - Math.abs(a.value))[0];
    const peakDemand = peakRecord ? {
      value: Math.abs(peakRecord.value),
      time: new Date(peakRecord.timestamp).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false })
    } : { value: 0, time: '' };

    // Calculate average load
    const averageLoad = allRecords.length > 0 ? totalConsumption / allRecords.length : 0;

    return {
      hourlyConsumption,
      totalConsumption,
      peakDemand,
      averageLoad
    };
  }, []);

  // Process power data
  const processPowerData = useCallback((
    powerData: HistoricalDataRecord<StatisticalRecord>[],
    timeRange: { start: Date, end: Date },
    viewType: ViewType
  ): AnalyticsData => {
    // Default empty analytics data structure
    const defaultData: AnalyticsData = {
      hourlyConsumption: [],
      totalConsumption: 0,
      peakDemand: { value: 0, time: '' },
      averageLoad: 0,
    };

    if (!powerData || powerData.length === 0) {
      return defaultData;
    }

    // Combine data from all devices
    let allRecords: StatisticalRecord[] = [];
    powerData.forEach(deviceData => {
      allRecords = [...allRecords, ...deviceData.records];
    });

    // Sort records by timestamp
    allRecords.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

    // Map records to consumption format based on view type - using mean_value for power
    const hourlyConsumption = allRecords.map(record => {
      const date = new Date(record.timestamp);
      let timeLabel: string;
      
      switch(viewType) {
        case 'day':
          timeLabel = date.toLocaleTimeString('en-US', { hour: '2-digit', hour12: false }) + ':00';
          break;
        case 'week':
          timeLabel = date.toLocaleDateString('en-US', { weekday: 'short' }); // Mon, Tue, etc.
          break;
        case 'month':
          timeLabel = date.toLocaleDateString('en-US', { day: '2-digit' }); // 01, 02, etc.
          break;
        case 'year':
          timeLabel = date.toLocaleDateString('en-US', { month: 'short' }); // Jan, Feb, etc.
          break;
        case 'multi-year':
          timeLabel = date.getFullYear().toString(); // 2023, 2024, etc.
          break;
        default:
          timeLabel = date.toISOString();
      }
      
      return {
        time: timeLabel,
        actual: Math.abs(record.mean_value), // Use mean value for power
        predicted: undefined,
        accumulatedValue: undefined,
      };
    });

    // Calculate peak demand using max_value from statistical records
    const peakRecord = [...allRecords].sort((a, b) => Math.abs(b.max_value) - Math.abs(a.max_value))[0];
    const peakDemand = peakRecord ? {
      value: Math.abs(peakRecord.max_value),
      time: new Date(peakRecord.timestamp).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false })
    } : { value: 0, time: '' };

    // Calculate average load using mean values
    const totalMean = allRecords.reduce((sum, record) => sum + Math.abs(record.mean_value), 0);
    const averageLoad = allRecords.length > 0 ? totalMean / allRecords.length : 0;

    return {
      hourlyConsumption,
      totalConsumption: totalMean, // For power, total consumption is the sum of mean values
      peakDemand,
      averageLoad
    };
  }, []);

  // Fetch data with error handling and loading state management
  const fetchData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Get time range based on selected view and date
      const timeRange = getTimeRange(selectedView, selectedDate);

      // Determine device IDs based on system selection
      let deviceIds: string[];
      const systemDevices = getSystemDeviceIds(selectedSystem);
      
      if (systemDevices.length > 0) {
        // Use system-specific devices when a system is selected
        deviceIds = systemDevices;
      } else {
        // Use building devices when 'all' systems is selected
        deviceIds = getDeviceIds(selectedBuilding);
      }

      // Clear previous data based on what we're fetching
      if (selectedTab === 'consumption') {
        if (chartType === 'energy') {
          setPowerData(null);
        } else {
          setAnalyticsData(null);
        }
      } else {
        // Clear both for comparison tab
        setPowerData(null);
      }

      // Fetch energy data for energy chart or comparison tab
      if (chartType === 'energy' || selectedTab === 'comparison') {
        setEnergyLoading(true);
        try {
          const energyQuery: HistoricalDataQuery = {
            table_name: getTableName(selectedView),
            site_id: SITE_ID,
            device_id: deviceIds,
            datapoints: ['daily_energy'],
            start_timestamp: timeRange.start.toISOString(),
            end_timestamp: timeRange.end.toISOString(),
          };

          const energyData = await fetchEnergyData(energyQuery);
          const processedEnergyData = processEnergyData(energyData, timeRange, selectedView);
          setAnalyticsData(processedEnergyData);
        } catch (err) {
          console.error('[Analytics] Energy data fetch failed:', err);
          // Don't clear all data on partial failure
          throw new Error(`Energy data fetch failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
        } finally {
          setEnergyLoading(false);
        }
      }

      // Fetch power data for power chart
      if (chartType === 'power' && selectedTab === 'consumption') {
        setPowerLoading(true);
        try {
          const powerQuery: HistoricalDataQuery = {
            table_name: getTableName(selectedView),
            site_id: SITE_ID,
            device_id: deviceIds,
            datapoints: ['power'],
            start_timestamp: timeRange.start.toISOString(),
            end_timestamp: timeRange.end.toISOString(),
          };

          const powerStatsData = await fetchStatisticalData(powerQuery);
          const processedPowerData = processPowerData(powerStatsData, timeRange, selectedView);
          setPowerData(processedPowerData);
        } catch (err) {
          console.error('[Analytics] Power data fetch failed:', err);
          throw new Error(`Power data fetch failed: ${err instanceof Error ? err.message : 'Unknown error'}`);
        } finally {
          setPowerLoading(false);
        }
      }

      // Fetch comparison data if needed
      if (selectedTab === 'comparison') {
        if (comparisonPeriod !== 'none') {
          setComparisonLoading(true);
          try {
            const compareDate = getComparisonDate(selectedDate, comparisonPeriod as 'day' | 'week' | 'month' | 'year');
            const compareTimeRange = getTimeRange(selectedView, compareDate);

            const comparisonQuery: HistoricalDataQuery = {
              table_name: getTableName(selectedView),
              site_id: SITE_ID,
              device_id: deviceIds,
              datapoints: ['daily_energy'],
              start_timestamp: compareTimeRange.start.toISOString(),
              end_timestamp: compareTimeRange.end.toISOString(),
            };

            const comparisonData = await fetchEnergyData(comparisonQuery);
            const processedComparisonData = processEnergyData(comparisonData, compareTimeRange, selectedView);
            setComparisonData(processedComparisonData);
          } catch (err) {
            console.error('[Analytics] Comparison data fetch failed:', err);
            setComparisonData(null);
          } finally {
            setComparisonLoading(false);
          }
        } else {
          setComparisonData(null);
        }
      } else {
        // Clear comparison data when not on comparison tab
        setComparisonData(null);
      }
    } catch (err) {
      console.error(`[Analytics] Error fetching data for ${selectedTab} tab:`, err);
      setError(`Failed to load ${selectedTab} data. ${err instanceof Error ? err.message : ''}`);
    } finally {
      setLoading(false);
      setEnergyLoading(false);
      setPowerLoading(false);
      setComparisonLoading(false);
    }
  }, [
    selectedTab,
    selectedView,
    selectedDate,
    selectedBuilding,
    selectedSystem,
    comparisonPeriod,
    chartType
  ]);

  // Enhanced retry functionality - defined after fetchData
  const { retry: retryFetch, isRetrying, retryCount } = useRetry(fetchData, {
    maxRetries: 3,
    initialDelay: 1000
  });

  // Fetch data when dependencies change
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleBuildingChange = (buildingId: BuildingType) => {
    setSelectedBuilding(buildingId);
  };

  const handleSystemChange = (systemId: SystemType) => {
    setSelectedSystem(systemId);
  };

  const handleTabChange = (tab: AnalyticsTab) => {
    setSelectedTab(tab);

    // Auto-select 'day' comparison when switching to comparison tab

    if (tab !== 'consumption') {
      setShowCumulative(false);
    }
  };

  const handleViewChange = (view: ControlsViewType) => {
    setSelectedView(view);
  };

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
  };

  const handleCumulativeToggle = (isCumulative: boolean) => {
    setShowCumulative(isCumulative);
  };

  // Export handlers
  const handleExportCSV = () => {
    // Get the current data based on the active tab
    const data = chartType === 'power' && powerData ? powerData : analyticsData;
    
    if (!data || !data.hourlyConsumption || data.hourlyConsumption.length === 0) {
      console.warn('No data to export');
      return;
    }

    // Format data for CSV
    const csvData = data.hourlyConsumption.map(point => ({
      Time: point.time,
      [chartType === 'power' ? 'Power (kW)' : 'Energy (kWh)']: point.actual.toFixed(2)
    }));

    // Convert to CSV string
    const headers = Object.keys(csvData[0]).join(',');
    const rows = csvData.map(row => Object.values(row).join(',')).join('\n');
    const csv = `${headers}\n${rows}`;

    // Create download
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', `${selectedTab}-data-${selectedView}-${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleExportPDF = () => {
    // This would be implemented with a PDF library
    alert('PDF export functionality would be implemented with a library like jsPDF');
  };

  const handleComparisonPeriodChange = (period: ComparisonPeriod) => {
    setComparisonPeriod(period);
  };

  const handleRetry = () => {
    retryFetch();
  };

  // Add handler for chart type changes
  const handleChartTypeChange = (type: 'energy' | 'power') => {
    // Clear opposite data type when switching
    if (type === 'energy') {
      setPowerData(null);
    } else {
      setAnalyticsData(null);
    }
    setChartType(type);
  };

  const renderTabContent = () => {
    // For power chart, use power data; for energy chart, use analytics data
    const currentData = chartType === 'power' && powerData ? powerData : analyticsData;
    
    // Determine if any relevant data is loading
    const isDataLoading = selectedTab === 'consumption' 
      ? (chartType === 'energy' ? energyLoading : powerLoading)
      : energyLoading || comparisonLoading;

    // Handle loading states for specific chart data
    if (isDataLoading) {
      return selectedTab === 'comparison' ? 
        <ComparisonTabSkeleton height={500} /> : 
        <AnalyticsChartSkeleton height={500} />;
    }

    // Don't return early if data is null - always render the UI
    return (
      <div className="fade-in">
        <div className="mb-4">
          {selectedTab === 'consumption' && (
            <ConsumptionTab
              chartData={currentData}
              view={selectedView}
              showCumulative={showCumulative}
              selectedDate={selectedDate}
              selectedSystem={selectedSystem}
              systemName={selectedSystem !== 'all' ? SYSTEM_TYPES[selectedSystem]?.name || 'All Systems' : 'All Systems'}
              chartType={chartType}
              onChartTypeChange={handleChartTypeChange}
            />
          )}
          {selectedTab === 'comparison' && comparisonPeriod !== 'none' && (
            <ComparisonTab
              chartData={{
                current: analyticsData?.hourlyConsumption?.map(point => ({
                  time: point.time,
                  consumption: point.actual,
                  value: point.actual,
                  fullDate: point.time // Ensure fullDate is included
                })) || [],
                comparison: comparisonData?.hourlyConsumption?.map(point => ({
                  time: point.time,
                  consumption: point.actual,
                  value: point.actual,
                  fullDate: point.time // Ensure fullDate is included
                })) || []
              }}
              selectedView={selectedView as AnalyticsViewType}
              comparisonPeriod={comparisonPeriod}
              selectedDate={selectedDate}
              selectedSystem={selectedSystem}
              systemName={selectedSystem !== 'all' ? SYSTEM_TYPES[selectedSystem]?.name || 'All Systems' : 'All Systems'}
            />
          )}
          {selectedTab === 'comparison' && comparisonPeriod !== 'none' && !comparisonData && !comparisonLoading && (
            <ChartDataSkeleton height={300} />
          )}
        </div>
      </div>
    );
  };

  // Show full page skeleton on initial load
  if (loading && !analyticsData && !error) {
    return <AnalyticsPageSkeleton />;
  }

  return (
    <div className="p-3 md:p-4 lg:p-5 space-y-2 bg-gray-50 min-h-screen">
      {/* Controls with reduced vertical spacing */}
      <AnalyticsControls
        selectedTab={selectedTab}
        selectedView={selectedView}
        selectedDate={selectedDate}
        showCumulative={showCumulative}
        onTabChange={handleTabChange}
        comparisonPeriod={comparisonPeriod}
        onComparisonPeriodChange={handleComparisonPeriodChange}
        onViewChange={handleViewChange}
        onDateChange={handleDateChange}
        onCumulativeToggle={handleCumulativeToggle}
        onExportCSV={handleExportCSV}
        onExportPDF={handleExportPDF}
      >
        {/* BuildingSelector and SystemSelector without unnecessary wrapper */}
        <BuildingSelector
          selectedBuilding={selectedBuilding}
          onBuildingChange={handleBuildingChange}
          buildings={buildings}
        />
        <SystemSelector
          selectedSystem={selectedSystem}
          onSystemChange={handleSystemChange}
        />
      </AnalyticsControls>

      {/* Main content with reduced top margin */}
      <div className="mt-2">
        {!loading && error && (
          <EnhancedErrorDisplay
            message={error}
            onRetry={handleRetry}
            isRetrying={isRetrying}
            retryCount={retryCount}
            variant="compact"
            errorType="data"
            className="mb-4"
          />
        )}
        {!loading && !error && (
          // Always render the UI, even if there's no data
          renderTabContent()
        )}
      </div>
    </div>
  );
}