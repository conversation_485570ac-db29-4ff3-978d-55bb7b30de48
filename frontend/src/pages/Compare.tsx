import React, { useState, useEffect, useMemo } from 'react';
import { X, Plus, LineChart, Power } from 'lucide-react';
import { CompareLineChart } from '../components/charts/CompareLineChart';
import { SYSTEM_ICONS } from '../lib/constants';
import { SYSTEM_TYPES } from '../lib/config/energy-systems';
import { fetchChildDeviceRelationsById, fetchDeviceById } from '../lib/api/devices';
import { fetchEnergyData, fetchStatisticalData, isUsingMockData } from '../lib/services/meterComparisonService';
import type { HistoricalDataQuery } from '../services/timescaleService';
import { MOCK_METERS, MOCK_CATEGORY_MAP } from '../lib/services/mockMeterData';
import { 
  getAllMainMetersFlat,
  getAllTowerBuildingMeters,
  getAllPodiumBuildingMeters,
  getAllCarParkBuildingMeters
} from '../lib/config/building/meters';

// Use keys of SYSTEM_ICONS as the definitive type for meters in this component
type MeterIconKey = keyof typeof SYSTEM_ICONS;

interface SelectedMeter {
  id: string;
  name: string;
  type: MeterIconKey; // Use the new type alias
  category: string;
}

// Category mapping similar to Dashboard.tsx
const CATEGORY_MAP: Record<string, string> = {
  "chiller_plant": "Chiller Plant",
  "air_distribution_system": "Air Side",
  "light_and_power": "Light & Power",
  "data_center_and_others": "Data Center & Others",
  "ev_charger": "EV Charger",
  "elevator_escalator": "Elevator & Escalator"
};

// Helper function to map meter type/category to icon key
const mapMeterTypeToIconKey = (type?: string, category?: string): MeterIconKey => {
  const typeOrCategory = (type || category || '').toLowerCase();
  
  if (typeOrCategory.includes('chiller') || typeOrCategory.includes('cooling')) {
    return 'chillerPlant';
  } else if (typeOrCategory.includes('air') || typeOrCategory.includes('ahu') || typeOrCategory.includes('hvac') || typeOrCategory.includes('fahu')) {
    return 'airSide';
  } else if (typeOrCategory.includes('light') || typeOrCategory.includes('lighting')) {
    return 'light_power';
  } else if (typeOrCategory.includes('power') && !typeOrCategory.includes('light')) {
    return 'power';
  } else if (typeOrCategory.includes('ev') || typeOrCategory.includes('charger')) {
    return 'evCharger';
  } else if (typeOrCategory.includes('elevator') || typeOrCategory.includes('escalator') || typeOrCategory.includes('lift')) {
    return 'escalator_elevator';
  } else if (typeOrCategory.includes('data') || typeOrCategory.includes('server') || typeOrCategory.includes('it')) {
    return 'data_center_others';
  } else if (typeOrCategory.includes('tenant') || typeOrCategory.includes('office')) {
    return 'tenant';
  } else if (typeOrCategory.includes('main') || typeOrCategory.includes('distribution')) {
    return 'main';
  } else if (typeOrCategory.includes('water') || typeOrCategory.includes('pump')) {
    return 'water';
  } else if (typeOrCategory.includes('fan') || typeOrCategory.includes('exhaust')) {
    return 'fan';
  } else if (typeOrCategory.includes('building')) {
    return 'building';
  }
  
  return 'other'; // default
};

// Convert our meter configuration to SelectedMeter format
const getAllMetersFromConfig = (): SelectedMeter[] => {
  const allMeters: SelectedMeter[] = [];
  
  // Add main meters
  getAllMainMetersFlat().forEach(meter => {
    // Use groupName as category if available
    const category = (meter as any).groupName || meter.category || meter.type || 'Other';
    allMeters.push({
      id: meter.id,
      name: meter.name,
      type: mapMeterTypeToIconKey(meter.type, category),
      category: category
    });
  });
  
  // Add tower meters
  getAllTowerBuildingMeters().forEach(meter => {
    const category = meter.category || meter.type || 'Tower';
    allMeters.push({
      id: meter.id,
      name: meter.name,
      type: mapMeterTypeToIconKey(meter.type, category),
      category: category
    });
  });
  
  // Add podium meters
  getAllPodiumBuildingMeters().forEach(meter => {
    const category = meter.category || meter.type || 'Podium';
    allMeters.push({
      id: meter.id,
      name: meter.name,
      type: mapMeterTypeToIconKey(meter.type, category),
      category: category
    });
  });
  
  // Add car park meters
  getAllCarParkBuildingMeters().forEach(meter => {
    const category = meter.category || meter.type || 'Car Park';
    allMeters.push({
      id: meter.id,
      name: meter.name,
      type: mapMeterTypeToIconKey(meter.type, category),
      category: category
    });
  });
  
  // Debug: Log elevator/escalator meters
  const elevatorMeters = allMeters.filter(m => 
    m.type === 'escalator_elevator' || 
    m.category?.toLowerCase().includes('elevator') || 
    m.category?.toLowerCase().includes('escalator')
  );
  console.log('Elevator/Escalator meters found:', elevatorMeters.length);
  
  return allMeters;
};

export default function Compare() {
  const [selectedMeters, setSelectedMeters] = useState<SelectedMeter[]>([]);
  const [selectedType, setSelectedType] = useState<MeterIconKey | null>(null);
  const [view, setView] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('daily');
  const [chartData, setChartData] = useState<any[] | null>(null);
  const [availableMeters, setAvailableMeters] = useState<SelectedMeter[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch available meters when component mounts
  useEffect(() => {
    const fetchAllAvailableMeters = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Use our configured meter list
        console.log('Loading meter configuration');
        const configuredMeters = getAllMetersFromConfig();
        console.log(`Loaded ${configuredMeters.length} meters from configuration`);
        setAvailableMeters(configuredMeters);
      } catch (err) {
        console.error('Error fetching meters:', err);
        setError('Failed to load meters. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllAvailableMeters();
  }, []);

  // Filter meters based on selected type with improved logging
  const filteredMeters = useMemo(() => {
    if (!selectedType) {
      console.log(`No type filter applied, showing all ${availableMeters.length} meters`);
      return availableMeters;
    }

    const filtered = availableMeters.filter(meter => meter.type === selectedType);
    console.log(`Filter applied: ${selectedType}, found ${filtered.length} meters out of ${availableMeters.length}`);
    return filtered;
  }, [availableMeters, selectedType]);

  // Fetch chart data when selected meters or view changes
  useEffect(() => {
    const fetchAndPrepareChartData = async () => {
      if (selectedMeters.length === 0) {
        setChartData(null);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const now = new Date();
        let startDate: Date, endDate: Date;

        // Set time range based on selected view
        switch (view) {
          case 'daily':
            startDate = new Date(now);
            startDate.setHours(0, 0, 0, 0);
            endDate = new Date(now);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'weekly':
            startDate = new Date(now);
            startDate.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
            startDate.setHours(0, 0, 0, 0);
            endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 6); // End of week (Saturday)
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'monthly':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
            break;
          case 'yearly':
            startDate = new Date(now.getFullYear(), 0, 1);
            endDate = new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999);
            break;
        }

        // Choose the appropriate table and datapoint based on view
        const tableName = view === 'daily'
          ? 'statistic_data_1hour'
          : view === 'weekly'
            ? 'energy_data_1day'
            : view === 'monthly'
              ? 'energy_data_1day'
              : 'energy_data_1month';

        const datapoints = view === 'daily' ? ['power'] : ['daily_energy'];

        // Create an array of promises for each meter's data request
        const dataPromises = selectedMeters.map(async (meter) => {
          const query: HistoricalDataQuery = {
            table_name: tableName,
            site_id: "set", // Default site ID
            device_id: meter.id,
            datapoints: datapoints,
            start_timestamp: startDate.toISOString(),
            end_timestamp: endDate.toISOString(),
          };

          try {
            const response = view === 'daily'
              ? await fetchStatisticalData(query)
              : await fetchEnergyData(query);

            return {
              meter,
              data: response
            };
          } catch (error) {
            console.error(`Error fetching data for meter ${meter.id}:`, error);
            return {
              meter,
              data: []
            };
          }
        });

        // Wait for all data to be fetched
        const results = await Promise.all(dataPromises);

        // Process and format the data for the chart
        let formattedData: any[] = [];

        if (view === 'daily') {
          // For daily view, we create 24 hourly data points
          formattedData = Array.from({ length: 24 }, (_, hourIndex) => {
            const hour = hourIndex < 10 ? `0${hourIndex}:00` : `${hourIndex}:00`;
            const dataPoint: any = { time: hour };

            // Add data for each meter
            results.forEach(({ meter, data }) => {
              if (data && data.length > 0 && data[0].records && data[0].records.length > 0) {
                const hourData = data[0].records.find((r: any) => {
                  const recordHour = new Date(r.timestamp).getHours();
                  return recordHour === hourIndex;
                });

                const key = `${meter.category.replace(/\s+/g, '_')}_${meter.id}`;
                dataPoint[key] = hourData ? (hourData.mean_value || hourData.value) : 0;
              }
            });

            return dataPoint;
          });
        } else if (view === 'weekly') {
          // For weekly view, create 7 daily data points
          const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
          formattedData = Array.from({ length: 7 }, (_, dayIndex) => {
            const dayDate = new Date(startDate);
            dayDate.setDate(startDate.getDate() + dayIndex);
            const dataPoint: any = { time: days[dayIndex] };

            // Add data for each meter
            results.forEach(({ meter, data }) => {
              if (data && data.length > 0 && data[0].records && data[0].records.length > 0) {
                const dayData = data[0].records.find((r: any) => {
                  const recordDate = new Date(r.timestamp);
                  return recordDate.getDay() === dayIndex;
                });

                const key = `${meter.category.replace(/\s+/g, '_')}_${meter.id}`;
                dataPoint[key] = dayData ? dayData.value : 0;
              }
            });

            return dataPoint;
          });
        } else if (view === 'monthly') {
          // For monthly view, create data points for each day of the month
          const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
          formattedData = Array.from({ length: daysInMonth }, (_, dayIndex) => {
            const day = dayIndex + 1;
            const dataPoint: any = { time: day.toString() };

            // Add data for each meter
            results.forEach(({ meter, data }) => {
              if (data && data.length > 0 && data[0].records && data[0].records.length > 0) {
                const dayData = data[0].records.find((r: any) => {
                  const recordDate = new Date(r.timestamp);
                  return recordDate.getDate() === day;
                });

                const key = `${meter.category.replace(/\s+/g, '_')}_${meter.id}`;
                dataPoint[key] = dayData ? dayData.value : 0;
              }
            });

            return dataPoint;
          });
        } else if (view === 'yearly') {
          // For yearly view, create 12 monthly data points
          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          formattedData = Array.from({ length: 12 }, (_, monthIndex) => {
            const dataPoint: any = { time: months[monthIndex] };

            // Add data for each meter
            results.forEach(({ meter, data }) => {
              if (data && data.length > 0 && data[0].records && data[0].records.length > 0) {
                const monthData = data[0].records.find((r: any) => {
                  const recordDate = new Date(r.timestamp);
                  return recordDate.getMonth() === monthIndex;
                });

                const key = `${meter.category.replace(/\s+/g, '_')}_${meter.id}`;
                dataPoint[key] = monthData ? monthData.value : 0;
              }
            });

            return dataPoint;
          });
        }

        setChartData(formattedData);
      } catch (err) {
        console.error('Error fetching chart data:', err);
        setError('Failed to load chart data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAndPrepareChartData();
  }, [selectedMeters, view]);

  const handleAddMeter = (meter: SelectedMeter) => {
    if (selectedMeters.length >= 10) return;
    // Check for duplicates using both id and category
    if (selectedMeters.some(m => m.id === meter.id && m.category === meter.category)) return;
    setSelectedMeters([...selectedMeters, meter]);
  };

  const handleRemoveMeter = (meterId: string) => {
    setSelectedMeters(selectedMeters.filter(m => m.id !== meterId));
  };

  return (
    <div className="h-[calc(100vh-56px)] p-4 bg-gradient-to-br from-[#F9FAFF] via-white to-blue-50/30 overflow-auto">

      <div className="grid grid-cols-12 gap-4">
        {/* Meter Selection Panel */}
        <div className="col-span-3 bg-white rounded-xl border border-[#EDEFF9] p-4">
          <div className="space-y-4">
            {/* System Type Filter */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-700">System Type</h3>
                {selectedType && (
                  <button
                    onClick={() => {
                      console.log('Clearing type filter');
                      setSelectedType(null);
                    }}
                    className="text-xs text-blue-600 hover:text-blue-800 transition-colors"
                  >
                    Clear filter
                  </button>
                )}
              </div>
              <div className="grid grid-cols-2 gap-1.5">
                {Object.entries(SYSTEM_TYPES).map(([id, { name, color }]) => {
                  const type = { id: id as MeterIconKey, name };
                  const Icon = SYSTEM_ICONS[type.id as keyof typeof SYSTEM_ICONS] || Power;
                  const isSelected = type.id === selectedType;

                  // Log what's happening with selections for debugging
                  console.log(`Filter button: ${type.id}, isSelected: ${isSelected}, selectedType: ${selectedType}`);

                  return (
                    <button
                      key={type.id}
                      onClick={() => {
                        console.log(`Clicked filter: ${type.id}, current selectedType: ${selectedType}`);
                        setSelectedType(isSelected ? null : type.id);
                      }}
                      className={`flex items-center gap-1.5 px-3 py-2 rounded-lg text-[10px] transition-all duration-200 border ${
                        isSelected
                          ? 'bg-white border-blue-200 shadow-sm'
                          : 'border-gray-100 bg-gray-50/50 hover:bg-blue-50/50 hover:border-blue-100 hover:shadow-sm'
                      }`}
                    >
                      <Icon
                        size={14}
                        className="transition-colors duration-300"
                        style={{ color: isSelected ? color : '#6B7280' }}
                      />
                      <span
                        className="transition-colors duration-300 text-left w-full"
                        style={{ color: isSelected ? color : '#4B5563' }}
                      >
                        {name}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>

            {/* Meter List */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                Available Meters
                {selectedType && (
                  <span className="ml-1 text-xs text-gray-500">
                    (Filtered by {SYSTEM_TYPES[selectedType]?.name || selectedType})
                  </span>
                )}
              </h3>
              <div className="h-[calc(100vh-320px)] overflow-auto space-y-2 pr-2">
                {isLoading && <div className="text-sm text-gray-500">Loading meters...</div>}
                {error && <div className="text-sm text-red-500">{error}</div>}
                {!isLoading && !error && filteredMeters.length === 0 &&
                  <div className="text-sm text-gray-500">
                    No meters available {selectedType ? `for ${SYSTEM_TYPES[selectedType]?.name || selectedType}` : ''}
                  </div>
                }
                {filteredMeters.map((meter) => {
                  const Icon = SYSTEM_ICONS[meter.type] || LineChart;
                  const isSelected = selectedMeters.some(m => m.id === meter.id);
                  return (
                    <button
                      key={`${meter.category}-${meter.id}`}
                      onClick={() => handleAddMeter(meter)}
                      disabled={isSelected || selectedMeters.length >= 10}
                      className={`w-full flex items-center gap-1.5 p-2 rounded-lg transition-all duration-200 ${
                        isSelected
                          ? 'bg-blue-50 text-primary-blue cursor-not-allowed'
                          : selectedMeters.length >= 10
                          ? 'opacity-50 cursor-not-allowed'
                          : 'hover:bg-blue-50/50'
                      }`}
                    >
                      <Icon size={16} className="text-gray-400" />
                      <div className="flex-1 text-left">
                        <div className="text-xs font-medium">{meter.name}</div>
                        <div className="text-xs text-gray-500">{meter.category}</div>
                      </div>
                      {!isSelected && selectedMeters.length < 10 && (
                        <Plus size={16} className="text-gray-400" />
                      )}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Chart Panel */}
        <div className="col-span-9 space-y-4">
          {/* Selected Meters */}
          <div className="bg-white rounded-xl border border-[#EDEFF9] p-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-sm font-medium text-gray-700">Selected Meters ({selectedMeters.length}/10)</h2>
              {selectedMeters.length > 0 && (
                <button
                  onClick={() => setSelectedMeters([])}
                  className="flex items-center gap-1.5 px-2 py-1 text-xs text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                >
                  <X size={14} />
                  <span>Clear all</span>
                </button>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
              {selectedMeters.map((meter) => {
                const Icon = SYSTEM_ICONS[meter.type] || LineChart; // meter.type is now MeterIconKey
                return (
                  <div
                    key={meter.id}
                    className="flex items-center gap-2 px-3 py-1.5 bg-blue-50 rounded-lg"
                  >
                    <Icon size={14} className="text-primary-blue" />
                    <span className="text-xs text-primary-blue">{meter.name}</span>
                    <button
                      onClick={() => handleRemoveMeter(meter.id)}
                      className="p-0.5 hover:bg-blue-100 rounded transition-colors"
                    >
                      <X size={14} className="text-primary-blue" />
                    </button>
                  </div>
                );
              })}
              {selectedMeters.length === 0 && (
                <div className="text-sm text-gray-500">
                  Select up to 10 meters to compare
                </div>
              )}
            </div>
          </div>

          {/* Chart */}
          {isLoading && (
            <div className="bg-white rounded-xl border border-[#EDEFF9] p-6 flex items-center justify-center h-[500px]">
              <div className="text-gray-500">Loading chart data...</div>
            </div>
          )}

          {!isLoading && error && (
            <div className="bg-white rounded-xl border border-[#EDEFF9] p-6 flex items-center justify-center h-[500px]">
              <div className="text-red-500">{error}</div>
            </div>
          )}

          {!isLoading && !error && chartData && chartData.length > 0 && (
            <div className="bg-white rounded-xl border border-[#EDEFF9] p-6">
              {/* View Selection */}
              <div className="flex items-center justify-end gap-1 mb-4">
                {(['daily', 'weekly', 'monthly', 'yearly'] as const).map((viewType) => (
                  <button
                    key={viewType}
                    onClick={() => setView(viewType)}
                    className={`flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-xs transition-all duration-200 ${
                      view === viewType
                        ? 'bg-gradient-to-br from-blue-50/80 via-blue-50/60 to-white border border-blue-200 shadow-[0_2px_8px_rgba(14,126,228,0.15)]'
                        : 'hover:bg-blue-50/50'
                    }`}
                  >
                    <span className={view === viewType ? 'text-primary-blue' : 'text-gray-600'}>
                      {viewType.charAt(0).toUpperCase() + viewType.slice(1)} View
                    </span>
                  </button>
                ))}
              </div>

              <div className="h-[500px]">
                <CompareLineChart
                  data={chartData}
                  selectedMeters={selectedMeters}
                  view={view}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}