import MeterGroupConfiguration from '../components/admin/MeterGroupConfiguration';
import ScreenshotUploadManager from '../components/admin/ScreenshotUploadManager';
import { useState } from 'react';

/**
 * Admin page for system configuration
 * 
 * This page provides access to administrative functions that are only
 * available to system administrators.
 */
export default function Admin() {
  const [activeTab, setActiveTab] = useState('meters');

  return (
    <div className="p-4 space-y-4">
      <h1 className="text-2xl font-bold text-gray-900">Admin Settings</h1>
      
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('meters')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'meters'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Meter Configuration
          </button>
          <button
            onClick={() => setActiveTab('screenshots')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'screenshots'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Screenshot Manager
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'meters' && (
          <div className="p-6 bg-white rounded-lg shadow-sm border border-gray-200">
            <MeterGroupConfiguration />
          </div>
        )}
        
        {activeTab === 'screenshots' && (
          <div className="p-6 bg-white rounded-lg shadow-sm border border-gray-200">
            <ScreenshotUploadManager />
          </div>
        )}
      </div>
    </div>
  );
}