export const colors = {
  brand: {
    blue: '#065BA9',
    blueDark: '#044B87',
    blueLight: '#E4EFFA'
  },
  neutral: {
    white: '#FFFFFF',
    gray50: '#F9FAFF',
    gray100: '#EDEFF9',
    gray200: '#DBE4FF',
    gray300: '#C1CCE8',
    gray400: '#9EAAC4',
    gray500: '#788796',
    gray600: '#4F5668',
    gray700: '#363C4B',
    gray800: '#272B35',
    black: '#212529'
  },
  semantic: {
    success: '#44B36B',
    warning: '#FFC94D',
    destructive: '#E53935',
    info: '#2BA7FF'
  }
};

export const spacing = {
  xs: '0.25rem', // 4px
  sm: '0.5rem', // 8px
  md: '1rem', // 16px
  lg: '1.5rem', // 24px
  xl: '2rem', // 32px
  xxl: '3rem' // 48px
};

export const radii = {
  none: '0',
  sm: '0.125rem', // 2px
  md: '0.375rem', // 6px
  lg: '0.5rem', // 8px
  full: '9999px'
};

export const typography = {
  fontFamily: {
    sans: '"Inter", ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont',
    mono: '"Fira Code", ui-monospace, SFMono-Regular'
  },
  fontSize: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem',
    '5xl': '3rem'
  },
  lineHeight: {
    tight: '1.2',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625'
  }
};

export const shadows = {
  xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
};

export const motion = {
  durations: {
    fast: '150ms',
    base: '300ms',
    slow: '500ms'
  },
  easing: {
    inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    out: 'cubic-bezier(0.0, 0, 0.2, 1)'
  }
};

export const zIndex = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070
};

export const tokens = {
  colors,
  spacing,
  radii,
  typography,
  shadows,
  motion,
  zIndex
};

export type DesignTokens = typeof tokens; 