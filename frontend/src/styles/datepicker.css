/* Custom DatePicker Styles */

/* Global react-datepicker z-index fix */
.react-datepicker-popper {
  z-index: 9999 !important;
}

.react-datepicker {
  z-index: 9999 !important;
}

.analytics-date-picker .react-datepicker {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  border-radius: 8px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(226, 232, 240, 1);
  overflow: visible;
  z-index: 9999 !important;
  position: relative;
}

.analytics-date-picker .react-datepicker__header {
  background-color: white;
  border-bottom: 1px solid rgba(226, 232, 240, 1);
  padding-top: 12px;
}

.analytics-date-picker .react-datepicker__month-container {
  padding-bottom: 12px;
}

.analytics-date-picker .react-datepicker__navigation {
  top: 12px;
}

.analytics-date-picker .react-datepicker__current-month {
  font-size: 14px;
  font-weight: 500;
  color: #1a202c;
  padding-bottom: 8px;
}

.analytics-date-picker .react-datepicker__day-name {
  color: #a0aec0;
  font-weight: 500;
  width: 36px;
  font-size: 12px;
  margin-top: 4px;
}

.analytics-date-picker .react-datepicker__day {
  width: 36px;
  height: 36px;
  line-height: 36px;
  margin: 0;
  border-radius: 50%;
  color: #4a5568;
  font-size: 13px;
  transition: all 0.2s;
}

.analytics-date-picker .react-datepicker__day:hover {
  background-color: #f7fafc;
  border-radius: 50%;
}

.analytics-date-picker .react-datepicker__day--selected {
  background-color: #3b82f6;
  color: white;
  font-weight: 500;
  border-radius: 50%;
}

.analytics-date-picker .react-datepicker__day--keyboard-selected {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border-radius: 50%;
}

.analytics-date-picker .react-datepicker__month-select,
.analytics-date-picker .react-datepicker__year-select {
  border-radius: 4px;
  padding: 4px 8px;
  border: 1px solid #e2e8f0;
  background-color: white;
  color: #4a5568;
  font-size: 14px;
  cursor: pointer;
}

.analytics-date-picker .react-datepicker__month-wrapper,
.analytics-date-picker .react-datepicker__year-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  padding: 8px 0;
}

.analytics-date-picker .react-datepicker__month-text,
.analytics-date-picker .react-datepicker__year-text {
  width: 4.5rem;
  padding: 8px 0;
  margin: 2px;
  border-radius: 4px;
  display: inline-block;
  color: #4a5568;
  text-align: center;
  cursor: pointer;
}

.analytics-date-picker .react-datepicker__month-text:hover,
.analytics-date-picker .react-datepicker__year-text:hover {
  background-color: #f7fafc;
}

.analytics-date-picker .react-datepicker__month-text--selected,
.analytics-date-picker .react-datepicker__year-text--selected {
  background-color: #3b82f6;
  color: white;
  font-weight: 500;
}

.date-picker-wrapper {
  display: inline-block;
}

/* Custom styles for power demand calendar */
.power-demand-calendar {
  font-family: inherit;
  border: none;
  box-shadow: none;
  font-size: 12px;
  z-index: 9999 !important;
  position: relative;
}

.power-demand-calendar .react-datepicker__header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 6px;
}

.power-demand-calendar .react-datepicker__day-name,
.power-demand-calendar .react-datepicker__day {
  width: 28px;
  line-height: 28px;
  margin: 2px;
  font-size: 11px;
}

.power-demand-calendar .react-datepicker__current-month {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

.power-demand-calendar .react-datepicker__navigation {
  top: 8px;
  width: 20px;
  height: 20px;
}

.power-demand-calendar .react-datepicker__day--selected,
.power-demand-calendar .react-datepicker__day--keyboard-selected {
  background-color: #065ba9;
  color: white;
}

.power-demand-calendar .react-datepicker__day--selected:hover,
.power-demand-calendar .react-datepicker__day--keyboard-selected:hover {
  background-color: #0874cc;
}

.power-demand-calendar .react-datepicker__day:hover {
  background-color: #e5e7eb;
}
