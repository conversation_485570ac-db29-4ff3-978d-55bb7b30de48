import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import './index.css';
import './styles/toggle-override.css';

// Initialize mock API for development
async function initMocks() {
  if (import.meta.env.DEV) {
    console.log('🔶 Mock API enabled for development - Disabling for testing backend');
    const { worker } = await import('./mocks/browser');
    // return worker.start({ onUnhandledRequest: 'bypass' }); // <-- Commented out to disable MSW

    // Load testing utilities
    import('./test-alarms').then(({ testAlarmsAPI }) => {
      (window as any).testAlarmsAPI = testAlarmsAPI;
      console.log('🧪 Alarms test available: window.testAlarmsAPI()');
    });
  }
  return Promise.resolve();
}

// Start the app after initializing mocks
initMocks().then(() => {
  createRoot(document.getElementById('root')!).render(
    <StrictMode>
      <App />
    </StrictMode>
  );
});