import { Active<PERSON>larm, AlarmRule, AlarmSeverity, AlarmOperator, AlarmHistory } from '@/types/alarms';
import { PaginatedResponse } from '@/types/api';

// --- Mock Alarm Rules ---
export const mockAlarmRules: PaginatedResponse<AlarmRule> = {
  count: 5, // Total active + inactive rules
  next: null,
  previous: null,
  results: [
    // 1. Meter Offline (no data received for >15 min)
    {
      id: 1,
      name: 'Meter Offline',
      metric: 'connectivity', // pseudo‑metric for ping/telemetry heartbeat
      threshold: 0, // 0 = offline
      operator: '=' as AlarmOperator,
      severity: 'CRITICAL' as AlarmSeverity,
      message: 'No data received from meter for 15 minutes.',
      is_active: true,
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(),
      updated_at: new Date().toISOString(),
    },

    // 2. Meter Disconnected (communication failure >1 h)
    {
      id: 2,
      name: 'Meter Disconnected',
      metric: 'connectivity',
      threshold: 0,
      operator: '=' as AlarmOperator,
      severity: 'CRITICAL' as AlarmSeverity,
      message: 'Meter communication link lost for more than 1 hour.',
      is_active: true,
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 20).toISOString(),
      updated_at: new Date().toISOString(),
    },

    // 3. High Power Consumption
    {
      id: 3,
      name: 'High Power Consumption',
      metric: 'active_power',
      threshold: 150.0, // kW
      operator: '>' as AlarmOperator,
      severity: 'CRITICAL' as AlarmSeverity,
      message: 'Power consumption exceeds defined threshold.',
      is_active: true,
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 10).toISOString(),
      updated_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
    },

    // 4. Low Voltage Phase A
    {
      id: 4,
      name: 'Low Voltage – Phase A',
      metric: 'voltage_an',
      threshold: 210.0, // V
      operator: '<' as AlarmOperator,
      severity: 'WARNING' as AlarmSeverity,
      message: 'Voltage Phase A below safe operating level.',
      is_active: true,
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(),
      updated_at: new Date().toISOString(),
    },

    // 5. Low Power Factor
    {
      id: 5,
      name: 'Low Power Factor',
      metric: 'power_factor',
      threshold: 0.85,
      operator: '<' as AlarmOperator,
      severity: 'WARNING' as AlarmSeverity,
      message: 'Overall power factor indicates inefficiency.',
      is_active: true,
      created_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(),
      updated_at: new Date().toISOString(),
    },
  ],
};

// --- Mock Active Alarms ---
export const mockActiveAlarms: PaginatedResponse<ActiveAlarm> = {
  count: 3,
  next: null,
  previous: null,
  results: [
    // Meter OFFLINE – T-1LP1
    {
      id: 201,
      rule: 1,
      rule_name: 'Meter Offline',
      metric: 'connectivity',
      severity: 'CRITICAL' as AlarmSeverity,
      operator: '=' as AlarmOperator,
      threshold: 0,
      trigger_value: 0,
      trigger_time: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 min ago
      last_checked_time: new Date(Date.now() - 1000 * 60 * 1).toISOString(), // 1 min ago
      message: 'No data received from meter for 45 minutes.',
      notification_sent: true,
      meter_name: 'T-1LP1',
    } as any,

    // Meter DISCONNECTED – T-DB-AHU-061
    {
      id: 202,
      rule: 2,
      rule_name: 'Meter Disconnected',
      metric: 'connectivity',
      severity: 'CRITICAL' as AlarmSeverity,
      operator: '=' as AlarmOperator,
      threshold: 0,
      trigger_value: 0,
      trigger_time: new Date(Date.now() - 1000 * 60 * 90).toISOString(), // 1.5 h ago
      last_checked_time: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
      message: 'Meter communication link lost.',
      notification_sent: true,
      meter_name: 'T-DB-AHU-061',
    } as any,

    // High Power Consumption – T-6PP1
    {
      id: 203,
      rule: 3,
      rule_name: 'High Power Consumption',
      metric: 'active_power',
      severity: 'CRITICAL' as AlarmSeverity,
      operator: '>' as AlarmOperator,
      threshold: 150.0,
      trigger_value: 172.4,
      trigger_time: new Date(Date.now() - 1000 * 60 * 25).toISOString(),
      last_checked_time: new Date(Date.now() - 1000 * 60 * 2).toISOString(),
      message: 'Power draw above threshold.',
      notification_sent: false,
      meter_name: 'T-6PP1',
    } as any,

    // Low Voltage Phase A – T-DB-AHU-101
    {
      id: 204,
      rule: 4,
      rule_name: 'Low Voltage – Phase A',
      metric: 'voltage_an',
      severity: 'WARNING' as AlarmSeverity,
      operator: '<' as AlarmOperator,
      threshold: 210.0,
      trigger_value: 208.2,
      trigger_time: new Date(Date.now() - 1000 * 60 * 120).toISOString(),
      last_checked_time: new Date(Date.now() - 1000 * 60 * 6).toISOString(),
      message: 'Voltage below safe operating level.',
      notification_sent: false,
      meter_name: 'T-DB-AHU-101',
    } as any,
  ],
};

// --- Mock Alarm History ---
export const mockAlarmHistory: PaginatedResponse<AlarmHistory> = {
  count: 2, // Updated count
  next: null,
  previous: null,
  results: [
    {
      id: 1,
      rule_name: 'High Power Consumption',
      metric: 'active_power',
      threshold: 100.0, // Previous threshold example
      operator: '>' as AlarmOperator,
      severity: 'CRITICAL' as AlarmSeverity,
      trigger_value: 135.2,
      trigger_time: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), // 3 hours ago
      acknowledge_time: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
      acknowledged_by: null,
      acknowledged_by_username: 'System',
      acknowledgment_notes: 'Power consumption returned to normal levels', // Corrected field name
      resolution_time: new Date(Date.now() - 1000 * 60 * 60 * 1.5).toISOString(), // Corrected field name
    },
    {
      id: 2,
      rule_name: 'Low Power Factor',
      metric: 'power_factor',
      threshold: 0.90, // Example historical threshold
      operator: '<' as AlarmOperator,
      severity: 'WARNING' as AlarmSeverity,
      trigger_value: 0.88,
      trigger_time: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3).toISOString(), // 3 days ago
      acknowledge_time: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2.9).toISOString(),
      acknowledged_by: 2, // Example User ID
      acknowledged_by_username: 'j.doe',
      acknowledgment_notes: 'Checked capacitor bank, seems okay now.',
      resolution_time: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2.8).toISOString(),
    },
  ],
};
