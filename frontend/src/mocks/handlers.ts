import { http, HttpResponse, delay } from 'msw';
import { mockActiveAlarms, mockAlarmRules, mockAlarmHistory } from './data';

// Add a small delay to simulate network latency
const ARTIFICIAL_DELAY_MS = 500;

export const handlers = [
  // GET active alarms
  http.get('/api/alarms/active/', async () => {
    await delay(ARTIFICIAL_DELAY_MS);
    return HttpResponse.json(mockActiveAlarms);
  }),

  // GET alarm rules
  http.get('/api/alarms/rules/', async () => {
    await delay(ARTIFICIAL_DELAY_MS);
    return HttpResponse.json(mockAlarmRules);
  }),

  // GET single alarm rule
  http.get('/api/alarms/rules/:id/', async ({ params }) => {
    await delay(ARTIFICIAL_DELAY_MS);
    const { id } = params;
    const rule = mockAlarmRules.results.find(r => r.id === Number(id));
    
    if (!rule) {
      return new HttpResponse(null, { status: 404 });
    }
    
    return HttpResponse.json(rule);
  }),

  // POST create new alarm rule
  http.post('/api/alarms/rules/', async ({ request }) => {
    await delay(ARTIFICIAL_DELAY_MS);
    const newRule = await request.json() as Record<string, any>;
    
    // Simulate creating a new rule with an ID
    const createdRule = {
      ...newRule,
      id: mockAlarmRules.results.length + 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    return HttpResponse.json(createdRule);
  }),

  // PUT update alarm rule
  http.put('/api/alarms/rules/:id/', async ({ request, params }) => {
    await delay(ARTIFICIAL_DELAY_MS);
    const { id } = params;
    const updatedRule = await request.json() as Record<string, any>;
    
    // Simulate updating a rule
    const existingRule = mockAlarmRules.results.find(r => r.id === Number(id));
    
    if (!existingRule) {
      return new HttpResponse(null, { status: 404 });
    }
    
    const updated = {
      ...existingRule,
      ...updatedRule,
      id: Number(id),
      updated_at: new Date().toISOString()
    };
    
    return HttpResponse.json(updated);
  }),

  // DELETE alarm rule
  http.delete('/api/alarms/rules/:id/', async ({ params }) => {
    await delay(ARTIFICIAL_DELAY_MS);
    const { id } = params;
    
    // Simulate deleting a rule
    const existingRule = mockAlarmRules.results.find(r => r.id === Number(id));
    
    if (!existingRule) {
      return new HttpResponse(null, { status: 404 });
    }
    
    return new HttpResponse(null, { status: 204 });
  }),

  // GET alarm history
  http.get('/api/alarms/history/', async () => {
    await delay(ARTIFICIAL_DELAY_MS);
    return HttpResponse.json(mockAlarmHistory);
  }),

  // GET email alert settings
  http.get('/api/notifications/email-alerts/settings/', async () => {
    await delay(ARTIFICIAL_DELAY_MS);
    return HttpResponse.json({
      preferences: [
        {
          id: 1,
          channel: {
            id: 1,
            name: "Email Notifications",
            type: "email"
          },
          category: "Alarms",
          category_display: "System Alarms",
          is_enabled: true,
          quiet_hours_start: null,
          quiet_hours_end: null,
          enabled_severities: ["critical", "high", "medium", "low"]
        },
        {
          id: 2,
          channel: {
            id: 1,
            name: "Email Notifications",
            type: "email"
          },
          category: "Reports",
          category_display: "System Reports",
          is_enabled: true,
          quiet_hours_start: null,
          quiet_hours_end: null,
          enabled_severities: null
        }
      ],
      channels: [
        {
          id: 1,
          name: "Email Notifications",
          type: "email",
          description: "Notifications sent via email"
        }
      ],
      alarm_subscriptions: [
        {
          id: 1,
          name: "Power Consumption Alert",
          category: "power",
          category_display: "Power",
          severity: "high",
          severity_display: "High",
          device: "Main Distribution Panel",
          parameter: "Active Power",
          condition: "Greater Than",
          threshold_value: 5000
        }
      ]
    });
  }),

  // POST update email preferences
  http.post('/api/notifications/email-alerts/update_preferences/', async ({ request }) => {
    await delay(ARTIFICIAL_DELAY_MS);
    const data = await request.json() as {
      id?: number;
      channel_id: number;
      category: string;
      is_enabled?: boolean;
      quiet_hours_start?: string | null;
      quiet_hours_end?: string | null;
      enabled_severities?: string[] | null;
    };
    
    return HttpResponse.json({
      id: data.id || 1,
      channel: {
        id: data.channel_id,
        name: "Email Notifications",
        type: "email"
      },
      category: data.category,
      category_display: data.category === "Alarms" ? "System Alarms" : "System Reports",
      is_enabled: data.is_enabled !== undefined ? data.is_enabled : true,
      quiet_hours_start: data.quiet_hours_start || null,
      quiet_hours_end: data.quiet_hours_end || null,
      enabled_severities: data.enabled_severities || ["critical", "high", "medium", "low"]
    });
  }),

  // POST test email
  http.post('/api/notifications/email-alerts/test_email/', async () => {
    await delay(ARTIFICIAL_DELAY_MS);
    return HttpResponse.json({
      message: "Test email sent successfully!"
    });
  }),

  // POST trigger scan
  http.post('/api/notifications/email-alerts/trigger_scan/', async ({ request }) => {
    await delay(ARTIFICIAL_DELAY_MS);
    const url = new URL(request.url);
    const dryRun = url.searchParams.get('dry_run') === 'true';
    
    if (dryRun) {
      return HttpResponse.json({
        dry_run: true,
        total_anomalies: 3,
        grouped_anomalies: 2,
        would_send: [
          {
            severity: "high",
            message: "Power consumption spike detected",
            recipients: ["<EMAIL>"],
            device: "Main Distribution Panel",
            parameter: "Active Power",
            value: 5500,
            timestamp: new Date().toISOString()
          }
        ]
      });
    } else {
      return HttpResponse.json({
        dry_run: false,
        total_anomalies: 3,
        grouped_anomalies: 2,
        alerts_sent: 1,
        alerts_suppressed: 1,
        errors: 0
      });
    }
  })
];
