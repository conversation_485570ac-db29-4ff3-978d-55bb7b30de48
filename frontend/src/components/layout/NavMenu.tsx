import React from 'react';
import { useLocation, Link } from 'react-router-dom';
import {
  LayoutDashboard,
  BarChart3,
  LineChart,
  Gauge,
  FileText,
  Settings,
  AlertTriangle,
  Network,
  Database
} from 'lucide-react';

// Navigation configuration
export interface NavigationItem {
  name: string;
  href: string;
  icon: React.ElementType;
  description: string;
  group?: string;
}

export const PRIMARY_NAVIGATION: NavigationItem[] = [
  {
    name: 'Overview',
    href: '/',
    icon: LayoutDashboard,
    description: 'Energy management dashboard',
    group: 'main'
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: BarChart3,
    description: 'Performance analytics and trends',
    group: 'main'
  },
  {
    name: 'Compare',
    href: '/compare',
    icon: LineChart,
    description: 'Compare energy metrics',
    group: 'main'
  },
  {
    name: 'Meters',
    href: '/meters',
    icon: Gauge,
    description: 'Smart meter management',
    group: 'main'
  },
  {
    name: 'Meter Diagram',
    href: '/meter-diagram',
    icon: Network,
    description: 'Interactive electricity meter diagrams',
    group: 'main'
  },
  {
    name: 'Alarms',
    href: '/alarms',
    icon: AlertTriangle,
    description: 'Monitor and manage system alarms',
    group: 'utility'
  },
  {
    name: 'Reports',
    href: '/reports',
    icon: FileText,
    description: 'Generate and view reports',
    group: 'utility'
  }
];

export interface NavMenuProps {
  orientation?: 'horizontal' | 'vertical';
  variant?: 'icon' | 'full' | 'compact';
  showDescriptions?: boolean;
  groups?: string[];
}

export function NavMenu({
  orientation = 'vertical',
  variant = 'icon',
  showDescriptions = false,
  groups = ['main', 'utility']
}: NavMenuProps) {
  const location = useLocation();
  const [hoveredItem, setHoveredItem] = React.useState<string | null>(null);

  // Group navigation items
  const groupedNavigation = React.useMemo(() => {
    return PRIMARY_NAVIGATION.reduce((acc, item) => {
      const group = item.group || 'main';
      if (!acc[group]) {
        acc[group] = [];
      }
      if (groups.includes(group)) {
        acc[group].push(item);
      }
      return acc;
    }, {} as Record<string, NavigationItem[]>);
  }, [groups]);

  return (
    <div className={`
      ${orientation === 'vertical' ? 'flex flex-col space-y-1' : 'flex space-x-1'}
    `}>
      {Object.entries(groupedNavigation).map(([group, items], groupIndex) => (
        <React.Fragment key={group}>
          {/* Add divider between groups */}
          {groupIndex > 0 && orientation === 'vertical' && (
            <div className="h-px bg-gray-200 my-2 mx-3"></div>
          )}

          {/* Group content */}
          {items.map((item) => (
            <NavMenuItem
              key={item.href}
              item={item}
              isActive={location.pathname === item.href}
              isHovered={hoveredItem === item.href}
              onMouseEnter={() => setHoveredItem(item.href)}
              onMouseLeave={() => setHoveredItem(null)}
              variant={variant}
              showDescription={showDescriptions}
            />
          ))}
        </React.Fragment>
      ))}
    </div>
  );
}

interface NavMenuItemProps {
  item: NavigationItem;
  isActive: boolean;
  isHovered: boolean;
  onMouseEnter: () => void;
  onMouseLeave: () => void;
  variant: 'icon' | 'full' | 'compact';
  showDescription: boolean;
}

export function NavMenuItem({
  item,
  isActive,
  isHovered,
  onMouseEnter,
  onMouseLeave,
  variant,
  showDescription
}: NavMenuItemProps) {
  const { href, icon: Icon, name, description } = item;

  return (
    <Link
      to={href}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      className={`
        relative flex items-center rounded-lg
        transition-all duration-200 group
        ${variant === 'icon' ? 'flex-col p-2 gap-1' : 'px-3 py-2 gap-2'}
        ${isActive
          ? 'bg-gradient-to-br from-blue-50 to-blue-100/50 text-primary-blue border border-blue-200/50 shadow-[0_2px_4px_rgba(14,125,228,0.08)]'
          : 'text-gray-400 hover:text-primary-blue hover:bg-blue-50/50'
        }
      `}
    >
      {/* Icon */}
      <div className={`
        ${variant === 'icon' ? 'p-1.5' : 'p-1'}
        rounded-md transition-all duration-200 relative
        ${isActive
          ? 'bg-white shadow-sm'
          : isHovered
            ? 'bg-gradient-to-br from-blue-50 to-blue-100/50 shadow-[0_2px_4px_rgba(14,125,228,0.1)]'
            : ''
        }
      `}>
        <Icon
          size={variant === 'icon' ? 16 : 14}
          className={`
            flex-shrink-0 transition-transform duration-200
            ${isHovered && !isActive ? 'scale-110' : ''}
          `}
        />
      </div>

      {/* Label - Always show for icon variant */}
      {(variant === 'icon' || variant === 'compact' || variant === 'full') && (
        <span className={`
          ${variant === 'icon' ? 'text-[10px] mt-1 text-center' : 'text-sm'}
          font-medium transition-all duration-200
          ${isActive ? 'text-primary-blue' : isHovered ? 'text-primary-blue scale-105' : 'text-gray-400'}
          ${variant === 'icon' ? 'leading-none' : 'flex-1'}
        `}>
          {name}
        </span>
      )}

      {/* Description (for full variant only) */}
      {variant === 'full' && showDescription && (
        <p className="text-xs text-gray-500 mt-0.5">{description}</p>
      )}

      {/* Tooltip */}
      {isHovered && !isActive && !showDescription && (
        <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded whitespace-nowrap z-10">
          {description}
        </div>
      )}

      {/* Active Indicator */}
      {isActive && (
        <div className="absolute -right-[2px] top-1/2 -translate-y-1/2 w-1 h-8 rounded-l-full bg-primary-blue opacity-50" />
      )}
    </Link>
  );
}