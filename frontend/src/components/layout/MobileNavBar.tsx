import React from 'react';
import { NavMenu } from './NavMenu';
import { Logo } from './Logo';

export function MobileNavBar() {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 lg:hidden z-20">
      <div className="flex justify-between items-center px-4 py-1">
        <NavMenu 
          orientation="horizontal" 
          variant="icon"
          showDescriptions={false}
          groups={['main']} // Only show main navigation items
        />
      </div>
    </div>
  );
}