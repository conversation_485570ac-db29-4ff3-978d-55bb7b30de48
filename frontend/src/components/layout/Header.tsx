import React from 'react';
import { Bell, Clock, User, Settings, LogOut, ChevronDown } from 'lucide-react';
import { useLocation, Link, useNavigate } from 'react-router-dom';
import { formatDate } from '../../lib/utils/formatters';
import { AutoRefresh } from '../common';

const PAGE_TITLES: Record<string, string> = {
  '/admin': 'The Stock Exchange of Thailand',
  '/': 'The Stock Exchange of Thailand',
  '/floor-usage': 'Tower Electricity Distribution',
  '/billing': 'Electricity Billing System',
  '/meters': 'Meter Management',
  '/meter-detail': 'Meter Details',
  '/analytics': 'Electricity Performance Analytics',
  '/compare': 'Meter Comparison',
  '/reports': 'Reports Center',
  '/settings': 'The Stock Exchange of Thailand',
  '/meter-diagram': 'Electricity Meter Diagram',
  '/alarms': 'Alarm Management',
  '/training': 'Training Center',
  '/training/overview': 'Training Center',
  '/training/analytics': 'Training Center',
  '/training/meters': 'Training Center',
  '/training/alarms': 'Training Center',
  '/training/comparison': 'Training Center',
  '/training/diagram': 'Training Center',
  '/training/settings': 'Training Center',
  '/training/quick-start': 'Training Center'
};

interface HeaderProps {
  notifications: number;
  isAutoRefresh?: boolean;
  onAutoRefreshToggle?: () => void;
  refreshInterval?: number;
}

function CurrentTime() {
  const [time, setTime] = React.useState(new Date());

  React.useEffect(() => {
    const timer = setInterval(() => setTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-gray-50/50 to-white backdrop-blur-sm rounded-lg border border-gray-100 hover:border-gray-200 transition-all duration-200">
      <Clock size={14} className="text-gray-400" />
      <div className="flex items-center gap-1.5">
        <span className="text-[11px] text-gray-500">{formatDate(time)}</span>
      </div>
    </div>
  );
}

export function Header({ notifications, isAutoRefresh, onAutoRefreshToggle, refreshInterval = 15000 }: HeaderProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const [showProfileMenu, setShowProfileMenu] = React.useState(false);
  const pageTitle = PAGE_TITLES[location.pathname] || 'Energy Management System';

  const handleClickOutside = React.useCallback((event: MouseEvent) => {
    const target = event.target as HTMLElement;
    if (!target.closest('.profile-menu') && !target.closest('.profile-button')) {
      setShowProfileMenu(false);
    }
  }, []);

  React.useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [handleClickOutside]);

  return (
    <header className="bg-white/80 backdrop-blur-md border-b border-gray-200/80 fixed top-0 right-0 left-0 z-50 h-12">
      <div className="px-2 md:px-3 lg:px-4 flex items-center justify-between h-full">
        <div className="flex items-center">
          {/* SET Logo */}
          <Link to="/" className="flex items-center mr-3">
            <img
              src="/images/logo.png"
              alt="SET Logo"
              className="h-6 w-auto mr-2"
            />
          </Link>
          <h1 className="text-lg lg:text-xl font-bold text-black">
            {pageTitle}
          </h1>
          <div className="h-6 w-px bg-gray-200/80 mx-4" />
        </div>

        <div className="flex items-center gap-3">
          <CurrentTime />

          {isAutoRefresh !== undefined && onAutoRefreshToggle && (
            <>
              <div className="hidden lg:block h-7 w-px bg-gray-200" />
              <AutoRefresh
                isEnabled={isAutoRefresh}
                onToggle={onAutoRefreshToggle}
                interval={refreshInterval}
              />
            </>
          )}

          <div className="flex items-center gap-2">
            <div className="relative">
              <button
                onClick={() => navigate('/alarms')}
                className="notification-button relative p-1.5 rounded-lg hover:bg-white/75 hover:shadow-sm transition-all duration-200 group bg-white/50"
              >
                <Bell size={16} className="text-gray-600 group-hover:text-primary-blue transition-colors lg:h-[18px] lg:w-[18px]" />
                {notifications > 0 && (
                  <div className="absolute -top-1 -right-1">
                    <div className="relative flex h-4 w-4">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-4 w-4 bg-red-500 justify-center items-center">
                        <span className="text-[9px] font-medium text-white">{notifications > 9 ? '9+' : notifications}</span>
                      </span>
                    </div>
                  </div>
                )}
              </button>
            </div>

            <div className="relative">
              <button 
                onClick={() => setShowProfileMenu(!showProfileMenu)}
                className="profile-button p-1.5 rounded-lg hover:bg-white/75 hover:shadow-sm transition-all duration-200 group bg-white/50 flex items-center gap-1"
              >
                <User size={16} className="text-gray-600 group-hover:text-primary-blue transition-colors lg:h-[18px] lg:w-[18px]" />
                <ChevronDown size={12} className={`text-gray-600 group-hover:text-primary-blue transition-all ${showProfileMenu ? 'rotate-180' : ''}`} />
              </button>
              
              {showProfileMenu && (
                <div className="profile-menu absolute right-0 top-10 mt-1 w-48 rounded-lg overflow-hidden shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                  <div className="py-1">
                    <div className="px-4 py-2 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-900">Admin User</p>
                      <p className="text-xs text-gray-500">{localStorage.getItem('userEmail') || '<EMAIL>'}</p>
                    </div>
                    <button
                      onClick={() => {
                        navigate('/settings');
                        setShowProfileMenu(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                    >
                      <Settings size={14} />
                      Settings
                    </button>
                    <button
                      onClick={() => {
                        localStorage.removeItem('isAuthenticated');
                        localStorage.removeItem('userEmail');
                        navigate('/login');
                        setShowProfileMenu(false);
                      }}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 border-t border-gray-100"
                    >
                      <LogOut size={14} />
                      Logout
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}