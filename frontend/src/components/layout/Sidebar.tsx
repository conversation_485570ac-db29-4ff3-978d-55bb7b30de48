import { type FC } from 'react';
import { NavMenu } from './NavMenu';
import { Link } from 'react-router-dom';

export const Sidebar: FC = () => {
  return (
    <aside className="
      bg-white
      border-b lg:border-b-0 lg:border-r border-[#EDEFF9]
      transition-all duration-300
      flex lg:flex-col
      h-12 lg:h-screen w-full lg:w-16
      overflow-visible
      fixed top-0 left-0
      z-40
    ">
      {/* Logo using PNG image */}
      <div className="hidden lg:flex items-center justify-center h-12 w-full border-r border-[#EDEFF9] bg-white">
        <Link to="/" className="flex items-center justify-center p-1">
          <img 
            src="/images/logo.png" 
            alt="SET Logo" 
            className="h-6 w-auto"
          />
        </Link>
      </div>
      
      {/* Navigation */}
      <nav className="
        flex flex-col flex-1
        p-1.5
        overflow-x-auto lg:overflow-x-visible
      ">
        <NavMenu 
          orientation="vertical"
          variant="icon"
          showDescriptions={false}
        />
      </nav>

    </aside>
  );
};