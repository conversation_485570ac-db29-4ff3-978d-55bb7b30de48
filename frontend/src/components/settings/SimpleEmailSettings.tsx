import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { AlertTriangle, Mail, CheckCircle, Server } from 'lucide-react';
import RecipientsSection from './RecipientsSection';

const SimpleEmailSettings: React.FC = () => {
  const [emailEnabled, setEmailEnabled] = useState(true);
  const [email, setEmail] = useState('<EMAIL>');

  return (
    <div className="space-y-6">
      {/* Critical Alarm Notifications Card */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="text-xl flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Critical Alarm Email Notifications
          </CardTitle>
          <CardDescription>
            Configure email notifications for critical alarms in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Enable/Disable Email Notifications */}
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="email-notifications" className="text-base font-medium">
                  Email Notifications for Critical Alarms
                </Label>
                <p className="text-sm text-gray-500 mt-1">
                  Receive email alerts when critical alarms are triggered
                </p>
              </div>
              <Switch
                id="email-notifications"
                checked={emailEnabled}
                onCheckedChange={() => setEmailEnabled(!emailEnabled)}
              />
            </div>

            {/* Email Address */}
            <div className="space-y-2">
              <Label htmlFor="email-address" className="text-base font-medium">
                Notification Email Address
              </Label>
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email-address"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10"
                    disabled={!emailEnabled}
                  />
                </div>
                <Button
                  variant="outline"
                  disabled={!emailEnabled || !email}
                >
                  <Mail className="mr-2 h-4 w-4" />
                  Send Test
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                This email address will receive notifications for all critical alarms
              </p>
            </div>

            {/* Information Card */}
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4 flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-blue-800">Email Notification Details</h4>
                <p className="text-sm text-blue-600 mt-1">
                  Critical alarm emails include detailed information about the alarm, including the meter name,
                  metric, trigger value, and timestamp. They also include a direct link to the alarm management page.
                </p>
              </div>
            </div>

            {/* Recipients Section */}
            <RecipientsSection className="pt-4 border-t border-gray-100" />
          </div>
        </CardContent>
      </Card>

      {/* Email Server Settings Card */}
      <Card className="w-full">
        <CardHeader className="py-4">
          <CardTitle className="text-lg flex items-center gap-2">
            <Server className="h-5 w-5 text-gray-500" />
            Email Server Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">SMTP Server:</span>
              <span className="ml-2 font-medium">smtp.alto-cero.com</span>
            </div>
            <div>
              <span className="text-gray-500">Port:</span>
              <span className="ml-2 font-medium">587</span>
            </div>
            <div>
              <span className="text-gray-500">Sender Email:</span>
              <span className="ml-2 font-medium"><EMAIL></span>
            </div>
            <div>
              <Button variant="outline" size="sm" className="text-xs">
                Edit Server Settings
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button className="bg-primary-blue hover:bg-primary-blue/90">
          Save Settings
        </Button>
      </div>
    </div>
  );
};

export default SimpleEmailSettings;
