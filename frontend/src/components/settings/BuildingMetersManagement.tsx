import React, { memo, useState, useCallback, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Building2, 
  Zap, 
  Wind, 
  Droplets, 
  ThermometerSun,
  Wrench,
  Factory,
  Building,
  Download,
  Filter,
  ChevronDown,
  ChevronRight,
  Activity,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Loader2,
  TreePine,
  Table
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  MAIN_METER_LIST, 
  getAllMainMetersFlat,
  TOWER_BUILDING_METERS,
  getAllTowerBuildingMeters,
  PODIUM_BUILDING_METERS,
  getAllPodiumBuildingMeters,
  CAR_PARK_BUILDING_METERS,
  getAllCarParkBuildingMeters
} from '@/lib/config/building/meters';

// Define the tab structure
const BUILDING_TABS = [
  { id: 'main', label: 'Main Meters', icon: Building2 },
  { id: 'tower', label: 'Tower Building', icon: Building },
  { id: 'podium', label: 'Podium Building', icon: Building },
  { id: 'carpark', label: 'Car Park Building', icon: Building }
] as const;

type TabId = typeof BUILDING_TABS[number]['id'];

// Define meter category icons
const CATEGORY_ICONS: Record<string, React.ComponentType<any>> = {
  'Interior Lighting': Zap,
  'Air side': Wind,
  'Office Equipment': Wrench,
  'Sanitary': Droplets,
  'Chiller': ThermometerSun,
  'Elevator & Escalator': Building2,
  'Data Center': Factory,
  'Other': AlertCircle,
  'lightPower': Zap,
  'airSide': Wind,
  'chillerPlant': ThermometerSun,
  'escalator_elevator': Building2,
  'tenant': Building,
  'data_center_others': Factory,
  'data_center_it': Factory
};

// Pre-calculate and cache meter statuses
const meterStatusCache = new Map<string, 'online' | 'offline' | 'warning'>();
const getMeterStatus = (meterId: string): 'online' | 'offline' | 'warning' => {
  if (meterStatusCache.has(meterId)) {
    return meterStatusCache.get(meterId)!;
  }
  const random = meterId.charCodeAt(0) + meterId.charCodeAt(1);
  let status: 'online' | 'offline' | 'warning' = 'online';
  if (random % 10 === 0) status = 'offline';
  else if (random % 7 === 0) status = 'warning';
  meterStatusCache.set(meterId, status);
  return status;
};

interface MeterGroup {
  name: string;
  category?: string;
  meters: Array<{
    id: string;
    name: string;
    type?: string;
    system?: string;
    status?: 'online' | 'offline' | 'warning';
  }>;
  isExpanded?: boolean;
}

// Loading skeleton component
const TableSkeleton = () => (
  <div className="animate-pulse">
    <div className="h-10 bg-gray-100 rounded mb-2"></div>
    {[...Array(5)].map((_, i) => (
      <div key={i} className="border-b border-gray-100 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-5 h-5 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-48"></div>
          </div>
          <div className="flex items-center gap-2">
            <div className="h-4 bg-gray-200 rounded w-24"></div>
            <div className="h-5 bg-gray-200 rounded-full w-16"></div>
          </div>
        </div>
      </div>
    ))}
  </div>
);

// Meter row component - memoized for performance
const MeterRow = memo(({ meter, isSelected, onToggle }: { 
  meter: any; 
  isSelected: boolean; 
  onToggle: () => void;
}) => {
  const StatusIcon = meter.status === 'online' ? CheckCircle2 : 
                     meter.status === 'offline' ? XCircle : 
                     AlertCircle;
  const statusColors = {
    online: 'text-green-600 bg-green-50',
    offline: 'text-red-600 bg-red-50',
    warning: 'text-yellow-600 bg-yellow-50'
  };

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap">
        <input
          type="checkbox"
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          checked={isSelected}
          onChange={onToggle}
        />
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {meter.id}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        {meter.name}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        {meter.type || meter.category || '-'}
      </td>
      <td className="px-6 py-4 whitespace-nowrap">
        <span className={cn(
          'inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium',
          statusColors[meter.status || 'offline']
        )}>
          <StatusIcon className="h-3 w-3" />
          {meter.status}
        </span>
      </td>
    </tr>
  );
});

const BuildingMetersManagement = memo(function BuildingMetersManagement() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<TabId>('main');
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const [selectedMeters, setSelectedMeters] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);
  const [statusFilter, setStatusFilter] = useState<'all' | 'online' | 'offline' | 'warning'>('all');
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [viewMode, setViewMode] = useState<'table' | 'tree'>('table');

  // Calculate tab totals for display
  const tabTotals = useMemo(() => ({
    main: getAllMainMetersFlat().length,
    tower: getAllTowerBuildingMeters().length,
    podium: getAllPodiumBuildingMeters().length,
    carpark: getAllCarParkBuildingMeters().length
  }), []);

  // Reset loading state and collapse groups when tab changes
  useEffect(() => {
    setIsInitialLoad(true);
    setExpandedGroups(new Set()); // Collapse all groups for performance
    const timer = setTimeout(() => setIsInitialLoad(false), 50);
    return () => clearTimeout(timer);
  }, [activeTab]);

  // Only process data for the active tab
  const processedData = useMemo(() => {
    if (isInitialLoad) {
      return null;
    }

    const data: { groups: MeterGroup[], totals: any } = { groups: [], totals: {} };

    // Only process the active tab data
    switch (activeTab) {
      case 'main': {
        const mainGroups: MeterGroup[] = Object.entries(MAIN_METER_LIST).map(([key, group]) => ({
          name: group.name,
          meters: group.meters.map(meter => ({
            ...meter,
            status: getMeterStatus(meter.id)
          }))
        }));
        
        data.groups = mainGroups;
        data.totals = {
          total: getAllMainMetersFlat().length,
          online: mainGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'online').length, 0),
          offline: mainGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'offline').length, 0),
          warning: mainGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'warning').length, 0)
        };
        break;
      }

      case 'tower': {
        const towerGroups: MeterGroup[] = [];
        Object.entries(TOWER_BUILDING_METERS).forEach(([floor, meters]) => {
          let floorName = floor.replace('floor', 'Floor ');
          if (floor === 'floorB') floorName = 'Floor B';
          if (floor === 'dataCenter') floorName = 'Data Center';
          if (floor === 'roof') floorName = 'Roof';
          
          towerGroups.push({
            name: floorName.charAt(0).toUpperCase() + floorName.slice(1),
            meters: meters.map(meter => ({
              ...meter,
              status: getMeterStatus(meter.id),
              category: meter.category
            })),
            category: 'floor'
          });
        });

        data.groups = towerGroups;
        data.totals = {
          total: getAllTowerBuildingMeters().length,
          online: towerGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'online').length, 0),
          offline: towerGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'offline').length, 0),
          warning: towerGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'warning').length, 0)
        };
        break;
      }

      case 'podium': {
        const podiumGroups: MeterGroup[] = Object.entries(PODIUM_BUILDING_METERS).map(([floor, meters]) => {
          const floorName = floor.replace('floor', 'Floor ').replace('Floor B', 'Floor B (Basement)');
          return {
            name: floorName.charAt(0).toUpperCase() + floorName.slice(1),
            meters: meters.map(meter => ({
              ...meter,
              status: getMeterStatus(meter.id)
            })),
            category: 'floor'
          };
        });

        data.groups = podiumGroups;
        data.totals = {
          total: getAllPodiumBuildingMeters().length,
          online: podiumGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'online').length, 0),
          offline: podiumGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'offline').length, 0),
          warning: podiumGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'warning').length, 0)
        };
        break;
      }

      case 'carpark': {
        const carParkGroups: MeterGroup[] = Object.entries(CAR_PARK_BUILDING_METERS).map(([floor, meters]) => {
          const floorName = floor.replace('floor', 'Floor ').replace('Floor B', 'Floor B (Basement)');
          return {
            name: floorName.charAt(0).toUpperCase() + floorName.slice(1),
            meters: meters.map(meter => ({
              ...meter,
              status: getMeterStatus(meter.id)
            })),
            category: 'floor'
          };
        });

        data.groups = carParkGroups;
        data.totals = {
          total: getAllCarParkBuildingMeters().length,
          online: carParkGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'online').length, 0),
          offline: carParkGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'offline').length, 0),
          warning: carParkGroups.reduce((sum, g) => sum + g.meters.filter(m => m.status === 'warning').length, 0)
        };
        break;
      }
    }

    return data;
  }, [activeTab, isInitialLoad]);

  // Filter data based on search and status
  const filteredData = useMemo(() => {
    if (!processedData) return { groups: [], totals: { total: 0, online: 0, offline: 0, warning: 0 } };
    
    const currentData = processedData;
    
    if (statusFilter === 'all') {
      return currentData;
    }

    const filtered = {
      groups: currentData.groups.map(group => ({
        ...group,
        meters: group.meters.filter(meter => {
          const matchesStatus = statusFilter === 'all' || meter.status === statusFilter;
          return matchesStatus;
        })
      })).filter(group => group.meters.length > 0),
      totals: currentData.totals
    };

    return filtered;
  }, [processedData, activeTab, statusFilter]);

  // Toggle group expansion
  const toggleGroup = useCallback((groupName: string) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupName)) {
        newSet.delete(groupName);
      } else {
        newSet.add(groupName);
      }
      return newSet;
    });
  }, []);

  // Toggle meter selection
  const toggleMeterSelection = useCallback((meterId: string) => {
    setSelectedMeters(prev => {
      const newSet = new Set(prev);
      if (newSet.has(meterId)) {
        newSet.delete(meterId);
      } else {
        newSet.add(meterId);
      }
      return newSet;
    });
  }, []);

  // Select all meters in a group
  const selectAllInGroup = useCallback((group: MeterGroup) => {
    setSelectedMeters(prev => {
      const newSet = new Set(prev);
      group.meters.forEach(meter => {
        newSet.add(meter.id);
      });
      return newSet;
    });
  }, []);

  // Export data
  const handleExport = useCallback(() => {
    const data = filteredData.groups.flatMap(group =>
      group.meters.map(meter => ({
        Building: BUILDING_TABS.find(t => t.id === activeTab)?.label,
        Group: group.name,
        'Meter ID': meter.id,
        'Meter Name': meter.name,
        Category: (meter as any).category || '',
        Type: meter.type || '',
        System: meter.system || '',
        Status: meter.status || ''
      }))
    );

    if (data.length === 0) {
      alert('No data to export');
      return;
    }

    const csv = [
      Object.keys(data[0]).join(','),
      ...data.map(row => Object.values(row).map(val => 
        typeof val === 'string' && val.includes(',') ? `"${val}"` : String(val)
      ).join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${activeTab}-meters-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
  }, [filteredData, activeTab]);

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Building Meters Management</h1>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={handleExport}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 px-6" aria-label="Tabs">
          {BUILDING_TABS.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            const total = tabTotals[tab.id];
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  'py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors',
                  isActive
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.label}</span>
                <span className={cn(
                  'ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium',
                  isActive ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                )}>
                  {total}
                </span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Controls */}
      <div className="bg-white px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-4">
            {/* Filter Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={cn(
                'inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium',
                showFilters
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
              )}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {statusFilter !== 'all' && (
                <span className="ml-2 py-0.5 px-1.5 bg-blue-100 text-blue-600 rounded text-xs">
                  1
                </span>
              )}
            </button>

            {/* View Mode Toggle */}
            <div className="inline-flex rounded-md shadow-sm" role="group">
              <button
                onClick={() => setViewMode('table')}
                className={cn(
                  'inline-flex items-center px-3 py-2 text-sm font-medium border rounded-l-md',
                  viewMode === 'table'
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                )}
              >
                <Table className="h-4 w-4 mr-2" />
                Table
              </button>
              <button
                onClick={() => navigate('/meters')}
                className={cn(
                  'inline-flex items-center px-3 py-2 text-sm font-medium border-t border-b border-r rounded-r-md',
                  viewMode === 'tree'
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                )}
              >
                <TreePine className="h-4 w-4 mr-2" />
                Tree
              </button>
            </div>
          </div>

          {/* Status Summary */}
          <div className="flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-500" />
              <span className="text-gray-600">Online:</span>
              <span className="font-medium text-gray-900">{processedData?.totals?.online || 0}</span>
            </div>
            <div className="flex items-center gap-2">
              <XCircle className="h-4 w-4 text-red-500" />
              <span className="text-gray-600">Offline:</span>
              <span className="font-medium text-gray-900">{processedData?.totals?.offline || 0}</span>
            </div>
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-yellow-500" />
              <span className="text-gray-600">Warning:</span>
              <span className="font-medium text-gray-900">{processedData?.totals?.warning || 0}</span>
            </div>
          </div>
        </div>

        {/* Tower Building Category Summary */}
        {activeTab === 'tower' && (
          <div className="mt-4 grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3 text-sm">
            {Object.entries(
              getAllTowerBuildingMeters().reduce((acc, meter) => {
                acc[meter.category] = (acc[meter.category] || 0) + 1;
                return acc;
              }, {} as Record<string, number>)
            ).map(([category, count]) => (
              <div key={category} className="bg-gray-100 rounded-md px-3 py-2">
                <div className="text-xs text-gray-500">{category}</div>
                <div className="font-medium text-gray-900">{count} meters</div>
              </div>
            ))}
          </div>
        )}

        {/* Filter Options */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-700">Status:</span>
              <div className="flex items-center gap-2">
                {(['all', 'online', 'offline', 'warning'] as const).map((status) => (
                  <button
                    key={status}
                    onClick={() => setStatusFilter(status)}
                    className={cn(
                      'px-3 py-1 rounded-md text-sm font-medium',
                      statusFilter === status
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-white text-gray-600 hover:bg-gray-100'
                    )}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto p-6">
        {isInitialLoad || !processedData ? (
          <TableSkeleton />
        ) : (
          <div className="space-y-4">
            {filteredData.groups.map((group) => {
            const isExpanded = expandedGroups.has(group.name);
            const Icon = group.category && CATEGORY_ICONS[group.category] || Building2;
            
            return (
              <div key={group.name} className="bg-white rounded-lg shadow-sm border border-gray-200">
                {/* Group Header */}
                <div 
                  className="px-4 py-3 flex items-center justify-between cursor-pointer hover:bg-gray-50"
                  onClick={() => toggleGroup(group.name)}
                >
                  <div className="flex items-center gap-3 flex-1">
                    <button className="p-1">
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4 text-gray-400" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                    <Icon className="h-5 w-5 text-gray-400" />
                    <h3 className="text-sm font-medium text-gray-900">{group.name}</h3>
                    <span className="text-sm text-gray-500">({group.meters.length} meters)</span>
                    {/* Show category breakdown for Tower Building */}
                    {activeTab === 'tower' && (
                      <div className="flex items-center gap-3 ml-4 text-xs text-gray-500">
                        {Object.entries(
                          group.meters.reduce((acc, meter) => {
                            const cat = (meter as any).category || 'Other';
                            acc[cat] = (acc[cat] || 0) + 1;
                            return acc;
                          }, {} as Record<string, number>)
                        ).slice(0, 3).map(([cat, count]) => (
                          <span key={cat} className="flex items-center gap-1">
                            <span>{cat}:</span>
                            <span className="font-medium">{count}</span>
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        selectAllInGroup(group);
                      }}
                      className="text-xs text-blue-600 hover:text-blue-700"
                    >
                      Select All
                    </button>
                  </div>
                </div>

                {/* Group Content */}
                {isExpanded && (
                  <div className="border-t border-gray-200">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input
                              type="checkbox"
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              checked={group.meters.every(m => selectedMeters.has(m.id))}
                              onChange={() => selectAllInGroup(group)}
                            />
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Meter ID
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Name
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Type
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            System
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {group.meters.slice(0, 20).map((meter, index) => {
                          const TypeIcon = CATEGORY_ICONS[meter.type || ''] || Activity;
                          
                          return (
                            <tr key={meter.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <input
                                  type="checkbox"
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                  checked={selectedMeters.has(meter.id)}
                                  onChange={() => toggleMeterSelection(meter.id)}
                                />
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {meter.id}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {meter.name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div className="flex items-center gap-2">
                                  <TypeIcon className="h-4 w-4 text-gray-400" />
                                  <span>{meter.type || 'N/A'}</span>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {meter.system || 'N/A'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={cn(
                                  'inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium',
                                  meter.status === 'online' && 'bg-green-100 text-green-700',
                                  meter.status === 'offline' && 'bg-red-100 text-red-700',
                                  meter.status === 'warning' && 'bg-yellow-100 text-yellow-700'
                                )}>
                                  {meter.status === 'online' && <CheckCircle2 className="h-3 w-3" />}
                                  {meter.status === 'offline' && <XCircle className="h-3 w-3" />}
                                  {meter.status === 'warning' && <AlertCircle className="h-3 w-3" />}
                                  {meter.status?.charAt(0).toUpperCase() + meter.status?.slice(1)}
                                </span>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            );
          })}
          </div>
        )}
      </div>
    </div>
  );
});

export default BuildingMetersManagement;