import React from 'react';

interface CustomBlueToggleProps {
  checked: boolean;
  onChange: () => void;
  id?: string;
}

const CustomBlueToggle: React.FC<CustomBlueToggleProps> = ({ checked, onChange, id }) => {
  return (
    <div className="inline-block">
      <div 
        role="switch"
        aria-checked={checked}
        tabIndex={0}
        onClick={onChange}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onChange();
          }
        }}
        id={id}
        className={`
          relative inline-block w-16 h-8 rounded-full cursor-pointer
          transition-all duration-300 ease-in-out
          ${checked 
            ? 'bg-blue-600 border-2 border-blue-700' 
            : 'bg-gray-300 border-2 border-transparent'}
          focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2
        `}
      >
        <span 
          className={`
            absolute top-1/2 -translate-y-1/2 w-6 h-6 rounded-full
            transition-all duration-300 ease-in-out
            ${checked 
              ? 'left-9 bg-blue-100 border-2 border-blue-600' 
              : 'left-1 bg-white border-2 border-transparent'}
            shadow-md
          `}
        />
      </div>
    </div>
  );
};

export default CustomBlueToggle;
