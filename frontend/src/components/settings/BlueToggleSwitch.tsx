import React from 'react';

interface BlueToggleSwitchProps {
  checked: boolean;
  onChange: () => void;
  id?: string;
}

const BlueToggleSwitch: React.FC<BlueToggleSwitchProps> = ({ checked, onChange, id }) => {
  return (
    <div className="relative inline-block w-16 h-8 select-none">
      <input
        type="checkbox"
        id={id}
        checked={checked}
        onChange={onChange}
        className="opacity-0 w-0 h-0"
        style={{ position: 'absolute' }}
      />
      <label
        htmlFor={id}
        className={`
          absolute cursor-pointer top-0 left-0 right-0 bottom-0 rounded-full 
          transition-all duration-300 shadow-md border-2
          ${checked 
            ? 'bg-blue-600 border-blue-700' 
            : 'bg-gray-200 border-gray-300'}
        `}
      >
        <span 
          className={`
            absolute h-7 w-7 rounded-full bg-white border-2
            transition-all duration-300 shadow-md
            ${checked 
              ? 'left-8 border-blue-600 bg-blue-100' 
              : 'left-0 border-gray-300'}
          `}
        />
      </label>
    </div>
  );
};

export default BlueToggleSwitch;
