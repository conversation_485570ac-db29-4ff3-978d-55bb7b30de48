import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/Button';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Loader2, Mail, AlertTriangle, CheckCircle } from 'lucide-react';
import { useToast } from '@/components/ui/ToastProvider';
import { emailApi } from '@/lib/api/emailClient';

// Mock data for user preferences
const mockUserPreferences = {
  email: '<EMAIL>',
  criticalAlarmsEmailEnabled: true
};

const CriticalAlarmEmailSettings: React.FC = () => {
  const [emailEnabled, setEmailEnabled] = useState(mockUserPreferences.criticalAlarmsEmailEnabled);
  const [email, setEmail] = useState(mockUserPreferences.email);
  const [isSaving, setIsSaving] = useState(false);
  const [isSendingTest, setIsSendingTest] = useState(false);
  const { showToast } = useToast();

  // Handle toggle for email notifications
  const handleToggleEmail = () => {
    setEmailEnabled(!emailEnabled);
  };

  // Handle email input change
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  // Save settings
  const handleSaveSettings = async () => {
    setIsSaving(true);

    try {
      // In a real implementation, this would call an API to save the settings
      // For now, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update mock preferences
      mockUserPreferences.email = email;
      mockUserPreferences.criticalAlarmsEmailEnabled = emailEnabled;

      showToast({
        message: 'Email notification settings saved successfully',
        type: 'success',
        duration: 3000
      });
    } catch (error) {
      console.error('Error saving email settings:', error);
      showToast({
        message: 'Failed to save email notification settings',
        type: 'error',
        duration: 5000
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Send test email
  const handleSendTestEmail = async () => {
    setIsSendingTest(true);

    try {
      // Create a formatted timestamp
      const timestamp = new Date().toLocaleString();

      // Create the email subject
      const subject = 'TEST: Critical Alarm Notification';

      // Create the HTML email body with styling
      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="background-color: #f44336; color: white; padding: 15px; border-radius: 5px 5px 0 0; margin-bottom: 20px;">
            <h1 style="margin: 0; font-size: 24px;">Test Critical Alarm Notification</h1>
          </div>

          <div style="padding: 0 15px;">
            <p style="font-size: 16px; color: #333;">This is a test email to verify your critical alarm notification settings:</p>

            <div style="background-color: #f9f9f9; border-left: 4px solid #f44336; padding: 15px; margin: 15px 0;">
              <h2 style="margin-top: 0; color: #d32f2f;">Test Critical Voltage Alarm</h2>
              <p style="margin: 5px 0;"><strong>Meter:</strong> Test Meter</p>
              <p style="margin: 5px 0;"><strong>Metric:</strong> voltage</p>
              <p style="margin: 5px 0;"><strong>Trigger Value:</strong> 265 V</p>
              <p style="margin: 5px 0;"><strong>Threshold:</strong> 250 V</p>
              <p style="margin: 5px 0;"><strong>Time:</strong> ${timestamp}</p>
            </div>

            <p style="font-size: 16px; color: #333;">This is a test message. No action is required.</p>

            <div style="margin: 25px 0; text-align: center;">
              <a href="${window.location.origin}/alarms" style="background-color: #2196f3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
                View Alarm Management
              </a>
            </div>

            <p style="color: #666; font-size: 14px; margin-top: 30px; border-top: 1px solid #eee; padding-top: 15px;">
              This is a test message from the Alto CERO Energy Management System.
              Please do not reply to this email.
            </p>
          </div>
        </div>
      `;

      // Send test email
      const response = await emailApi.sendEmail(email, subject, html);

      showToast({
        message: 'Test email sent successfully. Please check your inbox.',
        type: 'success',
        duration: 5000
      });
    } catch (error) {
      console.error('Error sending test email:', error);
      showToast({
        message: 'Failed to send test email. Please check your email settings.',
        type: 'error',
        duration: 5000
      });
    } finally {
      setIsSendingTest(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-red-500" />
          Critical Alarm Email Notifications
        </CardTitle>
        <CardDescription>
          Configure email notifications for critical alarms in the system
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Enable/Disable Email Notifications */}
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="email-notifications" className="text-base font-medium">
                Email Notifications for Critical Alarms
              </Label>
              <p className="text-sm text-gray-500 mt-1">
                Receive email alerts when critical alarms are triggered
              </p>
            </div>
            <Switch
              id="email-notifications"
              checked={emailEnabled}
              onCheckedChange={handleToggleEmail}
            />
          </div>

          {/* Email Address */}
          <div className="space-y-2">
            <Label htmlFor="email-address" className="text-base font-medium">
              Notification Email Address
            </Label>
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email-address"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={handleEmailChange}
                  className="pl-10"
                  disabled={!emailEnabled}
                />
              </div>
              <Button
                variant="outline"
                onClick={handleSendTestEmail}
                disabled={!emailEnabled || isSendingTest || !email}
              >
                {isSendingTest ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Mail className="mr-2 h-4 w-4" />
                    Send Test
                  </>
                )}
              </Button>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              This email address will receive notifications for all critical alarms
            </p>
          </div>

          {/* Information Card */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4 flex items-start gap-3">
            <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-blue-800">Email Notification Details</h4>
              <p className="text-sm text-blue-600 mt-1">
                Critical alarm emails include detailed information about the alarm, including the meter name,
                metric, trigger value, and timestamp. They also include a direct link to the alarm management page.
              </p>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button
              onClick={handleSaveSettings}
              disabled={isSaving}
              className="bg-primary-blue hover:bg-primary-blue/90"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Settings'
              )}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CriticalAlarmEmailSettings;
