import React from 'react';

interface SimpleToggleProps {
  checked: boolean;
  onChange: () => void;
  id?: string;
}

const SimpleToggle: React.FC<SimpleToggleProps> = ({ checked, onChange, id }) => {
  return (
    <div className="inline-block">
      <div 
        onClick={onChange}
        className={`
          w-16 h-8 flex items-center rounded-full p-1 cursor-pointer
          ${checked ? 'bg-blue-600' : 'bg-gray-300'}
        `}
      >
        <div 
          className={`
            bg-white w-6 h-6 rounded-full shadow-md transform duration-300 ease-in-out
            ${checked ? 'transform translate-x-8' : ''}
          `}
        ></div>
      </div>
    </div>
  );
};

export default SimpleToggle;
