import { useState } from 'react';
import { Users, Shield, Building, Wrench, Eye, Edit, Check, X, ArrowLeft, UserPlus, EyeOff } from 'lucide-react';
import { QuickActionsDropdown } from '../../components/common/QuickActionsDropdown';
import type { UserRole } from '../../types';
import { PermissionMatrix } from './PermissionMatrix';

const USER_ROLES: Record<UserRole, {
  label: string;
  description: string;
  permissions: string[];
  restrictions: string[];
  canManage: UserRole[];
}> = {
  system_admin: {
    label: 'System Administrator',
    description: 'Full system access with ability to manage all users and settings',
    permissions: [
      'Manage all users and roles',
      'Configure system settings',
      'Access all buildings and floors',
      'Generate and download reports',
      'Configure alerts and notifications',
      'View all building data',
      'View energy consumption analytics',
      'Respond to alerts',
      'Access equipment information',
      'View reports',
      'Receive notifications'
    ],
    restrictions: [],
    canManage: ['building_manager', 'facility_operator', 'tenant_admin', 'basic_user']
  },
  building_manager: {
    label: 'Facilities Manager (FM)',
    description: 'Overall management of building operations and tenant relationships',
    permissions: [
      'View all building data',
      'Generate and download reports',
      'View energy consumption analytics',
      'Access equipment information',
      'View reports',
      'Receive notifications',
      'Respond to alerts'
    ],
    restrictions: [
      'Cannot access system administration settings',
      'Cannot modify system-wide configurations',
      'Cannot manage other facilities managers',
      'Cannot access financial management system',
      'Cannot configure equipment settings',
      'Cannot modify system settings'
    ],
    canManage: ['facility_operator', 'tenant_admin', 'basic_user']
  },
  facility_operator: {
    label: 'Building Engineer',
    description: 'Technical implementation and monitoring of building systems',
    permissions: [
      'Respond to alerts',
      'View energy consumption analytics',
      'Access equipment information'
    ],
    restrictions: [
      'Cannot modify user permissions',
      'Cannot access billing information',
      'Cannot modify system configurations',
      'Cannot manage other engineers',
      'Cannot access sensitive tenant data'
    ],
    canManage: ['basic_user']
  },
  tenant_admin: {
    label: 'Maintenance Technician',
    description: 'Day-to-day operations and routine maintenance tasks',
    permissions: [
      'View energy consumption analytics',
      'Respond to alerts',
      'Access equipment information',
      'View reports'
    ],
    restrictions: [
      'Cannot access other tenants\' data',
      'Cannot modify building settings',
      'Cannot access system configurations',
      'Cannot manage users',
      'Cannot access maintenance systems'
    ],
    canManage: ['basic_user']
  },
  basic_user: {
    label: 'Guest Viewer',
    description: 'Limited access to view public building data',
    permissions: [
      'View reports',
      'Receive notifications',
      'View energy consumption analytics'
    ],
    restrictions: [
      'Cannot access admin functions',
      'Cannot modify system settings',
      'Cannot manage other users'
    ],
    canManage: []
  }
};

const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    firstName: 'Somchai',
    lastName: 'Suksai',
    role: 'system_admin',
    status: 'active',
    lastLogin: '2024-03-20T15:30:00Z',
    createdAt: '2023-10-01T00:00:00Z',
  },
  {
    id: '2',
    email: '<EMAIL>',
    firstName: 'Malee',
    lastName: 'Raksa',
    role: 'building_manager',
    status: 'active',
    lastLogin: '2024-03-19T22:45:00Z',
    createdAt: '2023-11-15T00:00:00Z',
  },
  {
    id: '3',
    email: '<EMAIL>',
    firstName: 'Sombat',
    lastName: 'Thongdee',
    role: 'building_manager',
    status: 'active',
    lastLogin: '2024-03-20T16:15:00Z',
    createdAt: '2023-12-01T00:00:00Z',
  },
  {
    id: '4',
    email: '<EMAIL>',
    firstName: 'Thana',
    lastName: 'Wongsa',
    role: 'facility_operator',
    status: 'active',
    lastLogin: '2024-03-20T14:00:00Z',
    createdAt: '2024-01-05T00:00:00Z',
  },
  {
    id: '5',
    email: '<EMAIL>',
    firstName: 'Pranee',
    lastName: 'Lapsri',
    role: 'facility_operator',
    status: 'inactive',
    lastLogin: '2024-03-15T11:20:00Z',
    createdAt: '2024-01-10T00:00:00Z',
  },
  {
    id: '6',
    email: '<EMAIL>',
    firstName: 'Supaporn',
    lastName: 'Panya',
    role: 'tenant_admin',
    status: 'active',
    lastLogin: '2024-03-20T10:45:00Z',
    createdAt: '2024-02-01T00:00:00Z',
  },
  {
    id: '7',
    email: '<EMAIL>',
    firstName: 'Nattapong',
    lastName: 'Chinda',
    role: 'tenant_admin',
    status: 'active',
    lastLogin: '2024-03-19T16:30:00Z',
    createdAt: '2024-02-05T00:00:00Z',
  },
  {
    id: '8',
    email: '<EMAIL>',
    firstName: 'Ratana',
    lastName: 'Sae-tang',
    role: 'basic_user',
    status: 'active',
    lastLogin: '2024-03-20T11:20:00Z',
    createdAt: '2024-02-10T00:00:00Z',
  },
  {
    id: '9',
    email: '<EMAIL>',
    firstName: 'Pichai',
    lastName: 'Kongkham',
    role: 'basic_user',
    status: 'active',
    lastLogin: '2024-03-20T08:45:00Z',
    createdAt: '2024-02-15T00:00:00Z',
  },
  {
    id: '10',
    email: '<EMAIL>',
    firstName: 'Sunee',
    lastName: 'Meesuk',
    role: 'basic_user',
    status: 'inactive',
    lastLogin: '2024-03-10T09:30:00Z',
    createdAt: '2024-02-20T00:00:00Z',
  }
];

export function UserManagement() {
  const [activeTab, setActiveTab] = useState<'users' | 'roles'>('users');
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [showInactive, setShowInactive] = useState(false);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showEditUserModal, setShowEditUserModal] = useState(false);
  const [editingUserId, setEditingUserId] = useState<string | null>(null);

  // User form state
  const [form, setForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    role: 'system_admin',
    status: 'active',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [formTouched, setFormTouched] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  // Reset password state is already declared above

  const userRolesList = Object.keys(USER_ROLES);

  function validateEmail(email: string) {
    return /^\S+@\S+\.\S+$/.test(email);
  }
  const isFormValid =
    form.firstName.trim() &&
    form.lastName.trim() &&
    validateEmail(form.email) &&
    form.role &&
    (isEditMode ? true : form.password.length >= 6); // Password only required for new users

  function handleInputChange(e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    setFormTouched(true);
  }
  function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!isFormValid) return;

    if (isEditMode) {
      // For now, just log the edited user data
      // TODO: connect to backend/API
      // eslint-disable-next-line no-console
      console.log('Updating user:', form, 'User ID:', editingUserId);
      setShowEditUserModal(false);
    } else {
      // For now, just log the new user data
      // TODO: connect to backend/API
      // eslint-disable-next-line no-console
      console.log('Creating user:', form);
      setShowAddUserModal(false);
    }

    // Reset form state
    setForm({ firstName: '', lastName: '', email: '', role: 'system_admin', status: 'active', password: '' });
    setFormTouched(false);
    setIsEditMode(false);
    setEditingUserId(null);
  }

  function handleEditUser(userId: string) {
    const userToEdit = mockUsers.find(user => user.id === userId);
    if (!userToEdit) return;

    setForm({
      firstName: userToEdit.firstName,
      lastName: userToEdit.lastName,
      email: userToEdit.email,
      role: userToEdit.role,
      status: userToEdit.status,
      password: '', // Don't populate password for security reasons
    });

    setEditingUserId(userId);
    setIsEditMode(true);
    setShowEditUserModal(true);
    setFormTouched(false); // Don't show validation initially
  }


  const handleRoleClick = (role: UserRole) => {
    setSelectedRole(role);
  };

  // Get the icon for a role
  const getRoleIcon = (role: UserRole) => {
    switch(role) {
      case 'system_admin': return <Shield className="text-blue-600" size={20} />;
      case 'building_manager': return <Building className="text-amber-600" size={20} />;
      case 'facility_operator': return <Building className="text-green-600" size={20} />;
      case 'tenant_admin': return <Wrench className="text-purple-600" size={20} />;
      case 'basic_user': return <Eye className="text-gray-600" size={20} />;
      default: return <Users className="text-gray-600" size={20} />;
    }
  };

  // Get avatar background color based on role
  const getAvatarColors = (role: UserRole, name: string) => {
    // Create a simple hash from the name for color variation within role
    const nameHash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % 3;

    // Base colors by role with variations
    const colors = {
      system_admin: [
        'bg-blue-100 text-blue-800 border-blue-200',
        'bg-blue-50 text-blue-700 border-blue-200',
        'bg-indigo-50 text-indigo-700 border-indigo-200'
      ],
      building_manager: [
        'bg-amber-50 text-amber-800 border-amber-200',
        'bg-yellow-50 text-yellow-700 border-yellow-200',
        'bg-orange-50 text-orange-700 border-orange-200'
      ],
      facility_operator: [
        'bg-green-50 text-green-800 border-green-200',
        'bg-emerald-50 text-emerald-700 border-emerald-200',
        'bg-teal-50 text-teal-700 border-teal-200'
      ],
      tenant_admin: [
        'bg-purple-50 text-purple-800 border-purple-200',
        'bg-fuchsia-50 text-fuchsia-700 border-fuchsia-200',
        'bg-violet-50 text-violet-700 border-violet-200'
      ],
      basic_user: [
        'bg-gray-50 text-gray-800 border-gray-200',
        'bg-slate-50 text-slate-700 border-slate-200',
        'bg-zinc-50 text-zinc-700 border-zinc-200'
      ]
    };

    return colors[role]?.[nameHash] || 'bg-gray-100 text-gray-800 border-gray-200';
  };

  // Card component for role
  const RoleCard = ({ roleKey, roleInfo }: { roleKey: UserRole, roleInfo: typeof USER_ROLES[UserRole] }) => (
    <div
      className={`bg-white rounded-xl p-4 shadow-sm border border-[#EDEFF9] hover:border-blue-200 hover:shadow-md transition-all cursor-pointer flex flex-col min-w-[260px] max-w-[320px] ${selectedRole === roleKey ? 'border-blue-500 ring-1 ring-blue-500' : ''}`}
      onClick={() => handleRoleClick(roleKey)}
    >
      <div className="flex items-center gap-2 mb-1">
        {getRoleIcon(roleKey)}
        <h3 className="text-base font-medium text-gray-800">{roleInfo.label}</h3>
      </div>
      <p className="text-xs text-gray-600 mb-2">{roleInfo.description}</p>
    </div>
  );


  return (
    <>
      {/* Tabs */}
      <div className="border-b border-gray-200 mb-4">
        <div className="flex space-x-8">
          <button
            className={`py-2 px-1 inline-flex items-center gap-2 border-b-2 font-medium text-sm ${
              activeTab === 'users'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('users')}
          >
            <Users size={18} />
            Users
          </button>
          <button
            className={`py-2 px-1 inline-flex items-center gap-2 border-b-2 font-medium text-sm ${
              activeTab === 'roles'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('roles')}
          >
            <Shield size={18} />
            Roles & Permissions
          </button>
        </div>
      </div>

      {/* Tab content */}
      {activeTab === 'users' ? (
        <>
          <div className="flex justify-end mb-2">
          </div>

          <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden mb-6">
            <div className="overflow-x-auto p-4">
              <table className="min-w-full border-collapse">
                <thead>
                  <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                    <th className="px-4 py-3.5 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Name</th>
                    <th className="px-4 py-3.5 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Role</th>
                    <th className="px-4 py-3.5 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Status</th>
                    <th className="px-4 py-3.5 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100 bg-white">
                {mockUsers
                  .filter(user => showInactive || user.status === 'active')
                  .map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50/80 transition-colors duration-150 ease-in-out">
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className={`flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center font-medium text-sm shadow-md ${getAvatarColors(user.role as UserRole, user.firstName + user.lastName)}`}>
                            {user.firstName[0]}{user.lastName[0]}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{user.firstName} {user.lastName}</div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700 border border-blue-100 shadow-sm">
                          {USER_ROLES[user.role as UserRole]?.label}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium shadow-sm border ${user.status === 'active' ? 'bg-green-50 text-green-700 border-green-100' : 'bg-red-50 text-red-700 border-red-100'}`}>
                          {user.status === 'active' ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <QuickActionsDropdown
                          onEdit={() => handleEditUser(user.id)}
                          onDelete={() => {/* TODO: handle delete */}}
                          onDeactivate={() => {/* TODO: handle deactivate */}}
                        />
                      </td>
                    </tr>
                  ))}
              </tbody>
              </table>
            </div>
          </div>
          <div className="flex justify-start mt-4 ml-4">
            <div className="flex items-center">
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={showInactive}
                  onChange={() => setShowInactive(v => !v)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:translate-x-[-100%] peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
              <span className="ms-3 text-sm font-medium text-gray-700">Show Inactive</span>
            </div>
          </div>
        </>
      ) : (
        <>
          {selectedRole ? (
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <div className="flex items-center gap-3 mb-3">
                {getRoleIcon(selectedRole)}
                <h3 className="text-lg font-semibold">{USER_ROLES[selectedRole].label}</h3>
              </div>
              <p className="text-gray-600 mb-4">{USER_ROLES[selectedRole].description}</p>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Permissions</h4>
                  <ul className="space-y-1">
                    {USER_ROLES[selectedRole].permissions.map(permission => (
                      <li key={permission} className="flex items-center text-sm text-gray-600">
                        <Check className="text-green-500 mr-2" size={16} />
                        {permission}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-gray-700 mb-2">Restrictions</h4>
                  <ul className="space-y-1">
                    {USER_ROLES[selectedRole].restrictions.map(restriction => (
                      <li key={restriction} className="flex items-center text-sm text-gray-600">
                        <X className="text-red-500 mr-2" size={16} />
                        {restriction}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <button
                className="text-blue-600 text-sm flex items-center gap-1 hover:underline"
                onClick={() => setSelectedRole(null)}
              >
                <ArrowLeft size={14} />
                Back to all roles
              </button>
            </div>
          ) : (
            <>
              <div className="flex flex-nowrap gap-4 mb-8 overflow-x-auto pb-2">
                {Object.entries(USER_ROLES).map(([roleKey, roleInfo]) => (
                  <RoleCard
                    key={roleKey}
                    roleKey={roleKey as UserRole}
                    roleInfo={roleInfo}
                  />
                ))}
              </div>

              <div className="mt-8">
                <PermissionMatrix roles={USER_ROLES} onRoleClick={handleRoleClick} />
              </div>
            </>
          )}
        </>
      )}
      <div className="fixed bottom-8 right-8 z-20 group">
        <button
          className="bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-xl border border-blue-300 w-14 h-14 flex items-center justify-center text-3xl transition-all focus:outline-none focus:ring-2 focus:ring-blue-400"
          title="Add User"
          aria-label="Add User"
          onClick={() => setShowAddUserModal(true)}
        >
          <UserPlus size={32} />
        </button>
        {/* Floating tooltip label, only on hover/focus (desktop) */}
        <span className="absolute bottom-16 right-0 opacity-0 group-hover:opacity-100 group-focus-within:opacity-100 transition-opacity bg-gray-900 text-white font-semibold px-4 py-2 rounded shadow-lg text-sm whitespace-nowrap pointer-events-none select-none">
          Add User
        </span>
      </div>
      {/* Add User Modal */}
      {showAddUserModal && (
        <div className="fixed inset-0 z-[100] flex items-center justify-center bg-black/60 backdrop-blur-sm">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full relative overflow-hidden border border-gray-100">
            {/* Header with enhanced style */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2.5">
                <div className="bg-blue-50 p-1.5 rounded-md">
                  <UserPlus size={20} className="text-blue-600" />
                </div>
                Add User
              </h2>
              <button
                className="text-gray-400 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/20 rounded-full p-2 transition-all hover:bg-gray-100"
                onClick={() => setShowAddUserModal(false)}
                aria-label="Close"
              >
                <X size={20} />
              </button>
            </div>
            <div className="p-6">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="flex gap-3">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">First Name<span className="text-red-500">*</span></label>
                  <input
                    type="text"
                    name="firstName"
                    value={form.firstName}
                    onChange={handleInputChange}
                    placeholder="John"
                    className={`w-full border rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all ${formTouched && !form.firstName.trim() ? 'border-red-400' : formTouched && form.firstName.trim() ? 'border-green-400' : 'border-gray-300'}`}
                    required
                  />
                  {formTouched && !form.firstName.trim() && (
                    <span className="text-xs text-red-500 flex items-center gap-1 mt-1.5"><X size={12} className="text-red-400" /> Required</span>
                  )}
                  {formTouched && form.firstName.trim() && (
                    <span className="text-xs text-green-600 flex items-center gap-1 mt-1.5"><Check size={12} className="text-green-500" /> Looks good</span>
                  )}
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Last Name<span className="text-red-500">*</span></label>
                  <input
                    type="text"
                    name="lastName"
                    value={form.lastName}
                    onChange={handleInputChange}
                    placeholder="Doe"
                    className={`w-full border rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all ${formTouched && !form.lastName.trim() ? 'border-red-400' : formTouched && form.lastName.trim() ? 'border-green-400' : 'border-gray-300'}`}
                    required
                  />
                  {formTouched && !form.lastName.trim() && (
                    <span className="text-xs text-red-500 flex items-center gap-1 mt-1.5"><X size={12} className="text-red-400" /> Required</span>
                  )}
                  {formTouched && form.lastName.trim() && (
                    <span className="text-xs text-green-600 flex items-center gap-1 mt-1.5"><Check size={12} className="text-green-500" /> Looks good</span>
                  )}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">Email<span className="text-red-500">*</span></label>
                <input
                  type="email"
                  name="email"
                  value={form.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  className={`w-full border rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all ${formTouched && !validateEmail(form.email) ? 'border-red-400' : formTouched && validateEmail(form.email) ? 'border-green-400' : 'border-gray-300'}`}
                  required
                />
                {formTouched && !validateEmail(form.email) && (
                  <span className="text-xs text-red-500 flex items-center gap-1 mt-1.5"><X size={12} className="text-red-400" /> Enter a valid email.</span>
                )}
                {formTouched && validateEmail(form.email) && form.email && (
                  <span className="text-xs text-green-600 flex items-center gap-1 mt-1.5"><Check size={12} className="text-green-500" /> Valid email</span>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">Role<span className="text-red-500">*</span></label>
                <select
                  name="role"
                  value={form.role}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all appearance-none bg-white"
                  required
                >
                  {userRolesList.map((role) => (
                    <option key={role} value={role}>{USER_ROLES[role as UserRole]?.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">Status</label>
                <select
                  name="status"
                  value={form.status}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all appearance-none bg-white"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">Password<span className="text-red-500">*</span></label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={form.password}
                    onChange={handleInputChange}
                    placeholder="••••••"
                    className={`w-full border rounded-lg px-3 py-2.5 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all ${formTouched && form.password.length > 0 && form.password.length < 6 ? 'border-red-400' : formTouched && form.password.length >= 6 ? 'border-green-400' : 'border-gray-300'}`}
                    required
                    minLength={6}
                  />
                  <button
                    type="button"
                    tabIndex={-1}
                    className="absolute inset-y-0 right-2 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                    onClick={() => setShowPassword((v) => !v)}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {formTouched && form.password.length > 0 && form.password.length < 6 && (
                  <span className="text-xs text-red-500 flex items-center gap-1 mt-1.5"><X size={12} className="text-red-400" /> Password must be at least 6 characters.</span>
                )}
                {formTouched && form.password.length >= 6 && (
                  <span className="text-xs text-green-600 flex items-center gap-1 mt-1.5"><Check size={12} className="text-green-500" /> Looks good</span>
                )}
              </div>
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-100 mt-6 -mx-6 px-6 pb-1">
                <button
                  type="button"
                  className="px-4 py-2.5 rounded-lg font-medium text-gray-600 hover:text-gray-800 bg-white hover:bg-gray-50 border border-gray-200 transition-all focus:outline-none focus:ring-2 focus:ring-gray-200 focus:ring-offset-1"
                  onClick={() => setShowAddUserModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-5 py-2.5 rounded-lg font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-sm transition-all disabled:opacity-60 disabled:pointer-events-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 min-w-[120px]"
                  disabled={!isFormValid}
                >
                  Create User
                </button>
              </div>
            </form>
            </div>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {showEditUserModal && (
        <div className="fixed inset-0 z-[100] flex items-center justify-center bg-black/60 backdrop-blur-sm">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full relative overflow-hidden border border-gray-100">
            {/* Header with enhanced style */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2.5">
                <div className="bg-blue-50 p-1.5 rounded-md">
                  <Edit size={20} className="text-blue-600" />
                </div>
                Edit User
              </h2>
              <button
                className="text-gray-400 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500/20 rounded-full p-2 transition-all hover:bg-gray-100"
                onClick={() => setShowEditUserModal(false)}
                aria-label="Close"
              >
                <X size={20} />
              </button>
            </div>
            <div className="p-6">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="flex gap-3">
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">First Name<span className="text-red-500">*</span></label>
                  <input
                    type="text"
                    name="firstName"
                    value={form.firstName}
                    onChange={handleInputChange}
                    placeholder="John"
                    className={`w-full border rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all ${formTouched && !form.firstName.trim() ? 'border-red-400' : formTouched && form.firstName.trim() ? 'border-green-400' : 'border-gray-300'}`}
                    required
                  />
                  {formTouched && !form.firstName.trim() && (
                    <span className="text-xs text-red-500 flex items-center gap-1 mt-1.5"><X size={12} className="text-red-400" /> Required</span>
                  )}
                  {formTouched && form.firstName.trim() && (
                    <span className="text-xs text-green-600 flex items-center gap-1 mt-1.5"><Check size={12} className="text-green-500" /> Looks good</span>
                  )}
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-1.5">Last Name<span className="text-red-500">*</span></label>
                  <input
                    type="text"
                    name="lastName"
                    value={form.lastName}
                    onChange={handleInputChange}
                    placeholder="Doe"
                    className={`w-full border rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all ${formTouched && !form.lastName.trim() ? 'border-red-400' : formTouched && form.lastName.trim() ? 'border-green-400' : 'border-gray-300'}`}
                    required
                  />
                  {formTouched && !form.lastName.trim() && (
                    <span className="text-xs text-red-500 flex items-center gap-1 mt-1.5"><X size={12} className="text-red-400" /> Required</span>
                  )}
                  {formTouched && form.lastName.trim() && (
                    <span className="text-xs text-green-600 flex items-center gap-1 mt-1.5"><Check size={12} className="text-green-500" /> Looks good</span>
                  )}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">Email<span className="text-red-500">*</span></label>
                <input
                  type="email"
                  name="email"
                  value={form.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  className={`w-full border rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all ${formTouched && !validateEmail(form.email) ? 'border-red-400' : formTouched && validateEmail(form.email) ? 'border-green-400' : 'border-gray-300'}`}
                  required
                />
                {formTouched && !validateEmail(form.email) && (
                  <span className="text-xs text-red-500 flex items-center gap-1 mt-1.5"><X size={12} className="text-red-400" /> Enter a valid email.</span>
                )}
                {formTouched && validateEmail(form.email) && form.email && (
                  <span className="text-xs text-green-600 flex items-center gap-1 mt-1.5"><Check size={12} className="text-green-500" /> Valid email</span>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">Role<span className="text-red-500">*</span></label>
                <select
                  name="role"
                  value={form.role}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all appearance-none bg-white"
                  required
                >
                  {userRolesList.map((role) => (
                    <option key={role} value={role}>{USER_ROLES[role as UserRole]?.label}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">Status</label>
                <select
                  name="status"
                  value={form.status}
                  onChange={handleInputChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all appearance-none bg-white"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1.5">New Password {!isEditMode && <span className="text-red-500">*</span>}</label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={form.password}
                    onChange={handleInputChange}
                    placeholder="••••••"
                    className={`w-full border rounded-lg px-3 py-2.5 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-400 transition-all ${formTouched && form.password.length > 0 && form.password.length < 6 ? 'border-red-400' : formTouched && form.password.length >= 6 ? 'border-green-400' : 'border-gray-300'}`}
                    required={!isEditMode} // Only required for new users
                    minLength={form.password ? 6 : 0} // Only apply minLength if password is being changed
                  />
                  <button
                    type="button"
                    tabIndex={-1}
                    className="absolute inset-y-0 right-2 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                    onClick={() => setShowPassword((v) => !v)}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                {isEditMode && (
                  <p className="text-xs text-gray-500 mt-1">Leave blank to keep current password.</p>
                )}
                {formTouched && form.password.length > 0 && form.password.length < 6 && (
                  <span className="text-xs text-red-500 flex items-center gap-1 mt-1.5"><X size={12} className="text-red-400" /> Password must be at least 6 characters.</span>
                )}
                {formTouched && form.password.length >= 6 && (
                  <span className="text-xs text-green-600 flex items-center gap-1 mt-1.5"><Check size={12} className="text-green-500" /> Looks good</span>
                )}
              </div>
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-100 mt-6 -mx-6 px-6 pb-1">
                <button
                  type="button"
                  className="px-4 py-2.5 rounded-lg font-medium text-gray-600 hover:text-gray-800 bg-white hover:bg-gray-50 border border-gray-200 transition-all focus:outline-none focus:ring-2 focus:ring-gray-200 focus:ring-offset-1"
                  onClick={() => setShowEditUserModal(false)}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-5 py-2.5 rounded-lg font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-sm transition-all disabled:opacity-60 disabled:pointer-events-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 min-w-[120px]"
                  disabled={!isFormValid}
                >
                  Save Changes
                </button>
              </div>
            </form>
            </div>
          </div>
        </div>
      )}

    </>
  );
}