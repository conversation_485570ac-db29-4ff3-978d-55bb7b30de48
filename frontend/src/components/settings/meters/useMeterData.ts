import { useState, useEffect, useMemo, useCallback } from 'react';
import { Meter, MeterTabType } from './types';
import { MAIN_METER_LIST, getAllMainMetersFlat } from '../../../lib/config/building/meters/main-meter-list';
import { fetchAllDevices } from '../../../lib/api/devices';
import { apiClient } from '../../../lib/api/enhancedApiClient';

// Export this constant to be used in other components
export const ITEMS_PER_PAGE = 10;

/**
 * Custom hook for managing meter data
 * 
 * This hook handles loading, filtering, and pagination of meter data.
 * It also provides functions for CRUD operations on meters.
 */
const useMeterData = () => {
  // State for meters data
  const [allMeters, setAllMeters] = useState<Meter[]>([]);
  const [filteredMeters, setFilteredMeters] = useState<Meter[]>([]);
  const [paginatedMeters, setPaginatedMeters] = useState<Meter[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // State for filtering and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<MeterTabType>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedMeters, setSelectedMeters] = useState<string[]>([]);
  
  // Pagination settings - use the exported constant
  const itemsPerPage = ITEMS_PER_PAGE;
  
  // Load meter data
  useEffect(() => {
    const loadMeters = async () => {
      setIsLoading(true);
      
      try {
        let physicalMeters: Meter[];
        
        // Check if we're using mock data
        if (apiClient.isUsingMockData()) {
          // In mock data mode, use the comprehensive main meter list
          physicalMeters = generatePhysicalMetersFromMainList();
        } else {
          // In production/API mode, fetch from the actual API
          try {
            const devices = await fetchAllDevices(['latest_data']);
            
            // Convert API devices to Meter format
            physicalMeters = devices.map(device => ({
              id: device.device_id,
              name: device.name,
              type: 'physical' as const,
              location: device.metadata?.location || 'Unknown',
              status: device.latest_data ? 'online' : 'offline' as 'online' | 'offline' | 'warning',
              lastReading: device.latest_data ? {
                value: device.latest_data.power?.value || 0,
                timestamp: new Date().toISOString(),
                unit: 'kW'
              } : undefined
            }));
          } catch (apiError) {
            console.error('Failed to fetch devices from API, using fallback:', apiError);
            // Fallback to sample data if API fails
            physicalMeters = generateMockPhysicalMeters(500);
          }
        }
        
        // Create example virtual meters
        const virtualMeters: Meter[] = [
          {
            id: 'VM-A3-LIGHTING-001',
            name: '3rd Floor Lighting Total',
            type: 'virtual',
            virtualType: 'aggregation',
            location: 'Building A, Floor 3',
            status: 'online',
            lastReading: {
              value: 45.2,
              timestamp: new Date().toISOString(),
              unit: 'kW'
            },
            sourceMeters: ['PHY-A3-LIGHT-003']
          },
          {
            id: 'VM-DATACENTER-002',
            name: 'Data Center Total',
            type: 'virtual',
            virtualType: 'aggregation',
            location: 'Building C, Basement',
            status: 'online',
            lastReading: {
              value: 302.9,
              timestamp: new Date().toISOString(),
              unit: 'kW'
            },
            sourceMeters: ['PHY-C-DATA-006', 'PHY-C-COOL-007']
          },
          {
            id: 'VM-BUILDING-A-004',
            name: 'Building A Total',
            type: 'virtual',
            virtualType: 'aggregation',
            location: 'Building A',
            status: 'online',
            lastReading: {
              value: 626.4,
              timestamp: new Date().toISOString(),
              unit: 'kW'
            },
            sourceMeters: ['PHY-A1-MAIN-001', 'PHY-A2-HVAC-002', 'PHY-A3-LIGHT-003']
          },
          {
            id: 'VM-BUILDING-B-005',
            name: 'Building B Total',
            type: 'virtual',
            virtualType: 'aggregation',
            location: 'Building B',
            status: 'warning',
            lastReading: {
              value: 372.1,
              timestamp: new Date().toISOString(),
              unit: 'kW'
            },
            sourceMeters: ['PHY-B1-MAIN-004', 'PHY-B2-HVAC-005']
          },
          {
            id: 'VM-NONHVAC-006',
            name: 'Non-HVAC Load',
            type: 'virtual',
            virtualType: 'difference',
            location: 'Building A, Floor 2',
            status: 'online',
            lastReading: {
              value: 324.4,
              timestamp: new Date().toISOString(),
              unit: 'kW'
            },
            sourceMeters: ['PHY-A1-MAIN-001', 'PHY-A2-HVAC-002']
          },
          {
            id: 'VM-DATACENTER-OTHERS-007',
            name: 'Data Center & Others',
            type: 'virtual',
            virtualType: 'calculated',
            location: 'Calculated',
            status: 'online',
            lastReading: {
              value: 450.7,
              timestamp: new Date().toISOString(),
              unit: 'kW'
            },
            sourceMeters: ['CALCULATED'],
            description: 'Calculated as Total Consumption minus sum of all other systems'
          }
        ];
        
        // Combine physical and virtual meters
        const allMetersData = [...physicalMeters, ...virtualMeters];
        
        // Update state
        setAllMeters(allMetersData);
        setFilteredMeters(allMetersData);
        setIsLoading(false);
      } catch (error) {
        console.error('Error loading meter data:', error);
        setIsLoading(false);
      }
    };
    
    loadMeters();
  }, []);
  
  /**
   * Generate physical meters from the comprehensive main meter list
   * NOTE: This is only used in MOCK DATA MODE to show the 172 real meters
   * In production mode, data should come from the API
   */
  const generatePhysicalMetersFromMainList = (): Meter[] => {
    const allMainMeters = getAllMainMetersFlat();
    
    // Convert main meter list to Meter format
    return allMainMeters.map(mainMeter => {
      // Determine status - 95% online, 3% warning, 2% offline
      let status: 'online' | 'warning' | 'offline';
      const statusRandom = Math.random() * 100;
      
      if (statusRandom <= 95) {
        status = 'online';
      } else if (statusRandom <= 98) {
        status = 'warning';
      } else {
        status = 'offline';
      }
      
      // Generate realistic power values based on meter type
      let value = 0;
      switch (mainMeter.type) {
        case 'chillerPlant':
          value = status === 'online' ? 150 + Math.random() * 350 : 0; // 150-500 kW
          break;
        case 'airSide':
          value = status === 'online' ? 50 + Math.random() * 150 : 0; // 50-200 kW
          break;
        case 'lightPower':
          value = status === 'online' ? 20 + Math.random() * 80 : 0; // 20-100 kW
          break;
        case 'escalator_elevator':
          value = status === 'online' ? 30 + Math.random() * 70 : 0; // 30-100 kW
          break;
        case 'tenant':
          value = status === 'online' ? 10 + Math.random() * 50 : 0; // 10-60 kW
          break;
        case 'data_center_others':
          value = status === 'online' ? 100 + Math.random() * 200 : 0; // 100-300 kW
          break;
        default:
          value = status === 'online' ? 50 + Math.random() * 150 : 0; // 50-200 kW
      }
      
      const meter: Meter = {
        id: mainMeter.id,
        name: mainMeter.name,
        type: 'physical',
        location: `${mainMeter.groupName} - ${mainMeter.system}`,
        status,
        lastReading: {
          value: parseFloat(value.toFixed(1)),
          timestamp: new Date().toISOString(),
          unit: 'kW'
        }
      };
      
      return meter;
    });
  };
  
  /**
   * Generate a large number of mock physical meters with 95%+ online status
   * @deprecated Use generatePhysicalMetersFromMainList instead
   */
  const generateMockPhysicalMeters = (count: number): Meter[] => {
    const meters: Meter[] = [];
    
    // Create more realistic building structure with 30+ floors in Building A
    const locations: string[] = [];
    
    // Building A - 30 floors
    for (let i = 1; i <= 30; i++) {
      locations.push(`Building A, Floor ${i}`);
    }
    
    // Building B - 20 floors
    for (let i = 1; i <= 20; i++) {
      locations.push(`Building B, Floor ${i}`);
    }
    
    // Building C - 15 floors and basement
    locations.push(`Building C, Basement`);
    for (let i = 1; i <= 15; i++) {
      locations.push(`Building C, Floor ${i}`);
    }
    
    // Building D - 12 floors
    for (let i = 1; i <= 12; i++) {
      locations.push(`Building D, Floor ${i}`);
    }
    
    // Common areas
    locations.push(
      `Data Center`,
      `Parking Lot A`, 
      `Parking Lot B`, 
      `Main Cafeteria`, 
      `Executive Lounge`, 
      `Auditorium`, 
      `Gym`, 
      `Lobby`
    );
    
    const meterTypes = [
      'Main', 'HVAC', 'Plug Loads', 'Cooling', 'Heating',
      'Solar', 'Battery', 'Server', 'Kitchen', 'Elevator'
    ];
    
    // Create the specified number of meters
    for (let i = 0; i < count; i++) {
      // Determine status - 95% online, 3% warning, 2% offline
      let status: 'online' | 'warning' | 'offline';
      const statusRandom = Math.random() * 100;
      
      if (statusRandom <= 95) {
        status = 'online';
      } else if (statusRandom <= 98) {
        status = 'warning';
      } else {
        status = 'offline';
      }
      
      // Generate random location and type
      const location = locations[Math.floor(Math.random() * locations.length)];
      const meterType = meterTypes[Math.floor(Math.random() * meterTypes.length)];
      
      // Generate a value appropriate for the meter type
      let value = Math.random() * 500;
      if (meterType === 'Plug Loads') value = Math.random() * 150;
      if (meterType === 'Solar') value = -Math.random() * 200; // Negative for generation
      if (status === 'offline') value = 0;
      
      // Create the meter
      const meter: Meter = {
        id: `PHY-${i.toString().padStart(4, '0')}`,
        name: `${location} ${meterType}`,
        type: 'physical',
        location,
        status,
        lastReading: {
          value: parseFloat(value.toFixed(1)),
          timestamp: new Date().toISOString(),
          unit: 'kW'
        }
      };
      
      meters.push(meter);
    }
    
    // Add a few specific meters that are referenced by virtual meters
    const specificMeters: Meter[] = [
      {
        id: 'PHY-A1-MAIN-001',
        name: 'Main Power Meter A1',
        type: 'physical',
        location: 'Building A, Floor 1',
        status: 'online',
        lastReading: {
          value: 452.8,
          timestamp: new Date().toISOString(),
          unit: 'kW'
        }
      },
      {
        id: 'PHY-A2-HVAC-002',
        name: 'HVAC System A2',
        type: 'physical',
        location: 'Building A, Floor 2',
        status: 'online',
        lastReading: {
          value: 128.4,
          timestamp: new Date().toISOString(),
          unit: 'kW'
        }
      },
      {
        id: 'PHY-A3-LIGHT-003',
        name: 'Lighting System A3',
        type: 'physical',
        location: 'Building A, Floor 3',
        status: 'warning',
        lastReading: {
          value: 45.2,
          timestamp: new Date().toISOString(),
          unit: 'kW'
        }
      },
      {
        id: 'PHY-B1-MAIN-004',
        name: 'Main Power Meter B1',
        type: 'physical',
        location: 'Building B, Floor 1',
        status: 'online',
        lastReading: {
          value: 372.1,
          timestamp: new Date().toISOString(),
          unit: 'kW'
        }
      },
      {
        id: 'PHY-B2-HVAC-005',
        name: 'HVAC System B2',
        type: 'physical',
        location: 'Building B, Floor 2',
        status: 'offline',
        lastReading: {
          value: 0,
          timestamp: new Date().toISOString(),
          unit: 'kW'
        }
      },
      {
        id: 'PHY-C-DATA-006',
        name: 'Data Center Main',
        type: 'physical',
        location: 'Building C, Basement',
        status: 'online',
        lastReading: {
          value: 215.3,
          timestamp: new Date().toISOString(),
          unit: 'kW'
        }
      },
      {
        id: 'PHY-C-COOL-007',
        name: 'Data Center Cooling',
        type: 'physical',
        location: 'Building C, Basement',
        status: 'online',
        lastReading: {
          value: 87.6,
          timestamp: new Date().toISOString(),
          unit: 'kW'
        }
      },
    ];
    
    // Replace the first few meters with our specific meters to ensure they exist
    return [...specificMeters, ...meters.slice(specificMeters.length, count)];
  };
  
  // Filter meters based on active tab and search term
  useEffect(() => {
    let filtered = [...allMeters];
    
    // Filter by tab
    if (activeTab === 'physical') {
      filtered = filtered.filter(meter => meter.type === 'physical');
    } else if (activeTab === 'virtual') {
      filtered = filtered.filter(meter => meter.type === 'virtual');
    }
    
    // Filter by search term
    if (searchTerm) {
      const lowerCaseSearchTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(
        meter => 
          meter.name.toLowerCase().includes(lowerCaseSearchTerm) || 
          meter.id.toLowerCase().includes(lowerCaseSearchTerm) ||
          (meter.location?.toLowerCase() || '').includes(lowerCaseSearchTerm)
      );
    }
    
    // Reset pagination when filters change
    setCurrentPage(1);
    setFilteredMeters(filtered);
    setSelectedMeters([]);
  }, [allMeters, activeTab, searchTerm]);
  
  // Update paginated meters when filtered meters or pagination changes
  useEffect(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    setPaginatedMeters(filteredMeters.slice(startIndex, endIndex));
  }, [filteredMeters, currentPage, itemsPerPage]);
  
  // Calculate total pages
  const totalPages = useMemo(() => {
    return Math.ceil(filteredMeters.length / itemsPerPage);
  }, [filteredMeters, itemsPerPage]);
  
  // Calculate meter counts for tabs
  const meterCounts = useMemo(() => {
    const physical = allMeters.filter(meter => meter.type === 'physical').length;
    const virtual = allMeters.filter(meter => meter.type === 'virtual').length;
    return {
      all: allMeters.length,
      physical,
      virtual
    };
  }, [allMeters]);
  
  // Calculate status counts
  const statusCounts = useMemo(() => {
    const online = allMeters.filter(meter => meter.status === 'online').length;
    const offline = allMeters.filter(meter => meter.status === 'offline').length;
    const warning = allMeters.filter(meter => meter.status === 'warning').length;
    const total = allMeters.length;
    const onlinePercentage = total > 0 ? Math.round((online / total) * 100) : 0;
    
    return {
      online,
      offline,
      warning,
      total,
      onlinePercentage
    };
  }, [allMeters]);
  
  // Toggle meter selection
  const toggleMeterSelection = (meterId: string) => {
    setSelectedMeters(prev => {
      if (prev.includes(meterId)) {
        return prev.filter(id => id !== meterId);
      } else {
        return [...prev, meterId];
      }
    });
  };
  
  // Select or deselect all meters
  const handleSelectAll = useCallback((select: boolean) => {
    if (select) {
      // Select all meters in the current page
      setSelectedMeters(paginatedMeters.map(meter => meter.id));
    } else {
      // Deselect all meters
      setSelectedMeters([]);
    }
  }, [paginatedMeters]);
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Add a new virtual meter
  const createVirtualMeter = (meter: { id?: string, name: string, type: 'aggregation' | 'difference', sourceMeters: string[] }) => {
    // Create a complete meter object from the simplified NewVirtualMeter
    const newMeter: Meter = {
      id: meter.id || `VM-${new Date().getTime()}`,
      name: meter.name,
      type: 'virtual',
      virtualType: meter.type,
      status: 'online',
      lastReading: {
        value: 0, // This would be calculated in a real implementation
        timestamp: new Date().toISOString(),
        unit: 'kW'
      },
      sourceMeters: meter.sourceMeters
    };
    
    setAllMeters(prevMeters => [...prevMeters, newMeter]);
  };

  // Delete a meter
  const deleteMeter = (meterId: string) => {
    setAllMeters(prevMeters => prevMeters.filter(meter => meter.id !== meterId));
    setSelectedMeters(prevSelected => prevSelected.filter(id => id !== meterId));
  };
  
  return {
    allMeters,
    filteredMeters,
    paginatedMeters,
    searchTerm,
    setSearchTerm,
    currentPage,
    activeTab,
    setActiveTab,
    selectedMeters,
    toggleMeterSelection,
    handleSelectAll,
    handlePageChange,
    isLoading,
    totalPages,
    meterCounts,
    statusCounts,
    createVirtualMeter,
    deleteMeter
  };
};

export default useMeterData;
