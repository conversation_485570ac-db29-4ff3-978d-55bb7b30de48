import { memo } from 'react';

interface MeterTypeBadgeProps {
  type: 'physical' | 'virtual';
  virtualType?: 'aggregation' | 'difference';
}

const MeterTypeBadge = memo(function MeterTypeBadge({ type, virtualType }: MeterTypeBadgeProps) {
  if (type === 'virtual') {
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
        Virtual {virtualType && `(${virtualType})`}
      </span>
    );
  }
  
  return (
    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
      Physical
    </span>
  );
});

MeterTypeBadge.displayName = 'MeterTypeBadge';

export default MeterTypeBadge;
