import { memo } from 'react';
import { AlertCircle, Plus } from 'lucide-react';
import { MeterTabType } from './types';

interface NoMetersFoundProps {
  searchTerm?: string;
  activeTab: MeterTabType;
  onAddVirtualMeter: () => void;
}

const NoMetersFound = memo(function NoMetersFound({ 
  searchTerm, 
  activeTab,
  onAddVirtualMeter
}: NoMetersFoundProps) {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-8 flex flex-col items-center text-center">
      <div className="rounded-full bg-gray-100 p-3 mb-4">
        <AlertCircle className="h-6 w-6 text-gray-400" />
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">No meters found</h3>
      
      {searchTerm ? (
        <p className="text-sm text-gray-500 max-w-md">
          No {activeTab !== 'all' ? activeTab : ''} meters match your search criteria.
          Try adjusting your search or filters.
        </p>
      ) : (
        <p className="text-sm text-gray-500 max-w-md mb-4">
          {activeTab === 'virtual' 
            ? 'No virtual meters have been created yet. Use the "Add Virtual Meter" button to create one.'
            : 'No meters were found. Please check your connection or contact support.'}
        </p>
      )}
      
      {activeTab === 'virtual' && !searchTerm && (
        <button
          onClick={onAddVirtualMeter}
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Virtual Meter
        </button>
      )}
    </div>
  );
});

NoMetersFound.displayName = 'NoMetersFound';

export default NoMetersFound;
