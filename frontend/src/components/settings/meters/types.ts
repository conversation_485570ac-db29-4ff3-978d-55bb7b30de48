import type { BuildingId } from '../../../types';

// Tab type for filtering meters
export type MeterTabType = 'all' | 'physical' | 'virtual';

// Types for our meters
export interface Meter {
  id: string;
  name: string;
  type: 'physical' | 'virtual';
  virtualType?: 'aggregation' | 'difference';
  location?: string;
  status: 'online' | 'offline' | 'warning';
  lastReading?: {
    value: number;
    timestamp: string;
    unit: string;
  };
  sourceMeters?: string[]; // For virtual meters
}

export interface NewVirtualMeter {
  id: string;
  name: string;
  type: 'aggregation' | 'difference';
  sourceMeters: string[];
}

// Type for the mock meter data
export interface MockMeter {
  id: string;
  name: string;
  type: string;
  building: BuildingId;
  floor: number | 'B';
  location: string;
  status: 'active' | 'disconnected' | 'warning';
  lastReading: number;
  unit: string;
  lastUpdate: string;
}
