import { memo, useState, useEffect, useMemo } from 'react';
import { X, Info, Search, Filter } from 'lucide-react';
import { Meter, NewVirtualMeter } from './types';

interface VirtualMeterModalProps {
  isOpen: boolean;
  onClose: () => void;
  allMeters: Meter[];
  onCreate: (meter: NewVirtualMeter) => void;
  newVirtualMeter: NewVirtualMeter;
  setNewVirtualMeter: React.Dispatch<React.SetStateAction<NewVirtualMeter>>;
}

const VirtualMeterModal = memo(function VirtualMeterModal({
  isOpen,
  onClose,
  allMeters,
  onCreate,
  newVirtualMeter,
  setNewVirtualMeter
}: VirtualMeterModalProps) {
  // Local state for the form
  const [selectedMeters, setSelectedMeters] = useState<string[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Search and filter state
  const [meterSearchTerm, setMeterSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);

  // Use either the external state or local state
  const virtualMeter = newVirtualMeter;
  const setVirtualMeter = setNewVirtualMeter;
  
  // Load physical meters when modal opens
  useEffect(() => {
    const loadPhysicalMeters = async () => {
      try {
        // We already have the meters available in allMeters prop
        // No need to generate example meters
      } catch (error) {
        console.error('Error loading physical meters:', error);
      }
    };
    
    if (isOpen) {
      loadPhysicalMeters();
    }
  }, [isOpen, allMeters]);
  
  // Initialize selected meters from virtual meter source meters
  useEffect(() => {
    if (virtualMeter.sourceMeters.length > 0) {
      setSelectedMeters(virtualMeter.sourceMeters);
    }
  }, [virtualMeter.sourceMeters]);
  
  // Get unique locations for filtering
  const uniqueLocations = useMemo(() => {
    const locations = new Set<string>();
    allMeters.forEach(meter => {
      if (meter.location) {
        locations.add(meter.location);
      }
    });
    return ['all', ...Array.from(locations)];
  }, [allMeters]);
  
  // Filter meters based on search and filters
  const filteredMeters = useMemo(() => {
    return allMeters.filter(meter => {
      // Filter by search term
      const searchMatch = !meterSearchTerm || 
        meter.name.toLowerCase().includes(meterSearchTerm.toLowerCase()) ||
        meter.id.toLowerCase().includes(meterSearchTerm.toLowerCase());
      
      // Filter by location
      const locationMatch = locationFilter === 'all' || 
        meter.location === locationFilter;
      
      // Filter by status
      const statusMatch = statusFilter === 'all' || 
        meter.status === statusFilter;
      
      return searchMatch && locationMatch && statusMatch;
    });
  }, [allMeters, meterSearchTerm, locationFilter, statusFilter]);
  
  // Group meters by location for better organization
  const metersByLocation = useMemo(() => {
    const grouped: Record<string, Meter[]> = {};
    
    filteredMeters.forEach(meter => {
      const location = meter.location || 'Unknown';
      if (!grouped[location]) {
        grouped[location] = [];
      }
      grouped[location].push(meter);
    });
    
    return grouped;
  }, [filteredMeters]);
  
  // Handle toggle of meter selection
  const handleToggleMeter = (meterId: string) => {
    if (selectedMeters.includes(meterId)) {
      const newSelectedMeters = selectedMeters.filter(id => id !== meterId);
      setSelectedMeters(newSelectedMeters);
      setVirtualMeter({
        ...virtualMeter,
        sourceMeters: newSelectedMeters
      });
    } else {
      const newSelectedMeters = [...selectedMeters, meterId];
      setSelectedMeters(newSelectedMeters);
      setVirtualMeter({
        ...virtualMeter,
        sourceMeters: newSelectedMeters
      });
    }
  };
  
  // Validate form before submission
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!virtualMeter.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (virtualMeter.sourceMeters.length < 2) {
      newErrors.sourceMeters = 'At least 2 source meters are required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      onCreate(virtualMeter);
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] flex flex-col">
        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">Create Virtual Meter</h3>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="flex-1 overflow-auto p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left column - Virtual meter details */}
            <div className="space-y-6">
              <div>
                <label htmlFor="virtual-meter-name" className="block text-sm font-medium text-gray-700">
                  Meter Name
                </label>
                <input
                  type="text"
                  id="virtual-meter-name"
                  value={virtualMeter.name}
                  onChange={(e) => setVirtualMeter({ ...virtualMeter, name: e.target.value })}
                  className={`mt-1 block w-full rounded-md shadow-sm sm:text-sm ${
                    errors.name ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  placeholder="Enter meter name"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">
                  Virtual Meter Type
                </label>
                <div className="mt-2 space-y-2">
                  <div className="flex items-center">
                    <input
                      id="aggregation"
                      name="meter-type"
                      type="radio"
                      checked={virtualMeter.type === 'aggregation'}
                      onChange={() => setVirtualMeter({ ...virtualMeter, type: 'aggregation' })}
                      className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <label htmlFor="aggregation" className="ml-3 block text-sm font-medium text-gray-700">
                      Aggregation (Sum of selected meters)
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      id="difference"
                      name="meter-type"
                      type="radio"
                      checked={virtualMeter.type === 'difference'}
                      onChange={() => setVirtualMeter({ ...virtualMeter, type: 'difference' })}
                      className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <label htmlFor="difference" className="ml-3 block text-sm font-medium text-gray-700">
                      Difference (First meter minus sum of others)
                    </label>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-md">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <Info className="h-5 w-5 text-blue-500" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-gray-900">About Virtual Meters</h3>
                    <div className="mt-2 text-sm text-gray-600">
                      <p className="mb-2">
                        Virtual meters allow you to monitor specific energy usage patterns by combining data from physical meters.
                      </p>
                      <p className="mb-2">
                        <strong>Aggregation:</strong> Sums the readings from all selected meters. Useful for measuring total consumption of a specific area or system.
                      </p>
                      <p>
                        <strong>Difference:</strong> Calculates the difference between the first selected meter and the sum of all other meters. Useful for measuring specific loads.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between items-center">
                  <label className="block text-sm font-medium text-gray-700">
                    Selected Source Meters ({selectedMeters.length})
                  </label>
                  {selectedMeters.length > 0 && (
                    <button
                      type="button"
                      onClick={() => {
                        setSelectedMeters([]);
                        setVirtualMeter({
                          ...virtualMeter,
                          sourceMeters: []
                        });
                      }}
                      className="text-xs text-red-600 hover:text-red-800"
                    >
                      Clear all
                    </button>
                  )}
                </div>
                {errors.sourceMeters && (
                  <p className="mt-1 text-sm text-red-600">{errors.sourceMeters}</p>
                )}
                {selectedMeters.length > 0 ? (
                  <div className="mt-2 border border-gray-200 rounded-md max-h-40 overflow-y-auto p-2">
                    <ul className="divide-y divide-gray-100">
                      {selectedMeters.map((meterId) => {
                        const meter = allMeters.find(m => m.id === meterId);
                        return meter ? (
                          <li key={meter.id} className="py-1 flex justify-between items-center">
                            <div>
                              <p className="text-sm font-medium text-gray-900">{meter.name}</p>
                              <p className="text-xs text-gray-500">{meter.id} • {meter.location}</p>
                            </div>
                            <button
                              onClick={() => handleToggleMeter(meter.id)}
                              className="text-gray-400 hover:text-red-500"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </li>
                        ) : null;
                      })}
                    </ul>
                  </div>
                ) : (
                  <div className="mt-2 border border-gray-200 rounded-md p-4 text-center text-sm text-gray-500">
                    No meters selected yet
                  </div>
                )}
              </div>
            </div>
            
            {/* Right column - Source meter selection */}
            <div>
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Available Source Meters
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowFilters(!showFilters)}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded hover:bg-gray-200"
                  >
                    <Filter className="h-3 w-3 mr-1" />
                    {showFilters ? 'Hide Filters' : 'Show Filters'}
                  </button>
                </div>
                
                {/* Search bar */}
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    value={meterSearchTerm}
                    onChange={(e) => setMeterSearchTerm(e.target.value)}
                    placeholder="Search meters by name or ID"
                    className="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                
                {/* Filters */}
                {showFilters && (
                  <div className="mt-2 p-3 bg-gray-50 rounded-md">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Location
                        </label>
                        <select
                          value={locationFilter}
                          onChange={(e) => setLocationFilter(e.target.value)}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-xs"
                        >
                          {uniqueLocations.map((location) => (
                            <option key={location} value={location}>
                              {location === 'all' ? 'All Locations' : location}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-700 mb-1">
                          Status
                        </label>
                        <select
                          value={statusFilter}
                          onChange={(e) => setStatusFilter(e.target.value)}
                          className="block w-full rounded-md border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-xs"
                        >
                          <option value="all">All Statuses</option>
                          <option value="online">Online</option>
                          <option value="warning">Warning</option>
                          <option value="offline">Offline</option>
                        </select>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Meter selection list */}
              <div className="border border-gray-200 rounded-md overflow-hidden">
                <div className="max-h-[400px] overflow-y-auto">
                  {Object.keys(metersByLocation).length > 0 ? (
                    <div className="divide-y divide-gray-200">
                      {Object.entries(metersByLocation).map(([location, meters]) => (
                        <div key={location} className="bg-white">
                          <div className="px-4 py-2 bg-gray-50 sticky top-0 z-10">
                            <h4 className="text-xs font-medium text-gray-500">{location}</h4>
                          </div>
                          <ul className="divide-y divide-gray-100">
                            {meters.map((meter) => (
                              <li key={meter.id} className="px-4 py-2 hover:bg-gray-50">
                                <div className="flex items-center">
                                  <input
                                    type="checkbox"
                                    checked={selectedMeters.includes(meter.id)}
                                    onChange={() => handleToggleMeter(meter.id)}
                                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                  />
                                  <div className="ml-3 flex-1">
                                    <div className="flex justify-between">
                                      <p className="text-sm font-medium text-gray-900">{meter.name}</p>
                                      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                        meter.status === 'online' ? 'bg-green-100 text-green-800' :
                                        meter.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-red-100 text-red-800'
                                      }`}>
                                        {meter.status}
                                      </span>
                                    </div>
                                    <p className="text-xs text-gray-500">{meter.id}</p>
                                  </div>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 text-center text-sm text-gray-500">
                      No meters match your search criteria
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Create Virtual Meter
          </button>
        </div>
      </div>
    </div>
  );
});

VirtualMeterModal.displayName = 'VirtualMeterModal';

export default VirtualMeterModal;
