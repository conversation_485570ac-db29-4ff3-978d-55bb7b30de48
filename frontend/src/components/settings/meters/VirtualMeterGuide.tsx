import { memo } from 'react';
import { Info, Check, Plus, X } from 'lucide-react';

interface VirtualMeterGuideProps {
  isOpen: boolean;
  onClose: () => void;
  onAddVirtualMeter?: () => void;
}

const VirtualMeterGuide = memo(function VirtualMeterGuide({ 
  isOpen, 
  onClose, 
  onAddVirtualMeter 
}: VirtualMeterGuideProps) {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden max-w-2xl w-full">
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50 flex justify-between items-center">
          <div className="flex items-center">
            <Info className="h-5 w-5 text-blue-500 mr-2" />
            <h3 className="text-base font-medium text-gray-900">Virtual Meters Guide</h3>
          </div>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-6 max-h-[70vh] overflow-y-auto">
          <div className="prose prose-sm max-w-none text-gray-500">
            <p>
              Virtual meters allow you to create logical meters based on data from physical meters. 
              They can help you gain insights into specific systems or areas without installing additional hardware.
            </p>
            
            <h4 className="text-sm font-medium text-gray-700 mt-4">Benefits of Virtual Meters:</h4>
            <ul className="mt-2 space-y-1">
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Track energy usage by system (lighting, HVAC, plug loads)</span>
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Aggregate consumption across multiple areas</span>
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Calculate differences to identify unknown loads</span>
              </li>
              <li className="flex items-start">
                <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>Create custom KPIs for specific business needs</span>
              </li>
            </ul>
            
            <h4 className="text-sm font-medium text-gray-700 mt-4">Available Virtual Meter Types:</h4>
            <div className="mt-2 space-y-3">
              <div className="bg-gray-50 p-3 rounded-md">
                <h5 className="text-sm font-medium text-gray-700">Aggregation Meters</h5>
                <p className="text-xs mt-1">
                  Combine readings from multiple meters to create a sum. 
                  Useful for floor totals, system totals, or building totals.
                </p>
              </div>
              
              <div className="bg-gray-50 p-3 rounded-md">
                <h5 className="text-sm font-medium text-gray-700">Difference Meters</h5>
                <p className="text-xs mt-1">
                  Subtract the sum of secondary meters from a primary meter.
                  Useful for isolating specific loads or calculating unknown consumption.
                </p>
              </div>
            </div>
          </div>
          
          {onAddVirtualMeter && (
            <div className="mt-6 flex justify-center">
              <button
                onClick={() => {
                  onAddVirtualMeter();
                  onClose();
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="mr-2 h-4 w-4" />
                Create Your First Virtual Meter
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

VirtualMeterGuide.displayName = 'VirtualMeterGuide';

export default VirtualMeterGuide;
