import { memo } from 'react';

interface MeterStatusBadgeProps {
  status: 'online' | 'offline' | 'warning';
}

const MeterStatusBadge = memo(function MeterStatusBadge({ status }: MeterStatusBadgeProps) {
  const getStatusStyles = () => {
    switch (status) {
      case 'online':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'offline':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = () => {
    switch (status) {
      case 'online':
        return 'Online';
      case 'warning':
        return 'Warning';
      case 'offline':
        return 'Offline';
      default:
        return 'Unknown';
    }
  };

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusStyles()}`}>
      {getStatusLabel()}
    </span>
  );
});

MeterStatusBadge.displayName = 'MeterStatusBadge';

export default MeterStatusBadge;
