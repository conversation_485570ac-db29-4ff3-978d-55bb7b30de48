import { memo } from 'react';
import { MeterTabType } from './types';

interface FilterTabsProps {
  activeTab: MeterTabType;
  setActiveTab: (tab: MeterTabType) => void;
  counts: {
    all: number;
    physical: number;
    virtual: number;
  };
}

const FilterTabs = memo(function FilterTabs({ 
  activeTab, 
  setActiveTab, 
  counts 
}: FilterTabsProps) {
  const tabs: Array<{ id: MeterTabType; label: string; count: number }> = [
    { id: 'all', label: 'All Meters', count: counts.all },
    { id: 'physical', label: 'Physical Meters', count: counts.physical },
    { id: 'virtual', label: 'Virtual Meters', count: counts.virtual }
  ];

  return (
    <div className="border-b border-gray-200">
      <nav className="-mb-px flex" aria-label="Tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`${
              activeTab === tab.id
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm flex items-center space-x-2`}
            aria-current={activeTab === tab.id ? 'page' : undefined}
          >
            <span>{tab.label}</span>
            <span className="bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full text-xs">
              {tab.count}
            </span>
          </button>
        ))}
      </nav>
    </div>
  );
});

FilterTabs.displayName = 'FilterTabs';

export default FilterTabs;
