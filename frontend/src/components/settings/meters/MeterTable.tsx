import { memo } from 'react';
import { Eye, Edit, Trash2, Info } from 'lucide-react';
import MeterStatusBadge from './MeterStatusBadge';
import MeterTypeBadge from './MeterTypeBadge';
import { Meter, MeterTabType } from './types';
import { Tooltip } from '@/components/ui/tooltip';

interface MeterTableProps {
  meters: Meter[];
  selectedMeters: string[];
  onSelect: (meterId: string) => void;
  onSelectAll: (select: boolean) => void;
  onDelete: (meterId: string) => void;
  activeTab: MeterTabType;
  onView?: (meterId: string) => void;
  onEdit?: (meterId: string) => void;
}

const MeterTable = memo(function MeterTable({
  meters,
  selectedMeters,
  onSelect,
  onSelectAll,
  onView,
  onEdit,
  onDelete,
  activeTab
}: MeterTableProps) {
  // Check if all meters are selected
  const allSelected = meters.length > 0 && meters.every(meter => selectedMeters.includes(meter.id));
  
  // Determine if we should show the location column based on the active tab
  const showLocationColumn = activeTab !== 'virtual';
  
  return (
    <div className="overflow-x-auto rounded-lg shadow border border-gray-200">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                onChange={(e) => onSelectAll(e.target.checked)}
                checked={allSelected}
                aria-label="Select all meters"
              />
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              ID
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Name
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Type
            </th>
            {showLocationColumn && (
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Location
              </th>
            )}
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Last Reading
            </th>
            <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {meters.map((meter) => (
            <tr key={meter.id} className="hover:bg-gray-50 transition-colors duration-150">
              <td className="px-6 py-4 whitespace-nowrap">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  checked={selectedMeters.includes(meter.id)}
                  onChange={() => onSelect(meter.id)}
                  aria-label={`Select meter ${meter.name}`}
                />
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {meter.id}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-medium">
                <div className="flex items-center gap-1">
                  <span>{meter.name}</span>
                  {meter.name === 'Data Center & Others' && (
                    <Tooltip content="Calculated value: Total consumption minus sum of all other systems">
                      <Info className="h-3.5 w-3.5 text-gray-400 cursor-help" />
                    </Tooltip>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <MeterTypeBadge type={meter.type} virtualType={meter.virtualType} />
              </td>
              {showLocationColumn && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500">
                    {meter.location || '—'}
                  </div>
                </td>
              )}
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <MeterStatusBadge status={meter.status} />
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {meter.lastReading ? (
                  <span className="font-medium">
                    {meter.lastReading.value} <span className="text-gray-400">{meter.lastReading.unit}</span>
                  </span>
                ) : (
                  '—'
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                <div className="flex justify-center space-x-2">
                  {onView && (
                    <button
                      onClick={() => onView(meter.id)}
                      className="text-blue-600 hover:text-blue-900 transition-colors p-1"
                      aria-label={`View meter ${meter.name}`}
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                  )}
                  {onEdit && (
                    <button
                      onClick={() => onEdit(meter.id)}
                      className="text-green-600 hover:text-green-900 transition-colors p-1"
                      aria-label={`Edit meter ${meter.name}`}
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                  )}
                  <button
                    onClick={() => onDelete(meter.id)}
                    className="text-red-600 hover:text-red-900 transition-colors p-1"
                    aria-label={`Delete meter ${meter.name}`}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
});

MeterTable.displayName = 'MeterTable';

export default MeterTable;
