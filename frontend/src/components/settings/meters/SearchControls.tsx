import { memo, useState } from 'react';
import { Search, Plus, HelpCircle, X } from 'lucide-react';

interface SearchControlsProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  selectedMeters: string[];
  bulkActionsOpen: boolean;
  setBulkActionsOpen: (open: boolean) => void;
  onAddVirtualMeter: () => void;
  onOpenVirtualMeterGuide?: () => void;
}

const SearchControls = memo(function SearchControls({
  searchTerm,
  setSearchTerm,
  selectedMeters,
  bulkActionsOpen,
  setBulkActionsOpen,
  onAddVirtualMeter,
  onOpenVirtualMeterGuide
}: SearchControlsProps) {
  // Show bulk actions only when meters are selected
  const showBulkActions = selectedMeters.length > 0;
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  
  return (
    <div className="flex flex-col md:flex-row gap-4 mb-4">
      <div className={`flex-1 relative ${isSearchFocused ? 'z-10' : ''}`}>
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-500" aria-hidden="true" />
        </div>
        
        <input
          type="text"
          placeholder="Search by meter name, location, status..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={() => setIsSearchFocused(true)}
          onBlur={() => setIsSearchFocused(false)}
          className={`pl-10 pr-10 block w-full h-12 rounded-lg border ${
            isSearchFocused 
              ? 'border-blue-500 ring-2 ring-blue-200' 
              : searchTerm 
                ? 'border-gray-400' 
                : 'border-gray-300'
          } shadow-sm focus:outline-none sm:text-sm transition-all duration-200`}
          aria-label="Search meters"
        />
        
        {searchTerm && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <button 
              onClick={() => setSearchTerm('')}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
              aria-label="Clear search"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>

      <div className="flex gap-3 items-center">
        {showBulkActions && (
          <div 
            className={`bg-gray-100 px-4 py-2 rounded-lg flex items-center justify-between shadow-sm border border-gray-200 ${bulkActionsOpen ? 'ring-2 ring-blue-200' : ''}`}
            onClick={() => setBulkActionsOpen(!bulkActionsOpen)}
          >
            <span className="text-sm font-medium text-gray-700 mr-3">
              {selectedMeters.length} selected
            </span>
            <div className="flex space-x-2">
              <button className="px-3 py-1.5 text-xs bg-white border border-gray-300 rounded-md shadow-sm text-gray-700 hover:bg-gray-50 transition-colors">
                Export
              </button>
              <button className="px-3 py-1.5 text-xs bg-red-50 border border-red-200 rounded-md shadow-sm text-red-700 hover:bg-red-100 transition-colors">
                Delete
              </button>
            </div>
          </div>
        )}
        
        <button
          onClick={onOpenVirtualMeterGuide}
          className="inline-flex items-center px-4 py-2.5 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-400 transition-all duration-200"
          aria-label="View virtual meter guide"
        >
          <HelpCircle className="mr-1.5 h-4 w-4" />
          Guide
        </button>
        
        <button
          onClick={onAddVirtualMeter}
          className="inline-flex items-center px-5 py-2.5 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
          aria-label="Add virtual meter"
        >
          <Plus className="mr-1.5 h-4 w-4" />
          Add Virtual Meter
        </button>
      </div>
    </div>
  );
});

SearchControls.displayName = 'SearchControls';

export default SearchControls;
