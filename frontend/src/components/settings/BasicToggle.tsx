import React from 'react';
import './toggle.css';

interface BasicToggleProps {
  checked: boolean;
  onChange: () => void;
  id?: string;
}

const BasicToggle: React.FC<BasicToggleProps> = ({ checked, onChange, id }) => {
  return (
    <label className="toggle-switch">
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        id={id}
      />
      <span className="toggle-slider"></span>
    </label>
  );
};

export default BasicToggle;
