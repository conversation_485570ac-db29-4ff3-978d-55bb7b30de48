import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { ChevronDownIcon, ChevronUpIcon, Loader2, ArrowLeft } from 'lucide-react';
import { getMockRateConfigs } from '@/utils/mockBillingData';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorDisplay from '@/components/common/ErrorDisplay';

interface ElectricityRateConfigurationProps {
  onBack?: () => void;
}

export const ElectricityRateConfiguration: React.FC<ElectricityRateConfigurationProps> = ({ onBack }) => {
  const [rate, setRate] = useState<string>("3.50");
  const [showHistory, setShowHistory] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);

  const defaultDate = () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return format(yesterday, 'yyyy-MM-dd');
  };

  const { data: rateConfigs, isLoading, isError } = useQuery({
    queryKey: ['rateConfigs'],
    queryFn: () => getMockRateConfigs(),
    staleTime: 60000,
  });

  const handleSave = () => {
    setIsSaving(true);
    console.log("Saving rate:", rate);
    setTimeout(() => {
      setIsSaving(false);
      // Show success toast or notification here
    }, 1500);
  };

  if (isLoading) return <LoadingSpinner />;
  if (isError) return <ErrorDisplay message="Failed to load rate data" />;

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Electricity Rate Configuration</CardTitle>
          {onBack && (
            <button
              onClick={onBack}
              className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
            >
              <ArrowLeft size={16} />
              Back to Settings
            </button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <p className="text-sm text-blue-800">
              Configure the electricity rate used for billing calculations. This rate will be applied to all energy consumption measurements.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="flat-rate" className="block text-sm font-medium text-gray-700 mb-1">
                Flat Rate (THB/kWh)
              </label>
              <div className="flex">
                <input
                  id="flat-rate"
                  type="number"
                  step="0.01"
                  min="0"
                  value={rate}
                  onChange={(e) => setRate(e.target.value)}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
              </div>
            </div>

            <div>
              <label htmlFor="effective-date" className="block text-sm font-medium text-gray-700 mb-1">
                Effective From
              </label>
              <input
                id="effective-date"
                type="date"
                defaultValue={defaultDate()}
                max={format(new Date(), 'yyyy-MM-dd')}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              />
            </div>
          </div>

          <div className="pt-4">
            <Button onClick={handleSave} disabled={isSaving} className="bg-blue-600 hover:bg-blue-700">
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Rate Configuration'
              )}
            </Button>
          </div>

          {/* Rate History Section */}
          <div className="pt-6 mt-2 border-t border-gray-200">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHistory(!showHistory)}
              className="flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800"
            >
              {showHistory ? (
                <>
                  <ChevronUpIcon className="h-4 w-4 mr-1" />
                  Hide Rate History
                </>
              ) : (
                <>
                  <ChevronDownIcon className="h-4 w-4 mr-1" />
                  View Rate History
                </>
              )}
            </Button>

            {showHistory && (
              <div className="mt-4 rounded-md border border-gray-200 overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Rate (THB/kWh)</TableHead>
                      <TableHead>Effective From</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {rateConfigs?.map((rateConfig) => (
                      <TableRow
                        key={rateConfig.id}
                        className={rateConfig.is_active ? "bg-blue-50" : ""} // Highlight active row
                      >
                        <TableCell>{rateConfig.rate_per_kwh.toFixed(2)}</TableCell>
                        <TableCell>{format(new Date(rateConfig.effective_from), 'MMM d, yyyy')}</TableCell>
                        <TableCell>
                          <Badge variant={rateConfig.is_active ? "default" : "secondary"}>
                            {rateConfig.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};