import React from 'react';
import { Check, Info } from 'lucide-react';
import type { UserRole } from '../../types';

interface PermissionMatrixProps {
  roles: Record<UserRole, {
    label: string;
    description: string;
    permissions: string[];
    restrictions: string[];
    canManage: UserRole[];
  }>;
  onRoleClick?: (role: UserRole) => void;
}

export function PermissionMatrix({ roles, onRoleClick }: PermissionMatrixProps) {
  // Extract all unique permissions across all roles
  const allPermissions = new Set<string>();
  
  Object.values(roles).forEach(role => {
    role.permissions.forEach(permission => {
      allPermissions.add(permission);
    });
  });

  // Sort permissions alphabetically for consistency
  const sortedPermissions = Array.from(allPermissions).sort();
  
  // Group similar permissions for better organization with color codes
  const permissionGroups: Record<string, { 
    permissions: string[], 
    color: { bg: string; text: string; border: string; lightBg: string; iconColor: string } 
  }> = {
    'User Management': {
      permissions: sortedPermissions.filter(p => p.toLowerCase().includes('user') || p.toLowerCase().includes('manage')),
      color: { bg: 'bg-blue-50', text: 'text-blue-800', border: 'border-blue-200', lightBg: 'bg-blue-50/30', iconColor: 'text-blue-600' }
    },
    'Reporting': {
      permissions: sortedPermissions.filter(p => p.toLowerCase().includes('report') || p.toLowerCase().includes('generate')),
      color: { bg: 'bg-amber-50', text: 'text-amber-800', border: 'border-amber-200', lightBg: 'bg-amber-50/30', iconColor: 'text-amber-600' }
    },
    'Configuration': {
      permissions: sortedPermissions.filter(p => p.toLowerCase().includes('config') || p.toLowerCase().includes('setting')),
      color: { bg: 'bg-purple-50', text: 'text-purple-800', border: 'border-purple-200', lightBg: 'bg-purple-50/30', iconColor: 'text-purple-600' }
    },
    'Access & Viewing': {
      permissions: sortedPermissions.filter(p => (p.toLowerCase().includes('access') || p.toLowerCase().includes('view')) && 
                                          !p.toLowerCase().includes('report')),
      color: { bg: 'bg-green-50', text: 'text-green-800', border: 'border-green-200', lightBg: 'bg-green-50/30', iconColor: 'text-green-600' }
    },
    'System': {
      permissions: sortedPermissions.filter(p => !p.toLowerCase().includes('user') && 
                                            !p.toLowerCase().includes('manage') && 
                                            !p.toLowerCase().includes('access') && 
                                            !p.toLowerCase().includes('view') && 
                                            !p.toLowerCase().includes('config') && 
                                            !p.toLowerCase().includes('setting') && 
                                            !p.toLowerCase().includes('report') && 
                                            !p.toLowerCase().includes('generate')),
      color: { bg: 'bg-gray-50', text: 'text-gray-800', border: 'border-gray-200', lightBg: 'bg-gray-50/30', iconColor: 'text-gray-600' }
    }
  };

  return (
    <div className="overflow-x-auto">
      <div className="bg-white rounded-xl p-4 shadow-sm border border-[#EDEFF9] min-w-[800px]">
        <table className="w-full border-collapse">
          <thead>
            <tr>
              <th className="text-left p-2 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-600 sticky left-0 z-10">
                Permissions / Roles
              </th>
              {Object.entries(roles).map(([roleKey, roleInfo]) => (
                <th 
                  key={roleKey} 
                  className="p-2 bg-gray-50 border-b border-gray-200 text-sm font-medium text-center text-gray-600"
                  onClick={() => onRoleClick && onRoleClick(roleKey as UserRole)}
                >
                  <div className="flex flex-col items-center justify-center cursor-pointer hover:text-blue-600 transition-colors">
                    <span>{roleInfo.label}</span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {Object.entries(permissionGroups).map(([groupName, groupData]) => (
              groupData.permissions.length > 0 && (
                <React.Fragment key={groupName}>
                  <tr>
                    <td 
                      colSpan={Object.keys(roles).length + 1}
                      className={`text-left px-3 py-2 text-sm font-medium ${groupData.color.bg} ${groupData.color.text} border-b ${groupData.color.border}`}
                    >
                      <div className="flex items-center space-x-2">
                        <span className={groupData.color.iconColor}>
                          <Info size={16} />
                        </span>
                        <span>{groupName}</span>
                      </div>
                    </td>
                  </tr>
                  {groupData.permissions.map(permission => (
                    <tr key={permission} className="hover:bg-gray-50">
                      <td className="text-left p-2 border-b border-gray-100 text-sm text-gray-700 sticky left-0 bg-white">
                        {permission}
                      </td>
                      {Object.entries(roles).map(([roleKey, roleInfo]) => (
                        <td key={`${roleKey}-${permission}`} className="p-2 border-b border-gray-100 text-center">
                          {roleInfo.permissions.includes(permission) ? (
                            <div className="flex justify-center">
                              <Check size={18} className="text-green-600" />
                            </div>
                          ) : (
                            <span className="text-gray-300">-</span>
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </React.Fragment>
              )
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
