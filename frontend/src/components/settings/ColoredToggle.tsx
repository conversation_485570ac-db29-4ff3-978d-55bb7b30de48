import React from 'react';

interface ColoredToggleProps {
  checked: boolean;
  onChange: () => void;
  id?: string;
}

const ColoredToggle: React.FC<ColoredToggleProps> = ({ checked, onChange, id }) => {
  return (
    <button
      onClick={onChange}
      className={`
        relative inline-flex items-center h-8 rounded-full w-16 
        transition-colors focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2
        ${checked ? 'bg-blue-600' : 'bg-gray-200'}
      `}
      role="switch"
      aria-checked={checked}
      id={id}
    >
      <span
        className={`
          inline-block w-7 h-7 transform rounded-full bg-white shadow-md
          transition-transform duration-200 ease-in-out
          ${checked ? 'translate-x-9 border-2 border-blue-500' : 'translate-x-1'}
        `}
      />
    </button>
  );
};

export default ColoredToggle;
