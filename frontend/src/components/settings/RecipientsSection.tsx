import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import CustomBlueToggle from './CustomBlueToggle';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Plus, X, User } from 'lucide-react';

// Mock data for recipients
export interface Recipient {
  id: string;
  name: string;
  email: string;
  enabled: boolean;
  initials: string;
}

const mockRecipients: Recipient[] = [
  { id: '1', name: '<PERSON>', email: '<EMAIL>', enabled: true, initials: 'J<PERSON>' },
  { id: '2', name: '<PERSON>', email: '<EMAIL>', enabled: true, initials: '<PERSON>' },
  { id: '3', name: '<PERSON>', email: '<EMAIL>', enabled: false, initials: 'DC' }
];

interface RecipientsSectionProps {
  className?: string;
}

const RecipientsSection: React.FC<RecipientsSectionProps> = ({ className = '' }) => {
  const [recipients, setRecipients] = useState<Recipient[]>(mockRecipients);
  const [newRecipient, setNewRecipient] = useState({ name: '', email: '' });
  const [showAddRecipient, setShowAddRecipient] = useState(false);
  const [emailError, setEmailError] = useState('');

  // Validate email format
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(email);

    if (!isValid && email) {
      setEmailError('Please enter a valid email address');
    } else {
      setEmailError('');
    }

    return isValid;
  };

  // Toggle recipient enabled status
  const handleToggleRecipient = (id: string) => {
    setRecipients(prev =>
      prev.map(recipient =>
        recipient.id === id ? { ...recipient, enabled: !recipient.enabled } : recipient
      )
    );
  };

  // Add new recipient
  const handleAddRecipient = () => {
    if (newRecipient.name && newRecipient.email && validateEmail(newRecipient.email)) {
      // Generate initials from name
      const initials = newRecipient.name
        .split(' ')
        .map(part => part[0])
        .join('')
        .substring(0, 2)
        .toUpperCase();

      const newRecipientObj: Recipient = {
        id: `new-${Date.now()}`,
        name: newRecipient.name,
        email: newRecipient.email,
        enabled: true,
        initials
      };

      setRecipients([...recipients, newRecipientObj]);
      setNewRecipient({ name: '', email: '' });
      setShowAddRecipient(false);
      setEmailError('');
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-2 mb-2">
        <User className="h-5 w-5 text-blue-600" />
        <h3 className="text-base font-medium">Recipients</h3>
      </div>

      <div className="space-y-3 max-h-[300px] overflow-y-auto">
        {recipients.map((recipient) => (
          <div key={recipient.id} className={`flex items-center justify-between border-b pb-3 ${recipient.enabled ? 'bg-blue-100 rounded-md p-2 -mx-2 border-l-4 border-blue-600' : ''}`}>
            <div className="flex items-center gap-3">
              <div className={`h-8 w-8 rounded-full ${recipient.enabled ? 'bg-blue-600 text-white' : 'bg-gray-400 text-gray-700'} flex items-center justify-center font-medium text-xs`}>
                {recipient.initials}
              </div>
              <div>
                <h3 className="font-medium text-sm">{recipient.name}</h3>
                <p className="text-xs text-gray-500">{recipient.email}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <span className={`text-xs font-semibold px-2 py-1 rounded-full ${recipient.enabled ? 'text-white bg-blue-600' : 'text-gray-500 bg-gray-100'}`}>
                {recipient.enabled ? 'Active' : 'Inactive'}
              </span>
              <CustomBlueToggle
                checked={recipient.enabled}
                onChange={() => handleToggleRecipient(recipient.id)}
              />
            </div>
          </div>
        ))}

        {showAddRecipient ? (
          <div className="border rounded-md p-4 space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Add New Recipient</h3>
              <button
                onClick={() => {
                  setShowAddRecipient(false);
                  setNewRecipient({ name: '', email: '' });
                  setEmailError('');
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            <div className="space-y-3">
              <div>
                <Label htmlFor="name" className="text-sm">Name</Label>
                <Input
                  id="name"
                  value={newRecipient.name}
                  onChange={(e) => setNewRecipient({...newRecipient, name: e.target.value})}
                  placeholder="Full Name"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="email" className="text-sm">Email</Label>
                <Input
                  id="email"
                  value={newRecipient.email}
                  onChange={(e) => {
                    setNewRecipient({...newRecipient, email: e.target.value});
                    if (emailError) validateEmail(e.target.value);
                  }}
                  onBlur={() => validateEmail(newRecipient.email)}
                  placeholder="<EMAIL>"
                  className="mt-1"
                />
                {emailError && <p className="text-xs text-red-500 mt-1">{emailError}</p>}
              </div>
            </div>
            <Button
              onClick={handleAddRecipient}
              disabled={!newRecipient.name || !newRecipient.email || !!emailError}
              className="w-full mt-2"
            >
              Add Recipient
            </Button>
          </div>
        ) : (
          <Button
            variant="outline"
            className="w-full flex items-center justify-center text-sm gap-1"
            onClick={() => setShowAddRecipient(true)}
          >
            <Plus className="h-3.5 w-3.5" />
            Add Recipient
          </Button>
        )}
      </div>
    </div>
  );
};

export default RecipientsSection;
