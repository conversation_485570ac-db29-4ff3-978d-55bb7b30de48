import React from 'react';

interface InlineToggleProps {
  checked: boolean;
  onChange: () => void;
  id?: string;
}

const InlineToggle: React.FC<InlineToggleProps> = ({ checked, onChange, id }) => {
  const toggleStyles = {
    container: {
      position: 'relative' as const,
      display: 'inline-block',
      width: '60px',
      height: '30px',
    },
    input: {
      opacity: 0,
      width: 0,
      height: 0,
    },
    slider: {
      position: 'absolute' as const,
      cursor: 'pointer',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: checked ? '#2563eb' : '#ccc',
      transition: '.4s',
      borderRadius: '34px',
      border: checked ? '2px solid #1d4ed8' : 'none',
    },
    sliderBefore: {
      position: 'absolute' as const,
      content: '""',
      height: '22px',
      width: '22px',
      left: checked ? '32px' : '4px',
      bottom: '4px',
      backgroundColor: checked ? '#e0f2fe' : 'white',
      transition: '.4s',
      borderRadius: '50%',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
      border: checked ? '1px solid #2563eb' : 'none',
    },
  };

  return (
    <div style={toggleStyles.container}>
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        id={id}
        style={toggleStyles.input}
      />
      <div style={toggleStyles.slider}>
        <div style={toggleStyles.sliderBefore}></div>
      </div>
    </div>
  );
};

export default InlineToggle;
