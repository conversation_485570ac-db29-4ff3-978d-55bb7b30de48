import React from 'react';

interface ButtonToggleProps {
  checked: boolean;
  onChange: () => void;
  id?: string;
}

const ButtonToggle: React.FC<ButtonToggleProps> = ({ checked, onChange, id }) => {
  return (
    <button
      id={id}
      onClick={onChange}
      className={`
        w-16 h-8 rounded-full relative transition-colors duration-300
        ${checked ? 'bg-blue-600' : 'bg-gray-300'}
      `}
      aria-checked={checked}
      role="switch"
    >
      <span 
        className={`
          absolute top-1 w-6 h-6 rounded-full bg-white shadow-md
          transition-transform duration-300
          ${checked ? 'translate-x-9' : 'translate-x-1'}
        `}
      />
    </button>
  );
};

export default ButtonToggle;
