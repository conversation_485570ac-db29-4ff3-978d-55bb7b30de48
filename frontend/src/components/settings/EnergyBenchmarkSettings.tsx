import React, { useState, useEffect } from 'react';
import { Save, TrendingDown, Calendar, Info } from 'lucide-react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';

interface BenchmarkSettings {
  sbtiTarget: number;
  pfmTarget: number;
  baselineYear: number;
}

const BENCHMARK_SETTINGS_KEY = 'energy-benchmark-settings';

interface EnergyBenchmarkSettingsProps {
  onBack?: () => void;
}

export function EnergyBenchmarkSettings({ onBack }: EnergyBenchmarkSettingsProps = {}) {
  const currentYear = new Date().getFullYear();
  const availableYears = Array.from({ length: 5 }, (_, i) => currentYear - i - 1); // Last 5 years

  const [settings, setSettings] = useState<BenchmarkSettings>(() => {
    const saved = localStorage.getItem(BENCHMARK_SETTINGS_KEY);
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (e) {
        console.error('Failed to parse benchmark settings:', e);
      }
    }
    return {
      sbtiTarget: 850000, // Default 850 MWh
      pfmTarget: 800000,  // Default 800 MWh
      baselineYear: 2023  // Default baseline year
    };
  });

  const [isSaving, setIsSaving] = useState(false);
  const [showSaveSuccess, setShowSaveSuccess] = useState(false);

  useEffect(() => {
    // Auto-save to localStorage whenever settings change
    localStorage.setItem(BENCHMARK_SETTINGS_KEY, JSON.stringify(settings));
  }, [settings]);

  const handleSave = () => {
    setIsSaving(true);
    // Simulate save operation
    setTimeout(() => {
      setIsSaving(false);
      setShowSaveSuccess(true);
      // Dispatch custom event to notify other components
      window.dispatchEvent(new CustomEvent('benchmarkSettingsUpdated', { detail: settings }));
      setTimeout(() => setShowSaveSuccess(false), 3000);
    }, 500);
  };

  const handleTargetChange = (field: 'sbtiTarget' | 'pfmTarget', value: string) => {
    const numValue = parseInt(value) || 0;
    setSettings(prev => ({
      ...prev,
      [field]: numValue
    }));
  };

  const handleBaselineYearChange = (year: number) => {
    setSettings(prev => ({
      ...prev,
      baselineYear: year
    }));
  };

  return (
    <div className="space-y-6">
      {/* SBTi Target Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="w-5 h-5 text-blue-600" />
            SBTi Target
          </CardTitle>
          <CardDescription>
            Science Based Targets initiative (SBTi) annual energy consumption target
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Annual Target (kWh)
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={settings.sbtiTarget}
                  onChange={(e) => handleTargetChange('sbtiTarget', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter target in kWh"
                />
                <div className="absolute right-3 top-2.5 text-sm text-gray-500">
                  {(settings.sbtiTarget / 1000000).toFixed(1)} MWh
                </div>
              </div>
              <p className="mt-1 text-xs text-gray-500">
                This target will be displayed as a reference line in the energy benchmark chart
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* PFM Target Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingDown className="w-5 h-5 text-purple-600" />
            PFM Target
          </CardTitle>
          <CardDescription>
            Property Facility Management annual energy consumption target
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Annual Target (kWh)
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={settings.pfmTarget}
                  onChange={(e) => handleTargetChange('pfmTarget', e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter target in kWh"
                />
                <div className="absolute right-3 top-2.5 text-sm text-gray-500">
                  {(settings.pfmTarget / 1000000).toFixed(1)} MWh
                </div>
              </div>
              <p className="mt-1 text-xs text-gray-500">
                This target will be displayed as a reference line in the energy benchmark chart
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Baseline Year Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-green-600" />
            Baseline Year
          </CardTitle>
          <CardDescription>
            Select the baseline year for average consumption calculations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Baseline Year for Averages
              </label>
              <select
                value={settings.baselineYear}
                onChange={(e) => handleBaselineYearChange(parseInt(e.target.value))}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {availableYears.map(year => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
              <p className="mt-1 text-xs text-gray-500">
                The average consumption for this year will be used as a reference in charts
              </p>
            </div>

            <div className="p-3 bg-blue-50 rounded-lg flex items-start gap-2">
              <Info className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-blue-800">
                <p className="font-medium mb-1">About Baseline Year</p>
                <p>The baseline year is used to calculate average energy consumption, which serves as a reference point for comparing current performance and setting reduction targets.</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between items-center">
        {onBack && (
          <button
            onClick={onBack}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            <span className="text-sm font-medium">Back to Settings</span>
          </button>
        )}
        <button
          onClick={handleSave}
          disabled={isSaving}
          className={`px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2 transition-colors ${!onBack ? 'ml-auto' : ''}`}
        >
          {isSaving ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save size={16} />
              Save Settings
            </>
          )}
        </button>
      </div>

      {/* Success Message */}
      {showSaveSuccess && (
        <div className="fixed bottom-4 right-4 bg-green-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2 animate-in slide-in-from-bottom-2">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          Settings saved successfully!
        </div>
      )}
    </div>
  );
}

// Export function to get current settings
export function getBenchmarkSettings(): BenchmarkSettings {
  const saved = localStorage.getItem(BENCHMARK_SETTINGS_KEY);
  if (saved) {
    try {
      return JSON.parse(saved);
    } catch (e) {
      console.error('Failed to parse benchmark settings:', e);
    }
  }
  return {
    sbtiTarget: 850000,
    pfmTarget: 800000,
    baselineYear: 2023
  };
}