import { memo } from 'react';
import { TrendingUp, TrendingDown, ChevronRight } from 'lucide-react';
import { formatNumber } from '../../lib/utils/formatters';
import { Equipment, EQUIPMENT_ICONS } from './EquipmentSelector';

interface TimeSlot {
  time: string;
  value: number;
}

interface ComparisonData {
  current: TimeSlot[];
  previous: TimeSlot[];
  currentTotal: number;
  previousTotal: number;
  unit: string;
  percentChange: number;
}

interface EquipmentComparisonProps {
  equipment: Equipment;
  data: ComparisonData;
  currentPeriod: string;
  previousPeriod: string;
  onViewDetails?: () => void;
}

// Mock data generator for preview purposes (will be replaced with real API calls)
export function generateMockComparisonData(equipmentId: string): ComparisonData {
  // Generate realistic hourly data for building equipment
  const generateHourlyData = () => {
    const result: TimeSlot[] = [];
    const baseValue = Math.random() * 50 + 20; // Base consumption between 20-70 kW
    
    for (let hour = 0; hour < 24; hour++) {
      // Create a pattern where usage increases during work hours
      let modifier = 1;
      if (hour >= 8 && hour <= 18) {
        modifier = 1.5 + Math.sin((hour - 8) * Math.PI / 10) * 0.5; // Peak at midday
      } else if (hour >= 19 && hour <= 22) {
        modifier = 1.2; // Evening hours
      } else {
        modifier = 0.6; // Night hours (lower usage)
      }
      
      // Add some noise
      const noise = (Math.random() * 0.4) - 0.2; // ±20% noise
      const value = baseValue * modifier * (1 + noise);
      
      result.push({
        time: `${hour}:00`,
        value: Math.round(value * 10) / 10
      });
    }
    
    return result;
  };
  
  const current = generateHourlyData();
  // Create previous with slight variations
  const previous = current.map(slot => ({
    time: slot.time,
    value: slot.value * (Math.random() * 0.3 + 0.85) // 85-115% of current value
  }));
  
  // Calculate totals
  const currentTotal = current.reduce((sum, slot) => sum + slot.value, 0);
  const previousTotal = previous.reduce((sum, slot) => sum + slot.value, 0);
  
  // Calculate percentage change
  const percentChange = ((currentTotal - previousTotal) / previousTotal) * 100;
  
  return {
    current,
    previous,
    currentTotal,
    previousTotal,
    unit: equipmentId.includes('water') ? 'm³' : 'kWh',
    percentChange
  };
}

// Styled mini chart for comparison visualization
function MiniComparisonChart({ data }: { data: ComparisonData }) {
  const maxValue = Math.max(
    ...data.current.map(d => d.value),
    ...data.previous.map(d => d.value)
  );
  
  return (
    <div className="h-24 w-full flex items-end gap-1">
      {data.current.map((slot, index) => {
        const currentHeight = (slot.value / maxValue) * 100;
        const previousHeight = (data.previous[index]?.value / maxValue) * 100 || 0;
        
        // Only show every third hour label for clarity
        const showLabel = index % 3 === 0;
        
        return (
          <div key={index} className="flex-1 flex flex-col items-center">
            <div className="relative w-full h-[85%] flex items-end">
              {/* Previous period bar */}
              <div 
                style={{ height: `${previousHeight}%` }}
                className="w-[40%] bg-gray-300 opacity-60 rounded-sm"
              />
              {/* Current period bar */}
              <div 
                style={{ height: `${currentHeight}%` }}
                className="w-[40%] ml-1 bg-blue-500 rounded-sm"
              />
            </div>
            {showLabel && (
              <span className="text-[9px] text-gray-500 mt-1">{slot.time.split(':')[0]}h</span>
            )}
          </div>
        );
      })}
    </div>
  );
}

// Main comparison component
function EquipmentComparison({ 
  equipment, 
  data, 
  currentPeriod, 
  previousPeriod,
  onViewDetails 
}: EquipmentComparisonProps) {
  const Icon = EQUIPMENT_ICONS[equipment.type];
  const isIncrease = data.percentChange > 0;
  
  return (
    <div className="bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden">
      <div className="p-4 border-b border-gray-100">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <div className={`p-2 rounded-lg ${isIncrease ? 'bg-red-50' : 'bg-green-50'}`}>
              <Icon size={18} className={isIncrease ? 'text-red-500' : 'text-green-500'} />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{equipment.name}</h3>
              <p className="text-xs text-gray-500">{equipment.location}</p>
            </div>
          </div>
          
          <div className="flex items-center">
            <span className="text-xs bg-blue-50 text-primary-blue px-2 py-0.5 rounded-full">
              {equipment.type.toUpperCase()}
            </span>
          </div>
        </div>
      </div>
      
      <div className="p-4">
        <div className="flex justify-between mb-2">
          <div className="text-xs font-medium text-gray-500">
            Consumption Comparison
          </div>
          <div className="text-xs text-gray-400 flex items-center gap-1">
            <span className="block w-2 h-2 bg-blue-500 rounded-full"></span> {currentPeriod}
            <span className="mx-1">vs</span>
            <span className="block w-2 h-2 bg-gray-300 rounded-full"></span> {previousPeriod}
          </div>
        </div>
        
        <MiniComparisonChart data={data} />
        
        <div className="flex justify-between mt-4 mb-2 border-t border-gray-100 pt-3">
          <div>
            <div className="text-xs text-gray-500">Current Total</div>
            <div className="text-lg font-semibold text-gray-900">
              {formatNumber(data.currentTotal)} <span className="text-xs font-normal">{data.unit}</span>
            </div>
          </div>
          
          <div>
            <div className="text-xs text-gray-500">Previous Total</div>
            <div className="text-lg font-semibold text-gray-600">
              {formatNumber(data.previousTotal)} <span className="text-xs font-normal">{data.unit}</span>
            </div>
          </div>
          
          <div>
            <div className="text-xs text-gray-500">Change</div>
            <div className={`text-lg font-semibold flex items-center ${
              isIncrease ? 'text-red-500' : 'text-green-500'
            }`}>
              {isIncrease ? 
                <TrendingUp size={14} className="mr-1" /> : 
                <TrendingDown size={14} className="mr-1" />
              }
              {Math.abs(data.percentChange).toFixed(1)}%
            </div>
          </div>
        </div>
        
        {onViewDetails && (
          <div className="mt-3 text-right">
            <button 
              onClick={onViewDetails}
              className="text-xs text-primary-blue hover:text-primary-blue/80 font-medium flex items-center gap-1 ml-auto transition-colors"
            >
              View Full Analysis
              <ChevronRight size={14} />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default memo(EquipmentComparison);
