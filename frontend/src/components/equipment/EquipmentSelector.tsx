import React, { useState, useRef, useEffect } from 'react';
import { Fan, ChevronDown, Lightbulb, Droplet, Sprout, Server, AirVent } from 'lucide-react';

// Equipment type definitions
export interface Equipment {
  id: string;
  type: EquipmentType;
  name: string;
  location: string;
}

export type EquipmentType = 'ahu' | 'chiller' | 'lighting' | 'water' | 'datacenter' | 'other';

// Mock equipment data - in a real app, this would come from an API
const EQUIPMENT_DATA: Record<EquipmentType, Equipment[]> = {
  ahu: [
    { id: 'ahu-23', type: 'ahu', name: 'AHU', location: 'Floor 23' },
    { id: 'ahu-24', type: 'ahu', name: 'AHU', location: 'Floor 24' },
    { id: 'ahu-25', type: 'ahu', name: 'AHU', location: 'Floor 25' },
    { id: 'ahu-26', type: 'ahu', name: 'AHU', location: 'Floor 26' },
  ],
  chiller: [
    { id: 'chiller-1', type: 'chiller', name: 'Chiller', location: 'Basement 1' },
    { id: 'chiller-2', type: 'chiller', name: 'Chiller', location: 'Basement 2' },
  ],
  lighting: [
    { id: 'lighting-common', type: 'lighting', name: 'Common Areas', location: 'All Floors' },
    { id: 'lighting-23', type: 'lighting', name: 'Lighting', location: 'Floor 23' },
    { id: 'lighting-24', type: 'lighting', name: 'Lighting', location: 'Floor 24' },
  ],
  water: [
    { id: 'water-pumps', type: 'water', name: 'Water Pumps', location: 'Utility Room' },
    { id: 'water-heating', type: 'water', name: 'Water Heating', location: 'Utility Room' },
  ],
  datacenter: [
    { id: 'datacenter-1', type: 'datacenter', name: 'Data Center', location: 'Floor 22' },
  ],
  other: [
    { id: 'elevators', type: 'other', name: 'Elevators', location: 'All Floors' },
  ]
};

// Equipment type icons
export const EQUIPMENT_ICONS: Record<EquipmentType, any> = {
  ahu: AirVent,
  chiller: Fan,
  lighting: Lightbulb,
  water: Droplet,
  datacenter: Server,
  other: Sprout,
};

interface EquipmentSelectorProps {
  selectedEquipment: Equipment | null;
  onEquipmentSelect: (equipment: Equipment) => void;
}

export function EquipmentSelector({ selectedEquipment, onEquipmentSelect }: EquipmentSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedType, setSelectedType] = useState<EquipmentType | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleTypeSelect = (type: EquipmentType) => {
    setSelectedType(type);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-1.5 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors shadow-sm"
      >
        {selectedEquipment ? (
          <>
            {React.createElement(EQUIPMENT_ICONS[selectedEquipment.type], { 
              size: 16, 
              className: "text-primary-blue" 
            })}
            <span className="text-sm font-medium text-primary-blue">
              {selectedEquipment.name} - {selectedEquipment.location}
            </span>
          </>
        ) : (
          <>
            <AirVent size={16} className="text-gray-400" />
            <span className="text-sm text-gray-700">Select Equipment</span>
          </>
        )}
        <ChevronDown size={16} className="text-gray-400" />
      </button>

      {isOpen && (
        <div className="absolute left-0 mt-1 w-72 bg-white rounded-lg border border-gray-200 shadow-lg z-30 p-2">
          <div className="flex flex-wrap gap-1 mb-2 border-b border-gray-100 pb-2">
            {Object.keys(EQUIPMENT_DATA).map((type) => {
              const equipType = type as EquipmentType;
              const Icon = EQUIPMENT_ICONS[equipType];
              return (
                <button
                  key={type}
                  onClick={() => handleTypeSelect(equipType)}
                  className={`flex items-center gap-1.5 px-2 py-1 rounded-md text-xs ${
                    selectedType === equipType
                      ? 'bg-blue-50 text-primary-blue border border-blue-100'
                      : 'bg-gray-50 text-gray-700 border border-gray-100 hover:bg-gray-100'
                  }`}
                >
                  <Icon size={12} />
                  {equipType.charAt(0).toUpperCase() + equipType.slice(1)}
                </button>
              );
            })}
          </div>

          <div className="max-h-60 overflow-y-auto">
            {selectedType && EQUIPMENT_DATA[selectedType].map((equipment) => (
              <button
                key={equipment.id}
                onClick={() => {
                  onEquipmentSelect(equipment);
                  setIsOpen(false);
                }}
                className="w-full flex items-center px-3 py-2 text-sm hover:bg-blue-50 rounded-md transition-colors"
              >
                <div className="flex items-center gap-2">
                  {React.createElement(EQUIPMENT_ICONS[equipment.type], { 
                    size: 14, 
                    className: "text-gray-500" 
                  })}
                  <div className="text-left">
                    <div className="font-medium">{equipment.name}</div>
                    <div className="text-xs text-gray-500">{equipment.location}</div>
                  </div>
                </div>
              </button>
            ))}

            {!selectedType && (
              <div className="text-center text-gray-500 py-4 text-sm">
                Select a system type above to view equipment
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
