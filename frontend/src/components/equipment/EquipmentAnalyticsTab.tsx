import React, { useState, useEffect, useMemo } from 'react';
import { Calendar, Clock } from 'lucide-react';
import { Equipment, EquipmentSelector, EQUIPMENT_ICONS } from './EquipmentSelector';
import EquipmentComparison, { generateMockComparisonData } from './EquipmentComparison';

interface EquipmentAnalyticsTabProps {
  selectedDate: Date;
  comparisonPeriod: 'day' | 'week' | 'month' | 'year' | 'none';
}

const getComparisonDateString = (date: Date, period: 'day' | 'week' | 'month' | 'year' | 'none'): string => {
  if (period === 'none') return '';
  
  const comparisonDate = new Date(date);
  
  switch (period) {
    case 'day':
      comparisonDate.setDate(comparisonDate.getDate() - 1);
      return 'Yesterday';
    case 'week':
      comparisonDate.setDate(comparisonDate.getDate() - 7);
      return 'Last Week';
    case 'month':
      comparisonDate.setMonth(comparisonDate.getMonth() - 1);
      return 'Last Month';
    case 'year':
      comparisonDate.setFullYear(comparisonDate.getFullYear() - 1);
      return 'Last Year';
    default:
      return '';
  }
};

const EquipmentAnalyticsTab: React.FC<EquipmentAnalyticsTabProps> = ({ 
  selectedDate,
  comparisonPeriod
}) => {
  const [selectedEquipment, setSelectedEquipment] = useState<Equipment | null>(null);
  const [recommendedEquipment, setRecommendedEquipment] = useState<Equipment[]>([]);
  
  // Generate formatted date strings for display
  const currentPeriodStr = useMemo(() => {
    return 'Today';
  }, [selectedDate]);
  
  const comparisonPeriodStr = useMemo(() => {
    return getComparisonDateString(selectedDate, comparisonPeriod);
  }, [selectedDate, comparisonPeriod]);
  
  // In a real app, fetch recommended equipment based on usage patterns
  useEffect(() => {
    // Simulate API call for recommended equipment
    setTimeout(() => {
      setRecommendedEquipment([
        { id: 'ahu-23', type: 'ahu', name: 'AHU', location: 'Floor 23' },
        { id: 'chiller-1', type: 'chiller', name: 'Chiller', location: 'Basement 1' },
        { id: 'lighting-common', type: 'lighting', name: 'Common Areas', location: 'All Floors' }
      ]);
    }, 500);
  }, []);
  
  // For demo purposes, preselect AHU Floor 23 since that was specifically mentioned in the requirement
  useEffect(() => {
    if (recommendedEquipment.length > 0 && !selectedEquipment) {
      setSelectedEquipment(recommendedEquipment[0]);
    }
  }, [recommendedEquipment, selectedEquipment]);
  
  return (
    <div className="mt-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex flex-col">
          <h2 className="text-lg font-semibold text-gray-800 mb-1">Equipment Comparison</h2>
          <p className="text-sm text-gray-500">
            Compare equipment usage between {currentPeriodStr} and {comparisonPeriodStr}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <EquipmentSelector 
            selectedEquipment={selectedEquipment}
            onEquipmentSelect={setSelectedEquipment}
          />
        </div>
      </div>
      
      {selectedEquipment ? (
        <div className="mb-8">
          <EquipmentComparison 
            equipment={selectedEquipment}
            data={generateMockComparisonData(selectedEquipment.id)}
            currentPeriod={currentPeriodStr}
            previousPeriod={comparisonPeriodStr}
            onViewDetails={() => {}}
          />
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
            <h3 className="text-sm font-medium text-primary-blue mb-2 flex items-center gap-2">
              <Clock size={16} />
              Time Comparison Insights
            </h3>
            <p className="text-sm text-gray-700 mb-3">
              {selectedEquipment.name} in {selectedEquipment.location} shows significant usage pattern differences between {currentPeriodStr} and {comparisonPeriodStr}:
            </p>
            <ul className="text-sm text-gray-700 space-y-2 ml-4">
              <li className="flex items-start gap-2">
                <span className="text-primary-blue font-bold mt-0.5">•</span>
                <span>Peak usage occurs between 10:00 - 14:00 on both days, but today's peak is 12% higher</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary-blue font-bold mt-0.5">•</span>
                <span>Overnight usage (22:00 - 06:00) is 18% lower today compared to {comparisonPeriodStr}</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary-blue font-bold mt-0.5">•</span>
                <span>Evening ramp-down starts 45 minutes earlier today than {comparisonPeriodStr}</span>
              </li>
            </ul>
          </div>
        </div>
      ) : (
        <div className="text-center py-8 bg-gray-50 rounded-lg border border-gray-100">
          <p className="text-gray-500">Select equipment to view comparison data</p>
        </div>
      )}
      
      <div className="mt-8">
        <h3 className="text-base font-semibold text-gray-800 mb-4">Recommended Comparisons</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {recommendedEquipment.map(equipment => (
            <div 
              key={equipment.id}
              className={`p-4 rounded-lg border transition-all cursor-pointer ${
                selectedEquipment?.id === equipment.id 
                  ? 'border-primary-blue bg-blue-50'
                  : 'border-gray-100 bg-white hover:border-gray-200'
              }`}
              onClick={() => setSelectedEquipment(equipment)}
            >
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-2">
                  <div className={`p-2 rounded ${
                    selectedEquipment?.id === equipment.id 
                      ? 'bg-blue-100' 
                      : 'bg-gray-50'
                  }`}>
                    {React.createElement(
                      EQUIPMENT_ICONS[equipment.type], 
                      { 
                        size: 16, 
                        className: selectedEquipment?.id === equipment.id 
                          ? 'text-primary-blue' 
                          : 'text-gray-500' 
                      }
                    )}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{equipment.name}</h4>
                    <p className="text-xs text-gray-500">{equipment.location}</p>
                  </div>
                </div>
                
                {selectedEquipment?.id === equipment.id && (
                  <span className="text-xs bg-primary-blue text-white px-2 py-0.5 rounded-full">
                    Selected
                  </span>
                )}
              </div>
              
              <div className="mt-3 text-xs text-gray-500">
                {comparisonPeriod !== 'none' ? (
                  <span className="flex items-center gap-1">
                    <Calendar size={12} />
                    {currentPeriodStr} vs {comparisonPeriodStr}
                  </span>
                ) : (
                  <span>No comparison selected</span>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EquipmentAnalyticsTab;
