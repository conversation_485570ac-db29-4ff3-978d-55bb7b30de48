import React, { useRef, useEffect, useState } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Row } from '@tanstack/react-table';
import { AlarmRule } from '@/types/alarms';
import MemoizedTableRow from './MemoizedTableRow';

interface VirtualizedTableProps {
  rows: Row<AlarmRule>[];
  onViewRule: (rule: AlarmRule) => void;
  onDeleteRule: (id: number) => void;
  isDeleting: boolean;
}

const VirtualizedTable: React.FC<VirtualizedTableProps> = ({
  rows,
  onViewRule,
  onDeleteRule,
  isDeleting
}) => {
  const parentRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });

  // Update dimensions when the component mounts or window resizes
  useEffect(() => {
    const updateDimensions = () => {
      if (parentRef.current) {
        const { width, height } = parentRef.current.getBoundingClientRect();
        // Set a reasonable max height (e.g., 400px) or calculate based on available space
        const maxHeight = Math.min(height, 400);
        setDimensions({ width, height: maxHeight });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    
    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, []);

  // Row renderer for react-window
  const RowRenderer = ({ index, style }: { index: number; style: React.CSSProperties }) => {
    const row = rows[index];
    return (
      <div style={style}>
        <MemoizedTableRow
          key={row.id}
          row={row}
          onViewRule={onViewRule}
          onDeleteRule={onDeleteRule}
          isDeleting={isDeleting}
        />
      </div>
    );
  };

  // If no dimensions yet, return a placeholder
  if (dimensions.width === 0 || dimensions.height === 0) {
    return <div ref={parentRef} className="w-full" style={{ minHeight: '200px' }} />;
  }

  return (
    <div ref={parentRef} className="w-full overflow-hidden">
      <List
        height={dimensions.height}
        width={dimensions.width}
        itemCount={rows.length}
        itemSize={56} // Adjust based on your row height
      >
        {RowRenderer}
      </List>
    </div>
  );
};

export default VirtualizedTable;
