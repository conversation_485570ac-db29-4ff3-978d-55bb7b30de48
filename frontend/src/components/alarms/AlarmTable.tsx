import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { format } from 'date-fns';
import { AlertTriangle, Gauge, ExternalLink, Eye, EyeOff, ArrowUpDown, ChevronUp, ChevronDown } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { getNormalizedSeverity, getSeverityBadge } from './utils/severityUtils';
import { getMetricUnit, formatValue } from './utils/metricUtils';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/Button";
import { ActiveAlarm, AlarmHistory } from '@/types/alarms';
import { Drawer } from '@/components/ui/drawer';
import { DrawerHeader } from '@/components/ui/drawer-header';
import { DrawerContent } from '@/components/ui/drawer-content';

interface AlarmTableProps {
  alarms: (ActiveAlarm | AlarmHistory)[];
  onAcknowledge?: (alarm: ActiveAlarm | AlarmHistory) => void;
  isHistoryView?: boolean;
}

const AlarmTable: React.FC<AlarmTableProps> = ({ alarms, onAcknowledge, isHistoryView = false }) => {
  const navigate = useNavigate();
  const [detailOpen, setDetailOpen] = useState(false);
  const [selected, setSelected] = useState<ActiveAlarm | AlarmHistory | null>(null);
  const [showInfoAlarms, setShowInfoAlarms] = useState(false);
  const [showOnlyCritical, setShowOnlyCritical] = useState(false);

  // Sorting state
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'ascending' | 'descending' | null;
  }>({ key: 'trigger_time', direction: 'descending' });

  const handleRowClick = (alarm: ActiveAlarm | AlarmHistory) => {
    setSelected(alarm);
    setDetailOpen(true);
  };

  const handleMeterClick = (e: React.MouseEvent, meterName: string) => {
    e.stopPropagation(); // Prevent row click
    if (meterName) {
      navigate(`/meter-detail?meter=${encodeURIComponent(meterName)}`);
    }
  };

  const handleCloseDrawer = () => {
    setDetailOpen(false);
  };

  // Sorting handler
  const requestSort = (key: string) => {
    let direction: 'ascending' | 'descending' | null = 'ascending';

    if (sortConfig.key === key) {
      if (sortConfig.direction === 'ascending') {
        direction = 'descending';
      } else if (sortConfig.direction === 'descending') {
        direction = null;
      } else {
        direction = 'ascending';
      }
    }

    setSortConfig({ key, direction });
  };

  // Format timestamp for display
  const formatTimestamp = (timestamp: string) => {
    return format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss');
  };

  // Handle ESC key to close drawer
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape' && detailOpen) {
      handleCloseDrawer();
    }
  }, [detailOpen]);

  useEffect(() => {
    // Add event listener when drawer is open
    if (detailOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    // Cleanup event listener
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [detailOpen, handleKeyDown]);

  // Filter and sort alarms based on preferences
  const filteredAndSortedAlarms = useMemo(() => {
    // Start with all alarms
    let filtered = [...alarms];

    // Apply Critical Only filter if enabled
    if (showOnlyCritical) {
      filtered = filtered.filter(alarm => getNormalizedSeverity(alarm.severity) === 'CRITICAL');
    }
    // Otherwise, filter out INFO alarms unless showInfoAlarms is true
    else if (!showInfoAlarms) {
      filtered = filtered.filter(alarm => getNormalizedSeverity(alarm.severity) !== 'INFO');
    }

    // Apply sorting if a sort direction is specified
    if (sortConfig.direction) {
      filtered.sort((a, b) => {
        // Handle different data types for sorting
        switch (sortConfig.key) {
          case 'trigger_time':
            // Date comparison
            return sortConfig.direction === 'ascending'
              ? new Date(a.trigger_time).getTime() - new Date(b.trigger_time).getTime()
              : new Date(b.trigger_time).getTime() - new Date(a.trigger_time).getTime();

          case 'severity':
            // Severity comparison (CRITICAL > WARNING > INFO)
            const severityOrder = { 'CRITICAL': 3, 'WARNING': 2, 'INFO': 1 };
            const severityA = getNormalizedSeverity(a.severity);
            const severityB = getNormalizedSeverity(b.severity);
            return sortConfig.direction === 'ascending'
              ? (severityOrder[severityA] || 0) - (severityOrder[severityB] || 0)
              : (severityOrder[severityB] || 0) - (severityOrder[severityA] || 0);

          case 'rule_name':
            // String comparison
            return sortConfig.direction === 'ascending'
              ? (a.rule_name || '').localeCompare(b.rule_name || '')
              : (b.rule_name || '').localeCompare(a.rule_name || '');

          case 'meter_name':
            // String comparison with null handling
            return sortConfig.direction === 'ascending'
              ? (a.meter_name || '').localeCompare(b.meter_name || '')
              : (b.meter_name || '').localeCompare(a.meter_name || '');

          case 'trigger_value':
            // Numeric comparison
            return sortConfig.direction === 'ascending'
              ? a.trigger_value - b.trigger_value
              : b.trigger_value - a.trigger_value;

          default:
            return 0;
        }
      });
    }

    return filtered;
  }, [alarms, showInfoAlarms, showOnlyCritical, sortConfig]);

  return (
    <div className="relative">
      <div className="flex justify-between items-center mb-3">
        <div className="text-sm text-gray-500">
          {filteredAndSortedAlarms.length} {filteredAndSortedAlarms.length === 1 ? 'alarm' : 'alarms'} displayed
        </div>
        <div className="flex items-center gap-2">
          {/* Critical Only Button */}
          <Button
            variant={showOnlyCritical ? "default" : "outline"}
            size="sm"
            onClick={() => {
              setShowOnlyCritical(!showOnlyCritical);
              // If enabling Critical Only, we should disable Show INFO Alarms
              if (!showOnlyCritical && showInfoAlarms) {
                setShowInfoAlarms(false);
              }
            }}
            className={`flex items-center gap-1.5 text-xs shadow-sm transition-all ${showOnlyCritical ? "bg-red-600 hover:bg-red-700 shadow-red-200" : "border-blue-200 hover:border-blue-300 hover:bg-blue-50"}`}
          >
            <AlertTriangle size={14} />
            Critical Only
          </Button>

          {/* Show/Hide INFO Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setShowInfoAlarms(!showInfoAlarms);
              // If enabling Show INFO, we should disable Critical Only
              if (!showInfoAlarms && showOnlyCritical) {
                setShowOnlyCritical(false);
              }
            }}
            className="flex items-center gap-1.5 text-xs shadow-sm border-blue-200 hover:border-blue-300 hover:bg-blue-50 transition-all"
            disabled={showOnlyCritical} // Disable when Critical Only is active
          >
            {showInfoAlarms ? (
              <>
                <EyeOff size={14} />
                Hide INFO Alarms
              </>
            ) : (
              <>
                <Eye size={14} />
                Show INFO Alarms
              </>
            )}
          </Button>
        </div>
      </div>
      <div className="rounded-lg border border-blue-100/50 bg-white/50 backdrop-blur-sm shadow-sm">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => requestSort('trigger_time')}
              >
                <div className="flex items-center gap-1">
                  Timestamp
                  {sortConfig.key === 'trigger_time' && sortConfig.direction === 'ascending' && <ChevronUp className="h-4 w-4" />}
                  {sortConfig.key === 'trigger_time' && sortConfig.direction === 'descending' && <ChevronDown className="h-4 w-4" />}
                  {(sortConfig.key !== 'trigger_time' || !sortConfig.direction) && <ArrowUpDown className="h-4 w-4 opacity-30" />}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => requestSort('meter_name')}
              >
                <div className="flex items-center gap-1">
                  Meter
                  {sortConfig.key === 'meter_name' && sortConfig.direction === 'ascending' && <ChevronUp className="h-4 w-4" />}
                  {sortConfig.key === 'meter_name' && sortConfig.direction === 'descending' && <ChevronDown className="h-4 w-4" />}
                  {(sortConfig.key !== 'meter_name' || !sortConfig.direction) && <ArrowUpDown className="h-4 w-4 opacity-30" />}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => requestSort('rule_name')}
              >
                <div className="flex items-center gap-1">
                  Rule
                  {sortConfig.key === 'rule_name' && sortConfig.direction === 'ascending' && <ChevronUp className="h-4 w-4" />}
                  {sortConfig.key === 'rule_name' && sortConfig.direction === 'descending' && <ChevronDown className="h-4 w-4" />}
                  {(sortConfig.key !== 'rule_name' || !sortConfig.direction) && <ArrowUpDown className="h-4 w-4 opacity-30" />}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => requestSort('severity')}
              >
                <div className="flex items-center gap-1">
                  Severity
                  {sortConfig.key === 'severity' && sortConfig.direction === 'ascending' && <ChevronUp className="h-4 w-4" />}
                  {sortConfig.key === 'severity' && sortConfig.direction === 'descending' && <ChevronDown className="h-4 w-4" />}
                  {(sortConfig.key !== 'severity' || !sortConfig.direction) && <ArrowUpDown className="h-4 w-4 opacity-30" />}
                </div>
              </TableHead>
              <TableHead
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => requestSort('trigger_value')}
              >
                <div className="flex items-center gap-1">
                  Value
                  {sortConfig.key === 'trigger_value' && sortConfig.direction === 'ascending' && <ChevronUp className="h-4 w-4" />}
                  {sortConfig.key === 'trigger_value' && sortConfig.direction === 'descending' && <ChevronDown className="h-4 w-4" />}
                  {(sortConfig.key !== 'trigger_value' || !sortConfig.direction) && <ArrowUpDown className="h-4 w-4 opacity-30" />}
                </div>
              </TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedAlarms.map((alarm) => (
              <TableRow
                key={alarm.id}
                onClick={() => handleRowClick(alarm)}
                className="cursor-pointer hover:bg-gray-50"
              >
                <TableCell>{formatTimestamp(alarm.trigger_time)}</TableCell>
                <TableCell>
                  {alarm.meter_name ? (
                    <button
                      onClick={(e) => handleMeterClick(e, alarm.meter_name || '')}
                      className="flex items-center gap-1 text-primary-blue hover:underline"
                    >
                      <Gauge size={14} />
                      <span>{alarm.meter_name}</span>
                      <ExternalLink size={12} className="opacity-70" />
                    </button>
                  ) : (
                    <span className="text-gray-500">N/A</span>
                  )}
                </TableCell>
                <TableCell>{alarm.rule_name}</TableCell>
                <TableCell>{getSeverityBadge(alarm.severity)}</TableCell>
                <TableCell>{formatValue(alarm.trigger_value, alarm.metric)} {getMetricUnit(alarm.metric)}</TableCell>
                <TableCell>
                  {!isHistoryView && (
                    <Button variant="outline" size="sm" onClick={(e) => {
                      e.stopPropagation(); // Prevent row click
                      if (onAcknowledge) {
                        onAcknowledge(alarm);
                      } else {
                        console.log('Acknowledge clicked for alarm:', alarm.id);
                      }
                    }}>
                      Acknowledge & Resolve
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Slide-in drawer for alarm details */}
      <Drawer open={detailOpen} onOpenChange={(open) => !open && handleCloseDrawer()} size="lg">
        <div className="flex flex-col h-full">
          {selected && (
            <DrawerHeader onClose={handleCloseDrawer}>
              <h2 className="text-xl font-semibold text-gray-900 mr-2">Alarm Detail</h2>
              <Badge variant={getNormalizedSeverity(selected.severity).toLowerCase() as any} className="capitalize">
                {getNormalizedSeverity(selected.severity)}
              </Badge>
            </DrawerHeader>
          )}
          {selected && (
            <DrawerContent>
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-1">{selected.rule_name}</h3>
                <p className="text-xs text-gray-500">Triggered: {formatTimestamp(selected.trigger_time)}</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-5">
                <div className="space-y-5 divide-y divide-gray-100">
                  {/* Meter Information */}
                  <div className="flex flex-col pt-2">
                    <dt className="text-xs uppercase tracking-wider font-medium text-gray-500">Meter</dt>
                    <dd className="mt-1 text-sm">
                      {selected.meter_name ? (
                        <button
                          onClick={() => {
                            handleCloseDrawer();
                            navigate(`/meter-detail?meter=${encodeURIComponent(selected.meter_name || '')}`);
                          }}
                          className="flex items-center gap-1 text-primary-blue hover:underline"
                        >
                          <Gauge size={14} />
                          <span>{selected.meter_name}</span>
                        </button>
                      ) : (
                        <span className="text-gray-500">N/A</span>
                      )}
                    </dd>
                  </div>
                  {/* Threshold */}
                  <div className="flex flex-col pt-2">
                    <dt className="text-xs uppercase tracking-wider font-medium text-gray-500">Threshold</dt>
                    <dd className="mt-1 text-sm">{formatValue(selected.threshold, selected.metric)} {getMetricUnit(selected.metric)}</dd>
                  </div>
                  {/* Trigger Value */}
                  <div className="flex flex-col pt-2">
                    <dt className="text-xs uppercase tracking-wider font-medium text-gray-500">Trigger Value</dt>
                    <dd className="mt-1 text-sm">{formatValue(selected.trigger_value, selected.metric)} {getMetricUnit(selected.metric)}</dd>
                  </div>
                </div>
                <div className="space-y-5 divide-y divide-gray-100">
                  {/* Timestamps */}
                  <div className="flex flex-col pt-2">
                    <dt className="text-xs uppercase tracking-wider font-medium text-gray-500">Timestamps</dt>
                    <dd className="mt-1 text-sm">Triggered: {formatTimestamp(selected.trigger_time)}</dd>
                  </div>
                  {/* Related Meters */}
                  <div className="flex flex-col pt-2">
                    <dt className="text-xs uppercase tracking-wider font-medium text-gray-500">Related Meters</dt>
                    <dd className="mt-1 text-sm">No related meters found.</dd>
                  </div>
                  {/* Alarm ID */}
                  <div className="flex flex-col pt-2">
                    <dt className="text-xs uppercase tracking-wider font-medium text-gray-500">IDS</dt>
                    <dd className="mt-1 text-sm flex items-center gap-2">Alarm ID: <span>{selected.id}</span></dd>
                  </div>
                </div>
              </div>
              {/* Actions */}
              <div className="flex justify-end mt-8 gap-2">
                <button
                  className="bg-white border border-gray-300 rounded-md px-4 py-2 text-gray-700 hover:bg-gray-50 font-medium"
                  onClick={handleCloseDrawer}
                >
                  Close
                </button>
              </div>
            </DrawerContent>
          )}
        </div>
      </Drawer>
    </div>
  );
};

export default AlarmTable;
