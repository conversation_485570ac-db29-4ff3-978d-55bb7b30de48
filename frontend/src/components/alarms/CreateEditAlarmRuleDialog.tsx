import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { AlarmRule, AlarmRulePayload } from '@/types/alarms';
import { createAlarmRule, updateAlarmRule } from '@/lib/api/alarms';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormMessage,
  FormLabel,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button, Card, ToggleSwitch } from "@/components/ui"; 
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import LoadingSpinner from '../common/LoadingSpinner'; 
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Info } from "lucide-react";

// Define the Zod schema for validation
const AlarmRuleSchema = z.object({
  id: z.number().optional(), // For edit mode
  name: z.string().min(1, "Rule name is required"),
  // Assume metric is fetched/provided elsewhere for now, or add if needed
  parameterId: z.string().min(1, "Parameter is required"), // Use string as value from Select
  conditionType: z.enum(['value_gt', 'value_lt', 'value_eq', 'value_rate_change', 'device_offline', 'custom']),
  thresholdValue: z.number().nullable().optional(), 
  thresholdValueLow: z.number().nullable().optional(), // Added
  severity: z.enum(['critical', 'high', 'medium', 'low', 'info']),
  message: z.string().nullable().optional(),
  triggerDurationSeconds: z.number().min(0).nullable().optional(), // Added
  resetThresholdHysteresis: z.number().min(0).nullable().optional(), // Added
  is_active: z.boolean().optional(),
});

type AlarmRuleFormValues = z.infer<typeof AlarmRuleSchema>;

// Mock API function (replace with actual fetch calls)
// Assuming an API function exists: fetchParameters(): Promise<Parameter[]>
interface Parameter {
  id: number;
  name: string;
  unit: string;
}

interface CreateEditAlarmRuleDialogProps {
  isOpen: boolean;
  onClose: () => void;
  rule?: Partial<AlarmRuleFormValues>; // Use form values type
  parameters: Parameter[]; // Add parameters prop
  // Add onSubmit prop for handling form submission
  onSubmit: (values: AlarmRuleFormValues) => Promise<void>; 
}

const CreateEditAlarmRuleDialog: React.FC<CreateEditAlarmRuleDialogProps> = ({ 
    isOpen, 
    onClose, 
    rule, 
    parameters, // Destructure parameters
    onSubmit // Destructure onSubmit
}) => {
  const queryClient = useQueryClient();
  const isEditMode = !!rule?.id;

  // Initialize form with default values or existing rule data
  const form = useForm<AlarmRuleFormValues>({
    resolver: zodResolver(AlarmRuleSchema),
    defaultValues: {
      id: rule?.id,
      name: rule?.name ?? '',
      parameterId: rule?.parameterId ?? '',
      conditionType: rule?.conditionType ?? 'value_gt',
      thresholdValue: rule?.thresholdValue ?? null,
      thresholdValueLow: rule?.thresholdValueLow ?? null, // Added default
      severity: rule?.severity ?? 'medium',
      message: rule?.message ?? null,
      triggerDurationSeconds: rule?.triggerDurationSeconds ?? 0, // Added default
      resetThresholdHysteresis: rule?.resetThresholdHysteresis ?? 0, // Added default
      is_active: rule?.is_active ?? true,
    },
  });

  // --- Reset form when rule changes (for editing) or dialog opens/closes ---
  useEffect(() => {
    if (isOpen) {
      if (isEditMode && rule) {
        form.reset({
          id: rule.id,
          name: rule.name,
          parameterId: rule.parameterId,
          conditionType: rule.conditionType,
          thresholdValue: rule.thresholdValue,
          thresholdValueLow: rule.thresholdValueLow,
          severity: rule.severity,
          message: rule.message,
          triggerDurationSeconds: rule.triggerDurationSeconds,
          resetThresholdHysteresis: rule.resetThresholdHysteresis,
          is_active: rule.is_active,
        });
      } else {
        form.reset(); 
      }
    } else {
        // Optional: reset errors when dialog closes
        form.clearErrors();
    }
  }, [isOpen, rule, isEditMode, form]);

  // --- Mutations ---
  const { mutate: createMutate, isPending: isCreating } = useMutation({
    mutationFn: createAlarmRule,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alarmRules'] });
      // TODO: Add success toast
      console.log('Rule created successfully');
      onClose(); 
    },
    onError: (error) => {
      // TODO: Add error toast
      console.error('Failed to create rule:', error);
      // Potentially set form error (e.g., if name is duplicate)
      // form.setError('name', { type: 'manual', message: 'Name already exists' });
    },
  });

  const { mutate: updateMutate, isPending: isUpdating } = useMutation({
    mutationFn: (payload: AlarmRulePayload) => {
        if (!rule?.id) {
            // Should not happen in edit mode, but good practice
            throw new Error("Cannot update rule without an ID.");
        }
        return updateAlarmRule(rule.id, payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['alarmRules'] });
      // TODO: Add success toast
      console.log('Rule updated successfully');
      onClose(); 
    },
    onError: (error) => {
      // TODO: Add error toast
      console.error('Failed to update rule:', error);
    },
  });

  const isPending = isCreating || isUpdating;

  // Handle form submission
  const handleFormSubmit = async (values: AlarmRuleFormValues) => {
    // Pass the raw Zod-validated values to the onSubmit prop.
    // The parent component is responsible for converting types for the API call.
    await onSubmit(values); 
    // Reset form or close dialog after successful submission (handled by parent potentially)
    // form.reset(); // Optional: Reset form fields
    onClose(); // Close dialog
  };

  // --- Render Logic ---
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px] bg-white border-0 shadow-xl">
        <DialogHeader>
          <DialogTitle>{isEditMode ? 'Edit Alarm Rule' : 'Create New Alarm Rule'}</DialogTitle>
          <DialogDescription>
            {isEditMode ? 'Modify the details of this alarm rule.' : 'Get notifications when power metrics exceed your thresholds.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleFormSubmit)} className="py-2">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="basic">Basic Settings</TabsTrigger>
                <TabsTrigger value="advanced">Advanced Options</TabsTrigger>
              </TabsList>
              
              <TabsContent value="basic" className="space-y-4 pt-4">
                {/* Step 1: Name your rule */}
                <Card className="p-4 border border-gray-100">
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 inline-flex items-center justify-center mr-2 text-xs">1</span>
                    Name your rule
                  </h3>
                  
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem className="mb-0">
                        <FormControl>
                          <Input 
                            placeholder="Enter a descriptive name" 
                            className="bg-gray-50 border border-gray-200" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Card>

                {/* Step 2: Select what to monitor */}
                <Card className="p-4 border border-gray-100">
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 inline-flex items-center justify-center mr-2 text-xs">2</span>
                    Select what to monitor
                  </h3>
                  
                  <FormField
                    control={form.control}
                    name="parameterId"
                    render={({ field }) => (
                      <FormItem className="mb-3">
                        <FormControl>
                          <Select 
                              onValueChange={field.onChange} 
                              defaultValue={field.value ? String(field.value) : undefined} // Ensure value is string
                          >
                            <SelectTrigger className="bg-gray-50 border border-gray-200">
                              <SelectValue placeholder="Choose a metric" />
                            </SelectTrigger>
                            <SelectContent className="bg-white">
                              {parameters.map((param) => (
                                <SelectItem key={param.id} value={String(param.id)}>
                                  {param.name} {param.unit ? `(${param.unit})` : ''}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {form.watch("parameterId") === "custom" && (
                    <FormField
                      control={form.control}
                      name="parameterId"
                      render={({ field }) => (
                        <FormItem className="mb-0">
                          <FormControl>
                            <Input 
                              placeholder="Enter custom metric identifier" 
                              className="bg-gray-50 border border-gray-200" 
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </Card>

                {/* Step 3: Set condition */}
                <Card className="p-4 border border-gray-100">
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 inline-flex items-center justify-center mr-2 text-xs">3</span>
                    Set condition
                  </h3>
                  
                  <div className="grid grid-cols-5 gap-3 items-start">
                    <div className="col-span-2">
                      <FormField
                        control={form.control}
                        name="conditionType"
                        render={({ field }) => (
                          <FormItem className="mb-0">
                            <FormControl>
                              <Select onValueChange={field.onChange} value={field.value}>
                                <SelectTrigger className="bg-gray-50 border border-gray-200">
                                  <SelectValue placeholder="Condition Type" />
                                </SelectTrigger>
                                <SelectContent className="bg-white">
                                  <SelectItem value="value_gt">Greater than</SelectItem>
                                  <SelectItem value="value_lt">Less than</SelectItem>
                                  <SelectItem value="value_eq">Equal to</SelectItem>
                                  <SelectItem value="value_rate_change">Rate change</SelectItem>
                                  <SelectItem value="device_offline">Device offline</SelectItem>
                                  <SelectItem value="custom">Custom</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <div className="col-span-3">
                      <FormField
                        control={form.control}
                        name="thresholdValue"
                        render={({ field }) => (
                          <FormItem className="mb-0">
                            <FormControl>
                              <Input
                                type="text" 
                                placeholder="Threshold value" 
                                className="bg-gray-50 border border-gray-200"
                                {...field}
                                onChange={e => {
                                  const value = e.target.value;
                                  if (/^\d*\.?\d*$/.test(value) || value === '') { 
                                    field.onChange(value === '' ? null : parseFloat(value)); 
                                  }
                                }}
                                value={field.value === null ? '' : String(field.value)}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                  
                  <div className="mt-2 flex items-start gap-2">
                    <Info size={16} className="text-blue-500 mt-0.5" />
                    <p className="text-xs text-gray-500">
                      {form.watch("conditionType") === "value_gt" && "Alert when the value exceeds this threshold"}
                      {form.watch("conditionType") === "value_lt" && "Alert when the value falls below this threshold"}
                      {form.watch("conditionType") === "value_eq" && "Alert when the value is equal to this threshold"}
                      {form.watch("conditionType") === "value_rate_change" && "Alert when the value changes by this amount"}
                      {form.watch("conditionType") === "device_offline" && "Alert when the device is offline"}
                      {form.watch("conditionType") === "custom" && "Custom condition"}
                    </p>
                  </div>
                </Card>
                
                {/* Step 4: Set priority */}
                <Card className="p-4 border border-gray-100">
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <span className="bg-blue-100 text-blue-800 rounded-full w-5 h-5 inline-flex items-center justify-center mr-2 text-xs">4</span>
                    Set priority
                  </h3>
                  
                  <FormField
                    control={form.control}
                    name="severity"
                    render={({ field }) => (
                      <FormItem className="mb-0">
                        <div className="grid grid-cols-3 gap-2">
                          <div 
                            className={`border rounded-md p-3 cursor-pointer text-center ${field.value === 'info' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}
                            onClick={() => field.onChange('info')}
                          >
                            <div className="text-sm font-medium">Info</div>
                            <div className="text-xs text-gray-500 mt-1">Informational</div>
                          </div>
                          <div 
                            className={`border rounded-md p-3 cursor-pointer text-center ${field.value === 'medium' ? 'border-yellow-500 bg-yellow-50' : 'border-gray-200'}`}
                            onClick={() => field.onChange('medium')}
                          >
                            <div className="text-sm font-medium">Warning</div>
                            <div className="text-xs text-gray-500 mt-1">Needs attention</div>
                          </div>
                          <div 
                            className={`border rounded-md p-3 cursor-pointer text-center ${field.value === 'critical' ? 'border-red-500 bg-red-50' : 'border-gray-200'}`}
                            onClick={() => field.onChange('critical')}
                          >
                            <div className="text-sm font-medium">Critical</div>
                            <div className="text-xs text-gray-500 mt-1">Urgent action</div>
                          </div>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Card>
              </TabsContent>
              
              <TabsContent value="advanced" className="space-y-4 pt-4">
                {/* Message and Active Toggle in Advanced tab */}
                <Card className="p-4 border border-gray-100">
                  <h3 className="text-sm font-medium mb-3">Notification Message</h3>
                  <FormField
                    control={form.control}
                    name="message"
                    render={({ field }) => (
                      <FormItem className="mb-0">
                        <FormControl>
                          <Textarea
                            placeholder="Add a descriptive message for the notification (optional)"
                            className="resize-none bg-gray-50 border border-gray-200"
                            {...field}
                            value={field.value ?? ''} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Card>
                
                <Card className="p-4 border border-gray-100">
                  <FormField
                    control={form.control}
                    name="is_active"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between">
                        <div className="space-y-0.5">
                          <h3 className="text-sm font-medium">Rule Status</h3>
                          <p className="text-xs text-gray-500">
                            Inactive rules will not be evaluated or trigger alarms
                          </p>
                        </div>
                        <FormControl>
                          <ToggleSwitch
                            checked={field.value || false}
                            onChange={() => field.onChange(!field.value)}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </Card>

                {/* Parameter - Advanced Options */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="parameterId"
                    render={({ field }) => (
                      <FormItem className="mb-0">
                        <FormLabel>Parameter</FormLabel>
                        <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value ? String(field.value) : undefined} // Ensure value is string
                        >
                          <FormControl>
                            <SelectTrigger className="bg-gray-50 border border-gray-200">
                              <SelectValue placeholder="Select parameter" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {parameters.map((param) => (
                              <SelectItem key={param.id} value={String(param.id)}>{param.name} ({param.unit})</SelectItem> // Ensure value is string
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Threshold Value(s) - Conditional based on conditionType */} 
                {(form.watch("conditionType") === 'value_gt' || form.watch("conditionType") === 'value_lt') && (
                    <div className="grid grid-cols-2 gap-4">
                        <FormField
                            control={form.control}
                            name="thresholdValue"
                            render={({ field }) => (
                                <FormItem className="mb-0">
                                    <FormLabel>
                                        {form.watch("conditionType") === 'value_gt' ? 'High Threshold' : 'Primary Threshold'}
                                    </FormLabel>
                                    <FormControl>
                                        <Input 
                                            type="number" 
                                            placeholder="e.g., 100" 
                                            {...field} 
                                            value={field.value ?? ''} // Handle null/undefined
                                            onChange={e => field.onChange(e.target.value === '' ? null : parseFloat(e.target.value))} // Convert to number or null
                                            className="bg-gray-50 border border-gray-200" 
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        {form.watch("conditionType") === 'value_lt' && (
                             <FormField
                                control={form.control}
                                name="thresholdValueLow"
                                render={({ field }) => (
                                    <FormItem className="mb-0">
                                        <FormLabel>Low Threshold (Optional)</FormLabel>
                                        <FormControl>
                                            <Input 
                                                type="number" 
                                                placeholder="e.g., 10" 
                                                {...field} 
                                                value={field.value ?? ''} // Handle null/undefined
                                                onChange={e => field.onChange(e.target.value === '' ? null : parseFloat(e.target.value))} // Convert to number or null
                                                className="bg-gray-50 border border-gray-200" 
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        )}
                    </div>
                )}

                {/* Debounce and Hysteresis - Advanced Options */} 
                {(form.watch("conditionType") === 'value_gt' || form.watch("conditionType") === 'value_lt') && (
                    <Card className="p-4 border border-gray-100">
                         <h3 className="text-sm font-medium mb-3">Timing & Reset</h3>
                         <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="triggerDurationSeconds"
                                render={({ field }) => (
                                    <FormItem className="mb-0">
                                        <FormLabel>Trigger Duration (sec)</FormLabel>
                                        <FormControl>
                                            <Input 
                                                type="number" 
                                                placeholder="0" 
                                                {...field} 
                                                value={field.value ?? ''} // Handle null/undefined
                                                onChange={e => field.onChange(e.target.value === '' ? null : parseInt(e.target.value, 10))} // Convert to number or null
                                                className="bg-gray-50 border border-gray-200" 
                                            />
                                        </FormControl>
                                        <FormDescription className="text-xs">Time condition must hold (0=immediate).</FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                             <FormField
                                control={form.control}
                                name="resetThresholdHysteresis"
                                render={({ field }) => (
                                    <FormItem className="mb-0">
                                        <FormLabel>Reset Hysteresis</FormLabel>
                                        <FormControl>
                                            <Input 
                                                type="number" 
                                                placeholder="0" 
                                                {...field} 
                                                value={field.value ?? ''} // Handle null/undefined
                                                onChange={e => field.onChange(e.target.value === '' ? null : parseFloat(e.target.value))} // Convert to number or null
                                                className="bg-gray-50 border border-gray-200" 
                                            />
                                        </FormControl>
                                        <FormDescription className="text-xs">Value offset to clear alarm.</FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                         </div>
                    </Card>
                )}

              </TabsContent>
            </Tabs>
            
            {/* Summary card */}
            <Card className="mt-5 p-4 border border-gray-100 bg-gray-50">
              <h3 className="text-sm font-medium mb-2">Rule Summary</h3>
              <p className="text-sm">
                {form.watch("name") || "Unnamed Rule"}: Alert when{" "}
                <span className="font-medium">{form.watch("parameterId") || "selected metric"}</span>{" "}
                is{" "}
                {form.watch("conditionType") === "value_gt" && "greater than"}
                {form.watch("conditionType") === "value_lt" && "less than"}
                {form.watch("conditionType") === "value_eq" && "equal to"}
                {form.watch("conditionType") === "value_rate_change" && "changes by"}
                {form.watch("conditionType") === "device_offline" && "offline"}
                {form.watch("conditionType") === "custom" && "custom condition"}
                {" "}
                <span className="font-medium">{form.watch("thresholdValue") || "..."}</span>
              </p>
            </Card>

            <DialogFooter className="mt-5">
                <Button type="button" variant="outline" onClick={onClose} disabled={isPending}>
                    Cancel
                </Button>
                <Button 
                    type="submit" 
                    className="bg-blue-600 hover:bg-blue-700 text-white font-medium shadow-sm"
                    disabled={isPending}
                >
                    {isPending && <LoadingSpinner className="mr-2 h-4 w-4" />} 
                    {isEditMode ? 'Save Changes' : 'Create Rule'}
                </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateEditAlarmRuleDialog;
