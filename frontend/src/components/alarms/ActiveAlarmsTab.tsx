import React, { useState, useMemo, useEffect } from 'react';
import { useToast } from '@/components/ui/ToastProvider';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  SortingState,
  ColumnDef,
  Row,
  Column,
  PaginationState,
} from '@tanstack/react-table';
// Navigation is handled by the AlarmTable component
import { ChevronDown, ChevronUp, ArrowUpDown } from 'lucide-react';
import { TablePagination } from '@/components/ui/table-pagination';
import { useQuery, keepPreviousData, useQueryClient } from '@tanstack/react-query';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/Button';
import { ActiveAlarmsTabSkeleton } from '@/components/ui/alarms-skeletons';
import { EnhancedErrorDisplay } from '@/components/ui/error-display';
import { useRetry } from '@/hooks/useRetry';
import { getAFDDLogs, acknowledgeAFDDLog, clearAFDDLog } from '@/lib/api/alarms';
import { AFDDLog } from '@/types/alarms';
import SimpleConfirmationDialog from '@/components/common/SimpleConfirmationDialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { flexRender } from '@tanstack/react-table';

const getSeverityClasses = (severity: string): string => {
  switch (severity.toLowerCase()) {
    case 'critical':
      return "bg-red-500 text-white";
    case 'warning':
      return "bg-amber-500 text-white";
    case 'info':
      return "bg-slate-500 text-white";
    default:
      return "bg-gray-500 text-white";
  }
};

const getAlarmStateClasses = (alarmState: string): string => {
  switch (alarmState) {
    case 'active':
      return "bg-red-100 text-red-800";
    case 'normal':
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getAckStateClasses = (ackState: string): string => {
  switch (ackState) {
    case 'unacknowledged':
      return "bg-yellow-100 text-yellow-800";
    case 'acknowledged':
      return "bg-blue-100 text-blue-800";
    case 'cleared':
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const baseColumns: ColumnDef<AFDDLog>[] = [
  {
    accessorKey: 'active_at',
    header: ({ column }: { column: Column<AFDDLog, unknown> }) => (
      <Button
        variant="ghost"
        className="-ml-4 h-8 data-[state=open]:bg-accent"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      >
        Active Time
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }: { row: Row<AFDDLog> }) => {
      const date = new Date(row.original.active_at);
      return (
        <div className="space-y-1">
          <div className="text-sm text-gray-900">{date.toLocaleDateString()}</div>
          <div className="text-xs text-gray-500">{date.toLocaleTimeString()}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'fault_name',
    header: ({ column }: { column: Column<AFDDLog, unknown> }) => (
      <Button
        variant="ghost"
        className="-ml-4 h-8 data-[state=open]:bg-accent"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      >
        Fault Description
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }: { row: Row<AFDDLog> }) => {
      const faultName = row.original.fault_name.replace(/_/g, ' ');
      return (
        <div className="space-y-1">
          <div className="font-medium text-gray-900 capitalize">{faultName}</div>
          <div className="text-xs text-gray-500 line-clamp-2">{row.original.message}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'severity',
    header: ({ column }: { column: Column<AFDDLog, unknown> }) => (
      <Button
        variant="ghost"
        className="-ml-4 h-8 data-[state=open]:bg-accent"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      >
        Severity
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }: { row: Row<AFDDLog> }) => (
      <Badge className={getSeverityClasses(row.original.severity)}>
        {row.original.severity.charAt(0).toUpperCase() + row.original.severity.slice(1)}
      </Badge>
    ),
  },
  {
    accessorKey: 'alarm_state',
    header: ({ column }: { column: Column<AFDDLog, unknown> }) => (
      <Button
        variant="ghost"
        className="-ml-4 h-8 data-[state=open]:bg-accent"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      >
        Status
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }: { row: Row<AFDDLog> }) => (
      <Badge className={getAlarmStateClasses(row.original.alarm_state)}>
        {row.original.alarm_state.charAt(0).toUpperCase() + row.original.alarm_state.slice(1)}
      </Badge>
    ),
  },
  {
    accessorKey: 'ack_state',
    header: ({ column }: { column: Column<AFDDLog, unknown> }) => (
      <Button
        variant="ghost"
        className="-ml-4 h-8 data-[state=open]:bg-accent"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      >
        Acknowledge
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }: { row: Row<AFDDLog> }) => (
      <Badge className={getAckStateClasses(row.original.ack_state)}>
        {row.original.ack_state === 'unacknowledged'
          ? 'Unacknowledged'
          : row.original.ack_state.charAt(0).toUpperCase() + row.original.ack_state.slice(1)}
      </Badge>
    ),
  },
  {
    accessorKey: 'duration',
    header: ({ column }: { column: Column<AFDDLog, unknown> }) => (
      <Button
        variant="ghost"
        className="-ml-4 h-8 data-[state=open]:bg-accent"
        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
      >
        Duration
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </Button>
    ),
    cell: ({ row }: { row: Row<AFDDLog> }) => {
      const duration = row.original.duration;
      const days = Math.floor(duration / 86400);
      const hours = Math.floor((duration % 86400) / 3600);
      const minutes = Math.floor((duration % 3600) / 60);

      if (days > 0) {
        return <span className="text-gray-700">{days}d {hours}h</span>;
      } else if (hours > 0) {
        return <span className="text-gray-700">{hours}h {minutes}m</span>;
      } else {
        return <span className="text-gray-700">{minutes}m</span>;
      }
    },
  },
];

// Remove the type guard since we're now using AFDDLog directly

export const ActiveAlarmsTab: React.FC = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToast();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [rowSelection, setRowSelection] = useState({});
  const [isConfirmOpen, setIsConfirmOpen] = useState(false);
  const [selectedAlarm, setSelectedAlarm] = useState<AFDDLog | null>(null);
  const [severityFilters, setSeverityFilters] = useState<string[]>(['critical', 'warning', 'info']);
  const [alarmStateFilter, setAlarmStateFilter] = useState<string>('all');
  const [ackStateFilter, setAckStateFilter] = useState<string>('all');

  // Reset pagination when filters change
  useEffect(() => {
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  }, [severityFilters, alarmStateFilter, ackStateFilter]);

  const queryOptions: any = {
    page: (pagination.pageIndex + 1).toString(),
    page_size: pagination.pageSize.toString(),
    ordering: sorting.length > 0 ?
      (sorting[0].desc ? '-' : '') + sorting[0].id :
      '-active_at',
  };

  const fetchData = () => getAFDDLogs(queryOptions);
  const { retry, retryCount, isRetrying } = useRetry(fetchData);

  const handleOpenConfirm = (alarm: AFDDLog) => {
    setSelectedAlarm(alarm);
    setIsConfirmOpen(true);
  };
  const handleConfirm = async () => {
    if (!selectedAlarm) {
      console.error('No alarm selected');
      setIsConfirmOpen(false);
      return;
    }

    console.log('Acknowledging and resolving alarm:', selectedAlarm);

    try {
      const success = await acknowledgeAndResolveAlarm(selectedAlarm.id);

      if (success) {
        console.log('Successfully acknowledged and resolved alarm');
        queryClient.invalidateQueries({ queryKey: ['activeAlarms'] });
        queryClient.invalidateQueries({ queryKey: ['alarmHistory'] });

        showToast({
          message: `Alarm for ${selectedAlarm.meter_name || 'meter'} has been acknowledged and moved to the History tab`,
          type: 'success',
          duration: 5000
        });
      } else {
        console.error('Failed to acknowledge and resolve alarm');
        showToast({
          message: 'Failed to acknowledge alarm. Please try again.',
          type: 'error',
          duration: 5000
        });
      }
    } catch (error) {
      console.error('Error acknowledging and resolving alarm:', error);
      showToast({
        message: 'An error occurred while acknowledging the alarm.',
        type: 'error',
        duration: 5000
      });
    } finally {
      setIsConfirmOpen(false);
    }
  };

  const actionsColumn: ColumnDef<AFDDLog> = {
    id: 'actions',
    header: () => <div className="text-right">Actions</div>,
    cell: ({ row }) => {
      const alarm = row.original;
      if (alarm.ack_state === 'cleared') {
        return (
          <div className="flex items-center justify-end h-8">
            <Badge className="bg-emerald-500 text-white">
              Cleared
            </Badge>
          </div>
        );
      }

      return (
        <div className="flex items-center justify-end h-8">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleOpenConfirm(alarm)}
            className="h-8 px-3 text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200"
          >
            {alarm.ack_state === 'unacknowledged' ? 'Acknowledge' : 'Clear'}
          </Button>
        </div>
      );
    },
  };

  const { data: alarmsData, isLoading, isError, error } = useQuery({
    queryKey: ['afdd-logs', queryOptions, severityFilters, alarmStateFilter, ackStateFilter],
    queryFn: () => getAFDDLogs(queryOptions),
    placeholderData: keepPreviousData,
    refetchInterval: false, // Disable auto-refresh for alarms
    refetchOnWindowFocus: false, // Disable refetch on window focus
  });

  console.log('Raw alarmsData:', alarmsData);
  if (alarmsData && alarmsData.results && alarmsData.results.length > 0) {
    console.log('ActiveAlarmsTab first row:', alarmsData.results[0]);
  }

  const validatedData = useMemo(() => {
    console.log('Validating data, alarmsData:', alarmsData);
    console.log('alarmsData?.results:', alarmsData?.results);

    if (!alarmsData) {
      console.log('No alarmsData');
      return [];
    }

    if (!alarmsData.results || !Array.isArray(alarmsData.results)) {
      console.log('No results array in alarmsData');
      return [];
    }

    if (alarmsData.results.length === 0) {
      console.log('Results array is empty');
      return [];
    }

    let filteredData = alarmsData.results;
    console.log('Initial data length:', filteredData.length);

    // Apply severity filters
    if (severityFilters.length > 0) {
      console.log('Applying severity filters:', severityFilters);
      filteredData = filteredData.filter(alarm =>
        severityFilters.includes(alarm.severity)
      );
      console.log('After severity filter:', filteredData.length);
    }

    // Apply alarm state filter
    if (alarmStateFilter !== 'all') {
      console.log('Applying alarm state filter:', alarmStateFilter);
      filteredData = filteredData.filter(alarm =>
        alarm.alarm_state === alarmStateFilter
      );
      console.log('After alarm state filter:', filteredData.length);
    }

    // Apply acknowledgment state filter
    if (ackStateFilter !== 'all') {
      console.log('Applying ack state filter:', ackStateFilter);
      filteredData = filteredData.filter(alarm =>
        alarm.ack_state === ackStateFilter
      );
      console.log('After ack state filter:', filteredData.length);
    }

    console.log('Final filtered data length:', filteredData.length);
    return filteredData;
  }, [alarmsData, severityFilters, alarmStateFilter, ackStateFilter]);

  const table = useReactTable({
    data: validatedData,
    columns: [...baseColumns, actionsColumn],
    state: {
      sorting,
      pagination,
      rowSelection,
    },
    pageCount: alarmsData?.count ? Math.ceil(alarmsData.count / pagination.pageSize) : -1,
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    manualSorting: true,
  });

  if (isLoading) {
    return <ActiveAlarmsTabSkeleton />;
  }

  if (isError) {
    return (
      <EnhancedErrorDisplay
        error={error}
        onRetry={retry}
        retryCount={retryCount}
        isRetrying={isRetrying}
        context="active alarms"
      />
    );
  }

  if (!alarmsData || !alarmsData.results) {
    alarmsData = { count: 0, next: null, previous: null, results: [] };
  }

  return (
    <div className="h-full flex flex-col p-4">
      {/* Filters */}
      <div className="pb-3 flex items-center justify-between border-b border-gray-100">
        {/* Severity Filter */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Severity:</span>
          <div className="flex gap-2">
            {['critical', 'warning', 'info'].map((severity) => {
              const isSelected = severityFilters.includes(severity);
              return (
                <button
                  key={severity}
                  onClick={() => {
                    if (isSelected) {
                      setSeverityFilters(prev => prev.filter(s => s !== severity));
                    } else {
                      setSeverityFilters(prev => [...prev, severity]);
                    }
                  }}
                  className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${isSelected
                      ? severity === 'critical'
                        ? 'bg-red-500 text-white'
                        : severity === 'warning'
                          ? 'bg-amber-500 text-white'
                          : 'bg-slate-500 text-white'
                      : 'bg-gray-50 text-gray-500 hover:bg-gray-100 border border-gray-200'
                    }`}
                >
                  {severity.charAt(0).toUpperCase() + severity.slice(1)}
                </button>
              );
            })}
          </div>
        </div>

        {/* Status Filters */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Status:</span>
            <select
              value={alarmStateFilter}
              onChange={(e) => setAlarmStateFilter(e.target.value)}
              className="px-2 py-1 text-xs border rounded-md"
            >
              <option value="all">All</option>
              <option value="active">Active</option>
              <option value="normal">Normal</option>
            </select>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Acknowledge:</span>
            <select
              value={ackStateFilter}
              onChange={(e) => setAckStateFilter(e.target.value)}
              className="px-2 py-1 text-xs border rounded-md"
            >
              <option value="all">All</option>
              <option value="unacknowledged">Unacknowledged</option>
              <option value="acknowledged">Acknowledged</option>
              <option value="cleared">Cleared</option>
            </select>
          </div>
        </div>
      </div>

      {/* Table Section */}
      <div className="flex-1 flex flex-col">
        <div className="flex-1 overflow-auto">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={table.getAllColumns().length}
                    className="h-24 text-center text-gray-500"
                  >
                    No Alarm Log found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination with alarm count */}
        <div className="border-t border-gray-200 py-3 flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Alarm Log ({alarmsData?.count ?? 0} total)
          </div>
          <TablePagination table={table} />
        </div>
      </div>

      <SimpleConfirmationDialog
        isOpen={isConfirmOpen}
        onClose={() => setIsConfirmOpen(false)}
        onConfirm={handleConfirm}
        title={selectedAlarm?.ack_state === 'unacknowledged' ? 'Acknowledge Alarm' : 'Clear Alarm'}
        message={`Are you sure you want to ${selectedAlarm?.ack_state === 'unacknowledged' ? 'acknowledge' : 'clear'} the alarm "${selectedAlarm?.fault_name}"?`}
        confirmButtonText={selectedAlarm?.ack_state === 'unacknowledged' ? 'Acknowledge' : 'Clear'}
      />
    </div>
  );
};

export default ActiveAlarmsTab;
