import React, { useState } from 'react';
import { Mail, Send, CheckCircle, AlertTriangle, User, Info, Plus, X, Bell, Users, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { ToggleSwitch, Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { getMockEmailSettings, updateMockPreference, sendMockTestEmail, getMockRecipients, updateRecipientStatus, addNewRecipient, Recipient } from '@/utils/mockNotifications';
import { SeverityLevel } from '@/components/EmailAlerts/EmailAlertSettings';

const severityIcons = {
  critical: <AlertTriangle className="h-4 w-4 text-red-600" />,
  high: <AlertTriangle className="h-4 w-4 text-amber-500" />,
  medium: <AlertTriangle className="h-4 w-4 text-blue-500" />,
  low: <AlertTriangle className="h-4 w-4 text-gray-500" />
};

export const NotificationsTab: React.FC = () => {
  const [testEmail, setTestEmail] = useState('');
  const [testEmailSent, setTestEmailSent] = useState(false);
  const [notificationPreferences, setNotificationPreferences] = useState(getMockEmailSettings().preferences);
  const [recipients, setRecipients] = useState(getMockRecipients());
  const [newRecipient, setNewRecipient] = useState({ name: '', email: '' });
  const [showAddRecipient, setShowAddRecipient] = useState(false);
  const [emailError, setEmailError] = useState('');
  const emailSettings = getMockEmailSettings();

  const handleTogglePreference = (id: number) => {
    const updatedPreferences = notificationPreferences.map(pref => {
      if (pref.id === id) {
        return { ...pref, is_enabled: !pref.is_enabled };
      }
      return pref;
    });
    setNotificationPreferences(updatedPreferences);
    updateMockPreference(id, { is_enabled: !notificationPreferences.find(p => p.id === id)?.is_enabled });
  };

  const handleToggleRecipient = (id: string) => {
    const updatedRecipients = recipients.map(recipient => {
      if (recipient.id === id) {
        return { ...recipient, enabled: !recipient.enabled };
      }
      return recipient;
    });
    setRecipients(updatedRecipients);
    const recipient = recipients.find(r => r.id === id);
    if (recipient) {
      updateRecipientStatus(id, !recipient.enabled);
    }
  };

  const handleSendTestEmail = () => {
    if (testEmail) {
      sendMockTestEmail();
      setTestEmailSent(true);
      setTimeout(() => setTestEmailSent(false), 3000);
    }
  };

  const validateEmail = (email: string): boolean => {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = re.test(email);
    if (!isValid) {
      setEmailError('Please enter a valid email address');
    } else if (recipients.some(r => r?.email && r.email.toLowerCase() === email.toLowerCase())) {
      setEmailError('This email is already in the recipients list');
      return false;
    } else {
      setEmailError('');
    }
    return isValid && !emailError;
  };

  const handleAddRecipient = () => {
    if (newRecipient.name && newRecipient.email && validateEmail(newRecipient.email)) {
      // Generate initials from name
      const initials = newRecipient.name
        .split(' ')
        .map(part => part[0])
        .join('')
        .substring(0, 2)
        .toUpperCase();

      const newRecipientObj: Recipient = {
        id: (recipients.length + 1).toString(),
        name: newRecipient.name,
        email: newRecipient.email,
        enabled: true,
        initials
      };

      setRecipients([...recipients, newRecipientObj]);
      addNewRecipient(newRecipient.name, newRecipient.email);
      setNewRecipient({ name: '', email: '' });
      setShowAddRecipient(false);
      setEmailError('');
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Notification Settings</h2>
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2">
              <Send className="h-4 w-4" />
              Send Test Alert
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Send Test Alert</DialogTitle>
              <DialogDescription>
                Send a test alert to verify your notification settings.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  placeholder="Enter email address"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                onClick={handleSendTestEmail}
                className="flex items-center gap-2"
                disabled={!testEmail || testEmailSent}
              >
                {testEmailSent ? (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    Sent!
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4" />
                    Send Test
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {/* Left Column: Email Preferences and Recipients */}
        <div className="flex flex-col space-y-4">
          {/* Email Alert Preferences */}
          <Card>
            <CardHeader>
              <div className="flex items-center">
                <Mail className="h-4 w-4 mr-2 text-blue-600" />
                <h3 className="font-medium">Email Alert Preferences</h3>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                {notificationPreferences.map((preference) => (
                  <div key={preference.id} className="flex items-center justify-between border-b pb-1.5">
                    <div>
                      <h3 className="font-medium text-sm">{preference.category_display}</h3>
                      <p className="text-xs text-gray-500">
                        {preference.enabled_severities?.join(', ') || 'All severity levels'}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-500">{preference.is_enabled ? 'On' : 'Off'}</span>
                      <ToggleSwitch 
                        checked={preference.is_enabled}
                        onChange={() => handleTogglePreference(preference.id as number)}
                        size="sm"
                        className="data-[state=checked]:bg-blue-600 data-[state=unchecked]:bg-gray-300"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          {/* Recipients Card */}
          <Card className="md:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2 text-blue-600" />
                  <h3 className="font-medium">Recipients</h3>
                </div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-gray-400 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="w-60">Manage who receives alarm notifications.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </CardHeader>
            <CardContent>
              <div className="max-h-[220px] overflow-y-auto space-y-2">
                {recipients.map((recipient) => (
                  <div key={recipient.id} className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center gap-2">
                      <div className={`h-7 w-7 rounded-full ${recipient.enabled ? 'bg-blue-600 text-white' : 'bg-gray-400 text-gray-700'} flex items-center justify-center font-medium text-xs`}>
                        {recipient.initials}
                      </div>
                      <div>
                        <h3 className="font-medium text-sm">{recipient.name}</h3>
                        <p className="text-xs text-gray-500">{recipient.email}</p>
                      </div>
                    </div>
                    <ToggleSwitch 
                      checked={recipient.enabled} 
                      onChange={() => handleToggleRecipient(recipient.id)}
                      size="sm"
                      className="data-[state=checked]:bg-blue-600 data-[state=unchecked]:bg-gray-300" 
                    />
                  </div>
                ))}
                
                {showAddRecipient ? (
                  <div className="border rounded-md p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">Add New Recipient</h3>
                      <button 
                        onClick={() => {
                          setShowAddRecipient(false);
                          setNewRecipient({ name: '', email: '' });
                          setEmailError('');
                        }}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                    <div className="space-y-2">
                      <div>
                        <Label htmlFor="name" className="text-xs">Name</Label>
                        <Input
                          id="name"
                          value={newRecipient.name}
                          onChange={(e) => setNewRecipient({...newRecipient, name: e.target.value})}
                          placeholder="Full Name"
                          className="h-8 text-sm"
                        />
                      </div>
                      <div>
                        <Label htmlFor="email" className="text-xs">Email</Label>
                        <Input
                          id="email"
                          value={newRecipient.email}
                          onChange={(e) => {
                            setNewRecipient({...newRecipient, email: e.target.value});
                            if (emailError) validateEmail(e.target.value);
                          }}
                          onBlur={() => validateEmail(newRecipient.email)}
                          placeholder="<EMAIL>"
                          className="h-8 text-sm"
                        />
                        {emailError && <p className="text-xs text-red-500 mt-1">{emailError}</p>}
                      </div>
                    </div>
                    <Button 
                      size="sm" 
                      onClick={handleAddRecipient}
                      disabled={!newRecipient.name || !newRecipient.email || !!emailError}
                      className="w-full mt-2"
                    >
                      Add Recipient
                    </Button>
                  </div>
                ) : (
                  <Button 
                    variant="outline" 
                    className="w-full flex items-center justify-center text-sm gap-1"
                    onClick={() => setShowAddRecipient(true)}
                  >
                    <Plus className="h-3.5 w-3.5" />
                    Add Recipient
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Right Column: Alert Subscriptions */}
        <Card>
          <CardHeader>
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2 text-blue-600" />
              <h3 className="font-medium">Alert Subscriptions</h3>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <div className="grid grid-cols-[auto_1fr_auto] gap-2 text-xs font-medium text-gray-500 pb-1 border-b">
                <div>Type</div>
                <div>Details</div>
                <div>Status</div>
              </div>
              
              {emailSettings.alarm_subscriptions.map((subscription) => (
                <div key={subscription.id} className="grid grid-cols-[auto_1fr_auto] gap-2 items-center py-1.5 border-b border-gray-100">
                  <div className="flex items-center">
                    {severityIcons[subscription.severity as SeverityLevel] || <AlertTriangle className="h-4 w-4" />}
                  </div>
                  <div>
                    <h3 className="font-medium text-sm">{subscription.category_display}</h3>
                    <div className="flex flex-wrap gap-1 mt-0.5">
                      {subscription.device && (
                        <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs bg-gray-100 text-gray-800">
                          {subscription.device}
                        </span>
                      )}
                      {subscription.parameter && (
                        <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs bg-gray-100 text-gray-800">
                          {subscription.parameter}
                        </span>
                      )}
                      <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs bg-gray-100 text-gray-800">
                        {subscription.condition} {subscription.threshold_value}
                      </span>
                    </div>
                  </div>
                  <ToggleSwitch 
                    checked={true} 
                    onChange={() => {}}
                    size="sm"
                    className="data-[state=checked]:bg-blue-600 data-[state=unchecked]:bg-gray-300" 
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
