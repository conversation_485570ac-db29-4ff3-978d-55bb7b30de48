import React, { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Edit } from 'lucide-react';
import { format } from 'date-fns';

import { AlarmRule, AlarmSeverity, AlarmOperator } from '@/types/alarms';
import { createAlarmRule, updateAlarmRule } from '@/lib/api/alarms';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { Drawer } from '@/components/ui/drawer';
import { DrawerHeader } from '@/components/ui/drawer-header';
import { DrawerContent } from '@/components/ui/drawer-content';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ToggleSwitch } from '@/components/ui';
import { cn } from '@/lib/utils';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import MeterMultiSelect, { MeterOption } from '@/components/ui/MeterMultiSelect';

// Mock parameters for alarm rules - should be passed as props in production
const MOCK_PARAMETERS = [
  { key: 'power_consumption', name: '⚡ Power', unit: 'kW' },
  { key: 'energy_consumption', name: '📊 Energy', unit: 'kWh' },
  { key: 'voltage', name: '⚡ Voltage', unit: 'V' },
  { key: 'current', name: '🔌 Current', unit: 'A' },
  { key: 'power_factor', name: '📈 Power Factor', unit: 'PF' },
] as const;
type MetricKey = typeof MOCK_PARAMETERS[number]['key'];

// Mock meters list (frontend only)
const MOCK_METERS: MeterOption[] = Array.from({ length: 30 }).map((_, i) => ({
  id: i + 1,
  name: `Meter ${i + 1}`,
}))

// Function to get readable operator text
const getOperatorText = (operator: AlarmOperator): string => {
  const operatorMap: Record<string, string> = {
    '>': 'exceeds',
    '<': 'falls below',
    '>=': 'is at least',
    '<=': 'is at most',
    '=': 'equals'
  };
  return operatorMap[operator] || operator;
};

// Compose a default notification message based on rule fields
const composeDefaultMessage = (
  metricName: string,
  operator: AlarmOperator,
  threshold: number,
  unit?: string
) => {
  return `${metricName} ${getOperatorText(operator)} ${threshold.toLocaleString()}${unit ? unit : ''}.`;
};

// Suggest a concise rule name
const composeSuggestedRuleName = (
  metricName: string,
  operator: AlarmOperator,
  threshold: number,
  unit: string | undefined,
  severity: AlarmSeverity
) => {
  const sev = severity.charAt(0) + severity.slice(1).toLowerCase();
  return `${sev} ${metricName} ${getOperatorText(operator)} ${threshold.toLocaleString()}${unit ?? ''}`;
};

interface RuleDetailDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  rule: AlarmRule | null;
  isCreating?: boolean;
  presetMeterIds?: number[]; // Prefill meter_ids when creating from meter page
  onCreate?: (rule: AlarmRule) => void; // callback when a new rule is created
}

const RuleDetailDrawer: React.FC<RuleDetailDrawerProps> = ({ isOpen, onClose, rule, isCreating = false, presetMeterIds, onCreate }) => {
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Form state
  const [formState, setFormState] = useState<{
    name: string;
    metric: MetricKey;
    threshold: number;
    operator: AlarmOperator;
    severity: AlarmSeverity;
    message: string | null;
    meter_ids: number[];
  }>({
    name: '',
    metric: 'power_consumption',
    threshold: 0,
    operator: '>' as AlarmOperator,
    severity: 'INFO' as AlarmSeverity,
    message: null,
    meter_ids: presetMeterIds ?? [],
  });

  // Reset form when rule changes or creating mode changes
  React.useEffect(() => {
    if (isCreating) {
      // Initialize with default values for new rule
      setFormState({
        name: '',
        metric: 'power_consumption',
        threshold: 100,
        operator: '>' as AlarmOperator,
        severity: 'WARNING' as AlarmSeverity,
        message: null,
        meter_ids: presetMeterIds ?? [],
      });
      setIsEditing(true); // Start in edit mode for new rules
    } else if (rule) {
      // Initialize with existing rule values
      setFormState({
        name: rule.name,
        metric: rule.metric as MetricKey,
        threshold: rule.threshold,
        operator: rule.operator,
        severity: rule.severity,
        message: rule.message,
        meter_ids: (rule as any).meter_ids ?? [],
      });
      setIsEditing(false); // Reset to view mode when rule changes
    }
  }, [rule, isCreating, presetMeterIds]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormState(prev => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: keyof typeof formState, value: any) => {
    setFormState(prev => ({ ...prev, [name]: value }));
  };

  // Handle save
  const handleSave = async () => {
    setIsSaving(true);
    const suggestedName = composeSuggestedRuleName(
      getDisplayName(formState.metric),
      formState.operator,
      formState.threshold,
      parameter?.unit,
      formState.severity
    );
    const payload = {
      ...formState,
      name: formState.name && formState.name.trim() !== '' ? formState.name.trim() : suggestedName,
      message: formState.message && formState.message.trim() !== '' ? formState.message.trim() : null,
    } as typeof formState;
    try {
      if (isCreating) {
        // Create new rule
        const newRule = await createAlarmRule(payload);
        // Notify parent with newly created rule
        onCreate?.(newRule);
        queryClient.invalidateQueries({ queryKey: ['alarmRules'] });
        onClose(); // Close drawer after creating
      } else if (rule) {
        // Update existing rule
        await updateAlarmRule(rule.id, payload);
        queryClient.invalidateQueries({ queryKey: ['alarmRules'] });
        setIsEditing(false);
      }
    } catch (error) {
      console.error('Failed to save rule:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Get parameter details
  const parameter = MOCK_PARAMETERS.find(p => p.key === formState.metric);

  // Format the metric name to be more readable
  const getDisplayName = (metricId: MetricKey) => {
    const parameter = MOCK_PARAMETERS.find(p => p.key === metricId);
    if (parameter) {
      return parameter.name;
    }
    // If no parameter match, try to format the raw metric string
    return metricId
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Only guard against null rule when not creating a new one
  if (!isCreating && !rule) return null;

  return (
    <Drawer
      open={isOpen}
      onOpenChange={open => { if (!open) onClose(); }}
      className="max-w-3xl w-[700px]"
    >
      <div className="flex flex-col h-full">
        <DrawerHeader onClose={onClose}>
          <h2 className="text-xl font-semibold text-gray-900">
            {isCreating ? 'Create New Alarm Rule' : isEditing ? 'Edit Alarm Rule' : 'Alarm Rule Details'}
          </h2>
          {!isCreating && (
            <Badge variant={formState.severity.toLowerCase() as any} className="capitalize">
              {formState.severity.toLowerCase()}
            </Badge>
          )}
        </DrawerHeader>

        <DrawerContent>
          {/* Rule Summary - only show for existing rules in view mode */}
          {!isCreating && !isEditing && (
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-2 mb-4">
                <span className={cn("text-sm font-medium", "text-green-600")}>
                  Active
                </span>
              </div>
              <h3 className="text-lg font-medium mb-3">{formState.name}</h3>
              <p className="text-sm text-gray-600">
                When <span className="font-medium">{getDisplayName(formState.metric)}</span>{' '}
                <span className="font-medium">{getOperatorText(formState.operator)}</span>{' '}
                <span className="font-medium">{formState.threshold.toLocaleString()}</span>
                {parameter?.unit && <span className="text-gray-500 ml-1">{parameter.unit}</span>}
              </p>
            </div>
          )}

          {isEditing ? (
            // Edit Form
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Rule Name <span className="text-gray-400 font-normal">(required)</span>
                </label>
                <Input
                  name="name"
                  value={formState.name}
                  onChange={handleInputChange}
                  className="w-full"
                  placeholder="e.g., High Energy Consumption"
                  maxLength={60}
                />
                {formState.name.trim() === '' && (
                  <p className="text-xs text-gray-500 mt-1">
                    Suggested: {composeSuggestedRuleName(
                      getDisplayName(formState.metric),
                      formState.operator,
                      formState.threshold,
                      parameter?.unit,
                      formState.severity
                    )}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Metric</label>
                <Select
                  value={formState.metric}
                  onValueChange={(value) => handleSelectChange('metric', value as MetricKey)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue>{getDisplayName(formState.metric)}</SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {MOCK_PARAMETERS.map((param) => (
                      <SelectItem key={param.key} value={param.key}>
                        {param.name} ({param.unit})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Operator</label>
                  <Select
                    value={formState.operator}
                    onValueChange={(value) => handleSelectChange('operator', value as AlarmOperator)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select operator" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value=">">Greater than (&gt;)</SelectItem>
                      <SelectItem value="<">Less than (&lt;)</SelectItem>
                      <SelectItem value=">=">Greater than or equal (≥)</SelectItem>
                      <SelectItem value="<=">Less than or equal (≤)</SelectItem>
                      <SelectItem value="=">Equal to (=)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Threshold</label>
                  <Input
                    name="threshold"
                    type="number"
                    value={formState.threshold}
                    onChange={(e) => handleInputChange({
                      ...e,
                      target: {
                        ...e.target,
                        name: 'threshold',
                        value: e.target.value === '' ? '0' : e.target.value
                      }
                    })}
                    className="w-full"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Severity</label>
                <Select
                  value={formState.severity}
                  onValueChange={(value) => handleSelectChange('severity', value as AlarmSeverity)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CRITICAL">Critical</SelectItem>
                    <SelectItem value="WARNING">Warning</SelectItem>
                    <SelectItem value="INFO">Info</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notification Message <span className="text-gray-400 font-normal">(optional)</span>
                </label>
                <Textarea
                  name="message"
                  value={formState.message || ''}
                  onChange={handleInputChange}
                  className="w-full"
                  placeholder="Enter custom notification message"
                  maxLength={140}
                />
                {(!formState.message || formState.message.trim() === '') && (
                  <p className="text-xs text-gray-500 mt-1">
                    Default:&nbsp;
                    {composeDefaultMessage(
                      getDisplayName(formState.metric),
                      formState.operator,
                      formState.threshold,
                      parameter?.unit
                    )}
                  </p>
                )}
                {formState.message && formState.message.length > 140 && (
                  <p className="text-xs text-red-500 mt-1">Message should be 140 characters or less.</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Meters <span className="text-gray-400 font-normal">(required)</span></label>
                {presetMeterIds && presetMeterIds.length > 0 ? (
                  <p className="text-sm text-gray-700">{presetMeterIds.length === 1 ? `Meter #${presetMeterIds[0]}` : `${presetMeterIds.length} meters selected`}</p>
                ) : (
                  <MeterMultiSelect
                    options={MOCK_METERS}
                    selectedIds={formState.meter_ids}
                    onChange={(ids) => handleSelectChange('meter_ids', ids)}
                  />
                )}
                {formState.meter_ids.length === 0 && !presetMeterIds && (
                  <p className="text-xs text-red-500 mt-1">Please select at least one meter.</p>
                )}
              </div>
            </div>
          ) : (
            // View Details
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Rule Details</h3>
                <Separator className="mb-4" />

                <div className="grid grid-cols-2 gap-x-4 gap-y-3">
                  <div>
                    <p className="text-xs text-gray-500">Metric</p>
                    <p className="font-medium">{getDisplayName(formState.metric)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Condition</p>
                    <p className="font-medium">
                      {getOperatorText(formState.operator)} {formState.threshold.toLocaleString()}
                      {parameter?.unit && <span className="text-gray-500 ml-1">{parameter.unit}</span>}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Severity</p>
                    <Badge variant={formState.severity.toLowerCase() as any} className="capitalize">
                      {formState.severity.toLowerCase()}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Status</p>
                    <span className="text-sm text-green-600">
                      Active
                    </span>
                  </div>
                </div>
              </div>

              {formState.message && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-1">Notification Message</h3>
                  <Separator className="mb-4" />
                  <p className="text-sm">{formState.message}</p>
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">System Information</h3>
                <Separator className="mb-4" />

                <div className="grid grid-cols-2 gap-x-4 gap-y-3">
                  <div>
                    <p className="text-xs text-gray-500">Rule ID</p>
                    <p className="text-sm">{rule?.id ?? '—'}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Created</p>
                    <p className="text-sm">{rule?.created_at ? format(new Date(rule.created_at), 'PPp') : '—'}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Last Updated</p>
                    <p className="text-sm">{rule?.updated_at ? format(new Date(rule.updated_at), 'PPp') : '—'}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DrawerContent>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex justify-end gap-3">
            {isCreating || isEditing ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => isCreating ? onClose() : setIsEditing(false)}
                  disabled={isSaving}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isSaving || !formState.metric || formState.meter_ids.length === 0}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isSaving && <LoadingSpinner className="mr-2 h-4 w-4" />}
                  {isCreating ? 'Create Rule' : 'Save Changes'}
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  onClick={onClose}
                >
                  Close
                </Button>
                <Button
                  onClick={() => setIsEditing(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Rule
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </Drawer>
  );
};

export default RuleDetailDrawer;
