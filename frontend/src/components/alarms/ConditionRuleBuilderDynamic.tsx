import React, { useState, useEffect } from 'react';
import { Plus, Trash2, Copy } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ConditionRule {
  id: string;
  device: string;
  datapoint: string;
  operator: string;
  valueType: 'single' | 'range' | 'datapoint';
  value?: string | number;
  minValue?: string | number;
  maxValue?: string | number;
  compareDevice?: string;
  compareDatapoint?: string;
}

interface ConditionRuleBuilderProps {
  rules: string[];
  onChange: (rules: string[]) => void;
  isReadOnly?: boolean;
}

// Mock devices and datapoints - in production, these would come from API
const DEVICES = [
  { id: 'CH-1', name: 'Chiller 1', type: 'chiller' },
  { id: 'CH-2', name: 'Chiller 2', type: 'chiller' },
  { id: 'CH-3', name: 'Chiller 3', type: 'chiller' },
  { id: 'zone_1', name: 'Zone 1', type: 'zone' },
  { id: 'zone_2', name: 'Zone 2', type: 'zone' },
  { id: 'zone_3', name: 'Zone 3', type: 'zone' },
  { id: 'pump_1', name: 'Pump 1', type: 'pump' },
  { id: 'pump_2', name: 'Pump 2', type: 'pump' },
  { id: 'ahu_1', name: 'AHU 1', type: 'ahu' },
  { id: 'ahu_2', name: 'AHU 2', type: 'ahu' },
  { id: 'server_room', name: 'Server Room', type: 'zone' },
  { id: 'outdoor', name: 'Outdoor', type: 'environment' },
  { id: 'main', name: 'Main Meter', type: 'meter' },
  { id: 'tank_1', name: 'Tank 1', type: 'tank' },
  { id: 'vfd_1', name: 'VFD 1', type: 'vfd' },
];

const DATAPOINTS_BY_TYPE: Record<string, Array<{id: string, name: string, unit?: string, type?: 'numeric' | 'string' | 'boolean'}>> = {
  chiller: [
    { id: 'status', name: 'Status', type: 'string' },
    { id: 'power', name: 'Power', unit: 'kW', type: 'numeric' },
    { id: 'cop', name: 'COP', type: 'numeric' },
    { id: 'efficiency', name: 'Efficiency', unit: '%', type: 'numeric' },
    { id: 'load', name: 'Load', unit: '%', type: 'numeric' },
    { id: 'approach_temp', name: 'Approach Temp', unit: '°C', type: 'numeric' },
    { id: 'chilled_water_temp', name: 'Chilled Water Temp', unit: '°C', type: 'numeric' },
    { id: 'fault', name: 'Fault', type: 'string' },
  ],
  zone: [
    { id: 'temperature', name: 'Temperature', unit: '°C', type: 'numeric' },
    { id: 'humidity', name: 'Humidity', unit: '%', type: 'numeric' },
    { id: 'co2', name: 'CO2', unit: 'ppm', type: 'numeric' },
    { id: 'occupancy', name: 'Occupancy', type: 'boolean' },
    { id: 'setpoint', name: 'Setpoint', unit: '°C', type: 'numeric' },
    { id: 'actual_temp', name: 'Actual Temp', unit: '°C', type: 'numeric' },
  ],
  pump: [
    { id: 'status', name: 'Status', type: 'string' },
    { id: 'power', name: 'Power', unit: 'kW', type: 'numeric' },
    { id: 'flow', name: 'Flow', unit: 'm³/h', type: 'numeric' },
    { id: 'pressure', name: 'Pressure', unit: 'bar', type: 'numeric' },
    { id: 'speed', name: 'Speed', unit: '%', type: 'numeric' },
    { id: 'hours', name: 'Run Hours', unit: 'h', type: 'numeric' },
  ],
  ahu: [
    { id: 'status', name: 'Status', type: 'string' },
    { id: 'supply_temp', name: 'Supply Temp', unit: '°C', type: 'numeric' },
    { id: 'return_temp', name: 'Return Temp', unit: '°C', type: 'numeric' },
    { id: 'fan_speed', name: 'Fan Speed', unit: '%', type: 'numeric' },
    { id: 'damper_position', name: 'Damper Position', unit: '%', type: 'numeric' },
  ],
  environment: [
    { id: 'temp', name: 'Temperature', unit: '°C', type: 'numeric' },
    { id: 'humidity', name: 'Humidity', unit: '%', type: 'numeric' },
  ],
  meter: [
    { id: 'power', name: 'Power', unit: 'kW', type: 'numeric' },
    { id: 'energy', name: 'Energy', unit: 'kWh', type: 'numeric' },
    { id: 'voltage', name: 'Voltage', unit: 'V', type: 'numeric' },
    { id: 'current', name: 'Current', unit: 'A', type: 'numeric' },
    { id: 'power_factor', name: 'Power Factor', type: 'numeric' },
  ],
  tank: [
    { id: 'level', name: 'Level', unit: '%', type: 'numeric' },
    { id: 'volume', name: 'Volume', unit: 'm³', type: 'numeric' },
    { id: 'capacity', name: 'Capacity', unit: 'm³', type: 'numeric' },
  ],
  vfd: [
    { id: 'status', name: 'Status', type: 'string' },
    { id: 'speed', name: 'Speed', unit: 'Hz', type: 'numeric' },
    { id: 'fault', name: 'Fault', type: 'string' },
  ],
};

const OPERATORS = [
  { id: '>', name: '>', label: 'Greater than', types: ['numeric'] },
  { id: '>=', name: '≥', label: 'Greater or equal', types: ['numeric'] },
  { id: '<', name: '<', label: 'Less than', types: ['numeric'] },
  { id: '<=', name: '≤', label: 'Less or equal', types: ['numeric'] },
  { id: '==', name: '=', label: 'Equal to', types: ['numeric', 'string', 'boolean'] },
  { id: '!=', name: '≠', label: 'Not equal to', types: ['numeric', 'string', 'boolean'] },
  { id: 'between', name: '↔', label: 'Between', types: ['numeric'], isRange: true },
  { id: 'not between', name: '↮', label: 'Not between', types: ['numeric'], isRange: true },
];

// Common status values
const STATUS_VALUES = ['running', 'stopped', 'fault', 'maintenance', 'offline'];
const FAULT_VALUES = ['none', 'minor', 'major', 'critical'];

export const ConditionRuleBuilderDynamic: React.FC<ConditionRuleBuilderProps> = ({
  rules,
  onChange,
  isReadOnly = false,
}) => {
  const [parsedRules, setParsedRules] = useState<ConditionRule[]>([]);

  // Parse string rules into structured format
  useEffect(() => {
    const parsed = rules.map((rule, index) => {
      // Try to parse the rule
      const betweenMatch = rule.match(/^([\w_-]+)\.([\w_]+)\s+(between|not between)\s+(-?\d+(?:\.\d+)?)\s+and\s+(-?\d+(?:\.\d+)?)$/);
      const compareMatch = rule.match(/^([\w_-]+)\.([\w_]+)\s*([><=!]+)\s*([\w_-]+)\.([\w_]+)$/);
      const simpleMatch = rule.match(/^([\w_-]+)\.([\w_]+)\s*([><=!]+)\s*["']?([^"']+)["']?$/);

      if (betweenMatch) {
        return {
          id: `rule_${index}`,
          device: betweenMatch[1],
          datapoint: betweenMatch[2],
          operator: betweenMatch[3],
          valueType: 'range' as const,
          minValue: betweenMatch[4],
          maxValue: betweenMatch[5],
        };
      } else if (compareMatch) {
        return {
          id: `rule_${index}`,
          device: compareMatch[1],
          datapoint: compareMatch[2],
          operator: compareMatch[3],
          valueType: 'datapoint' as const,
          compareDevice: compareMatch[4],
          compareDatapoint: compareMatch[5],
        };
      } else if (simpleMatch) {
        return {
          id: `rule_${index}`,
          device: simpleMatch[1],
          datapoint: simpleMatch[2],
          operator: simpleMatch[3],
          valueType: 'single' as const,
          value: simpleMatch[4],
        };
      }

      // Default empty rule
      return {
        id: `rule_${index}`,
        device: '',
        datapoint: '',
        operator: '>',
        valueType: 'single' as const,
        value: '',
      };
    });

    // Only set parsed rules if there are actual rules, don't add default empty rule
    setParsedRules(parsed.length > 0 ? parsed : []);
  }, [rules]);

  // Convert parsed rules back to strings
  const updateRules = (newParsedRules: ConditionRule[]) => {
    const stringRules = newParsedRules
      .filter(rule => rule.device && rule.datapoint) // Only include complete rules
      .map(rule => {
        const deviceDatapoint = `${rule.device}.${rule.datapoint}`;

        if (rule.operator.includes('between')) {
          return `${deviceDatapoint} ${rule.operator} ${rule.minValue || 0} and ${rule.maxValue || 0}`;
        } else if (rule.valueType === 'datapoint' && rule.compareDevice && rule.compareDatapoint) {
          return `${deviceDatapoint} ${rule.operator} ${rule.compareDevice}.${rule.compareDatapoint}`;
        } else {
          // Handle string values that need quotes
          const datapoint = getDatapointInfo(rule.device, rule.datapoint);
          const needsQuotes = datapoint?.type === 'string' && rule.value && 
                            !['true', 'false'].includes(String(rule.value));
          const value = needsQuotes ? `"${rule.value}"` : rule.value;
          return `${deviceDatapoint} ${rule.operator} ${value}`;
        }
      });

    onChange(stringRules);
  };

  const getDeviceType = (deviceId: string): string => {
    const device = DEVICES.find(d => d.id === deviceId);
    return device?.type || 'meter';
  };

  const getDatapointInfo = (deviceId: string, datapointId: string) => {
    const deviceType = getDeviceType(deviceId);
    return DATAPOINTS_BY_TYPE[deviceType]?.find(dp => dp.id === datapointId);
  };

  const getAvailableOperators = (deviceId: string, datapointId: string) => {
    const datapoint = getDatapointInfo(deviceId, datapointId);
    if (!datapoint) return OPERATORS;
    
    return OPERATORS.filter(op => 
      op.types.includes(datapoint.type || 'numeric')
    );
  };

  const addRule = () => {
    const newRule: ConditionRule = {
      id: `rule_${Date.now()}`,
      device: '',
      datapoint: '',
      operator: '>',
      valueType: 'single',
      value: '',
    };
    const newRules = [...parsedRules, newRule];
    setParsedRules(newRules);
  };

  const updateRule = (index: number, updates: Partial<ConditionRule>) => {
    const newRules = [...parsedRules];
    const currentRule = newRules[index];
    
    // Reset dependent fields when device changes
    if (updates.device && updates.device !== currentRule.device) {
      updates.datapoint = '';
      updates.value = '';
      updates.compareDatapoint = '';
    }
    
    // Reset value when datapoint changes
    if (updates.datapoint && updates.datapoint !== currentRule.datapoint) {
      updates.value = '';
    }
    
    // Handle operator changes
    if (updates.operator) {
      const op = OPERATORS.find(o => o.id === updates.operator);
      if (op?.isRange) {
        updates.valueType = 'range';
        updates.value = undefined;
      } else if (currentRule.valueType === 'range') {
        updates.valueType = 'single';
        updates.minValue = undefined;
        updates.maxValue = undefined;
      }
    }
    
    // Reset compare fields when switching value type
    if (updates.valueType && updates.valueType !== currentRule.valueType) {
      if (updates.valueType === 'datapoint') {
        updates.value = undefined;
        updates.minValue = undefined;
        updates.maxValue = undefined;
      } else {
        updates.compareDevice = undefined;
        updates.compareDatapoint = undefined;
      }
    }
    
    newRules[index] = { ...currentRule, ...updates };
    setParsedRules(newRules);
    
    // Only update the parent rules if this rule is complete
    // This prevents rules from disappearing while being edited
    const updatedRule = newRules[index];
    if (updatedRule.device && updatedRule.datapoint && 
        ((updatedRule.valueType === 'single' && updatedRule.value !== undefined && updatedRule.value !== '') ||
         (updatedRule.valueType === 'range' && updatedRule.minValue && updatedRule.maxValue) ||
         (updatedRule.valueType === 'datapoint' && updatedRule.compareDevice && updatedRule.compareDatapoint))) {
      updateRules(newRules);
    }
  };

  const removeRule = (index: number) => {
    const newRules = parsedRules.filter((_, i) => i !== index);
    setParsedRules(newRules);
    updateRules(newRules);
  };

  const duplicateRule = (index: number) => {
    const ruleToCopy = parsedRules[index];
    const newRule = { ...ruleToCopy, id: `rule_${Date.now()}` };
    const newRules = [...parsedRules];
    newRules.splice(index + 1, 0, newRule);
    setParsedRules(newRules);
    updateRules(newRules);
  };

  const renderValueInput = (rule: ConditionRule, index: number) => {
    const datapoint = getDatapointInfo(rule.device, rule.datapoint);
    
    if (rule.valueType === 'range') {
      return (
        <div className="flex items-center gap-2">
          <Input
            type="number"
            value={rule.minValue || ''}
            onChange={(e) => updateRule(index, { minValue: e.target.value })}
            disabled={isReadOnly}
            placeholder="Min"
            className="text-xs h-8"
          />
          <span className="text-xs text-gray-500">to</span>
          <Input
            type="number"
            value={rule.maxValue || ''}
            onChange={(e) => updateRule(index, { maxValue: e.target.value })}
            disabled={isReadOnly}
            placeholder="Max"
            className="text-xs h-8"
          />
          {datapoint?.unit && (
            <span className="text-xs text-gray-500">{datapoint.unit}</span>
          )}
        </div>
      );
    }
    
    if (rule.valueType === 'datapoint') {
      return (
        <div className="grid grid-cols-2 gap-2">
          <Select
            value={rule.compareDevice || ''}
            onValueChange={(value) => updateRule(index, { compareDevice: value, compareDatapoint: '' })}
            disabled={isReadOnly}
          >
            <SelectTrigger className="text-xs h-8">
              <SelectValue placeholder="Select device" />
            </SelectTrigger>
            <SelectContent>
              {DEVICES.map(device => (
                <SelectItem key={device.id} value={device.id}>
                  <span className="text-xs">{device.name}</span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={rule.compareDatapoint || ''}
            onValueChange={(value) => updateRule(index, { compareDatapoint: value })}
            disabled={isReadOnly || !rule.compareDevice}
          >
            <SelectTrigger className="text-xs h-8">
              <SelectValue placeholder="Select datapoint" />
            </SelectTrigger>
            <SelectContent>
              {rule.compareDevice && DATAPOINTS_BY_TYPE[getDeviceType(rule.compareDevice)]?.map(dp => (
                <SelectItem key={dp.id} value={dp.id}>
                  <span className="text-xs">
                    {dp.name} {dp.unit && <span className="text-gray-400">({dp.unit})</span>}
                  </span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );
    }
    
    // Single value input
    if (datapoint?.type === 'string' && ['status', 'fault'].includes(datapoint.id)) {
      const options = datapoint.id === 'status' ? STATUS_VALUES : FAULT_VALUES;
      return (
        <Select
          value={String(rule.value || '')}
          onValueChange={(value) => updateRule(index, { value })}
          disabled={isReadOnly}
        >
          <SelectTrigger className="text-xs h-8">
            <SelectValue placeholder="Select value" />
          </SelectTrigger>
          <SelectContent>
            {options.map(option => (
              <SelectItem key={option} value={option}>
                <span className="text-xs">{option}</span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      );
    }
    
    if (datapoint?.type === 'boolean') {
      return (
        <Select
          value={String(rule.value || '')}
          onValueChange={(value) => updateRule(index, { value })}
          disabled={isReadOnly}
        >
          <SelectTrigger className="text-xs h-8">
            <SelectValue placeholder="Select value" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="true">
              <span className="text-xs">True</span>
            </SelectItem>
            <SelectItem value="false">
              <span className="text-xs">False</span>
            </SelectItem>
          </SelectContent>
        </Select>
      );
    }
    
    return (
      <div className="flex items-center gap-2">
        <Input
          type={datapoint?.type === 'numeric' ? 'number' : 'text'}
          value={rule.value || ''}
          onChange={(e) => updateRule(index, { value: e.target.value })}
          disabled={isReadOnly}
          placeholder="Enter value"
          className="text-xs h-8"
        />
        {datapoint?.unit && (
          <span className="text-xs text-gray-500">{datapoint.unit}</span>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-3">
      {parsedRules.map((rule, index) => (
        <div key={rule.id} className="bg-gray-50 rounded-lg p-4 space-y-3">
          {/* Rule header */}
          <div className="flex items-center justify-between">
            <span className="text-xs font-medium text-gray-700">Condition {index + 1}</span>
            {!isReadOnly && (
              <div className="flex items-center gap-1">
                <button
                  type="button"
                  onClick={() => duplicateRule(index)}
                  className="p-1 text-gray-400 hover:text-gray-600"
                  title="Duplicate"
                >
                  <Copy className="h-3 w-3" />
                </button>
                <button
                  type="button"
                  onClick={() => removeRule(index)}
                  className="p-1 text-gray-400 hover:text-red-500"
                  title="Remove"
                >
                  <Trash2 className="h-3 w-3" />
                </button>
              </div>
            )}
          </div>

          {/* Device and Datapoint */}
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="text-[10px] text-gray-600 mb-1 block">Device</label>
              <Select
                value={rule.device}
                onValueChange={(value) => updateRule(index, { device: value })}
                disabled={isReadOnly}
              >
                <SelectTrigger className="text-xs h-8">
                  <SelectValue placeholder="Select device" />
                </SelectTrigger>
                <SelectContent>
                  {DEVICES.map(device => (
                    <SelectItem key={device.id} value={device.id}>
                      <span className="text-xs">{device.name}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-[10px] text-gray-600 mb-1 block">Datapoint</label>
              <Select
                value={rule.datapoint}
                onValueChange={(value) => updateRule(index, { datapoint: value })}
                disabled={isReadOnly || !rule.device}
              >
                <SelectTrigger className="text-xs h-8">
                  <SelectValue placeholder="Select datapoint" />
                </SelectTrigger>
                <SelectContent>
                  {(() => {
                    const deviceType = getDeviceType(rule.device);
                    const datapoints = DATAPOINTS_BY_TYPE[deviceType];
                    
                    if (!datapoints || datapoints.length === 0) {
                      return <SelectItem value="none" disabled>No datapoints available</SelectItem>;
                    }
                    
                    return datapoints.map(dp => (
                      <SelectItem key={dp.id} value={dp.id}>
                        <span className="text-xs">
                          {dp.name} {dp.unit && <span className="text-gray-400">({dp.unit})</span>}
                        </span>
                      </SelectItem>
                    ));
                  })()}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Operator and Value */}
          {rule.device && rule.datapoint && (
            <>
              <div>
                <label className="text-[10px] text-gray-600 mb-1 block">Operator</label>
                <Select
                  value={rule.operator}
                  onValueChange={(value) => updateRule(index, { operator: value })}
                  disabled={isReadOnly}
                >
                  <SelectTrigger className="text-xs h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {getAvailableOperators(rule.device, rule.datapoint).map(op => (
                      <SelectItem key={op.id} value={op.id}>
                        <span className="text-xs">
                          <span className="font-medium mr-2">{op.name}</span>
                          {op.label}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <div className="flex items-center justify-between mb-1">
                  <label className="text-[10px] text-gray-600">Compare to</label>
                  {!isReadOnly && rule.operator && !OPERATORS.find(o => o.id === rule.operator)?.isRange && (
                    <select
                      value={rule.valueType}
                      onChange={(e) => updateRule(index, { valueType: e.target.value as 'single' | 'datapoint' })}
                      className="text-[10px] border rounded px-2 py-0.5"
                    >
                      <option value="single">Fixed value</option>
                      <option value="datapoint">Another datapoint</option>
                    </select>
                  )}
                </div>
                {renderValueInput(rule, index)}
              </div>

              {/* Generated rule preview */}
              {(rule.value || (rule.minValue && rule.maxValue) || (rule.compareDevice && rule.compareDatapoint)) && (
                <div className="bg-blue-50 rounded px-3 py-2 border border-blue-200">
                  <span className="text-xs text-blue-900 italic">
                    {rule.device}.{rule.datapoint} {rule.operator} {
                      rule.valueType === 'range' 
                        ? `${rule.minValue} and ${rule.maxValue}`
                        : rule.valueType === 'datapoint'
                        ? `${rule.compareDevice}.${rule.compareDatapoint}`
                        : rule.value
                    }
                  </span>
                </div>
              )}
            </>
          )}
        </div>
      ))}

      {!isReadOnly && (
        <button
          type="button"
          onClick={addRule}
          className="w-full py-2 border-2 border-dashed border-gray-300 rounded-lg text-xs text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors"
        >
          <Plus className="h-3 w-3 inline mr-1" />
          Add Condition
        </button>
      )}
    </div>
  );
};