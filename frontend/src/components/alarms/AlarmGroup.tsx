import React from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { AlarmData, AlarmGroup as AlarmGroupType } from './utils/useAlarmGrouping';
import { getSeverityClasses } from './utils/severityUtils';
import { getMetricUnit } from './utils/metricUtils';
import { Badge } from '@/components/ui/badge';

interface AlarmGroupProps<T extends AlarmData> {
  group: AlarmGroupType<T>;
  index: number;
  toggleGroupExpand: (index: number) => void;
  renderAlarmRow: (alarm: T) => React.ReactNode;
}

export function AlarmGroup<T extends AlarmData>({
  group,
  index,
  toggleGroupExpand,
  renderAlarmRow
}: AlarmGroupProps<T>) {
  const severityClasses = getSeverityClasses(group.severity);
  const metricUnit = getMetricUnit(group.metric);

  return (
    <div className="mb-4 border border-gray-200 rounded-md overflow-hidden shadow-sm">
      <div
        className="flex items-center justify-between p-3 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
        onClick={() => toggleGroupExpand(index)}
      >
        <div className="flex items-center space-x-3">
          {group.expanded ?
            <ChevronDown className="h-4 w-4 text-gray-500" /> :
            <ChevronRight className="h-4 w-4 text-gray-500" />
          }

          <div className="font-medium">
            {group.metric.split('_').map(word =>
              word.charAt(0).toUpperCase() + word.slice(1)
            ).join(' ')}
            {metricUnit && <span className="ml-1 text-gray-500 text-sm">{metricUnit}</span>}
          </div>

          <Badge className={severityClasses}>
            {group.severity}
          </Badge>
        </div>

        <div className="text-sm text-gray-600">
          {group.alarms.length} {group.alarms.length === 1 ? 'alarm' : 'alarms'}
        </div>
      </div>

      {group.expanded && (
        <div className="border-t border-gray-200">
          {group.alarms.map(alarm => renderAlarmRow(alarm))}
        </div>
      )}
    </div>
  );
}
