import React, { memo } from 'react';
import { Row, flexRender } from '@tanstack/react-table';
import { TableCell, TableRow } from "@/components/ui/table";
import { AlarmRule } from '@/types/alarms';
import { Button } from '@/components/ui/Button';
import { Edit, Trash2 } from 'lucide-react';

interface MemoizedTableRowProps {
  row: Row<AlarmRule>;
  onViewRule: (rule: AlarmRule) => void;
  onDeleteRule: (id: number) => void;
  isDeleting: boolean;
}

// Custom equality function to prevent unnecessary re-renders
const areEqual = (prevProps: MemoizedTableRowProps, nextProps: MemoizedTableRowProps) => {
  // Only re-render if the row data has changed or if the deleting state has changed
  const prevOriginal = prevProps.row.original;
  const nextOriginal = nextProps.row.original;
  
  return (
    prevOriginal.id === nextOriginal.id &&
    prevOriginal.name === nextOriginal.name &&
    prevOriginal.metric === nextOriginal.metric &&
    prevOriginal.threshold === nextOriginal.threshold &&
    prevOriginal.operator === nextOriginal.operator &&
    prevOriginal.severity === nextOriginal.severity &&
    prevOriginal.is_active === nextOriginal.is_active &&
    prevProps.isDeleting === nextProps.isDeleting
  );
};

const MemoizedTableRow = memo(function MemoizedTableRow({
  row,
  onViewRule,
  onDeleteRule,
  isDeleting
}: MemoizedTableRowProps) {
  return (
    <TableRow
      key={row.id}
      className="hover:bg-gray-50 cursor-pointer"
      onClick={() => onViewRule(row.original)}
    >
      {row.getVisibleCells().map((cell) => {
        // Special rendering for action buttons to attach handlers
        if (cell.column.id === 'actions') {
          return (
            <TableCell key={cell.id} className="text-right py-2 whitespace-nowrap">
              <Button
                variant="ghost"
                size="icon"
                className="mr-1 h-8 w-8 text-gray-500 hover:text-blue-600 hover:bg-blue-50"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent row click
                  onViewRule(row.original);
                }}
                title="Edit Rule"
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-500 hover:text-red-600 hover:bg-red-50"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent row click
                  onDeleteRule(row.original.id);
                }}
                disabled={isDeleting}
                title="Delete Rule"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </TableCell>
          );
        }
        // Normal cells
        return (
          <TableCell key={cell.id} className="py-2">
            {flexRender(cell.column.columnDef.cell, cell.getContext())}
          </TableCell>
        );
      })}
    </TableRow>
  );
}, areEqual);

export default MemoizedTableRow;
