import React, { useState, useEffect } from 'react';
import { X, Info, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ConditionRuleBuilder } from './ConditionRuleBuilder';

interface FaultConfigFormData {
  name: string;
  description?: string;
  category?: string;
  severity: 'critical' | 'warning' | 'info';
  enabled: boolean;
  condition_logic: 'all' | 'any';
  condition_rules: string[];
  delay_seconds: number;
  operating_hours?: string | null;
  metadata?: Record<string, any> | null;
}

interface FaultConfigFormProps {
  mode: 'create' | 'edit' | 'view';
  initialData?: Partial<FaultConfigFormData>;
  onSubmit: (data: FaultConfigFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const defaultFormData: FaultConfigFormData = {
  name: '',
  description: '',
  severity: 'info',
  enabled: true,
  condition_logic: 'all',
  condition_rules: [''],
  delay_seconds: 0,
  operating_hours: null,
  metadata: null,
};

// Operator options for the dropdown
const operators = [
  { value: '>', label: 'Greater than (>)' },
  { value: '>=', label: 'Greater than or equal (>=)' },
  { value: '<', label: 'Less than (<)' },
  { value: '<=', label: 'Less than or equal (<=)' },
  { value: '==', label: 'Equal to (==)' },
  { value: '!=', label: 'Not equal to (!=)' },
  { value: 'between', label: 'Between' },
  { value: 'not between', label: 'Not between' },
];

// Common device.datapoint examples
const commonDatapoints = [
  'temperature', 'humidity', 'power', 'energy', 'voltage', 'current',
  'flow', 'pressure', 'level', 'status', 'efficiency', 'cop',
  'setpoint', 'actual_temp', 'load', 'hours', 'fault'
];

export const FaultConfigForm: React.FC<FaultConfigFormProps> = ({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const [formData, setFormData] = useState<FaultConfigFormData>({
    ...defaultFormData,
    ...initialData,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const isReadOnly = mode === 'view';

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.includes(' ')) {
      newErrors.name = 'Name cannot contain spaces';
    }

    if (formData.condition_rules.length === 0 || formData.condition_rules.every(rule => !rule.trim())) {
      newErrors.condition_rules = 'At least one condition rule is required';
    }

    // Validate each condition rule syntax
    formData.condition_rules.forEach((rule, index) => {
      if (rule.trim() && !validateConditionRule(rule)) {
        newErrors[`condition_rule_${index}`] = 'Invalid rule syntax';
      }
    });

    if (formData.delay_seconds < 0) {
      newErrors.delay_seconds = 'Delay must be 0 or positive';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Basic validation for condition rules
  const validateConditionRule = (rule: string): boolean => {
    // Basic pattern: device.datapoint operator value
    const basicPattern = /^\s*[\w_]+\.[\w_]+\s*(<|>|<=|>=|==|!=|between|not between)\s*.+$/;
    const functionPattern = /^\s*(abs|min|max|round)\s*\(/;
    
    return basicPattern.test(rule) || functionPattern.test(rule);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      // Clean up empty rules
      const cleanedData = {
        ...formData,
        condition_rules: formData.condition_rules.filter(rule => rule.trim()),
      };
      onSubmit(cleanedData);
    }
  };

  const handleFieldChange = (field: keyof FaultConfigFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };


  const formatDelay = (seconds: number): string => {
    if (seconds === 0) return 'No delay';
    if (seconds < 60) return `${seconds} seconds`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)} minutes`;
    return `${Math.floor(seconds / 3600)} hours`;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">
          {mode === 'create' ? 'Create Fault Configuration' : 
           mode === 'edit' ? 'Edit Fault Configuration' : 
           'Fault Configuration Details'}
        </h3>
        <button
          type="button"
          onClick={onCancel}
          className="text-gray-500 hover:text-gray-700"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      <Separator />

      {/* Basic Information */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-900">Basic Information</h4>
        
        {/* Name */}
        <div>
          <Label htmlFor="name" className="text-xs">
            Name <span className="text-red-500">*</span>
          </Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => handleFieldChange('name', e.target.value)}
            disabled={isReadOnly || mode === 'edit'}
            placeholder="e.g., high_temp_zone_1"
            className={`text-xs ${errors.name ? "border-red-500" : ""}`}
          />
          {errors.name && (
            <p className="mt-1 text-[10px] text-red-500">{errors.name}</p>
          )}
          <p className="mt-1 text-[10px] text-gray-500">
            Unique identifier, no spaces allowed
          </p>
        </div>

        {/* Description */}
        <div>
          <Label htmlFor="description" className="text-xs">Description</Label>
          <Textarea
            id="description"
            value={formData.description || ''}
            onChange={(e) => handleFieldChange('description', e.target.value)}
            disabled={isReadOnly}
            placeholder="Brief description of the fault"
            rows={2}
            className="text-xs"
          />
        </div>

        {/* Category */}
        <div>
          <Label htmlFor="category" className="text-xs">Category</Label>
          <Input
            id="category"
            value={formData.category || ''}
            onChange={(e) => handleFieldChange('category', e.target.value)}
            disabled={isReadOnly}
            placeholder="e.g., Critical Systems, Comfort, Energy Efficiency"
            className="text-xs"
          />
        </div>

        {/* Severity and Enabled */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="severity" className="text-xs">Severity</Label>
            {isReadOnly ? (
              <div className="mt-1">
                <Badge className={`text-[10px] px-2 py-0.5 ${
                  formData.severity === 'critical' 
                    ? 'bg-red-500 text-white'
                    : formData.severity === 'warning'
                    ? 'bg-amber-500 text-white'
                    : 'bg-gray-500 text-white'
                }`}>
                  {formData.severity.charAt(0).toUpperCase() + formData.severity.slice(1)}
                </Badge>
              </div>
            ) : (
              <Select
                value={formData.severity}
                onValueChange={(value) => handleFieldChange('severity', value)}
                disabled={isReadOnly}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="warning">Warning</SelectItem>
                  <SelectItem value="info">Info</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="enabled" className="text-xs">Enabled</Label>
            <Switch
              id="enabled"
              checked={formData.enabled}
              onCheckedChange={(checked) => handleFieldChange('enabled', checked)}
              disabled={isReadOnly}
            />
          </div>
        </div>
      </div>

      <Separator />

      {/* Condition Rules */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-gray-900">
            Condition Rules <span className="text-red-500">*</span>
          </h4>
          <Select
            value={formData.condition_logic}
            onValueChange={(value) => handleFieldChange('condition_logic', value)}
            disabled={isReadOnly}
          >
            <SelectTrigger className="w-32 text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All conditions</SelectItem>
              <SelectItem value="any">Any condition</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {errors.condition_rules && (
          <div className="flex items-center gap-2 text-[10px] text-red-500">
            <AlertCircle className="h-3 w-3" />
            {errors.condition_rules}
          </div>
        )}

        <ConditionRuleBuilder
          rules={formData.condition_rules}
          onChange={(newRules) => {
            setFormData(prev => ({ ...prev, condition_rules: newRules }));
            // Clear errors when rules change
            if (errors.condition_rules) {
              setErrors(prev => ({ ...prev, condition_rules: '' }));
            }
          }}
          isReadOnly={isReadOnly}
        />

        {/* Help text */}
        <div className="bg-gray-50 rounded-md p-3 space-y-1">
          <div className="flex items-start gap-2">
            <Info className="h-3 w-3 text-gray-500 mt-0.5" />
            <div className="text-[10px] text-gray-600 space-y-1">
              <p className="font-medium">Rule syntax: device.datapoint operator value</p>
              <p>Examples:</p>
              <ul className="list-disc list-inside space-y-0.5 ml-2">
                <li>chiller_1.power &gt; 150</li>
                <li>zone_1.temp between 20 and 26</li>
                <li>pump_1.status == 'running'</li>
                <li>abs(setpoint - actual) &gt; 2</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Advanced Settings */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-900">Advanced Settings</h4>
        
        {/* Delay */}
        <div>
          <Label htmlFor="delay" className="text-xs">Delay before alert</Label>
          <div className="flex items-center gap-2">
            <Input
              id="delay"
              type="number"
              value={formData.delay_seconds}
              onChange={(e) => handleFieldChange('delay_seconds', parseInt(e.target.value) || 0)}
              disabled={isReadOnly}
              min="0"
              className={`text-xs w-24 ${errors.delay_seconds ? "border-red-500" : ""}`}
            />
            <span className="text-xs text-gray-500">seconds</span>
            <span className="text-xs text-gray-400">
              ({formatDelay(formData.delay_seconds)})
            </span>
          </div>
          {errors.delay_seconds && (
            <p className="mt-1 text-[10px] text-red-500">{errors.delay_seconds}</p>
          )}
        </div>

        {/* Operating Hours */}
        <div>
          <Label htmlFor="operating_hours" className="text-xs">Operating Hours</Label>
          <Input
            id="operating_hours"
            value={formData.operating_hours || ''}
            onChange={(e) => handleFieldChange('operating_hours', e.target.value || null)}
            disabled={isReadOnly}
            placeholder="e.g., 08:00-18:00 (leave empty for 24/7)"
            className="text-xs"
          />
        </div>
      </div>

      {/* Actions */}
      {!isReadOnly && (
        <>
          <Separator />
          <div className="flex items-center justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={onCancel}
              disabled={isLoading}
              className="text-xs"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              size="sm"
              disabled={isLoading}
              className="text-xs"
            >
              {isLoading ? 'Saving...' : mode === 'create' ? 'Create' : 'Save Changes'}
            </Button>
          </div>
        </>
      )}
    </form>
  );
};