import React, { useState, useMemo, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient, keepPreviousData } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import {
  ColumnDef,
  SortingState,
  PaginationState,
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  useReactTable,
  flexRender,
} from '@tanstack/react-table';
import { ArrowUpDown, PlusCircle } from 'lucide-react';
import { TablePagination } from '@/components/ui/table-pagination';

import { getAFDDFaults, deleteAlarmRule } from '@/lib/api/alarms';
import { AlarmRule, AlarmOperator } from '@/types/alarms';
import { PaginatedResponse } from '@/types/api';
import { AlarmRulesTabSkeleton } from '@/components/ui/alarms-skeletons';
import { EnhancedErrorDisplay } from '@/components/ui/error-display';
import { useRetry } from '@/hooks/useRetry';
import RuleDetailPanel from './RuleDetailPanel';
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from '@/components/ui/Button';
import { AlertCircle } from 'lucide-react';

// Mock parameters for alarm rules
const MOCK_PARAMETERS = [
  { id: 1, name: 'Power', unit: 'kW' },
  { id: 2, name: 'Energy', unit: 'kWh' },
  { id: 3, name: 'Voltage', unit: 'V' },
  { id: 4, name: 'Current', unit: 'A' },
  { id: 5, name: 'Power Factor', unit: 'PF' },
];

// Function to get a display name for a metric
const getDisplayName = (metric: string): string => {
  // Replace underscores with spaces and capitalize each word
  return metric
    .replace(/_/g, ' ')
    .replace(/\b\w/g, (char) => char.toUpperCase());
};

// Function to get a human-readable operator text
const getOperatorText = (operator: AlarmOperator): string => {
  switch (operator) {
    case '>':
      return 'exceeds';
    case '<':
      return 'falls below';
    case '>=':
      return 'is at least';
    case '<=':
      return 'is at most';
    default:
      return operator;
  }
};

// Define columns for the table with sorting
const createColumns = (onRowClick: (rule: AlarmRule) => void) => {
  const columns: ColumnDef<AlarmRule>[] = [
    {
      accessorKey: 'is_active',
      header: () => <div className="text-center text-xs">Status</div>,
      cell: ({ row }) => (
        <div className="flex justify-center">
          <Badge
            variant="outline"
            className={`text-[10px] px-2 py-0.5 ${
              row.original.is_active
                ? "bg-blue-100 text-blue-800 border-blue-200"
                : "bg-gray-100 text-gray-600 border-gray-200"
            }`}
          >
            {row.original.is_active ? 'Enabled' : 'Disabled'}
          </Badge>
        </div>
      ),
      size: 80,
    },
    {
      id: 'name',
      accessorKey: 'name',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="-ml-4 h-7 text-xs data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Name
          <ArrowUpDown className="ml-1 h-3 w-3" />
        </Button>
      ),
      cell: ({ row }) => {
        const name = row.original.name || 'Unknown';
        return (
          <div className="text-xs text-gray-900 truncate" title={name}>
            {name}
          </div>
        );
      },
      size: 300,
    },
    {
      id: 'category',
      accessorKey: 'category',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="-ml-4 h-7 text-xs data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Category
          <ArrowUpDown className="ml-1 h-3 w-3" />
        </Button>
      ),
      cell: ({ row }) => (
        <span className="text-xs text-gray-700">{row.original.category || '-'}</span>
      ),
      size: 150,
    },
    {
      id: 'severity',
      accessorKey: 'severity',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="-ml-4 h-7 text-xs data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Severity
          <ArrowUpDown className="ml-1 h-3 w-3" />
        </Button>
      ),
      cell: ({ row }) => {
        const severity = row.original.severity.toLowerCase();
        return (
          <Badge 
            className={`text-[10px] px-2 py-0.5 ${
              severity === 'critical' 
                ? 'bg-red-500 text-white hover:bg-red-500'
                : severity === 'warning'
                ? 'bg-amber-500 text-white hover:bg-amber-500'
                : 'bg-gray-500 text-white hover:bg-gray-500'
            }`}
          >
            {severity.charAt(0).toUpperCase() + severity.slice(1)}
          </Badge>
        );
      },
      size: 80,
    },
  ];

  return columns;
};

const AlarmRulesTab: React.FC = () => {
  const queryClient = useQueryClient();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedRule, setSelectedRule] = useState<AlarmRule | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([]);
  const [severityFilters, setSeverityFilters] = useState<string[]>(['critical', 'warning', 'info']);
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  // Debug log sorting state changes
  console.log('Current sorting state:', sorting);
  

  // --- API Query Parameters ---
  const queryParams = useMemo(() => {
    const params: Record<string, string> = {
      page: String(pagination.pageIndex + 1),
      page_size: String(pagination.pageSize),
    };

    // Add sorting
    if (sorting.length > 0) {
      const sort = sorting[0];
      params.ordering = `${sort.desc ? '-' : ''}${sort.id}`;
      console.log('Sorting params - id:', sort.id, 'desc:', sort.desc, 'ordering:', params.ordering);
    }

    // Note: Filtering is handled client-side in the rules useMemo
    // Server-side filtering could be added here if needed in the future

    console.log('Query params being sent to API:', params);
    return params;
  }, [pagination, sorting]);

  const { retry, retryCount, isRetrying: isRetryingHook } = useRetry(async () => {
    await getAFDDFaults(queryParams);
  });

  // Memoize query key to include params
  const queryKey = useMemo(() => ['afdd-faults', queryParams], [queryParams]);

  // Fetch alarm rules with optimized configuration
  const { data: rulesResponse, isLoading, error, isError, isFetching } = useQuery<
    PaginatedResponse<AlarmRule>,
    Error
  >({
    queryKey,
    queryFn: () => {
      console.log('Fetching AFDD faults with params:', queryParams);
      return getAFDDFaults(queryParams);
    },
    staleTime: 0, // Always refetch when sorting changes
    gcTime: 5 * 60 * 1000,
    refetchInterval: false,
    refetchOnWindowFocus: false,
    placeholderData: keepPreviousData, // Keep previous data while fetching
  });

  // Handler functions
  const handleViewRule = (rule: AlarmRule) => {
    // When clicking on a row while in create mode, just close the create panel
    if (isCreating) {
      setIsCreating(false);
      setSelectedRule(null);
    } else {
      setSelectedRule(rule);
      setIsCreating(false);
    }
  };

  const handleCreateRule = () => {
    setSelectedRule(null);
    setIsCreating(true);
  };

  const handleClosePanel = () => {
    setSelectedRule(null);
    setIsCreating(false);
  };

  const { mutate: deleteRule, isPending: isDeleting } = useMutation({
    mutationFn: deleteAlarmRule,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['afdd-faults'] });
      // TODO: Add success toast notification
    },
    onError: (err) => {
      // TODO: Add error toast notification
      console.error("Failed to delete rule:", err);
    },
  });

  const handleDeleteRule = (id: number) => {
    if (window.confirm(`Are you sure you want to delete the rule?`)) {
      deleteRule(id);
    }
  };

  // Debug log API response
  console.log('API Response:', rulesResponse);

  // Memoized columns based on panel state
  const columns = useMemo(() => createColumns(handleViewRule), []);

  // Filter rules based on severity and status filters
  const rules = useMemo(() => {
    let filtered = rulesResponse?.results ?? [];

    // Apply severity filter
    if (severityFilters.length > 0) {
      filtered = filtered.filter(rule =>
        severityFilters.includes(rule.severity)
      );
    }


    return filtered;
  }, [rulesResponse?.results, severityFilters]);

  // Calculate page count
  const pageCount = useMemo(() => {
    const count = rulesResponse?.count;
    return count ? Math.ceil(count / pagination.pageSize) : -1;
  }, [rulesResponse?.count, pagination.pageSize]);

  // Handle fault parameter from URL (coming from log detail)
  useEffect(() => {
    const faultId = searchParams.get('fault');
    if (faultId && rules.length > 0) {
      const rule = rules.find(r => r.id === parseInt(faultId));
      if (rule) {
        setSelectedRule(rule);
        setIsCreating(false);
        // Clear the parameter after handling it
        searchParams.delete('fault');
        setSearchParams(searchParams);
      }
    }
  }, [searchParams, rules, setSearchParams]);

  // We're using the MOCK_PARAMETERS defined at the top of the file

  // Table instance - must be called unconditionally
  const table = useReactTable<AlarmRule>({
    data: rules,
    columns,
    // Configure server-side features
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
    pageCount: pageCount,
    state: {
      sorting,
      pagination, // Add pagination state
    },
    onSortingChange: (updater) => {
      console.log('onSortingChange called with:', updater);
      setSorting(updater);
    },
    onPaginationChange: setPagination, // Add pagination handler
    getCoreRowModel: getCoreRowModel(),
    // Keep faceted utils if using faceted filters UI
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  // --- UI Render Functions ---
  // Memoize the header rendering to prevent re-renders
  const renderRulesHeader = useMemo(() => (
    <div className="pb-3 flex items-center justify-between border-b border-gray-100">
      <div className="flex items-center gap-3">
        {/* Severity Filter */}
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-600">Severity:</span>
          <div className="flex gap-2">
            {['critical', 'warning', 'info'].map((severity) => {
              const isSelected = severityFilters.includes(severity);
              return (
                <button
                  key={severity}
                  onClick={() => {
                    if (isSelected) {
                      setSeverityFilters(prev => prev.filter(s => s !== severity));
                    } else {
                      setSeverityFilters(prev => [...prev, severity]);
                    }
                    table.setPageIndex(0);
                  }}
                  className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${isSelected
                    ? severity === 'critical'
                      ? 'bg-red-500 text-white'
                      : severity === 'warning'
                        ? 'bg-amber-500 text-white'
                        : 'bg-slate-500 text-white'
                    : 'bg-gray-50 text-gray-500 hover:bg-gray-100 border border-gray-200'
                    }`}
                >
                  {severity === 'critical' ? 'Critical' : severity === 'warning' ? 'Warning' : 'Info'}
                </button>
              );
            })}
          </div>
        </div>

      </div>

      {/* Create button */}
      <Button
        variant="outline"
        size="sm"
        className="h-9 text-xs bg-white border-gray-300 hover:bg-gray-50 transition-colors duration-150"
        onClick={handleCreateRule}
      >
        <PlusCircle className="mr-2 h-4 w-4" /> Create New Rule
      </Button>
    </div>
  ), [table]); // Removed API count dependency since it's moving to pagination area

  if (isLoading && !rulesResponse) {
    return <AlarmRulesTabSkeleton />;
  }

  if (isError) {
    // Show empty table on error
    console.error('Error fetching AFDD faults:', error);
  }

  return (
    <div className="h-full flex">
      {/* Main Content Section */}
      <div className={`flex flex-col p-4 transition-all duration-300 ${selectedRule || isCreating ? 'w-1/2' : 'w-full'}`}>
        {renderRulesHeader}

        <div className="flex-1 flex flex-col min-h-0">
          {rules.length === 0 && !isFetching ? ( // Show empty state only when not fetching
            <div className="py-12 text-center">
              <AlertCircle className="mx-auto h-12 w-12 text-gray-300" />
              <h3 className="mt-2 text-xs font-medium text-gray-800">No Alarm Rules</h3>
              <p className="mt-1 text-xs text-gray-500">Get started by creating a new alarm rule.</p>
              <div className="mt-6">
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9 text-xs bg-white border-gray-300 hover:bg-gray-50 transition-colors duration-150"
                  onClick={handleCreateRule}
                >
                  <PlusCircle className="mr-2 h-4 w-4" /> Create New Rule
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex-1 overflow-auto">
              <Table className="text-xs">
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={headerGroup.id}>
                      {headerGroup.headers.map((header) => (
                        <TableHead key={header.id} className="py-2 h-auto">
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                        </TableHead>
                      ))}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table.getRowModel().rows?.length ? (
                    table.getRowModel().rows.map((row) => (
                      <TableRow 
                        key={row.id}
                        className={`cursor-pointer transition-colors ${
                          selectedRule?.id === row.original.id && !isCreating
                            ? 'bg-blue-50 hover:bg-blue-100' 
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleViewRule(row.original);
                        }}
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell key={cell.id} className="py-2">
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-24 text-center text-gray-500"
                      >
                        No fault configurations found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Pagination with rule count */}
          <div className="border-t border-gray-200 py-1 flex items-center justify-between">
            <div className="text-xs text-gray-600 pl-2">
              Alarm Rules ({rulesResponse?.count ?? 0} total)
            </div>
            <TablePagination table={table} />
          </div>
        </div>
      </div>

      {/* Detail Panel */}
      {(selectedRule || isCreating) && (
        <RuleDetailPanel
          rule={selectedRule}
          isCreating={isCreating}
          onClose={handleClosePanel}
          onCreate={(_rule) => {
            queryClient.invalidateQueries({ queryKey: ['afdd-faults'] });
            handleClosePanel();
          }}
          onEdit={handleViewRule}
          onDelete={handleDeleteRule}
        />
      )}
    </div>
  );
};

export default AlarmRulesTab;
