import React, { useState, useMemo, useEffect } from 'react';
import { useToast } from '@/components/ui/ToastProvider';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getPaginationRowModel,
  SortingState,
  ColumnDef,
  Row,
  Column,
  PaginationState,
} from '@tanstack/react-table';
import { ArrowUpDown } from 'lucide-react';
import { TablePagination } from '@/components/ui/table-pagination';
import { useQuery, keepPreviousData, useQueryClient } from '@tanstack/react-query';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/Button';
import { ActiveAlarmsTabSkeleton } from '@/components/ui/alarms-skeletons';
import { EnhancedErrorDisplay } from '@/components/ui/error-display';
import { useRetry } from '@/hooks/useRetry';
import { getAFDDLogs } from '@/lib/api/alarms';
import { AFDDLog } from '@/types/alarms';
import LogDetailPanel from './LogDetailPanel';
import StateDropdown from './StateDropdown';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { flexRender } from '@tanstack/react-table';

// Style helper functions
const getSeverityClasses = (severity: string): string => {
  switch (severity) {
    case 'critical':
      return "bg-red-500 text-white";
    case 'warning':
      return "bg-amber-500 text-white";
    case 'info':
      return "bg-slate-500 text-white";
    default:
      return "bg-gray-500 text-white";
  }
};

const getAlarmStateClasses = (alarmState: string): string => {
  switch (alarmState) {
    case 'active':
      return "bg-red-100 text-red-800 border border-red-200";
    case 'normal':
      return "bg-green-100 text-green-800 border border-green-200";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getAckStateClasses = (ackState: string): string => {
  switch (ackState) {
    case 'unacknowledged':
      return "bg-yellow-100 text-yellow-800 border border-yellow-200";
    case 'acknowledged':
      return "bg-blue-100 text-blue-800 border border-blue-200";
    case 'cleared':
      return "bg-gray-100 text-gray-600 border border-gray-200";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

// Table column definitions
const createColumns = (
  onDetailClick: (log: AFDDLog) => void
): ColumnDef<AFDDLog>[] => [
    {
      accessorKey: 'active_at',
      header: ({ column }: { column: Column<AFDDLog, unknown> }) => (
        <Button
          variant="ghost"
          className="-ml-4 h-7 text-xs data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Date Time
          <ArrowUpDown className="ml-1 h-3 w-3" />
        </Button>
      ),
      cell: ({ row }: { row: Row<AFDDLog> }) => {
        const date = new Date(row.original.active_at);
        const day = date.getDate();
        const month = date.toLocaleDateString('en-US', { month: 'short' });
        const year = date.getFullYear().toString().slice(-2);
        const time = date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        });
        return (
          <div className="text-[11px] text-gray-700">
            {`${day} ${month} ${year}, ${time}`}
          </div>
        );
      },
      size: 100,
    },
    {
      accessorKey: 'duration',
      header: ({ column }: { column: Column<AFDDLog, unknown> }) => (
        <Button
          variant="ghost"
          className="-ml-4 h-7 text-xs data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Duration
          <ArrowUpDown className="ml-1 h-3 w-3" />
        </Button>
      ),
      cell: ({ row }: { row: Row<AFDDLog> }) => {
        const duration = row.original.duration;
        const days = Math.floor(duration / 86400);
        const hours = Math.floor((duration % 86400) / 3600);
        const minutes = Math.floor((duration % 3600) / 60);

        if (days > 0) {
          return <span className="text-xs text-gray-700">{days}d {hours}h</span>;
        } else if (hours > 0) {
          return <span className="text-xs text-gray-700">{hours}h {minutes}m</span>;
        } else {
          return <span className="text-xs text-gray-700">{minutes}m</span>;
        }
      },
      size: 80,
    },
    {
      accessorKey: 'alarm_state',
      header: () => <div className="text-center text-xs">Status</div>,
      cell: ({ row }: { row: Row<AFDDLog> }) => (
        <div className="flex justify-center">
          <Badge
            variant="outline"
            className={`text-[10px] px-2 py-0.5 ${row.original.alarm_state === 'active'
                ? 'bg-red-100 text-red-800 border-red-200'
                : 'bg-green-100 text-green-800 border-green-200'
              }`}
          >
            {row.original.alarm_state === 'active' ? 'Active' : 'Normal'}
          </Badge>
        </div>
      ),
      size: 80,
    },
    {
      accessorKey: 'fault_name',
      header: ({ column }: { column: Column<AFDDLog, unknown> }) => (
        <Button
          variant="ghost"
          className="-ml-4 h-7 text-xs data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Fault
          <ArrowUpDown className="ml-1 h-3 w-3" />
        </Button>
      ),
      cell: ({ row }: { row: Row<AFDDLog> }) => {
        const faultName = row.original.fault_name || 'Unknown';
        return (
          <div className="text-xs text-gray-900 truncate" title={faultName}>
            {faultName}
          </div>
        );
      },
      size: 300,
    },
    {
      accessorKey: 'severity',
      header: ({ column }: { column: Column<AFDDLog, unknown> }) => (
        <Button
          variant="ghost"
          className="-ml-4 h-7 text-xs data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Severity
          <ArrowUpDown className="ml-1 h-3 w-3" />
        </Button>
      ),
      cell: ({ row }: { row: Row<AFDDLog> }) => {
        const severityStyles = {
          critical: 'bg-red-50 text-red-700 border-red-200',
          warning: 'bg-amber-50 text-amber-700 border-amber-200',
          info: 'bg-gray-50 text-gray-700 border-gray-200'
        };
        return (
          <Badge
            className={`text-[10px] px-2 py-0.5 ${row.original.severity === 'critical'
                ? 'bg-red-500 text-white hover:bg-red-500'
                : row.original.severity === 'warning'
                  ? 'bg-amber-500 text-white hover:bg-amber-500'
                  : 'bg-gray-500 text-white hover:bg-gray-500'
              }`}
          >
            {row.original.severity.charAt(0).toUpperCase() + row.original.severity.slice(1)}
          </Badge>
        );
      },
      size: 80,
    },
    {
      accessorKey: 'ack_state',
      header: ({ column }: { column: Column<AFDDLog, unknown> }) => (
        <Button
          variant="ghost"
          className="-ml-4 h-7 text-xs data-[state=open]:bg-accent"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          State
          <ArrowUpDown className="ml-1 h-3 w-3" />
        </Button>
      ),
      cell: ({ row }: { row: Row<AFDDLog> }) => {
        const log = row.original;

        return (
          <StateDropdown
            logId={log.id}
            currentState={log.ack_state}
            isDisabled={false}
          />
        );
      },
      size: 120,
    },
  ];

export const AFDDLogsTab: React.FC = () => {
  const queryClient = useQueryClient();
  const { showToast } = useToast();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [detailLog, setDetailLog] = useState<AFDDLog | null>(null);
  const [severityFilters, setSeverityFilters] = useState<string[]>(['critical', 'warning', 'info']);
  const [alarmStateFilter, setAlarmStateFilter] = useState<string>('all');
  const [ackStateFilter, setAckStateFilter] = useState<string>('all');

  // Reset pagination when filters change
  useEffect(() => {
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  }, [severityFilters, alarmStateFilter, ackStateFilter]);

  // Build query parameters
  const queryOptions = {
    page: (pagination.pageIndex + 1).toString(),
    page_size: pagination.pageSize.toString(),
    ordering: sorting.length > 0 ?
      (sorting[0].desc ? '-' : '') + sorting[0].id :
      '-active_at',
  };

  // Fetch data
  const fetchData = () => getAFDDLogs(queryOptions);
  const { retry, retryCount, isRetrying } = useRetry(fetchData);

  const { data: logsData, isLoading, isError, error } = useQuery({
    queryKey: ['afdd-logs', queryOptions, severityFilters, alarmStateFilter, ackStateFilter],
    queryFn: fetchData,
    placeholderData: keepPreviousData,
    refetchInterval: false,
    refetchOnWindowFocus: false,
  });

  // Custom severity sorting function
  const severityOrder = { critical: 0, warning: 1, info: 2 };

  // Filter data based on selected filters
  const filteredData = useMemo(() => {
    if (!logsData || !logsData.results) return [];

    let filtered = logsData.results;

    // Apply severity filters
    if (severityFilters.length > 0 && severityFilters.length < 3) {
      filtered = filtered.filter(log =>
        severityFilters.includes(log.severity)
      );
    }

    // Apply alarm state filter
    if (alarmStateFilter !== 'all') {
      filtered = filtered.filter(log =>
        log.alarm_state === alarmStateFilter
      );
    }

    // Apply acknowledgment state filter
    if (ackStateFilter !== 'all') {
      filtered = filtered.filter(log =>
        log.ack_state === ackStateFilter
      );
    }

    return filtered;
  }, [logsData, severityFilters, alarmStateFilter, ackStateFilter]);

  // Handle detail click
  const handleDetailClick = (log: AFDDLog) => {
    console.log('handleDetailClick called with log:', log);
    setDetailLog(log);
  };

  // Create columns with handlers
  const columns = useMemo(() => createColumns(handleDetailClick), []);

  // Create table instance
  const table = useReactTable({
    data: filteredData,
    columns,
    state: {
      sorting,
      pagination,
    },
    pageCount: logsData?.count ? Math.ceil(logsData.count / pagination.pageSize) : -1,
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: true,
    manualSorting: true,
    sortingFns: {
      auto: (rowA, rowB, columnId) => {
        if (columnId === 'severity') {
          const a = severityOrder[rowA.getValue(columnId) as keyof typeof severityOrder] ?? 999;
          const b = severityOrder[rowB.getValue(columnId) as keyof typeof severityOrder] ?? 999;
          return a - b;
        }
        return 0;
      }
    }
  });

  if (isLoading) {
    return <ActiveAlarmsTabSkeleton />;
  }

  if (isError) {
    return (
      <EnhancedErrorDisplay
        error={error}
        onRetry={retry}
        retryCount={retryCount}
        isRetrying={isRetrying}
        context="Alarm Log"
      />
    );
  }

  return (
    <div className="h-full flex">
      {/* Main Content Section */}
      <div className={`flex flex-col p-4 transition-all duration-300 ${detailLog ? 'w-1/2' : 'w-full'}`}>
        {/* Filters */}
        <div className="pb-3 flex items-center justify-between border-b border-gray-100">
          {/* Severity Filter */}
          <div className="flex items-center gap-2">
            <span className="text-xs text-gray-600">Severity:</span>
            <div className="flex gap-2">
              {['critical', 'warning', 'info'].map((severity) => {
                const isSelected = severityFilters.includes(severity);
                return (
                  <button
                    key={severity}
                    onClick={() => {
                      if (isSelected && severityFilters.length > 1) {
                        setSeverityFilters(prev => prev.filter(s => s !== severity));
                      } else if (!isSelected) {
                        setSeverityFilters(prev => [...prev, severity]);
                      }
                    }}
                    className={`px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200 ${isSelected
                        ? severity === 'critical'
                          ? 'bg-red-500 text-white'
                          : severity === 'warning'
                            ? 'bg-amber-500 text-white'
                            : 'bg-slate-500 text-white'
                        : 'bg-gray-50 text-gray-500 hover:bg-gray-100 border border-gray-200'
                      }`}
                  >
                    {severity.charAt(0).toUpperCase() + severity.slice(1)}
                  </button>
                );
              })}
            </div>
          </div>

          {/* Status Filters */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-600">Status:</span>
              <select
                value={alarmStateFilter}
                onChange={(e) => setAlarmStateFilter(e.target.value)}
                className="px-2 py-1 text-xs border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All</option>
                <option value="active">Active</option>
                <option value="normal">Normal</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-600">Acknowledge:</span>
              <select
                value={ackStateFilter}
                onChange={(e) => setAckStateFilter(e.target.value)}
                className="px-2 py-1 text-xs border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All</option>
                <option value="unacknowledged">Unacknowledged</option>
                <option value="acknowledged">Acknowledged</option>
                <option value="cleared">Cleared</option>
              </select>
            </div>
          </div>
        </div>

        {/* Table Section */}
        <div className="flex-1 flex flex-col min-h-0">
          <div className="flex-1 overflow-auto">
            <Table className="text-xs">
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => (
                      <TableHead key={header.id} className="py-2 h-auto">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      className={`cursor-pointer transition-colors ${
                        detailLog?.id === row.original.id 
                          ? 'bg-blue-50 hover:bg-blue-100' 
                          : 'hover:bg-gray-50'
                      }`}
                      onClick={(e) => {
                        console.log('Row clicked, event:', e);
                        e.preventDefault();
                        e.stopPropagation();
                        handleDetailClick(row.original);
                      }}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="py-2">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center text-gray-500"
                    >
                      No Alarm Log found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="border-t border-gray-200 py-1 flex items-center justify-between">
            <div className="text-xs text-gray-600 pl-2">
              Alarm Log ({logsData?.count ?? 0} total)
            </div>
            <TablePagination table={table} />
          </div>
        </div>

      </div>

      {/* Detail Panel */}
      {detailLog && (
        <LogDetailPanel
          log={detailLog}
          onClose={() => {
            setDetailLog(null);
          }}
        />
      )}
    </div>
  );
};

export default AFDDLogsTab;