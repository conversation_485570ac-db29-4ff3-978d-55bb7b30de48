import React, { useState } from 'react';
import { Mail, Plus, X, AlertTriangle, Server, User, CheckCircle, Loader2, Send, Bell, Shield, Settings, UserPlus, Trash2 } from 'lucide-react';
import { ToggleSwitch } from '@/components/ui/ToggleSwitch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';

interface Recipient {
  id: string;
  name: string;
  email: string;
  initials: string;
  enabled: boolean;
}

const EmailSettingsTab: React.FC = () => {
  const [emailEnabled, setEmailEnabled] = useState(true);
  const [email, setEmail] = useState('<EMAIL>');
  const [recipients, setRecipients] = useState<Recipient[]>([
    { id: '1', name: '<PERSON>', email: '<EMAIL>', initials: '<PERSON><PERSON>', enabled: true },
    { id: '2', name: '<PERSON>', email: '<EMAIL>', initials: '<PERSON>', enabled: true },
    { id: '3', name: '<PERSON>', email: '<EMAIL>', initials: 'DC', enabled: false }
  ]);
  const [newRecipient, setNewRecipient] = useState({ name: '', email: '' });
  const [showAddForm, setShowAddForm] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isSendingTest, setIsSendingTest] = useState(false);

  const handleToggleEmail = () => {
    setEmailEnabled(!emailEnabled);
  };

  const handleToggleRecipient = (id: string) => {
    setRecipients(
      recipients.map(recipient =>
        recipient.id === id ? { ...recipient, enabled: !recipient.enabled } : recipient
      )
    );
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  const handleNewRecipientChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewRecipient(prev => ({ ...prev, [name]: value }));
  };

  const handleAddRecipient = () => {
    if (!newRecipient.name || !newRecipient.email) return;

    const initials = newRecipient.name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);

    const newRecipientObj = {
      id: Date.now().toString(),
      name: newRecipient.name,
      email: newRecipient.email,
      initials,
      enabled: true
    };

    setRecipients([...recipients, newRecipientObj]);
    setNewRecipient({ name: '', email: '' });
    setShowAddForm(false);
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      // TODO: Implement actual save and show toast
    } catch (error) {
      console.error('Error saving email settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSendTestEmail = async () => {
    setIsSendingTest(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      // TODO: Implement actual test email and show toast
    } catch (error) {
      console.error('Error sending test email:', error);
    } finally {
      setIsSendingTest(false);
    }
  };


  return (
    <div className="h-full overflow-y-auto bg-gray-50">
      <div className="max-w-5xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <Mail className="h-5 w-5 text-blue-600" />
                Email Notification Settings
              </h1>
              <p className="text-sm text-gray-500 mt-1">
                Configure email alerts and manage notification recipients
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Badge 
                variant="outline" 
                className={`text-xs px-3 py-1 ${
                  emailEnabled 
                    ? 'bg-green-50 text-green-700 border-green-200' 
                    : 'bg-gray-50 text-gray-600 border-gray-200'
                }`}
              >
                <div className={`w-2 h-2 rounded-full mr-2 ${emailEnabled ? 'bg-green-500' : 'bg-gray-400'}`} />
                {emailEnabled ? 'Active' : 'Inactive'}
              </Badge>
              <Button
                size="sm"
                onClick={handleSaveSettings}
                disabled={isSaving}
                className="text-xs"
                style={{ backgroundColor: '#2563eb' }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#1d4ed8'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#2563eb'}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <CheckCircle className="mr-2 h-3 w-3" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Main Settings Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Notification Settings */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-4 border-b border-gray-100">
              <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                <Bell className="h-4 w-4 text-gray-600" />
                Notification Settings
              </h3>
            </div>
            <div className="p-4 space-y-4">
              {/* Master Toggle */}
              <div className={`rounded-lg p-4 transition-colors ${
                emailEnabled 
                  ? 'bg-blue-50 border border-blue-200' 
                  : 'bg-gray-50 border border-gray-200'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900">
                      Enable Email Notifications
                    </h4>
                    <p className="text-xs text-gray-600 mt-1">
                      Receive alerts when critical alarms are triggered
                    </p>
                  </div>
                  <div className="scale-75 origin-right">
                    <ToggleSwitch 
                      checked={emailEnabled} 
                      onChange={handleToggleEmail} 
                    />
                  </div>
                </div>
              </div>

              {/* Primary Email */}
              <div className="space-y-2">
                <Label className="text-xs font-medium text-gray-700">
                  Primary Notification Email
                </Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                  <Input
                    type="email"
                    value={email}
                    onChange={handleEmailChange}
                    placeholder="<EMAIL>"
                    className="pl-10 text-xs h-9"
                    disabled={!emailEnabled}
                  />
                </div>
              </div>

              {/* Test Email Button */}
              <div className="pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSendTestEmail}
                  disabled={!emailEnabled || isSendingTest || !email}
                  className="w-full text-xs"
                >
                  {isSendingTest ? (
                    <>
                      <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      Sending Test Email...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-3 w-3" />
                      Send Test Email
                    </>
                  )}
                </Button>
              </div>

              {/* Alert Types */}
              <div className="pt-4 border-t border-gray-100">
                <h5 className="text-xs font-medium text-gray-700 mb-3">Alert Types</h5>
                <div className="space-y-2">
                  {['Critical Alarms', 'System Failures', 'Maintenance Reminders'].map((type) => (
                    <label key={type} className="flex items-center gap-3 p-2 rounded hover:bg-gray-50 cursor-pointer">
                      <input 
                        type="checkbox" 
                        defaultChecked 
                        disabled={!emailEnabled}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-xs text-gray-700">{type}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Recipients Management */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-600" />
                  Notification Recipients
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowAddForm(true)}
                  disabled={showAddForm || !emailEnabled}
                  className="text-xs"
                >
                  <UserPlus className="h-3 w-3 mr-1" />
                  Add
                </Button>
              </div>
            </div>
            <div className="p-4">
              {/* Add Recipient Form */}
              {showAddForm && (
                <div className="bg-gray-50 rounded-lg border border-gray-200 p-4 mb-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-xs font-medium text-gray-900">New Recipient</h4>
                    <button
                      onClick={() => setShowAddForm(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <Label className="text-xs text-gray-700">Name</Label>
                      <Input
                        name="name"
                        value={newRecipient.name}
                        onChange={handleNewRecipientChange}
                        placeholder="John Doe"
                        className="text-xs h-8 mt-1"
                      />
                    </div>
                    <div>
                      <Label className="text-xs text-gray-700">Email</Label>
                      <Input
                        name="email"
                        type="email"
                        value={newRecipient.email}
                        onChange={handleNewRecipientChange}
                        placeholder="<EMAIL>"
                        className="text-xs h-8 mt-1"
                      />
                    </div>
                    <Button
                      size="sm"
                      onClick={handleAddRecipient}
                      disabled={!newRecipient.name || !newRecipient.email}
                      className="w-full text-xs"
                    >
                      Add Recipient
                    </Button>
                  </div>
                </div>
              )}

              {/* Recipients List */}
              <div className="space-y-2">
                {recipients.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <User className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                    <p className="text-xs">No recipients added yet</p>
                  </div>
                ) : (
                  recipients.map((recipient) => (
                    <div 
                      key={recipient.id} 
                      className={`flex items-center justify-between p-3 rounded-lg border transition-colors ${
                        recipient.enabled && emailEnabled
                          ? 'border-gray-200 bg-white' 
                          : 'border-gray-100 bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`h-9 w-9 rounded-full flex items-center justify-center text-xs font-medium ${
                          recipient.enabled && emailEnabled
                            ? 'bg-blue-100 text-blue-700' 
                            : 'bg-gray-100 text-gray-500'
                        }`}>
                          {recipient.initials}
                        </div>
                        <div>
                          <p className="text-xs font-medium text-gray-900">{recipient.name}</p>
                          <p className="text-[11px] text-gray-500">{recipient.email}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="scale-75 origin-right">
                          <ToggleSwitch 
                            size="sm" 
                            checked={recipient.enabled} 
                            onChange={() => handleToggleRecipient(recipient.id)}
                            disabled={!emailEnabled}
                          />
                        </div>
                        <button
                          onClick={() => setRecipients(recipients.filter(r => r.id !== recipient.id))}
                          className="text-gray-400 hover:text-red-600 p-1"
                          disabled={!emailEnabled}
                        >
                          <Trash2 className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Server Configuration */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="p-4 border-b border-gray-100">
            <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
              <Server className="h-4 w-4 text-gray-600" />
              Email Server Configuration
            </h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div>
                <p className="text-[11px] text-gray-500 mb-1">SMTP Server</p>
                <p className="text-xs font-medium text-gray-900">smtp.alto-cero.com</p>
              </div>
              <div>
                <p className="text-[11px] text-gray-500 mb-1">Port</p>
                <p className="text-xs font-medium text-gray-900">587 (TLS)</p>
              </div>
              <div>
                <p className="text-[11px] text-gray-500 mb-1">Sender Email</p>
                <p className="text-xs font-medium text-gray-900"><EMAIL></p>
              </div>
              <div>
                <p className="text-[11px] text-gray-500 mb-1">Authentication</p>
                <div className="flex items-center gap-1">
                  <Shield className="h-3 w-3 text-green-600" />
                  <p className="text-xs font-medium text-green-600">Secure</p>
                </div>
              </div>
            </div>
            <div className="mt-6 pt-6 border-t border-gray-100 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <span className="text-xs text-gray-600">Server connection healthy</span>
              </div>
              <Button variant="outline" size="sm" className="text-xs">
                <Settings className="h-3 w-3 mr-1" />
                Configure Server
              </Button>
            </div>
          </div>
        </div>

        {/* Email Preview */}
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="p-4 border-b border-gray-100">
            <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
              <Mail className="h-4 w-4 text-gray-600" />
              Email Preview
            </h3>
          </div>
          <div className="p-4">
            <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
              <div className="text-xs space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-gray-500">From:</span>
                  <span className="font-medium">Alto Cero Alerts &lt;<EMAIL>&gt;</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-gray-500">To:</span>
                  <span className="font-medium">{email || '<EMAIL>'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-gray-500">Subject:</span>
                  <span className="font-medium">🚨 Critical Alarm: Chiller High Temperature Alert</span>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t border-gray-200">
                <p className="text-[11px] text-gray-600">
                  This is a preview of how alarm notification emails will appear to recipients.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailSettingsTab;