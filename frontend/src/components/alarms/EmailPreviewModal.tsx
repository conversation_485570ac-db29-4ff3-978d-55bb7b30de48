import React from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/Button';
import { X, Mail, AlertTriangle, Clock } from 'lucide-react';
import { AlarmSeverity } from '@/types/alarms';

interface EmailPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  recipientEmail: string;
}

const EmailPreviewModal: React.FC<EmailPreviewModalProps> = ({
  isOpen,
  onClose,
  recipientEmail,
}) => {
  // Get current date and time for the preview
  const now = new Date();
  const formattedDate = now.toLocaleString();
  const formattedTime = `${now.getMonth() + 1}/${now.getDate()}/${now.getFullYear()}, ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')} ${now.getHours() >= 12 ? 'PM' : 'AM'}`;

  // Email configuration for critical alarm
  const config = {
    subject: 'CRITICAL ALARM: Energy Consumption Threshold Exceeded',
    severity: 'CRITICAL' as AlarmSeverity,
    color: 'bg-red-600',
    icon: <AlertTriangle className="h-5 w-5" />,
    message: 'Energy consumption has exceeded the critical threshold of 12,782 kWh.',
    value: '13,245 kWh',
    threshold: '12,782 kWh',
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[600px]">
        <div className="text-center mb-2">
          <h2 className="text-lg font-semibold mb-1">Email Preview</h2>
          <p className="text-sm text-gray-500">This is how the email notification will appear to recipients.</p>
        </div>

        <div className="border rounded-md overflow-hidden">
          {/* Email Header */}
          <div className="bg-gray-100 p-3 border-b">
            <div className="grid grid-cols-[auto_1fr] gap-x-2 gap-y-1 text-sm">
              <div className="font-medium text-gray-500">From:</div>
              <div><EMAIL></div>
              <div className="font-medium text-gray-500">To:</div>
              <div>{recipientEmail}</div>
              <div className="font-medium text-gray-500">Subject:</div>
              <div className="font-medium">{config.subject}</div>
            </div>
          </div>

          {/* Email Body */}
          <div className="p-4">
            {/* Email Template Header */}
            <div className="bg-red-600 text-white p-3 px-4">
              <h2 className="text-lg font-semibold">CRITICAL ALARM: Energy Consumption Threshold Exceeded</h2>
            </div>

            {/* Email Content */}
            <div className="p-4 space-y-4">
              <p>An anomaly has been detected in your energy management system:</p>

              <table className="w-full border-collapse border border-gray-200">
                <tbody>
                  <tr className="border-b border-gray-200">
                    <td className="py-2 px-3 bg-gray-50 font-medium w-1/3">Device</td>
                    <td className="py-2 px-3">Main Meter</td>
                  </tr>
                  <tr className="border-b border-gray-200">
                    <td className="py-2 px-3 bg-gray-50 font-medium">Parameter</td>
                    <td className="py-2 px-3">Energy Consumption</td>
                  </tr>
                  <tr className="border-b border-gray-200">
                    <td className="py-2 px-3 bg-gray-50 font-medium">Value</td>
                    <td className="py-2 px-3">13,245 kWh</td>
                  </tr>
                  <tr className="border-b border-gray-200">
                    <td className="py-2 px-3 bg-gray-50 font-medium">Threshold</td>
                    <td className="py-2 px-3">12,782 kWh</td>
                  </tr>
                  <tr className="border-b border-gray-200">
                    <td className="py-2 px-3 bg-gray-50 font-medium">Time</td>
                    <td className="py-2 px-3">4/22/2024, 4:27:26 PM</td>
                  </tr>
                  <tr>
                    <td className="py-2 px-3 bg-gray-50 font-medium">Severity</td>
                    <td className="py-2 px-3 font-medium">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        CRITICAL
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>

              <p>Energy consumption has exceeded the critical threshold of 12,782 kWh.</p>

              <div className="bg-blue-50 border border-blue-100 rounded p-3 text-sm text-blue-700">
                <p>
                  <strong>Action Required:</strong> Please log in to the Alto CERO Energy Management System to view details and take appropriate action.
                </p>
              </div>
            </div>

            {/* Email Footer */}
            <div className="mt-4 pt-3 border-t text-xs text-gray-500 text-center">
              <p>This is an automated message from the Alto CERO Energy Management System.</p>
              <p>Please do not reply to this email.</p>
            </div>
          </div>
        </div>

        <div className="flex justify-end items-center gap-2 mt-4 pt-2 border-t">
          <Button onClick={onClose} variant="outline">Close Preview</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EmailPreviewModal;
