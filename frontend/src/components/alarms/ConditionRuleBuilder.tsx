import React, { useState, useEffect } from 'react';
import { Plus, Trash2, Copy, ChevronDown } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/Button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface ConditionRule {
  id: string;
  device: string;
  datapoint: string;
  operator: string;
  valueType: 'single' | 'range' | 'datapoint';
  value?: string | number;
  minValue?: number;
  maxValue?: number;
  compareDevice?: string;
  compareDatapoint?: string;
  raw?: string; // For manual entry
}

interface ConditionRuleBuilderProps {
  rules: string[];
  onChange: (rules: string[]) => void;
  isReadOnly?: boolean;
}

// Mock devices and datapoints - in production, these would come from API
const DEVICES = [
  { id: 'CH-1', name: 'Chiller 1', type: 'chiller' },
  { id: 'CH-2', name: 'Chiller 2', type: 'chiller' },
  { id: 'CH-3', name: 'Chiller 3', type: 'chiller' },
  { id: 'zone_1', name: 'Zone 1', type: 'zone' },
  { id: 'zone_2', name: 'Zone 2', type: 'zone' },
  { id: 'zone_3', name: 'Zone 3', type: 'zone' },
  { id: 'pump_1', name: 'Pump 1', type: 'pump' },
  { id: 'pump_2', name: 'Pump 2', type: 'pump' },
  { id: 'ahu_1', name: 'AHU 1', type: 'ahu' },
  { id: 'ahu_2', name: 'AHU 2', type: 'ahu' },
  { id: 'server_room', name: 'Server Room', type: 'zone' },
  { id: 'outdoor', name: 'Outdoor', type: 'environment' },
  { id: 'main', name: 'Main Meter', type: 'meter' },
];

const DATAPOINTS_BY_TYPE: Record<string, Array<{id: string, name: string, unit?: string}>> = {
  chiller: [
    { id: 'status', name: 'Status' },
    { id: 'power', name: 'Power', unit: 'kW' },
    { id: 'cop', name: 'COP' },
    { id: 'efficiency', name: 'Efficiency', unit: '%' },
    { id: 'load', name: 'Load', unit: '%' },
    { id: 'approach_temp', name: 'Approach Temp', unit: '°C' },
    { id: 'chilled_water_temp', name: 'Chilled Water Temp', unit: '°C' },
  ],
  zone: [
    { id: 'temperature', name: 'Temperature', unit: '°C' },
    { id: 'humidity', name: 'Humidity', unit: '%' },
    { id: 'co2', name: 'CO2', unit: 'ppm' },
    { id: 'occupancy', name: 'Occupancy' },
    { id: 'setpoint', name: 'Setpoint', unit: '°C' },
    { id: 'actual_temp', name: 'Actual Temp', unit: '°C' },
  ],
  pump: [
    { id: 'status', name: 'Status' },
    { id: 'power', name: 'Power', unit: 'kW' },
    { id: 'flow', name: 'Flow', unit: 'm³/h' },
    { id: 'pressure', name: 'Pressure', unit: 'bar' },
    { id: 'speed', name: 'Speed', unit: '%' },
    { id: 'hours', name: 'Run Hours', unit: 'h' },
  ],
  ahu: [
    { id: 'status', name: 'Status' },
    { id: 'supply_temp', name: 'Supply Temp', unit: '°C' },
    { id: 'return_temp', name: 'Return Temp', unit: '°C' },
    { id: 'fan_speed', name: 'Fan Speed', unit: '%' },
    { id: 'damper_position', name: 'Damper Position', unit: '%' },
  ],
  environment: [
    { id: 'temp', name: 'Temperature', unit: '°C' },
    { id: 'humidity', name: 'Humidity', unit: '%' },
  ],
  meter: [
    { id: 'power', name: 'Power', unit: 'kW' },
    { id: 'energy', name: 'Energy', unit: 'kWh' },
    { id: 'voltage', name: 'Voltage', unit: 'V' },
    { id: 'current', name: 'Current', unit: 'A' },
    { id: 'power_factor', name: 'Power Factor' },
  ],
};

const OPERATORS = [
  { id: '>', name: 'Greater than', supportsRange: false },
  { id: '>=', name: 'Greater than or equal', supportsRange: false },
  { id: '<', name: 'Less than', supportsRange: false },
  { id: '<=', name: 'Less than or equal', supportsRange: false },
  { id: '==', name: 'Equal to', supportsRange: false },
  { id: '!=', name: 'Not equal to', supportsRange: false },
  { id: 'between', name: 'Between', supportsRange: true },
  { id: 'not between', name: 'Not between', supportsRange: true },
];

export const ConditionRuleBuilder: React.FC<ConditionRuleBuilderProps> = ({
  rules,
  onChange,
  isReadOnly = false,
}) => {
  const [parsedRules, setParsedRules] = useState<ConditionRule[]>([]);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Parse string rules into structured format
  useEffect(() => {
    const parsed = rules.map((rule, index) => {
      // Try to parse the rule
      const betweenMatch = rule.match(/^([\w_-]+)\.([\w_]+)\s+(between|not between)\s+(-?\d+(?:\.\d+)?)\s+and\s+(-?\d+(?:\.\d+)?)$/);
      const compareMatch = rule.match(/^([\w_-]+)\.([\w_]+)\s*([><=!]+)\s*([\w_-]+)\.([\w_]+)$/);
      const simpleMatch = rule.match(/^([\w_-]+)\.([\w_]+)\s*([><=!]+)\s*(.+)$/);

      if (betweenMatch) {
        return {
          id: `rule_${index}`,
          device: betweenMatch[1],
          datapoint: betweenMatch[2],
          operator: betweenMatch[3],
          valueType: 'range' as const,
          minValue: parseFloat(betweenMatch[4]),
          maxValue: parseFloat(betweenMatch[5]),
        };
      } else if (compareMatch) {
        return {
          id: `rule_${index}`,
          device: compareMatch[1],
          datapoint: compareMatch[2],
          operator: compareMatch[3],
          valueType: 'datapoint' as const,
          compareDevice: compareMatch[4],
          compareDatapoint: compareMatch[5],
        };
      } else if (simpleMatch) {
        return {
          id: `rule_${index}`,
          device: simpleMatch[1],
          datapoint: simpleMatch[2],
          operator: simpleMatch[3],
          valueType: 'single' as const,
          value: simpleMatch[4].replace(/['"]/g, ''),
        };
      }

      // If we can't parse it, store as raw
      return {
        id: `rule_${index}`,
        device: '',
        datapoint: '',
        operator: '>',
        valueType: 'single' as const,
        raw: rule,
      };
    });

    setParsedRules(parsed);
  }, [rules]);

  // Convert parsed rules back to strings
  const updateRules = (newParsedRules: ConditionRule[]) => {
    const stringRules = newParsedRules.map(rule => {
      if (rule.raw) {
        return rule.raw;
      }

      const deviceDatapoint = `${rule.device}.${rule.datapoint}`;

      if (rule.valueType === 'range' && rule.operator.includes('between')) {
        return `${deviceDatapoint} ${rule.operator} ${rule.minValue} and ${rule.maxValue}`;
      } else if (rule.valueType === 'datapoint' && rule.compareDevice && rule.compareDatapoint) {
        return `${deviceDatapoint} ${rule.operator} ${rule.compareDevice}.${rule.compareDatapoint}`;
      } else {
        const value = typeof rule.value === 'string' && rule.value.includes(' ') 
          ? `"${rule.value}"` 
          : rule.value;
        return `${deviceDatapoint} ${rule.operator} ${value}`;
      }
    });

    onChange(stringRules);
  };

  const addRule = () => {
    const newRule: ConditionRule = {
      id: `rule_${Date.now()}`,
      device: '',
      datapoint: '',
      operator: '>',
      valueType: 'single',
      value: '',
    };
    updateRules([...parsedRules, newRule]);
  };

  const updateRule = (index: number, updates: Partial<ConditionRule>) => {
    const newRules = [...parsedRules];
    newRules[index] = { ...newRules[index], ...updates };
    
    // Reset value fields when changing operator
    if (updates.operator) {
      const op = OPERATORS.find(o => o.id === updates.operator);
      if (op?.supportsRange) {
        newRules[index].valueType = 'range';
        newRules[index].value = undefined;
      } else if (newRules[index].valueType === 'range') {
        newRules[index].valueType = 'single';
        newRules[index].minValue = undefined;
        newRules[index].maxValue = undefined;
      }
    }

    updateRules(newRules);
  };

  const removeRule = (index: number) => {
    updateRules(parsedRules.filter((_, i) => i !== index));
  };

  const duplicateRule = (index: number) => {
    const ruleToCopy = parsedRules[index];
    const newRule = { ...ruleToCopy, id: `rule_${Date.now()}` };
    const newRules = [...parsedRules];
    newRules.splice(index + 1, 0, newRule);
    updateRules(newRules);
  };

  const getDeviceType = (deviceId: string): string => {
    const device = DEVICES.find(d => d.id === deviceId);
    return device?.type || 'meter';
  };

  if (parsedRules.length === 0 && !isReadOnly) {
    return (
      <div className="text-center py-4">
        <button
          type="button"
          onClick={addRule}
          className="inline-flex items-center gap-2 text-xs text-blue-600 hover:text-blue-700"
        >
          <Plus className="h-3 w-3" />
          Add first condition
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {parsedRules.map((rule, index) => (
        <div key={rule.id} className="bg-gray-50 rounded-lg p-3 space-y-3">
          {/* Rule number and actions */}
          <div className="flex items-center justify-between">
            <span className="text-xs font-medium text-gray-600">Condition {index + 1}</span>
            {!isReadOnly && (
              <div className="flex items-center gap-1">
                <button
                  type="button"
                  onClick={() => duplicateRule(index)}
                  className="p-1 text-gray-400 hover:text-gray-600"
                  title="Duplicate"
                >
                  <Copy className="h-3 w-3" />
                </button>
                {parsedRules.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeRule(index)}
                    className="p-1 text-gray-400 hover:text-red-500"
                    title="Remove"
                  >
                    <Trash2 className="h-3 w-3" />
                  </button>
                )}
              </div>
            )}
          </div>

          {rule.raw && !showAdvanced ? (
            // Show raw rule with option to switch to builder
            <div className="space-y-2">
              <Input
                value={rule.raw}
                onChange={(e) => updateRule(index, { raw: e.target.value })}
                disabled={isReadOnly}
                className="text-xs font-mono"
                placeholder="e.g., device.datapoint > value"
              />
              {!isReadOnly && (
                <button
                  type="button"
                  onClick={() => {
                    updateRule(index, { raw: undefined });
                    setShowAdvanced(false);
                  }}
                  className="text-[10px] text-blue-600 hover:text-blue-700"
                >
                  Use visual builder
                </button>
              )}
            </div>
          ) : (
            // Visual builder
            <div className="space-y-3">
              {/* Device and Datapoint Selection */}
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-[10px] text-gray-600 mb-1 block">Device</label>
                  <Select
                    value={rule.device}
                    onValueChange={(value) => updateRule(index, { device: value, datapoint: '' })}
                    disabled={isReadOnly}
                  >
                    <SelectTrigger className="text-xs h-8">
                      <SelectValue placeholder="Select device" />
                    </SelectTrigger>
                    <SelectContent>
                      {DEVICES.map(device => (
                        <SelectItem key={device.id} value={device.id}>
                          <span className="text-xs">{device.name}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-[10px] text-gray-600 mb-1 block">Datapoint</label>
                  <Select
                    value={rule.datapoint}
                    onValueChange={(value) => updateRule(index, { datapoint: value })}
                    disabled={isReadOnly || !rule.device}
                  >
                    <SelectTrigger className="text-xs h-8">
                      <SelectValue placeholder="Select datapoint" />
                    </SelectTrigger>
                    <SelectContent>
                      {DATAPOINTS_BY_TYPE[getDeviceType(rule.device)]?.map(dp => (
                        <SelectItem key={dp.id} value={dp.id}>
                          <span className="text-xs">
                            {dp.name} {dp.unit && <span className="text-gray-400">({dp.unit})</span>}
                          </span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Operator Selection */}
              <div>
                <label className="text-[10px] text-gray-600 mb-1 block">Operator</label>
                <Select
                  value={rule.operator}
                  onValueChange={(value) => updateRule(index, { operator: value })}
                  disabled={isReadOnly}
                >
                  <SelectTrigger className="text-xs h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {OPERATORS.map(op => (
                      <SelectItem key={op.id} value={op.id}>
                        <span className="text-xs">{op.name}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Value Input */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <label className="text-[10px] text-gray-600">Compare to</label>
                  {!isReadOnly && rule.valueType !== 'range' && (
                    <select
                      value={rule.valueType}
                      onChange={(e) => updateRule(index, { 
                        valueType: e.target.value as 'single' | 'datapoint',
                        value: undefined,
                        compareDevice: undefined,
                        compareDatapoint: undefined,
                      })}
                      className="text-[10px] border rounded px-1 py-0.5"
                    >
                      <option value="single">Fixed value</option>
                      <option value="datapoint">Another datapoint</option>
                    </select>
                  )}
                </div>

                {rule.valueType === 'range' ? (
                  // Range input for between operators
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      value={rule.minValue || ''}
                      onChange={(e) => updateRule(index, { minValue: parseFloat(e.target.value) || 0 })}
                      disabled={isReadOnly}
                      placeholder="Min"
                      className="text-xs h-8"
                    />
                    <span className="text-xs text-gray-500">and</span>
                    <Input
                      type="number"
                      value={rule.maxValue || ''}
                      onChange={(e) => updateRule(index, { maxValue: parseFloat(e.target.value) || 0 })}
                      disabled={isReadOnly}
                      placeholder="Max"
                      className="text-xs h-8"
                    />
                  </div>
                ) : rule.valueType === 'datapoint' ? (
                  // Device/datapoint selection for comparison
                  <div className="grid grid-cols-2 gap-2">
                    <Select
                      value={rule.compareDevice || ''}
                      onValueChange={(value) => updateRule(index, { compareDevice: value, compareDatapoint: '' })}
                      disabled={isReadOnly}
                    >
                      <SelectTrigger className="text-xs h-8">
                        <SelectValue placeholder="Select device" />
                      </SelectTrigger>
                      <SelectContent>
                        {DEVICES.map(device => (
                          <SelectItem key={device.id} value={device.id}>
                            <span className="text-xs">{device.name}</span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Select
                      value={rule.compareDatapoint || ''}
                      onValueChange={(value) => updateRule(index, { compareDatapoint: value })}
                      disabled={isReadOnly || !rule.compareDevice}
                    >
                      <SelectTrigger className="text-xs h-8">
                        <SelectValue placeholder="Select datapoint" />
                      </SelectTrigger>
                      <SelectContent>
                        {DATAPOINTS_BY_TYPE[getDeviceType(rule.compareDevice || '')]?.map(dp => (
                          <SelectItem key={dp.id} value={dp.id}>
                            <span className="text-xs">
                              {dp.name} {dp.unit && <span className="text-gray-400">({dp.unit})</span>}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                ) : (
                  // Single value input
                  <Input
                    value={rule.value || ''}
                    onChange={(e) => updateRule(index, { value: e.target.value })}
                    disabled={isReadOnly}
                    placeholder="Enter value"
                    className="text-xs h-8"
                  />
                )}
              </div>

              {/* Show generated rule */}
              {rule.device && rule.datapoint && (
                <div className="bg-gray-100 rounded px-2 py-1">
                  <code className="text-[10px] text-gray-600">
                    {rule.device}.{rule.datapoint} {rule.operator} {
                      rule.valueType === 'range' 
                        ? `${rule.minValue || '?'} and ${rule.maxValue || '?'}`
                        : rule.valueType === 'datapoint'
                        ? `${rule.compareDevice || '?'}.${rule.compareDatapoint || '?'}`
                        : rule.value || '?'
                    }
                  </code>
                </div>
              )}
            </div>
          )}
        </div>
      ))}

      {!isReadOnly && (
        <div className="flex items-center justify-between pt-2">
          <button
            type="button"
            onClick={addRule}
            className="inline-flex items-center gap-2 text-xs text-blue-600 hover:text-blue-700"
          >
            <Plus className="h-3 w-3" />
            Add condition
          </button>

          {parsedRules.some(r => !r.raw) && (
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-[10px] text-gray-500 hover:text-gray-700"
            >
              {showAdvanced ? 'Hide' : 'Show'} advanced mode
            </button>
          )}
        </div>
      )}
    </div>
  );
};