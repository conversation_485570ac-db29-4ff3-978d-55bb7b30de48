import React from 'react';
import { AlarmSeverity } from '@/types/alarms';
import { cn } from '@/lib/utils';

// Severity and metric utility functions have been moved to severityUtils.tsx and metricUtils.ts

/**
 * Reusable severity filter buttons component
 */
export const SeverityFilterButtons: React.FC<{
  selectedSeverity: string | undefined;
  setSelectedSeverity: (severity: string | undefined) => void;
  setShowOnlyCritical: (show: boolean) => void;
  alarmStats: { total: number; critical: number; warning: number; info: number };
}> = ({ selectedSeverity, setSelectedSeverity, setShowOnlyCritical, alarmStats }) => {
  return (
    <div className="flex flex-wrap gap-2">
      <button
        onClick={() => {
          setSelectedSeverity(undefined);
          setShowOnlyCritical(false);
        }}
        className={`px-3 py-1.5 text-sm font-medium rounded-full border ${
          !selectedSeverity
            ? 'bg-gray-100 border-gray-300 font-semibold'
            : 'border-gray-200 hover:bg-gray-50'
        }`}
      >
        All ({alarmStats.total})
      </button>
      <button
        onClick={() => {
          setSelectedSeverity('CRITICAL');
          setShowOnlyCritical(false);
        }}
        className={`px-3 py-1.5 text-sm font-medium rounded-full border flex items-center ${
          selectedSeverity === 'CRITICAL'
            ? 'bg-red-50 border-red-200 text-red-700 font-semibold'
            : 'border-gray-200 hover:bg-gray-50'
        }`}
      >
        <span className="w-2 h-2 rounded-full bg-red-600 mr-2"></span>
        🚨 Critical ({alarmStats.critical})
      </button>
      <button
        onClick={() => {
          setSelectedSeverity('WARNING');
          setShowOnlyCritical(false);
        }}
        className={`px-3 py-1.5 text-sm font-medium rounded-full border flex items-center ${
          selectedSeverity === 'WARNING'
            ? 'bg-amber-50 border-amber-200 text-amber-700 font-semibold'
            : 'border-gray-200 hover:bg-gray-50'
        }`}
      >
        <span className="w-2 h-2 rounded-full bg-amber-500 mr-2"></span>
        ⚠️ Warning ({alarmStats.warning})
      </button>
      <button
        onClick={() => {
          setSelectedSeverity('INFO');
          setShowOnlyCritical(false);
        }}
        className={`px-3 py-1.5 text-sm font-medium rounded-full border flex items-center ${
          selectedSeverity === 'INFO'
            ? 'bg-blue-50 border-blue-200 text-primary-blue font-semibold'
            : 'border-gray-200 hover:bg-gray-50'
        }`}
      >
        <span className="w-2 h-2 rounded-full bg-primary-blue mr-2"></span>
        ℹ️ Info ({alarmStats.info})
      </button>
    </div>
  );
};

/**
 * Reusable grouping toggle component
 */
export const GroupingToggle: React.FC<{
  groupingEnabled: boolean;
  setGroupingEnabled: (enabled: boolean) => void;
  groupBy: 'severity' | 'metric';
  setGroupBy: (groupBy: 'severity' | 'metric') => void;
}> = ({ groupingEnabled, setGroupingEnabled, groupBy, setGroupBy }) => {
  return (
    <div className="inline-flex h-9 items-center rounded-md border border-gray-300 bg-white p-1 text-sm shadow-sm">
      <button
        type="button"
        onClick={() => setGroupingEnabled(false)}
        className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
          !groupingEnabled
            ? 'bg-blue-600 text-white shadow-sm'
            : 'text-gray-700 hover:bg-gray-100'
        }`}
      >
        None
      </button>
      <button
        type="button"
        onClick={() => {
          setGroupingEnabled(true);
          setGroupBy('metric');
        }}
        className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
          groupingEnabled && groupBy === 'metric'
            ? 'bg-blue-600 text-white shadow-sm'
            : 'text-gray-700 hover:bg-gray-100'
        }`}
      >
        Metric
      </button>
      <button
        type="button"
        onClick={() => {
          setGroupingEnabled(true);
          setGroupBy('severity');
        }}
        className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1 ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 ${
          groupingEnabled && groupBy === 'severity'
            ? 'bg-blue-600 text-white shadow-sm'
            : 'text-gray-700 hover:bg-gray-100'
        }`}
      >
        Severity
      </button>
    </div>
  );
};
