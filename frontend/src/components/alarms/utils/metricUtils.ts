import { METRIC_UNITS } from '../../../utils/mockAlarms';

/**
 * Helper function to get units based on metric
 */
export const getMetricUnit = (metric: string) => {
  return METRIC_UNITS[metric] || '';
};

/**
 * Helper function to format values with commas for thousands
 */
export const formatValue = (value: number, metric: string): string => {
  // Add commas for kWh values
  if (metric === 'energy_consumption') {
    return value.toLocaleString('en-US', { maximumFractionDigits: 1 });
  }
  return value.toString();
};
