import React from 'react';
import { Badge } from "@/components/ui/badge";
import { AlarmSeverity } from '@/types/alarms';

/**
 * Helper function to normalize severity values
 */
export function getNormalizedSeverity(severity: string | undefined): 'CRITICAL' | 'WARNING' | 'INFO' {
  // Treat missing, empty, or non-WARNING/CRITICAL as INFO
  return !severity || (severity !== 'CRITICAL' && severity !== 'WARNING')
    ? 'INFO'
    : severity as 'CRITICAL' | 'WARNING' | 'INFO';
}

/**
 * Helper function to get severity badge styling
 */
export const getSeverityBadge = (severity: string | undefined) => {
  // Use the normalized severity
  const normalizedSeverity = getNormalizedSeverity(severity);

  switch (normalizedSeverity) {
    case 'CRITICAL':
      return <Badge className="bg-red-500 hover:bg-red-600 text-white">🚨 {normalizedSeverity}</Badge>;
    case 'WARNING':
      return <Badge className="bg-amber-500 hover:bg-amber-600 text-white">⚠️ {normalizedSeverity}</Badge>;
    case 'INFO':
      return <Badge className="bg-primary-blue hover:bg-primary-blue/90 text-white">ℹ️ {normalizedSeverity}</Badge>;
    default: // This should never happen with our normalization, but keeping as a fallback
      return <Badge className="bg-primary-blue hover:bg-primary-blue/90 text-white">ℹ️ INFO</Badge>;
  }
};

/**
 * Helper function to get severity background color
 */
export const getSeverityBackgroundClass = (severity: string | undefined) => {
  // Use the normalized severity
  const normalizedSeverity = getNormalizedSeverity(severity);

  switch (normalizedSeverity) {
    case 'CRITICAL':
      return 'bg-gradient-to-r from-red-500 to-red-600';
    case 'WARNING':
      return 'bg-gradient-to-r from-amber-500 to-amber-600';
    case 'INFO':
      return 'bg-gradient-to-r from-primary-blue to-primary-blue/90';
    default: // This should never happen with our normalization, but keeping as a fallback
      return 'bg-gradient-to-r from-primary-blue to-primary-blue/90';
  }
};

/**
 * Helper function to get severity header color
 */
export const getSeverityHeaderClass = (severity: string | undefined) => {
  // Use the normalized severity
  const normalizedSeverity = getNormalizedSeverity(severity);

  switch (normalizedSeverity) {
    case 'CRITICAL':
      return 'bg-gradient-to-r from-red-50 to-red-100 border-red-200';
    case 'WARNING':
      return 'bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200';
    case 'INFO':
      return 'bg-gradient-to-r from-background-light-grey to-background-white-blue border-background-white-blue';
    default: // This should never happen with our normalization, but keeping as a fallback
      return 'bg-gradient-to-r from-background-light-grey to-background-white-blue border-background-white-blue';
  }
};

/**
 * Returns styling classes for different alarm severity levels
 */
export function getSeverityClasses(severity: AlarmSeverity | string | undefined): string {
  // Use the normalized severity
  const normalizedSeverity = getNormalizedSeverity(severity);
  
  switch (normalizedSeverity) {
    case 'CRITICAL':
      return "bg-red-600 text-white hover:bg-red-700";
    case 'WARNING':
      return "bg-amber-500 text-white hover:bg-amber-600";
    case 'INFO':
      return "bg-primary-blue text-white hover:bg-primary-blue/90";
    default:
      return "bg-primary-blue text-white hover:bg-primary-blue/90";
  }
}

/**
 * Define severity order (lower number = higher priority)
 */
export const severityOrder: { [key: string]: number } = {
  CRITICAL: 1,
  WARNING: 2,
  INFO: 3,
};
