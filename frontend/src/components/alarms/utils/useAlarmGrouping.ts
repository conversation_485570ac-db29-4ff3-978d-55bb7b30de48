import { useState, useEffect, useMemo } from 'react';
import { AlarmSeverity } from '@/types/alarms';
import { severityOrder } from './severityUtils';

// Generic interface for alarm data with required fields for grouping
export interface AlarmData {
  id: number;
  metric: string;
  severity: AlarmSeverity;
  [key: string]: any;
}

// Interface for grouped alarms
export interface AlarmGroup<T extends AlarmData> {
  metric: string;
  severity: AlarmSeverity;
  alarms: T[];
  expanded: boolean;
}

/**
 * Custom hook for handling alarm grouping functionality
 */
export function useAlarmGrouping<T extends AlarmData>(
  alarms: T[] | undefined,
  groupingEnabled: boolean,
  groupBy: 'severity' | 'metric'
) {
  const [groupedAlarms, setGroupedAlarms] = useState<AlarmGroup<T>[]>([]);

  // Group alarms when data changes or groupBy changes
  useEffect(() => {
    if (!alarms || !groupingEnabled) {
      setGroupedAlarms([]);
      return;
    }

    const groupMap = new Map<string, T[]>();

    // Group alarms by the selected criteria
    alarms.forEach(alarm => {
      const key = groupBy === 'metric' ? alarm.metric : alarm.severity;
      if (!groupMap.has(key)) {
        groupMap.set(key, []);
      }
      groupMap.get(key)!.push(alarm);
    });

    // Convert map to array of AlarmGroup objects
    const groups: AlarmGroup<T>[] = Array.from(groupMap.entries()).map(([key, groupAlarms]) => {
      const severity = groupBy === 'severity' ? key as AlarmSeverity : groupAlarms[0].severity;
      const hasCritical = groupBy === 'severity'
        ? severity === 'CRITICAL'
        : groupAlarms.some(alarm => alarm.severity === 'CRITICAL');

      return {
        metric: groupBy === 'metric' ? key : groupAlarms[0].metric,
        severity: severity,
        alarms: groupAlarms,
        expanded: hasCritical // Auto-expand groups with critical alarms
      };
    });

    // Sort groups by priority (severity) then by alarm count
    groups.sort((a, b) => {
      if (groupBy === 'severity') {
        return severityOrder[a.severity] - severityOrder[b.severity];
      } else {
        // If grouped by metric, sort by highest severity then by count
        const sevA = Math.min(...a.alarms.map(alarm => severityOrder[alarm.severity]));
        const sevB = Math.min(...b.alarms.map(alarm => severityOrder[alarm.severity]));
        if (sevA !== sevB) return sevA - sevB;
        return b.alarms.length - a.alarms.length;
      }
    });

    setGroupedAlarms(groups);
  }, [alarms, groupBy, groupingEnabled]);

  // Toggle expansion of a group
  const toggleGroupExpand = (index: number) => {
    setGroupedAlarms(prev => {
      const newGroups = [...prev];
      newGroups[index] = { ...newGroups[index], expanded: !newGroups[index].expanded };
      return newGroups;
    });
  };

  // Toggle all groups expanded/collapsed
  const toggleAllGroups = (expanded: boolean) => {
    setGroupedAlarms(prev => prev.map(group => ({ ...group, expanded })));
  };

  return {
    groupedAlarms,
    toggleGroupExpand,
    toggleAllGroups
  };
}

/**
 * Custom hook for calculating alarm statistics
 */
export function useAlarmStats<T extends { severity: AlarmSeverity }>(alarms: T[] | undefined, totalCount?: number) {
  return useMemo(() => {
    if (!alarms) return { total: 0, critical: 0, warning: 0, info: 0 };

    const stats = {
      total: totalCount ?? alarms.length,
      critical: 0,
      warning: 0,
      info: 0
    };

    alarms.forEach((alarm) => {
      if (alarm.severity === 'CRITICAL') stats.critical++;
      else if (alarm.severity === 'WARNING') stats.warning++;
      else if (alarm.severity === 'INFO') stats.info++;
    });

    return stats;
  }, [alarms, totalCount]);
}
