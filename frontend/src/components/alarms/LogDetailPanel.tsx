import React, { useEffect, useState } from 'react';
import { ChevronRight, AlertCircle, Clock, CheckCircle, XCircle, Info, ExternalLink, Loader2 } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { AFDDLog } from '@/types/alarms';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/Button';
import { getAFDDLogDetail } from '@/lib/api/alarms';

interface LogDetailPanelProps {
  log: AFDDLog;
  onClose: () => void;
  onAcknowledge?: () => void;
  onClear?: () => void;
}

const LogDetailPanel: React.FC<LogDetailPanelProps> = ({ 
  log, 
  onClose,
  onAcknowledge,
  onClear 
}) => {
  const navigate = useNavigate();
  const [detailData, setDetailData] = useState<any>(null);
  
  // Fetch detailed log data
  const { data: logDetail, isLoading, error } = useQuery({
    queryKey: ['afdd-log-detail', log.id],
    queryFn: () => getAFDDLogDetail(log.id),
    enabled: !!log.id,
  });

  const faultName = logDetail?.fault?.name || log.fault_name || 'Unknown';
  const activeDate = new Date(log.active_at);
  const normalDate = log.normal_at ? new Date(log.normal_at) : null;
  const acknowledgedDate = log.acknowledged_at ? new Date(log.acknowledged_at) : null;
  
  const formatDuration = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days} days, ${hours} hours`;
    if (hours > 0) return `${hours} hours, ${minutes} minutes`;
    return `${minutes} minutes`;
  };

  const canAcknowledge = log.ack_state === 'unacknowledged';
  const canClear = log.ack_state === 'acknowledged';
  
  // Navigate to fault configuration
  const handleViewFaultConfig = () => {
    const faultId = logDetail?.fault?.id || (typeof log.fault === 'number' ? log.fault : null);
    if (faultId) {
      navigate(`/alarms/configuration?fault=${faultId}`);
    }
  };

  return (
    <div className="w-1/2 h-full flex flex-col bg-white border-l border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8 hover:bg-gray-100"
          >
            <ChevronRight className="h-4 w-4 text-gray-500" />
          </Button>
          <h2 className="text-sm font-medium text-gray-900">Fault Log Details</h2>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        ) : (
          <div className="space-y-4">
            {/* Status and Info Card */}
            <div className="rounded-lg p-4 border border-blue-100">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h3 className="text-sm font-semibold text-gray-900">
                    {faultName}
                  </h3>
                  {logDetail?.fault?.description && (
                    <p className="text-xs text-gray-600 mt-1">{logDetail.fault.description}</p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    className={`text-[10px] px-2 py-0.5 ${
                      log.severity === 'critical'
                        ? 'bg-red-500 text-white'
                        : log.severity === 'warning'
                        ? 'bg-amber-500 text-white'
                        : 'bg-gray-500 text-white'
                    }`}
                  >
                    {log.severity.charAt(0).toUpperCase() + log.severity.slice(1)}
                  </Badge>
                  <Badge
                    variant="outline"
                    className={`text-[10px] px-2 py-0.5 ${
                      log.alarm_state === 'active'
                        ? 'bg-red-50 text-red-700 border-red-200'
                        : 'bg-green-50 text-green-700 border-green-200'
                    }`}
                  >
                    {log.alarm_state === 'active' ? 'Active' : 'Normal'}
                  </Badge>
                </div>
              </div>
              
              {/* View Fault Configuration Link */}
              <div className="mt-3 pt-3 border-t border-gray-100">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleViewFaultConfig}
                  className="text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200"
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  View Fault Configuration
                </Button>
              </div>
            </div>
            
            {/* Condition Values - Key for troubleshooting */}
            {(logDetail?.active_conditions || logDetail?.normal_conditions) && (
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
                  <h4 className="text-xs font-medium text-gray-900">
                    Condition Analysis
                  </h4>
                </div>
                <div className="p-4 space-y-3">
                  {/* Show condition rules */}
                  {logDetail?.fault?.condition_rules && (
                    <div className="mb-4">
                      <p className="text-[11px] text-gray-600 mb-2">Trigger Rules:</p>
                      <div className="bg-gray-50 rounded p-2">
                        {logDetail.fault.condition_rules.map((rule: string, index: number) => (
                          <p key={index} className="text-xs text-gray-700 italic">{rule}</p>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* Active Conditions */}
                  {logDetail?.active_conditions && (
                    <div>
                      <p className="text-[11px] text-gray-600 mb-2">Values when triggered:</p>
                      <div className="bg-red-50 rounded p-3 border border-red-100">
                        <div className="space-y-1">
                          {Object.entries(logDetail.active_conditions).map(([key, value]) => (
                            <div key={key} className="flex justify-between text-xs">
                              <span className="text-gray-600">{key}:</span>
                              <span className="font-medium text-red-700">{value}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {/* Normal Conditions */}
                  {logDetail?.normal_conditions && normalDate && (
                    <div>
                      <p className="text-[11px] text-gray-600 mb-2">Values when returned to normal:</p>
                      <div className="bg-green-50 rounded p-3 border border-green-100">
                        <div className="space-y-1">
                          {Object.entries(logDetail.normal_conditions).map(([key, value]) => (
                            <div key={key} className="flex justify-between text-xs">
                              <span className="text-gray-600">{key}:</span>
                              <span className="font-medium text-green-700">{value}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

          {/* Timeline Section */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
              <h4 className="text-xs font-medium text-gray-900">Event Timeline</h4>
            </div>
            <div className="p-4">
              <div className="space-y-3">
                {/* Active */}
                <div className="flex items-start gap-3 group">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-red-100 flex items-center justify-center">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-xs font-medium text-gray-900">Fault Activated</p>
                    <p className="text-[10px] text-gray-500">
                      {activeDate.toLocaleDateString()} at {activeDate.toLocaleTimeString()}
                    </p>
                  </div>
                </div>

                {/* Acknowledged */}
                {acknowledgedDate && (
                  <div className="flex items-start gap-3 group">
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-xs font-medium text-gray-900">Acknowledged</p>
                      <p className="text-[10px] text-gray-500">
                        {acknowledgedDate.toLocaleDateString()} at {acknowledgedDate.toLocaleTimeString()}
                      </p>
                      {log.acknowledged_by && (
                        <p className="text-[10px] text-gray-500 mt-0.5">
                          By: {log.acknowledged_by.username}
                          {log.acknowledged_by.first_name && log.acknowledged_by.last_name && 
                            ` (${log.acknowledged_by.first_name} ${log.acknowledged_by.last_name})`}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {/* Normal */}
                {normalDate && (
                  <div className="flex items-start gap-3 group">
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-xs font-medium text-gray-900">Returned to Normal</p>
                      <p className="text-[10px] text-gray-500">
                        {normalDate.toLocaleDateString()} at {normalDate.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                )}

                {/* Cleared */}
                {log.is_cleared && (
                  <div className="flex items-start gap-3 group">
                    <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <XCircle className="h-4 w-4 text-gray-600" />
                    </div>
                    <div className="flex-1">
                      <p className="text-xs font-medium text-gray-900">Cleared</p>
                      <p className="text-[10px] text-gray-500">Operator cleared this fault</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        )}
      </div>

      {/* Actions */}
      {(canAcknowledge || canClear) && onAcknowledge && onClear && (
        <div className="p-4 border-t bg-gray-50">
          <div className="flex gap-2">
            {canAcknowledge && (
              <Button
                variant="outline"
                size="sm"
                onClick={onAcknowledge}
                className="flex-1 bg-yellow-50 text-yellow-700 hover:bg-yellow-100 border-yellow-300"
              >
                Acknowledge Fault
              </Button>
            )}
            {canClear && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClear}
                className="flex-1 bg-blue-50 text-blue-700 hover:bg-blue-100 border-blue-300"
              >
                Clear Fault
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default LogDetailPanel;