import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Info } from 'lucide-react';
import { ConditionRuleBuilderDynamic } from './ConditionRuleBuilderDynamic';

interface FaultConfigFormData {
  name: string;
  description?: string;
  category?: string;
  severity: 'critical' | 'warning' | 'info';
  enabled: boolean;
  condition_logic: 'all' | 'any';
  condition_rules: string[];
  delay_seconds: number;
  operating_hours?: string | null;
  metadata?: Record<string, any> | null;
}

interface FaultConfigFormProps {
  mode: 'create' | 'edit' | 'view';
  initialData?: Partial<FaultConfigFormData>;
  onSubmit: (data: FaultConfigFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const defaultFormData: FaultConfigFormData = {
  name: '',
  description: '',
  category: undefined,
  severity: 'info',
  enabled: true,
  condition_logic: 'all',
  condition_rules: [],
  delay_seconds: 0,
  operating_hours: null,
  metadata: null,
};

// Common categories for quick selection
const COMMON_CATEGORIES = [
  'Chiller',
  'HVAC', 
  'Lighting',
  'Power',
];

export const FaultConfigFormNew: React.FC<FaultConfigFormProps> = ({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}) => {
  const [formData, setFormData] = useState<FaultConfigFormData>({
    ...defaultFormData,
    ...initialData,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [activeTab, setActiveTab] = useState<'basic' | 'conditions' | 'advanced'>('basic');
  const isReadOnly = mode === 'view';

  // Reset form when mode changes or initial data changes
  useEffect(() => {
    if (mode === 'create') {
      // Always reset to default values in create mode
      setFormData(defaultFormData);
      setErrors({});
      setActiveTab('basic');
    } else if (initialData) {
      // Only use initial data if not in create mode
      setFormData({
        ...defaultFormData,
        ...initialData,
      });
    }
  }, [mode, initialData]);


  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.includes(' ')) {
      newErrors.name = 'Name cannot contain spaces';
    }

    if (formData.condition_rules.length === 0) {
      newErrors.condition_rules = 'At least one condition rule is required';
    }

    if (formData.delay_seconds < 0) {
      newErrors.delay_seconds = 'Delay must be 0 or positive';
    }

    setErrors(newErrors);
    
    // Switch to tab with first error
    if (newErrors.name || newErrors.description) {
      setActiveTab('basic');
    } else if (newErrors.condition_rules) {
      setActiveTab('conditions');
    }
    
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  const handleFieldChange = (field: keyof FaultConfigFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const formatDelay = (seconds: number): string => {
    if (seconds === 0) return 'Immediate';
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
    return `${Math.floor(seconds / 3600)}h`;
  };

  // Tab indicator for errors
  const hasBasicErrors = !!(errors.name || errors.description || errors.category);
  const hasConditionErrors = !!errors.condition_rules;
  const hasAdvancedErrors = !!(errors.delay_seconds || errors.operating_hours);

  return (
    <form id="fault-config-form" onSubmit={handleSubmit} className="h-full flex flex-col">
      {/* Tabs */}
      {mode !== 'view' && (
        <div className="flex border-b border-gray-200">
          <button
            type="button"
            onClick={() => setActiveTab('basic')}
            className={`px-4 py-2 text-xs font-medium border-b-2 transition-colors ${
              activeTab === 'basic'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Basic Info
            {hasBasicErrors && <span className="ml-1 text-red-500">•</span>}
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('conditions')}
            className={`px-4 py-2 text-xs font-medium border-b-2 transition-colors ${
              activeTab === 'conditions'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Conditions
            {hasConditionErrors && <span className="ml-1 text-red-500">•</span>}
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('advanced')}
            className={`px-4 py-2 text-xs font-medium border-b-2 transition-colors ${
              activeTab === 'advanced'
                ? 'border-blue-600 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
          >
            Advanced
            {hasAdvancedErrors && <span className="ml-1 text-red-500">•</span>}
          </button>
        </div>
      )}

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        {mode === 'view' ? (
          // View mode - show all fields in a clean layout
          <div className="space-y-4">
            {/* Status and Info Card */}
            <div className="rounded-lg p-4 border border-blue-100">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-gray-900">
                  {formData.name}
                </h3>
                <div className="flex items-center gap-2">
                  <Badge
                    variant="outline"
                    className={`text-[10px] px-2 py-0.5 ${
                      formData.enabled
                        ? 'bg-green-50 text-green-700 border-green-200'
                        : 'bg-gray-50 text-gray-600 border-gray-200'
                    }`}
                  >
                    {formData.enabled ? 'Active' : 'Inactive'}
                  </Badge>
                  <Badge
                    className={`text-[10px] px-2 py-0.5 ${
                      formData.severity === 'critical'
                        ? 'bg-red-500 text-white'
                        : formData.severity === 'warning'
                        ? 'bg-amber-500 text-white'
                        : 'bg-gray-500 text-white'
                    }`}
                  >
                    {formData.severity.charAt(0).toUpperCase() + formData.severity.slice(1)}
                  </Badge>
                </div>
              </div>
              {formData.description && (
                <p className="text-xs text-gray-600 leading-relaxed">{formData.description}</p>
              )}
              <div className="flex items-center gap-4 mt-3">
                {formData.category && (
                  <div className="flex items-center gap-1">
                    <span className="text-[10px] text-gray-500">Category:</span>
                    <span className="text-[11px] font-medium text-gray-700">{formData.category}</span>
                  </div>
                )}
                {formData.delay_seconds > 0 && (
                  <div className="flex items-center gap-1">
                    <span className="text-[10px] text-gray-500">Delay:</span>
                    <span className="text-[11px] font-medium text-gray-700">{formatDelay(formData.delay_seconds)}</span>
                  </div>
                )}
                {formData.operating_hours && (
                  <div className="flex items-center gap-1">
                    <span className="text-[10px] text-gray-500">Hours:</span>
                    <span className="text-[11px] font-medium text-gray-700">{formData.operating_hours}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Conditions Section */}
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="bg-gray-50 px-4 py-2 border-b border-gray-200">
                <h4 className="text-xs font-medium text-gray-900">
                  Trigger Conditions
                  <span className="ml-2 text-[10px] font-normal text-gray-500">
                    ({formData.condition_logic === 'all' ? 'All conditions must be met' : 'Any condition triggers alarm'})
                  </span>
                </h4>
              </div>
              <div className="p-4">
                {formData.condition_rules.length > 0 ? (
                  <div className="space-y-2">
                    {formData.condition_rules.map((rule, index) => (
                      <div key={index} className="flex items-start gap-3 group">
                        <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-[10px] font-medium">
                          {index + 1}
                        </div>
                        <div className="flex-1 bg-gray-50 rounded-md px-3 py-2 border border-gray-100 group-hover:border-gray-200 transition-colors">
                          <span className="text-xs italic text-gray-700">{rule}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-xs text-gray-400 italic">No conditions configured</p>
                )}
              </div>
            </div>

            {/* Configuration Details */}
            <div className="grid grid-cols-2 gap-3">
              {/* Alert Configuration */}
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h5 className="text-[11px] font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Alert Configuration
                </h5>
                <div className="space-y-2">
                  <div>
                    <p className="text-[10px] text-gray-500">Response Time</p>
                    <p className="text-xs font-medium text-gray-800">
                      {formData.delay_seconds > 0 ? formatDelay(formData.delay_seconds) : 'Immediate'}
                    </p>
                  </div>
                  <div>
                    <p className="text-[10px] text-gray-500">Logic Type</p>
                    <p className="text-xs font-medium text-gray-800">
                      {formData.condition_logic === 'all' ? 'AND Logic' : 'OR Logic'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Operating Schedule */}
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h5 className="text-[11px] font-medium text-gray-700 mb-2 flex items-center gap-1">
                  <Info className="h-3 w-3" />
                  Operating Schedule
                </h5>
                <div className="space-y-2">
                  <div>
                    <p className="text-[10px] text-gray-500">Monitoring Period</p>
                    <p className="text-xs font-medium text-gray-800">
                      {formData.operating_hours || '24/7 Continuous'}
                    </p>
                  </div>
                  <div>
                    <p className="text-[10px] text-gray-500">Status</p>
                    <p className="text-xs font-medium text-gray-800">
                      {formData.enabled ? 'Monitoring Active' : 'Monitoring Paused'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Edit/Create mode with tabs
          <>
            {activeTab === 'basic' && (
              <div className="space-y-4">
                {/* Name Field */}
                <div>
                  <Label htmlFor="name" className="text-xs font-medium">
                    Unique Name <span className="text-red-500">*</span>
                  </Label>
                  <div className="mt-1">
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleFieldChange('name', e.target.value)}
                      disabled={mode === 'edit'}
                      placeholder="e.g., chiller_high_temp_alert"
                      className={`text-xs ${errors.name ? 'border-red-500' : ''}`}
                    />
                    {errors.name && (
                      <p className="mt-1 text-[10px] text-red-500">{errors.name}</p>
                    )}
                    <p className="mt-1 text-[10px] text-gray-500">
                      Lowercase letters, numbers, and underscores only
                    </p>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <Label htmlFor="description" className="text-xs font-medium">
                    Description
                  </Label>
                  <div className="mt-1">
                    <Textarea
                      id="description"
                      value={formData.description || ''}
                      onChange={(e) => handleFieldChange('description', e.target.value)}
                      placeholder="Describe what this fault monitors and when it should trigger"
                      rows={2}
                      className="text-xs"
                    />
                  </div>
                </div>

                {/* Category Selection */}
                <div>
                  <Label htmlFor="category" className="text-xs font-medium">
                    Category
                  </Label>
                  <div className="mt-1 space-y-2">
                    <div className="flex flex-wrap gap-2">
                      {COMMON_CATEGORIES.map((cat) => (
                        <button
                          key={cat}
                          type="button"
                          onClick={() => handleFieldChange('category', cat)}
                          className={`px-2 py-1 text-xs rounded-md transition-colors ${
                            formData.category === cat
                              ? 'bg-blue-100 text-blue-700 border border-blue-300'
                              : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          {cat}
                        </button>
                      ))}
                    </div>
                    <Input
                      id="category"
                      value={formData.category || ''}
                      onChange={(e) => handleFieldChange('category', e.target.value)}
                      placeholder="Or type a custom category"
                      className="text-xs"
                    />
                  </div>
                </div>

                {/* Severity and Enabled */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="severity" className="text-xs font-medium">
                      Severity
                    </Label>
                    <Select
                      value={formData.severity}
                      onValueChange={(value) => handleFieldChange('severity', value)}
                    >
                      <SelectTrigger className="text-xs mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="critical">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-red-500" />
                            Critical
                          </div>
                        </SelectItem>
                        <SelectItem value="warning">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-amber-500" />
                            Warning
                          </div>
                        </SelectItem>
                        <SelectItem value="info">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-gray-500" />
                            Info
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label className="text-xs font-medium">Status</Label>
                    <div className="mt-2 flex items-center justify-between">
                      <span className="text-xs text-gray-600">
                        {formData.enabled ? 'Enabled' : 'Disabled'}
                      </span>
                      <div className="scale-75 origin-right">
                        <Switch
                          checked={formData.enabled}
                          onCheckedChange={(checked) => handleFieldChange('enabled', checked)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'conditions' && (
              <div className="space-y-4">
                {/* Condition Logic */}
                <div>
                  <Label className="text-xs font-medium">
                    Condition Logic
                  </Label>
                  <div className="mt-2 space-y-2">
                    <label className={`flex items-start gap-3 cursor-pointer p-3 rounded-lg border transition-colors hover:bg-gray-50 ${
                      formData.condition_logic === 'all' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                    }`}>
                      <input
                        type="radio"
                        value="all"
                        checked={formData.condition_logic === 'all'}
                        onChange={(e) => handleFieldChange('condition_logic', e.target.value)}
                        className="mt-0.5 text-blue-600"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-xs">All</div>
                        <div className="text-[11px] text-gray-600 mt-0.5">
                          Every condition must be true for the alarm to trigger
                        </div>
                      </div>
                    </label>
                    <label className={`flex items-start gap-3 cursor-pointer p-3 rounded-lg border transition-colors hover:bg-gray-50 ${
                      formData.condition_logic === 'any' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                    }`}>
                      <input
                        type="radio"
                        value="any"
                        checked={formData.condition_logic === 'any'}
                        onChange={(e) => handleFieldChange('condition_logic', e.target.value)}
                        className="mt-0.5 text-blue-600"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-xs">Any</div>
                        <div className="text-[11px] text-gray-600 mt-0.5">
                          At least one condition must be true for the alarm to trigger
                        </div>
                      </div>
                    </label>
                  </div>
                </div>

                {/* Condition Rules */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label className="text-xs font-medium">
                      Condition Rules <span className="text-red-500">*</span>
                    </Label>
                    {errors.condition_rules && (
                      <span className="text-[10px] text-red-500 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.condition_rules}
                      </span>
                    )}
                  </div>
                  
                  <ConditionRuleBuilderDynamic
                    rules={formData.condition_rules}
                    onChange={(newRules) => {
                      handleFieldChange('condition_rules', newRules);
                    }}
                    isReadOnly={false}
                  />
                </div>
              </div>
            )}

            {activeTab === 'advanced' && (
              <div className="space-y-4">
                {/* Alert Delay */}
                <div>
                  <Label className="text-xs font-medium">
                    Alert Delay
                  </Label>
                  <div className="mt-1 space-y-2">
                    <div className="flex gap-2">
                      {[0, 60, 300, 900, 1800, 3600].map((seconds) => (
                        <button
                          key={seconds}
                          type="button"
                          onClick={() => handleFieldChange('delay_seconds', seconds)}
                          className={`px-3 py-1.5 text-xs rounded-md transition-colors ${
                            formData.delay_seconds === seconds
                              ? 'bg-blue-100 text-blue-700 border border-blue-300'
                              : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                          }`}
                        >
                          {formatDelay(seconds)}
                        </button>
                      ))}
                    </div>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        value={formData.delay_seconds}
                        onChange={(e) => handleFieldChange('delay_seconds', parseInt(e.target.value) || 0)}
                        min="0"
                        className="text-xs w-24"
                      />
                      <span className="text-xs text-gray-500">seconds</span>
                    </div>
                  </div>
                  <p className="mt-1 text-[10px] text-gray-500">
                    Wait this long after condition is met before triggering alert
                  </p>
                </div>

                {/* Operating Hours */}
                <div>
                  <Label className="text-xs font-medium">
                    Operating Hours
                  </Label>
                  <div className="mt-2 space-y-3">
                    {/* Preset Options */}
                    <div className="flex flex-wrap gap-2">
                      <button
                        type="button"
                        onClick={() => handleFieldChange('operating_hours', null)}
                        className={`px-3 py-1.5 text-xs rounded-md transition-colors ${
                          !formData.operating_hours
                            ? 'bg-blue-100 text-blue-700 border border-blue-300'
                            : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                        }`}
                      >
                        24/7
                      </button>
                      <button
                        type="button"
                        onClick={() => handleFieldChange('operating_hours', '08:00-17:00')}
                        className={`px-3 py-1.5 text-xs rounded-md transition-colors ${
                          formData.operating_hours === '08:00-17:00'
                            ? 'bg-blue-100 text-blue-700 border border-blue-300'
                            : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                        }`}
                      >
                        Business Hours
                      </button>
                      <button
                        type="button"
                        onClick={() => handleFieldChange('operating_hours', '00:00-06:00')}
                        className={`px-3 py-1.5 text-xs rounded-md transition-colors ${
                          formData.operating_hours === '00:00-06:00'
                            ? 'bg-blue-100 text-blue-700 border border-blue-300'
                            : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                        }`}
                      >
                        Night Shift
                      </button>
                    </div>
                    
                    {/* Custom Time Selection */}
                    <div className="border border-gray-200 rounded-lg bg-white shadow-sm relative">
                      <div className="p-3">
                        <Label className="text-[11px] text-gray-600 font-medium">Custom Hours</Label>
                      </div>
                      <div className="border-t border-gray-100 p-3 relative">
                        <div className="flex items-center gap-2">
                          <div className="flex-1">
                            <Select
                              value={formData.operating_hours?.split('-')[0] || ''}
                              onValueChange={(value) => {
                                const endTime = formData.operating_hours?.split('-')[1] || '17:00';
                                handleFieldChange('operating_hours', value ? `${value}-${endTime}` : null);
                              }}
                            >
                              <SelectTrigger className="h-8 text-xs">
                                <SelectValue placeholder="Start time" />
                              </SelectTrigger>
                              <SelectContent align="start" side="bottom" className="max-h-[200px]">
                                {Array.from({ length: 24 }, (_, i) => {
                                  const hour = i.toString().padStart(2, '0');
                                  return ['00', '30'].map(min => {
                                    const time = `${hour}:${min}`;
                                    return (
                                      <SelectItem key={time} value={time}>
                                        <span className="text-xs">{time}</span>
                                      </SelectItem>
                                    );
                                  });
                                }).flat()}
                              </SelectContent>
                            </Select>
                          </div>
                          
                          <div className="flex items-center justify-center px-2">
                            <span className="text-xs text-gray-400">→</span>
                          </div>
                          
                          <div className="flex-1">
                            <Select
                              value={formData.operating_hours?.split('-')[1] || ''}
                              onValueChange={(value) => {
                                const startTime = formData.operating_hours?.split('-')[0] || '08:00';
                                handleFieldChange('operating_hours', value ? `${startTime}-${value}` : null);
                              }}
                            >
                              <SelectTrigger className="h-8 text-xs">
                                <SelectValue placeholder="End time" />
                              </SelectTrigger>
                              <SelectContent align="start" side="bottom" className="max-h-[200px]">
                                {Array.from({ length: 24 }, (_, i) => {
                                  const hour = i.toString().padStart(2, '0');
                                  return ['00', '30'].map(min => {
                                    const time = `${hour}:${min}`;
                                    return (
                                      <SelectItem key={time} value={time}>
                                        <span className="text-xs">{time}</span>
                                      </SelectItem>
                                    );
                                  });
                                }).flat()}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-[10px] text-gray-500">
                      {formData.operating_hours 
                        ? `Alarm will only trigger between ${formData.operating_hours.split('-')[0]} and ${formData.operating_hours.split('-')[1]}`
                        : 'Alarm will be monitored 24/7'}
                    </p>
                  </div>
                </div>

                {/* Info Box */}
                <div className="bg-blue-50 rounded-lg p-3 mt-4">
                  <div className="flex items-start gap-2">
                    <Info className="h-4 w-4 text-blue-600 mt-0.5" />
                    <div className="text-xs text-blue-800 space-y-1">
                      <p className="font-medium">Advanced Options</p>
                      <ul className="list-disc list-inside space-y-0.5 text-[11px]">
                        <li>Delays help prevent false alarms from temporary spikes</li>
                        <li>Operating hours limit alerts to business hours</li>
                        <li>Conditions are checked every minute by default</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

    </form>
  );
};