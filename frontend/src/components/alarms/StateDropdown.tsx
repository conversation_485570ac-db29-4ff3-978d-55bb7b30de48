import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { updateAFDDLogState } from '@/lib/api/alarms';
import { useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/components/ui/ToastProvider';

interface StateDropdownProps {
  logId: number;
  currentState: string;
  isDisabled?: boolean;
}

const stateOptions = [
  { value: 'unacknowledged', label: 'Unacknowledged' },
  { value: 'acknowledged', label: 'Acknowledged' },
  { value: 'cleared', label: 'Cleared' },
];

const StateDropdown: React.FC<StateDropdownProps> = ({ logId, currentState, isDisabled = false }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();
  const { showToast } = useToast();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const currentOption = stateOptions.find(opt => opt.value === currentState) || stateOptions[0];

  const handleStateChange = async (newState: string) => {
    console.log('handleStateChange called:', { newState, currentState, logId });
    if (newState === currentState || isUpdating) return;

    setIsUpdating(true);
    try {
      console.log('Calling updateAFDDLogState with:', { logId, newState });
      await updateAFDDLogState(logId, newState);
      queryClient.invalidateQueries({ queryKey: ['afdd-logs'] });
      showToast({
        message: `State updated to ${stateOptions.find(opt => opt.value === newState)?.label}`,
        type: 'success',
        duration: 3000
      });
    } catch (error) {
      console.error('Error updating state:', error);
      showToast({
        message: 'Failed to update state',
        type: 'error',
        duration: 3000
      });
    } finally {
      setIsUpdating(false);
      setIsOpen(false);
    }
  };

  const getStateColor = (state: string) => {
    switch (state) {
      case 'unacknowledged':
        return 'text-amber-700';
      case 'acknowledged':
        return 'text-blue-700';
      case 'cleared':
        return 'text-gray-500';
      default:
        return 'text-gray-700';
    }
  };

  if (isDisabled) {
    return (
      <span className={`text-xs ${getStateColor(currentState)}`}>
        {currentOption.label}
      </span>
    );
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={(e) => {
          e.stopPropagation();
          setIsOpen(!isOpen);
        }}
        disabled={isUpdating}
        className={`
          inline-flex items-center gap-1 text-xs py-0.5 px-1.5 rounded hover:bg-gray-50 transition-colors
          ${getStateColor(currentState)}
          ${isUpdating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        <span>{currentOption.label}</span>
        <ChevronDown className={`h-3 w-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && !isUpdating && (
        <div 
          className="absolute top-full left-0 mt-1 w-36 bg-white rounded-md shadow-lg border border-gray-200 z-50"
          onClick={(e) => e.stopPropagation()}
        >
          {stateOptions.map((option, index) => (
            <button
              key={option.value}
              onClick={(e) => {
                e.stopPropagation();
                handleStateChange(option.value);
              }}
              className={`
                w-full text-left px-3 py-1.5 text-xs hover:bg-gray-50 transition-colors
                ${option.value === currentState ? 'bg-gray-50 font-medium' : ''}
                ${index === 0 ? 'rounded-t-md' : ''}
                ${index === stateOptions.length - 1 ? 'rounded-b-md' : ''}
              `}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default StateDropdown;