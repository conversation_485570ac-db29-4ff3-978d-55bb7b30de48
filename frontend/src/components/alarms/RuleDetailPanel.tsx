import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ChevronRight, Loader2 } from 'lucide-react';
import { apiClient } from '@/lib/api/enhancedApiClient';
import { AlarmRule } from '@/types/alarms';
import { FaultConfigFormNew } from './FaultConfigFormNew';
import { Button } from '@/components/ui/Button';
import { useToast } from '@/components/ui/ToastProvider';

interface RuleDetailPanelProps {
  rule: AlarmRule | null;
  isCreating?: boolean;
  onClose: () => void;
  onCreate?: (rule: AlarmRule) => void;
  onEdit?: (rule: AlarmRule) => void;
  onDelete?: (id: number) => void;
}

interface AFDDFaultDetail {
  id: number;
  site: {
    id: string;
    name: string;
  };
  name: string;
  description: string;
  category: string;
  severity: 'critical' | 'warning' | 'info';
  enabled: boolean;
  notify_admin_only: boolean;
  condition_logic: 'all' | 'any';
  condition_rules: string[];
  delay_seconds: number;
  operating_hours: string | null;
  metadata: Record<string, any> | null;
  created_by: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
  updated_by: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
  created_at: string;
  updated_at: string;
}

const RuleDetailPanel: React.FC<RuleDetailPanelProps> = ({ 
  rule, 
  isCreating = false, 
  onClose, 
  onCreate, 
  onEdit, 
  onDelete 
}) => {
  const queryClient = useQueryClient();
  const { showToast } = useToast();
  const [mode, setMode] = useState<'create' | 'edit' | 'view'>(isCreating ? 'create' : 'view');

  // Reset mode when switching between rules or create mode
  useEffect(() => {
    if (isCreating) {
      setMode('create');
    } else {
      setMode('view');
    }
  }, [isCreating, rule?.id]);

  // Fetch fault details if we have a rule ID
  const { data: faultDetail, isLoading, error } = useQuery({
    queryKey: ['afdd-fault', rule?.id],
    queryFn: async () => {
      if (!rule?.id) return null;
      const response = await apiClient.get(`/afdd/faults/${rule.id}/`);
      
      // Handle AFDD API wrapper format
      if (response && response.data) {
        return response.data as AFDDFaultDetail;
      }
      return response as AFDDFaultDetail;
    },
    enabled: !!rule?.id && !isCreating,
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiClient.post('/afdd/faults/', data);
      // Handle AFDD API wrapper format
      if (response && response.data) {
        return response.data;
      }
      return response;
    },
    onSuccess: (data) => {
      showToast({
        message: 'Fault configuration created successfully',
        type: 'success',
        duration: 3000
      });
      queryClient.invalidateQueries({ queryKey: ['afdd-faults'] });
      onCreate?.(data);
      onClose();
    },
    onError: (error: any) => {
      showToast({
        message: error.message || 'Failed to create fault configuration',
        type: 'error',
        duration: 5000
      });
    }
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: any }) => {
      const response = await apiClient.put(`/afdd/faults/${id}/`, data);
      // Handle AFDD API wrapper format
      if (response && response.data) {
        return response.data;
      }
      return response;
    },
    onSuccess: (data) => {
      showToast({
        message: 'Fault configuration updated successfully',
        type: 'success',
        duration: 3000
      });
      queryClient.invalidateQueries({ queryKey: ['afdd-faults'] });
      queryClient.invalidateQueries({ queryKey: ['afdd-fault', rule?.id] });
      onEdit?.(data);
      setMode('view');
    },
    onError: (error: any) => {
      showToast({
        message: error.message || 'Failed to update fault configuration',
        type: 'error',
        duration: 5000
      });
    }
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiClient.delete(`/afdd/faults/${id}/`);
    },
    onSuccess: () => {
      showToast({
        message: 'Fault configuration deleted successfully',
        type: 'success',
        duration: 3000
      });
      queryClient.invalidateQueries({ queryKey: ['afdd-faults'] });
      if (rule?.id) {
        onDelete?.(rule.id);
      }
      onClose();
    },
    onError: (error: any) => {
      showToast({
        message: error.message || 'Failed to delete fault configuration',
        type: 'error',
        duration: 5000
      });
    }
  });

  // Handle form submission
  const handleSubmit = async (formData: any) => {
    // Prepare payload according to API requirements
    const payload = {
      name: formData.name,
      description: formData.description || '',
      category: formData.category || null,
      severity: formData.severity,
      enabled: formData.enabled,
      condition_logic: formData.condition_logic,
      condition_rules: formData.condition_rules,
      delay_seconds: formData.delay_seconds,
      operating_hours: formData.operating_hours || null,
      metadata: formData.metadata || null,
    };

    if (isCreating) {
      createMutation.mutate(payload);
    } else if (rule?.id) {
      updateMutation.mutate({ id: rule.id, data: payload });
    }
  };

  // Handle delete
  const handleDelete = () => {
    if (rule?.id && window.confirm('Are you sure you want to delete this fault configuration?')) {
      deleteMutation.mutate(rule.id);
    }
  };

  // Convert fault detail to form data
  const getInitialData = () => {
    // Don't return any data if we're in create mode
    if (isCreating) {
      return undefined;
    }
    
    if (faultDetail) {
      return {
        name: faultDetail.name,
        description: faultDetail.description,
        category: faultDetail.category,
        severity: faultDetail.severity,
        enabled: faultDetail.enabled,
        condition_logic: faultDetail.condition_logic,
        condition_rules: faultDetail.condition_rules,
        delay_seconds: faultDetail.delay_seconds,
        operating_hours: faultDetail.operating_hours,
        metadata: faultDetail.metadata,
      };
    }
    return undefined;
  };

  // Loading state
  if (!isCreating && isLoading) {
    return (
      <div className="w-1/2 h-full flex flex-col bg-white border-l border-gray-200">
        <div className="flex items-center justify-between p-3 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8 hover:bg-gray-100"
            >
              <ChevronRight className="h-4 w-4 text-gray-500" />
            </Button>
            <h2 className="text-base font-medium text-gray-900">Loading...</h2>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    );
  }

  // Error state
  if (!isCreating && error) {
    return (
      <div className="w-1/2 h-full flex flex-col bg-white border-l border-gray-200">
        <div className="flex items-center justify-between p-3 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={onClose}
              className="h-8 w-8 hover:bg-gray-100"
            >
              <ChevronRight className="h-4 w-4 text-gray-500" />
            </Button>
            <h2 className="text-base font-medium text-gray-900">Error</h2>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center p-6">
          <div className="text-center">
            <p className="text-sm text-red-600">Failed to load fault configuration</p>
            <p className="text-xs text-gray-500 mt-1">{(error as Error).message}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-1/2 h-full flex flex-col bg-white border-l border-gray-200">
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8 hover:bg-gray-100"
          >
            <ChevronRight className="h-4 w-4 text-gray-500" />
          </Button>
          <h2 className="text-sm font-medium text-gray-900">
            {isCreating ? 'Create Fault Configuration' : 
             faultDetail?.name || rule?.name || 'Fault Configuration'}
          </h2>
        </div>
        <div className="flex gap-2">
          {mode === 'edit' || mode === 'create' ? (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={mode === 'create' ? onClose : () => setMode('view')}
                className="text-xs"
              >
                Cancel
              </Button>
              <Button
                type="button"
                size="sm"
                onClick={() => {
                  // Trigger form submission
                  const formElement = document.getElementById('fault-config-form');
                  if (formElement) {
                    const submitEvent = new Event('submit', { 
                      bubbles: true, 
                      cancelable: true 
                    });
                    formElement.dispatchEvent(submitEvent);
                  }
                }}
                disabled={createMutation.isPending || updateMutation.isPending}
                className="text-xs text-white"
                style={{ backgroundColor: '#2563eb', }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#1d4ed8'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#2563eb'}
              >
                {createMutation.isPending || updateMutation.isPending ? 'Saving...' : mode === 'create' ? 'Create' : 'Save'}
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setMode('edit')}
                className="text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200"
              >
                Edit
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                disabled={deleteMutation.isPending}
                className="text-xs text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
              >
                Delete
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        <FaultConfigFormNew
          mode={mode}
          initialData={isCreating ? undefined : getInitialData()}
          onSubmit={handleSubmit}
          onCancel={mode === 'create' ? onClose : () => setMode('view')}
          isLoading={createMutation.isPending || updateMutation.isPending}
        />
      </div>
    </div>
  );
};

export default RuleDetailPanel;