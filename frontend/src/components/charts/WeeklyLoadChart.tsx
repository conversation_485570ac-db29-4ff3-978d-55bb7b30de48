import React, { useRef } from 'react';
import * as echarts from 'echarts';
import { useLayoutEffect } from 'react';
import { CHART_STYLES } from '../../lib/config/ui';
import { BaseChartProps } from './types';
import { useChartConfig } from './useChartConfig';

export function WeeklyLoadChart({ data, comparisonData }: BaseChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts>();
  const isInitialized = useRef(false);

  const { option } = useChartConfig({
    data,
    comparisonData,
    view: 'weekly',
    showPeakHours: false,
    smooth: false
  });

  // Initialize chart with proper sizing
  const initializeChart = React.useCallback(() => {
    if (!chartRef.current) return;
    if (isInitialized.current) return;

    // Dispose existing instance if any
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    // Create new instance
    chartInstance.current = echarts.init(chartRef.current);
    chartInstance.current.setOption(option);
    
    isInitialized.current = true;
  }, [option]);

  // Handle window resize with debounce
  const handleResize = React.useCallback(() => {
    if (chartInstance.current) {
      chartInstance.current.resize();
    }
  }, []);

  // Use useLayoutEffect for synchronous initialization
  useLayoutEffect(() => {
    initializeChart();

    // Add resize listener
    window.addEventListener('resize', handleResize);

    // Clean up
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current && typeof chartInstance.current.dispose === 'function') {
        try {
          chartInstance.current.dispose();
        } catch (e) {
          console.error("Error disposing chart:", e);
        }
      }
      chartInstance.current = undefined;
      isInitialized.current = false;
    };
  }, [initializeChart, handleResize]);

  return (
    <div
      ref={chartRef}
      className="w-full h-full min-w-0 will-change-transform overflow-hidden relative focus:outline-none bg-gradient-to-br from-white to-blue-50/10"
      style={{ minHeight: '200px' }}
    />
  );
}