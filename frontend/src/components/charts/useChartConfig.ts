import { useMemo } from 'react';
import * as echarts from 'echarts';
import { CHART_STYLES } from '../../lib/config/ui';
import { isPeakHour } from '../../lib/config/load-profile';
import { getUnitForView } from '../../lib/config/units';
import type { ChartConfigOptions, ChartConfig } from './types';

// Helper function to round to a nice number
const roundToNiceNumber = (value: number): number => {
  const magnitude = Math.pow(10, Math.floor(Math.log10(value)));
  const normalized = value / magnitude;

  if (normalized <= 1.5) return Math.ceil(normalized) * magnitude;
  if (normalized <= 3) return 3 * magnitude;
  if (normalized <= 5) return 5 * magnitude;
  if (normalized <= 7) return 7.5 * magnitude;
  return Math.ceil(normalized) * magnitude;
};

// Helper functions
const getAxisLabels = (view: ChartConfigOptions['view']) => {
  switch (view) {
    case 'daily':
      return Array.from({ length: 25 }, (_, i) => `${i}:00`);
    case 'weekly':
      return ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    case 'monthly':
      return Array.from({ length: 31 }, (_, i) => String(i + 1));
    case 'yearly':
      return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    case 'multi-year':
      const currentYear = new Date().getFullYear();
      return Array.from({ length: 5 }, (_, i) => String(currentYear - 4 + i));
    default:
      return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  }
};

const getLegendData = (isComparison: boolean) => [
  {
    name: isComparison ? '2024' : 'Today (Real-time)',
    itemStyle: { color: CHART_STYLES.colors.primary },
    textStyle: { color: '#374151', fontSize: 11, fontWeight: 500 }
  },
  {
    name: isComparison ? '2023' : 'Yesterday',
    itemStyle: { color: CHART_STYLES.colors.secondary },
    textStyle: { color: '#374151', fontSize: 11, fontWeight: 500 },
    lineStyle: {
      type: 'dashed',
      width: 1.5
    }
  }
];

const getBarItemStyle = (isComparison: boolean, isPrimary: boolean) => ({
  color: isComparison
    ? (isPrimary ? '#065BA9' : '#3B82F6')
    : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: isPrimary ? '#065BA9' : '#3B82F6' },
        { offset: 1, color: isPrimary ? '#0550A0' : '#2563EB' }
      ]),
  borderRadius: [4, 4, 0, 0]
});

const getLineStyle = (isPrimary: boolean) => ({
  symbol: isPrimary ? 'circle' : 'none',
  showSymbol: false,
  lineStyle: {
    width: isPrimary ? 2.5 : 1.5,
    type: isPrimary ? 'solid' : 'dashed',
    color: isPrimary ? CHART_STYLES.colors.primary : CHART_STYLES.colors.secondary,
    opacity: isPrimary ? 1 : 0.5
  },
  itemStyle: {
    color: isPrimary ? CHART_STYLES.colors.primary : CHART_STYLES.colors.secondary,
  },
  ...(isPrimary && {
    areaStyle: {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: 'rgba(6, 91, 169, 0.35)' },
        { offset: 1, color: 'rgba(6, 91, 169, 0.02)' }
      ])
    }
  })
});

export function useChartConfig({
  data,
  comparisonData,
  view,
  showPeakHours,
  smooth,
  isComparison = false
}: ChartConfigOptions & { isComparison?: boolean }): ChartConfig {
  return useMemo(() => {
    // Calculate max value from data
    const maxValue = Math.max(
      ...data.map(d => d.demand),
      ...(comparisonData?.map(d => d.demand) || [])
    );

    // Set y-axis max to 1.25 times the max value
    const yAxisMax = roundToNiceNumber(maxValue * 1.25);

    const axisLabels = getAxisLabels(view);
    const isBarChart = view !== 'daily';

    const theme = {
      colors: [CHART_STYLES.colors.primary],
      backgroundColor: 'transparent',
      gradients: {
        primary: new echarts.graphic.LinearGradient(0, 0, 0, 1, CHART_STYLES.gradients.area),
        secondary: new echarts.graphic.LinearGradient(0, 0, 0, 1, CHART_STYLES.gradients.secondaryArea)
      }
    };

    const option: echarts.EChartsOption = {
      legend: {
        show: true,
        top: 0,
        right: 35,
        itemWidth: 12,
        itemHeight: 3,
        itemGap: 24,
        padding: [4, 8],
        textStyle: {
          fontSize: 11,
          color: '#374151',
          fontWeight: 500
        },
        icon: 'roundRect',
        data: getLegendData(isComparison)
      },
      animation: true,
      animationDuration: 300,
      animationEasing: 'cubicInOut',
      // Enable click events
      emphasis: {
        focus: 'series'
      },
      // Use responsive grid layout
      grid: {
        containLabel: true,
        top: view === 'daily' ? 40 : 20,
        right: 20,
        bottom: 30,
        left: 50
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          snap: true,
          lineStyle: {
            color: 'rgba(96, 165, 250, 0.15)',
            width: 1,
            type: 'dashed'
          }
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#E2E8F0',
        borderWidth: 0,
        padding: [8, 12],
        textStyle: { fontSize: 12, color: '#374151' },
        formatter: (params: any[]) => {
          const time = params[0].name;
          const current = params.find(p => p.seriesName === (isComparison ? '2024' : 'Today (Real-time)'))?.value ?? 0;
          const comparison = params.find(p => p.seriesName === (isComparison ? '2023' : 'Yesterday'))?.value ?? 0;
          const diff = ((current - comparison) / comparison * 100).toFixed(1);
          const isPeakTime = view === 'daily' && isPeakHour(parseInt(time.split(':')[0]));
          const unit = view === 'daily' ? 'kW' : 'kWh';

          return `
            <div style="font-weight: 500; margin-bottom: 6px;">
              <span style="font-size: 11px; color: #1E293B;">${time}</span>
              ${isPeakTime ? '<span style="font-size: 10px; color: #94A3B8; margin-left: 4px;">(Peak Hours Mon-Fri)</span>' : ''}
            </div>
            <div style="display: grid; gap: 4px;">
              ${[
                { name: isComparison ? '2024' : 'Today (Real-time)', value: current, color: CHART_STYLES.colors.primary },
                { name: isComparison ? '2023' : 'Yesterday', value: comparison, color: CHART_STYLES.colors.secondary }
              ].map(item => `
                <div style="display: flex; align-items: center; justify-content: space-between; gap: 12px;">
                  <div style="display: flex; align-items: center; gap: 6px;">
                    <span style="display: inline-block; width: 6px; height: 6px; border-radius: 50%; background: ${item.color};"></span>
                    <span style="color: #374151; font-size: 9px;">${item.name}</span>
                  </div>
                  <span style="font-weight: 500; color: ${item.color}; font-size: 9px;">${item.value.toLocaleString()} ${unit}</span>
                </div>
              `).join('')}
              <div style="margin-top: 2px; padding-top: 4px; border-top: 1px dashed #E2E8F0;">
                <div style="display: flex; align-items: center; justify-content: space-between; gap: 12px;">
                  <span style="color: #64748B; font-size: 9px;">Difference</span>
                  <span style="font-weight: 500; font-size: 9px; color: ${parseFloat(diff) > 0 ? '#EF4444' : '#10B981'};">
                    ${parseFloat(diff) > 0 ? '+' : ''}${diff}%
                  </span>
                </div>
              </div>
            </div>
          `;
        }
      },
      xAxis: {
        type: 'category',
        boundaryGap: isBarChart,
        axisLine: {
          show: true,
          lineStyle: { color: '#E2E8F0' }
        },
        axisTick: {
          show: false
        },
        data: view === 'multi-year' ? data.map(d => d.time) : axisLabels,
        axisLabel: {
          interval: view === 'daily' ? 3 : 0,
          color: '#94A3B8',
          fontSize: 10,
          margin: 12,
          formatter: view === 'multi-year' ? (value: string) => value : undefined
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        max: yAxisMax,
        nameTextStyle: {
          color: '#64748B',
          fontSize: 10,
          align: 'left',
          padding: [0, 0, 0, -20]
        },
        axisLine: { show: true, lineStyle: { color: '#E2E8F0' } },
        axisTick: { show: false },
        splitLine: {
          z: -10, // Set negative z-index to push gridlines behind data series
          zlevel: -1, // Use zlevel to ensure gridlines are rendered on a lower canvas layer
          lineStyle: { color: '#F1F5F9', type: 'dashed', width: 1 }
        },
        position: 'left',
        inverse: false,
        axisLabel: {
          color: '#94A3B8',
          fontSize: 10,
          margin: 12,
          position: 'outside',
          align: 'right',
          padding: [0, 16, 0, 0],
          formatter: (value: number) => {
            if (value >= 1000000) {
              return `${Math.round(value / 1000000)}M`;
            }
            if (value >= 1000) {
              return `${Math.round(value / 1000)}k`;
            }
            return Math.round(value);
          }
        }
      },
      series: [
        ...(showPeakHours && !isComparison ? [{
          name: 'Peak Hours',
          type: 'bar',
          z: -1,
          silent: true,
          showSymbol: false,
          barWidth: '100%',
          stack: 'background',
          data: axisLabels.map((time) => {
            const hour = parseInt(time.split(':')[0]);
            return isPeakHour(hour)
              ? { value: yAxisMax, itemStyle: { color: CHART_STYLES.colors.peakZone } }
              : { value: 0, itemStyle: { color: 'transparent' } };
          })
        }] : []),
        {
          name: isComparison ? '2024' : 'Today (Real-time)',
          type: isBarChart ? 'bar' : 'line',
          barMaxWidth: view === 'multi-year' ? 40 : 30,
          barGap: '20%',
          cursor: isBarChart && view === 'yearly' ? 'pointer' : 'default',
          z: 2,
          zlevel: 1,
          smooth,
          data: data.map(d => d.demand),
          ...(isBarChart
            ? {
                barWidth: undefined,
                itemStyle: getBarItemStyle(isComparison, true)
              }
            : getLineStyle(true)),
          // Add click event data
          selectedMode: isBarChart && view === 'yearly' ? 'single' : false,
          emphasis: {
            focus: 'series',
            scale: true,
            scaleSize: 6,
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0,0,0,0.1)',
              // Add cursor style for clickable bars
              cursor: isBarChart && view === 'yearly' ? 'pointer' : 'default'
            }
          }
        },
        comparisonData && {
          name: isComparison ? '2023' : 'Yesterday',
          type: isBarChart ? 'bar' : 'line',
          barMaxWidth: view === 'multi-year' ? 40 : 30,
          z: 1,
          zlevel: 1,
          smooth,
          data: comparisonData.map(d => d.lastWeek),
          ...(isBarChart
            ? { barWidth: undefined, itemStyle: getBarItemStyle(isComparison, false) }
            : getLineStyle(false)),
          emphasis: {
            focus: 'series',
            scale: true,
            scaleSize: 6,
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0,0,0,0.1)'
            }
          }
        }
      ].filter(Boolean) as echarts.SeriesOption[]
    };

    return { option, theme };
  }, [data, comparisonData, view, showPeakHours, smooth, isComparison]);
}