import type { LoadProfileData } from '../../lib/config/load-profile';

export type ChartView = 'daily' | 'weekly' | 'monthly' | 'yearly' | 'multi-year';

export interface BaseChartProps {
  data?: LoadProfileData[];
  comparisonData?: LoadProfileData[];
  years?: number[];
  onBarClick?: (date: Date) => void;
}

export interface ChartConfigOptions {
  data: LoadProfileData[];
  comparisonData?: LoadProfileData[];
  view: ChartView;
  showPeakHours: boolean;
  smooth: boolean;
}

export interface ChartConfig {
  option: echarts.EChartsOption;
  theme: {
    colors: string[];
    backgroundColor: string;
    gradients: {
      primary: echarts.graphic.LinearGradient;
      secondary: echarts.graphic.LinearGradient;
    };
  };
}