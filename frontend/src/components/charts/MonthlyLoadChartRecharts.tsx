import React, { useMemo } from 'react';
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Legend
} from 'recharts';
import { CHART_STYLES } from '../../constants/chartStyles';
import { formatNumber } from '../../utils/formatting';
import { BaseChartProps } from './types';
import { LoadProfileData } from '../../lib/config/load-profile';

interface ChartDataPoint {
  time: string;
  today: number;
  yesterday?: number;
  demand: number;
}

export function MonthlyLoadChart({ 
  data = [], 
  comparisonData = [],
}: BaseChartProps) {
  
  // Transform data for Recharts
  const chartData = useMemo(() => {
    // Generate full month days (1-31)
    const fullMonthDays = Array.from({ length: 31 }, (_, i) => (i + 1).toString());
    
    // Create maps for easy lookup
    const todayMap = new Map(
      data?.map(item => [item.time, item.demand]) || []
    );
    const yesterdayMap = new Map(
      comparisonData?.map(item => [item.time, item.demand]) || []
    );
    
    // Generate full chart data with all days
    return fullMonthDays.map((day) => ({
      time: day,
      today: todayMap.get(day) || 0,
      yesterday: yesterdayMap.get(day) || undefined,
      demand: todayMap.get(day) || 0,
    }));
  }, [data, comparisonData]);

  // Calculate y-axis domain
  const yAxisDomain = useMemo(() => {
    if (chartData.length === 0) return [0, 100];
    
    const maxToday = Math.max(...chartData.map(d => d.today || 0));
    const maxYesterday = Math.max(...chartData.map(d => d.yesterday || 0));
    const overallMax = Math.max(maxToday, maxYesterday);
    const maxValue = overallMax > 0 ? Math.ceil((overallMax * 1.1) / 100) * 100 : 100;
    
    return [0, maxValue];
  }, [chartData]);

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (!active || !payload || !payload.length) return null;
    
    return (
      <div className="bg-white/95 p-3 border border-gray-200 rounded-lg shadow-lg text-sm">
        <p className="font-medium text-gray-800 mb-2">Day {label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2 mb-1">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: entry.color }} 
            />
            <span className="text-gray-600">
              {entry.name === 'today' ? 'This Month' : 'Last Month'}:
            </span>
            <span className="font-semibold">
              {formatNumber(entry.value)} kWh
            </span>
          </div>
        ))}
      </div>
    );
  };

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={chartData}
        margin={{ top: 24, right: 15, left: 40, bottom: 25 }}
        maxBarSize={CHART_STYLES.bar.maxBarSize}
        barCategoryGap={CHART_STYLES.bar.barCategoryGap}
      >
        <defs>
          <linearGradient id="thisMonthBarGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor={CHART_STYLES.bar.colors.primary} stopOpacity={1.0} />
            <stop offset="100%" stopColor={CHART_STYLES.bar.colors.secondary} stopOpacity={0.95} />
          </linearGradient>
          <linearGradient id="lastMonthBarGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="0%" stopColor="#94A3B8" stopOpacity={0.8} />
            <stop offset="100%" stopColor="#64748B" stopOpacity={0.9} />
          </linearGradient>
        </defs>
        
        <CartesianGrid 
          strokeDasharray={CHART_STYLES.grid.strokeDasharray}
          vertical={CHART_STYLES.grid.vertical}
          stroke={CHART_STYLES.grid.stroke}
          strokeOpacity={CHART_STYLES.grid.strokeOpacity}
        />
        
        <XAxis 
          dataKey="time"
          tick={CHART_STYLES.axis.tick}
          axisLine={CHART_STYLES.axis.axisLine}
          tickLine={CHART_STYLES.axis.tickLine}
          interval="preserveStartEnd"
          minTickGap={20}
        />
        
        <YAxis 
          domain={yAxisDomain}
          tick={CHART_STYLES.axis.tick}
          axisLine={CHART_STYLES.axis.axisLine}
          tickLine={CHART_STYLES.axis.tickLine}
          tickFormatter={(value) => formatNumber(value)}
          label={{ 
            value: 'kWh', 
            angle: -90, 
            position: 'insideLeft',
            style: CHART_STYLES.axis.yLabel.style
          }}
        />
        
        <Tooltip 
          contentStyle={CHART_STYLES.tooltip.contentStyle}
          content={<CustomTooltip />} 
        />
        
        <Legend 
          verticalAlign="top" 
          height={36}
          iconType="rect"
          wrapperStyle={CHART_STYLES.legend.wrapperStyle}
        />

        {/* Last month bars (if comparison data exists) */}
        {comparisonData && comparisonData.length > 0 && (
          <Bar
            dataKey="yesterday"
            fill="url(#lastMonthBarGradient)"
            radius={CHART_STYLES.bar.radius}
            name="Last Month"
            isAnimationActive={true}
            animationDuration={CHART_STYLES.bar.animation.duration}
          />
        )}

        {/* This month bars */}
        <Bar
          dataKey="today"
          fill="url(#thisMonthBarGradient)"
          radius={CHART_STYLES.bar.radius}
          name="This Month"
          isAnimationActive={true}
          animationDuration={CHART_STYLES.bar.animation.duration}
        />
      </BarChart>
    </ResponsiveContainer>
  );
}