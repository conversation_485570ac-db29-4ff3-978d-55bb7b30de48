import React, { useRef, useEffect } from 'react';
import { UNIFIED_CHART_STYLES, CHART_COLORS } from '../../lib/config/chart-styles';

export interface PieChartDataItem {
  name: string;
  value: number;
}

export interface CanvasPieChartProps {
  data: PieChartDataItem[];
  width?: number;
  height?: number;
  onSliceClick?: (item: PieChartDataItem) => void;
}

const CanvasPieChart: React.FC<CanvasPieChartProps> = ({ data, width = 300, height = 300, onSliceClick }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!data || data.length === 0) return;
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Adjust for retina displays
    const dpr = window.devicePixelRatio || 1;
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;
    ctx.scale(dpr, dpr);

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    const total = data.reduce((sum, item) => sum + item.value, 0);
    const centerX = width / 2;
    const centerY = height / 2;
    // Use a radius that leaves some padding
    const radius = Math.min(width, height) / 2 - 10;
    let startAngle = -Math.PI / 2;

    data.forEach((item, index) => {
      const sliceAngle = (item.value / total) * Math.PI * 2;
      const endAngle = startAngle + sliceAngle;

      // Pick color either from the chart colors or fallback
      const fillColor = CHART_COLORS[index % CHART_COLORS.length];

      // Draw pie slice
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();

      ctx.fillStyle = fillColor;
      ctx.fill();

      // Draw slice border to match SVG style
      ctx.strokeStyle = '#fff';
      ctx.lineWidth = 2;
      ctx.stroke();

      startAngle = endAngle;
    });

    // Optionally, add labels in the center of each slice
    // Uncomment below if labels are needed
    /*
    startAngle = -Math.PI / 2;
    data.forEach((item, index) => {
      const sliceAngle = (item.value / total) * Math.PI * 2;
      const midAngle = startAngle + sliceAngle / 2;
      const labelX = centerX + (radius / 2) * Math.cos(midAngle);
      const labelY = centerY + (radius / 2) * Math.sin(midAngle);
      ctx.fillStyle = UNIFIED_CHART_STYLES.colors.text.primary;
      ctx.font = `${UNIFIED_CHART_STYLES.fonts.size.sm}px ${UNIFIED_CHART_STYLES.fonts.family}`;
      ctx.textAlign = 'center';
      ctx.fillText(item.name, labelX, labelY);
      startAngle += sliceAngle;
    });
    */

    // Handle click events by mapping mouse coordinates to slice areas if onSliceClick is provided.
    const handleClick = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const dx = x - centerX;
      const dy = y - centerY;
      const distance = Math.sqrt(dx * dx + dy * dy);
      if (distance > radius) return; // clicked outside pie
      let clickAngle = Math.atan2(dy, dx);
      if (clickAngle < -Math.PI / 2) {
        clickAngle += 2 * Math.PI;
      }
      let currentAngle = -Math.PI / 2;
      for (let i = 0; i < data.length; i++) {
        const sliceAngle = (data[i].value / total) * Math.PI * 2;
        if (clickAngle >= currentAngle && clickAngle < currentAngle + sliceAngle) {
          onSliceClick && onSliceClick(data[i]);
          break;
        }
        currentAngle += sliceAngle;
      }
    };

    if (onSliceClick) {
      canvas.addEventListener('click', handleClick);
    }

    return () => {
      if (onSliceClick) {
        canvas.removeEventListener('click', handleClick);
      }
    };
  }, [data, height, onSliceClick, width]);

  return <canvas ref={canvasRef} />;
};

export default React.memo(CanvasPieChart);
