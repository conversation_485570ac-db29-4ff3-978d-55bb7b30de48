import React from 'react';
import { useNavigate } from 'react-router-dom';
import { EnergySystemData } from './EnergyDonutChart';
import { Droplets, Wind, Power, BatteryCharging, Database, ArrowUpDown, Lightbulb } from 'lucide-react';

// System Icons mapping aligned with SYSTEM_TYPES IDs
export const SYSTEM_ICONS = {
  // Canonical IDs from SYSTEM_TYPES
  chillerPlant: Droplets,
  airSide: Wind,
  data_center_others: Database,
  light_power: Lightbulb,
  evCharger: BatteryCharging,
  escalator_elevator: ArrowUpDown,

  // Names from SYSTEM_TYPES (as fallbacks or for display name mapping if needed elsewhere)
  'Chiller Plant': Droplets,
  'Air Side': Wind,
  'Light & Power': Lightbulb,
  'EV Charger': BatteryCharging,
  'Escalator / Elevator': ArrowUpDown,
  'Data Center & Others': Database
} as const;

export interface SystemBreakdownCardProps {
  system: EnergySystemData;
  layout?: 'compact' | 'detailed' | 'grid' | 'pill';
  navigateOnClick?: boolean;
  showConsumption?: boolean;
  showPercentage?: boolean;
}

const SystemBreakdownCard: React.FC<SystemBreakdownCardProps> = ({
  system,
  layout = 'compact',
  navigateOnClick = true,
  showConsumption = false,
  showPercentage = true
}) => {
  const navigate = useNavigate();

  // Determine the icon to use
  const SystemIcon = SYSTEM_ICONS[system.id as keyof typeof SYSTEM_ICONS] ||
                     SYSTEM_ICONS[system.name as keyof typeof SYSTEM_ICONS] ||
                     Power;

  // Event handler for card click
  const handleClick = () => {
    if (navigateOnClick) {
      navigate(`/analytics?system=${system.id}`);
    }
  };

  // Format consumption number
  const formatConsumption = (value: number): string => {
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    });
  };

  // Percentage value (comes from either percentage or value prop)
  const percentage = system.percentage !== undefined ? system.percentage : system.value;

  // New pill layout for horizontal category tags
  if (layout === 'pill') {
    // Handle special case for Escalator/Elevator
    const displayName = system.name === 'Escalator / Elevator' || system.name === 'Escalator/Elevator'
      ? 'Escalator/Elevator'
      : system.name;

    return (
      <div
        className={`inline-flex items-center px-2 py-1 rounded-full border transition-all duration-200 ${
          navigateOnClick ? 'cursor-pointer hover:shadow-sm' : ''
        }`}
        style={{
          backgroundColor: `${system.color}10`,
          borderColor: `${system.color}30`
        }}
        onClick={navigateOnClick ? handleClick : undefined}
        title={`${system.name} - ${percentage}%`}
      >
        <div className="mr-1 flex-shrink-0">
          <SystemIcon size={12} style={{ color: system.color }} strokeWidth={2.5} />
        </div>

        <span className="text-xs font-medium truncate leading-none" style={{ color: `${system.color}CC` }}>
          {displayName}
        </span>

        {showPercentage && (
          <span className="ml-1 text-[9px] font-semibold bg-white/50 px-1 py-0.5 rounded-sm leading-none" style={{ color: system.color }}>
            {percentage}%
          </span>
        )}
      </div>
    );
  }

  // Render based on layout type
  if (layout === 'compact') {
    return (
      <div
        className="py-2 px-1.5 bg-white rounded-xl border border-slate-100 shadow-sm hover:shadow-md hover:border-slate-200 transition-all duration-300 group cursor-pointer"
        onClick={handleClick}
        title={`View ${system.name} analytics`}
      >
        <div className="flex flex-col items-center gap-1.5">
          {/* System Icon */}
          <div
            className="p-1.5 rounded-lg transition-all duration-300 group-hover:scale-110"
            style={{
              background: `linear-gradient(135deg, ${system.color}20, ${system.color}05)`,
              border: `1px solid ${system.color}20`
            }}
          >
            <SystemIcon
              size={14}
              className="transition-colors duration-300"
              style={{ color: system.color }}
            />
          </div>

          {/* System Name */}
          <span
            className="text-[10px] font-medium text-center transition-all duration-300 group-hover:text-gray-800"
            style={{ color: `${system.color}CC` }}
          >
            {system.name}
          </span>
        </div>
      </div>
    );
  }

  if (layout === 'grid') {
    // Handle special display names with line breaks
    const displayName = system.name === 'Escalator / Elevator' || system.name === 'Escalator/Elevator' ?
      <span className="inline-block">Escalator/<br/>Elevator</span> :
      system.name;

    return (
      <div
        className={`flex items-center p-2 rounded-lg border border-gray-100 shadow-sm hover:shadow-md hover:border-gray-200 transition-all duration-200 ${navigateOnClick ? 'cursor-pointer' : ''}`}
        onClick={navigateOnClick ? handleClick : undefined}
      >
        <div className="p-1 rounded-md mr-2 flex-shrink-0" style={{ backgroundColor: `${system.color}15` }}>
          <SystemIcon size={16} color={system.color} strokeWidth={2} />
        </div>
        <div className="flex flex-col flex-1 min-w-0">
          <span className="text-xs font-medium text-gray-800 leading-tight" title={system.name}>
            {displayName}
          </span>
          {showPercentage && (
            <span className="text-[10px] text-gray-500 mt-0.5">
              {percentage}%
            </span>
          )}
          {showConsumption && system.consumption && (
            <span className="text-[10px] text-gray-400">
              {formatConsumption(system.consumption)} kWh
            </span>
          )}
        </div>
      </div>
    );
  }

  // Default detailed layout
  return (
    <div
      className={`p-2.5 rounded-lg border transition-all duration-200 ${
        navigateOnClick
          ? 'bg-gradient-to-br from-white to-blue-50/20 border-[#EDEFF9] hover:shadow-[0_4px_12px_rgba(14,126,228,0.08)] cursor-pointer'
          : 'bg-white border-slate-100'
      }`}
      onClick={navigateOnClick ? handleClick : undefined}
    >
      <div className="flex flex-col items-center gap-2">
        <div className="p-2 rounded-lg transition-all duration-300 bg-gray-100/50">
          <SystemIcon size={16} className="text-gray-500" style={{ color: system.color }} />
        </div>
        <div className="text-center">
          <div className="text-xs font-medium text-gray-900">{system.name}</div>
          {showConsumption && system.consumption && (
            <div className="text-[10px] text-gray-500 mt-0.5">
              {formatConsumption(system.consumption)} kWh
            </div>
          )}
          {showPercentage && (
            <div className="text-[10px] text-gray-400">
              {percentage}% of total
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(SystemBreakdownCard);