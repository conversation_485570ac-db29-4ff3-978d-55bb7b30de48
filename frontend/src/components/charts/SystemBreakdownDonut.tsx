import React, { useMemo } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip, TooltipProps } from 'recharts';
import { Snowflake, Wind, Server, Lightbulb, Plug, ArrowUpDown, Activity } from 'lucide-react';
import CHART_STYLES, { COLORS } from '../../constants/chartStyles';
import { SystemBreakdownItem } from '../../types';

// Custom color mapping per segment for clear distinction
const SLICE_COLORS: Record<string, string> = {
  'Chiller Plant': CHART_STYLES.bar.colors.primary,
  'Air Side': CHART_STYLES.bar.colors.secondary,
  'Light & Power': COLORS.gray[500],
  'Data Center & Others': COLORS.gray[700],
  'EV Charger': '#065F46',          // dark green for clear distinction
  'Escalator / Elevator': '#6D28D9' // darker purple for distinction
};

interface SystemBreakdownDonutProps {
  data: SystemBreakdownItem[];
  containerClassName?: string;
}

export const SystemBreakdownDonut: React.FC<SystemBreakdownDonutProps> = ({
  data,
  containerClassName = "h-full flex flex-col" // Default container style
}) => {
  // Calculate total value for percentage calculation
  const totalSystemValue = useMemo(() => data.reduce((sum, entry) => sum + entry.value, 0), [data]);

  // Pie chart label formatter for outside labels with lines
  const renderCustomizedLabel = (props: any) => {
    const { cx, cy, midAngle, outerRadius, index, value } = props;
    const RADIAN = Math.PI / 180;
    const sx = cx + (outerRadius + 3) * Math.cos(-midAngle * RADIAN);
    const sy = cy + (outerRadius + 3) * Math.sin(-midAngle * RADIAN);
    const mx = cx + (outerRadius + 12) * Math.cos(-midAngle * RADIAN);
    const my = cy + (outerRadius + 12) * Math.sin(-midAngle * RADIAN);
    const ex = mx + (Math.cos(-midAngle * RADIAN) >= 0 ? 1 : -1) * 16;
    const ey = my;
    const textAnchor = Math.cos(-midAngle * RADIAN) >= 0 ? 'start' : 'end';

    const entry = data[index];
    const percentage = entry.percentage !== undefined ? entry.percentage : (totalSystemValue > 0 ? (value / totalSystemValue) * 100 : 0);
    const formattedValue = `${Math.round(value).toLocaleString()} kWh (${Math.round(percentage)}%)`;

    return (
      <g>
        <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={COLORS.gray[200]} fill="none" strokeWidth={1} />
        <text
          x={ex + (Math.cos(-midAngle * RADIAN) >= 0 ? 4 : -4)}
          y={ey}
          textAnchor={textAnchor}
          fill="#4b5563"
          fontSize={10}
          dominantBaseline="central"
        >
          {formattedValue}
        </text>
      </g>
    );
  };

  // Map system breakdown items to Lucide icons
  const getIcon = (name: string) => {
    switch (name) {
      case 'Chiller Plant': return <Snowflake size={10} strokeWidth={1.5} />;
      case 'Air Side': return <Wind size={10} strokeWidth={1.5} />;
      case 'Data Center & Others': return <Server size={10} strokeWidth={1.5} />;
      case 'Light & Power': return <Lightbulb size={10} strokeWidth={1.5} />;
      case 'EV Charger': return <Plug size={10} strokeWidth={1.5} />;
      case 'Escalator / Elevator': return <ArrowUpDown size={10} strokeWidth={1.5} />;
      default: return <Activity size={10} strokeWidth={1.5} />;
    }
  };

  // Custom Tooltip
  const CustomTooltip = ({ active, payload }: TooltipProps<number, string>) => {
    if (active && payload && payload.length) {
      const entry = payload[0].payload as SystemBreakdownItem;
      const percentage = entry.percentage !== undefined ? entry.percentage : (totalSystemValue > 0 ? (entry.value / totalSystemValue) * 100 : 0);
      return (
        <div className="bg-white p-2 border border-gray-200 rounded-md shadow-lg">
          <p className="text-sm font-medium text-gray-700">{entry.name}</p>
          <p className="text-xs text-blue-600 font-semibold">
            {formatNumber(entry.value)} kWh ({percentage.toFixed(0)}%)
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className={containerClassName}>
      {/* Make ResponsiveContainer take full available height */}
      <ResponsiveContainer width="100%" height="100%">
        <PieChart margin={{ top: 15, right: 100, bottom: 15, left: 100 }}>
          <Pie
            data={data}
            dataKey="value"
            nameKey="name"
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            paddingAngle={1}
            fill="#8884d8"
            label={renderCustomizedLabel}
            labelLine={false}
            stroke="white"
            strokeWidth={1}
            animationDuration={500}
          >
            {data.map((_, index) => {
              const entry = data[index];
              const fillColor = SLICE_COLORS[entry.name];
              return (
                <Cell
                  key={`cell-${index}`}
                  fill={fillColor}
                  stroke="rgba(0,0,0,0.05)"
                  strokeWidth={1}
                />
              );
            })}
          </Pie>
          <Tooltip content={<CustomTooltip />} />

          {/* Center total sum */}
          <text
            x="50%"
            y="45%"
            textAnchor="middle"
            dominantBaseline="middle"
            className="fill-gray-900"
            fontSize="16"
            fontWeight="600"
          >
            {totalSystemValue.toLocaleString()}
          </text>
          <text
            x="50%"
            y="58%"
            textAnchor="middle"
            dominantBaseline="middle"
            className="fill-gray-500"
            fontSize="11"
            fontWeight="500"
          >
            kWh
          </text>
        </PieChart>
      </ResponsiveContainer>

      {/* Text Labels Below Chart */}
      <div className="flex flex-wrap justify-center gap-x-2 gap-y-1 mt-4 px-4 pb-2">
        {data.map((item, index) => {
          const fillColor = SLICE_COLORS[item.name];
          return (
            <div
              key={`label-${index}`}
              className="flex items-center"
            >
              <div className="flex items-center justify-center w-4 h-4 rounded-sm mr-0.5" style={{ backgroundColor: `${fillColor}20` }}>
                <span style={{ color: fillColor }}>
                  {getIcon(item.name)}
                </span>
              </div>
              <div>
                <span className="font-medium text-[9px]" style={{ color: fillColor }}>
                  {item.name}
                </span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Helper function needed by tooltip (can be moved to utils if needed elsewhere)
function formatNumber(value: number, options: { decimals?: number } = {}): string {
  return value.toLocaleString(undefined, {
    minimumFractionDigits: options.decimals ?? 0,
    maximumFractionDigits: options.decimals ?? 0,
  });
}