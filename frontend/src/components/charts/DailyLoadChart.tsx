import React, { useRef } from 'react';
import * as echarts from 'echarts';
import { useLayoutEffect } from 'react';
import { WORKDAY_LOAD_PROFILE } from '../../lib/config/load-profile';
import { CHART_STYLES } from '../../lib/config/ui';
import { BaseChartProps } from './types';
import { useChartConfig } from './useChartConfig';
import { formatNumber } from '../../lib/utils/formatters';

// Generate a default 24-hour domain with 15-minute intervals for X-axis
const generateFullDayXAxisData = () => {
  const data = [];
  for (let i = 0; i < 24; i++) {
    for (let j = 0; j < 60; j += 15) {
      data.push(`${String(i).padStart(2, '0')}:${String(j).padStart(2, '0')}`);
    }
  }
  data.push('24:00'); // Add end of day for visual completeness of axis if needed
  return data;
};

const DEFAULT_X_AXIS_DOMAIN = generateFullDayXAxisData();

export interface DailyLoadChartProps extends BaseChartProps {
  xAxisDomain?: string[]; // Optional: allows overriding the default full day X-axis
}

export function DailyLoadChart({ // Updated props
  data = WORKDAY_LOAD_PROFILE,
  comparisonData,
  xAxisDomain = DEFAULT_X_AXIS_DOMAIN, // Use default if not provided
}: DailyLoadChartProps) { // Updated props type
  const chartRef = React.useRef<HTMLDivElement>(null);
  const chartInstance = React.useRef<echarts.ECharts>();
  const isInitialized = React.useRef(false);

  const { option } = useChartConfig({
    data, // Pass original data here for base config
    comparisonData, // Pass original comparisonData here
    view: 'daily',
    showPeakHours: true,
    smooth: true,
    isComparison: false
  });

  // Refined ECharts options for polishing
  const enhancedOption = React.useMemo(() => {
    if (!option) return {};

    const xAxisConfig = Array.isArray(option.xAxis) ? option.xAxis[0] : option.xAxis;
    const yAxisConfig = Array.isArray(option.yAxis) ? option.yAxis[0] : option.yAxis;
    const baseSeries = Array.isArray(option.series) ? option.series : [option.series].filter(Boolean);

    const maxToday = Math.max(0, ...data.map(item => item.demand));
    const maxYesterday = Math.max(0, ...(comparisonData?.map(item => item.demand) ?? [0]));
    const overallMax = Math.max(maxToday, maxYesterday);
    const yAxisMax = overallMax > 0 ? Math.ceil((overallMax * 1.10) / 100) * 100 : 100;

    const finalSeries = [];

    const mapDataToDomain = (seriesData: { time: string; demand: number }[], domain: string[]) => {
      const demandMap = new Map(seriesData.map(item => [item.time, item.demand]));
      return domain.map(time => demandMap.get(time)); 
    };

    const todaySeriesData = mapDataToDomain(data, xAxisDomain);
    const yesterdaySeriesData = comparisonData ? mapDataToDomain(comparisonData, xAxisDomain) : [];

    if (baseSeries[0]) {
      finalSeries.push({
        ...baseSeries[0],
        name: 'Today (Real-time)',
        type: 'line', smooth: true, symbol: 'none',
        lineStyle: { width: 1.8, color: CHART_STYLES.colors.primary }, 
        areaStyle: { 
          origin: 'start',
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(2, 132, 199, 0.20)' }, 
            { offset: 1, color: 'rgba(2, 132, 199, 0.01)' }  
          ])
        },
        data: todaySeriesData,
        emphasis: { focus: 'series', lineStyle: { width: 2.2 } },
        z: 2, 
        zlevel: 1 
      });
    }
    if (baseSeries[1] && comparisonData && comparisonData.length > 0) {
       finalSeries.push({
        ...baseSeries[1],
        name: 'Yesterday',
        type: 'line', smooth: true, symbol: 'none',
        lineStyle: { width: 1.8, type: 'dashed', color: '#94A3B8', dashOffset: 5 },
        data: yesterdaySeriesData,
        emphasis: { focus: 'series', lineStyle: { width: 2.2 } },
        z: 1, 
        zlevel: 1 
      });
    }

    return {
      ...option,
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e2e8f0',
        borderWidth: 1,
        textStyle: { color: '#1e293b', fontSize: 12 },
        padding: [5, 10],
        formatter: (params: any) => { 
          const point = params[0]; 
          if (!point || typeof point.axisValueLabel === 'undefined') {
            return ''; 
          }
          let tooltip = `<div style="font-weight: 500; margin-bottom: 5px;">Time: ${point.axisValueLabel}</div>`;
          params.forEach((p: any) => {
            const valueDisplay = (typeof p.value !== 'undefined' && p.value !== null) 
                               ? formatNumber(p.value) 
                               : '-'; 
            tooltip += `
              <div style="display: flex; align-items: center; gap: 5px; margin-bottom: 3px;">
                <div style="width: 8px; height: 8px; border-radius: 50%; background-color: ${p.color};"></div>
                <span style="color: #64748b;">${p.seriesName}:</span>
                <span style="font-weight: 600;">${valueDisplay} kW</span>
              </div>`;
          });
          return tooltip;
        }
      },
      legend: { 
        show: true,
        top: 5, right: 15, 
        itemWidth: 12, itemHeight: 8, 
        itemGap: 18,
        icon: 'rect',
        textStyle: { fontSize: 10, color: '#475569' }, 
        data: [
          { name: 'Today (Real-time)', itemStyle: { color: CHART_STYLES.colors.primary } },
          ...(comparisonData && comparisonData.length > 0 ? [{
              name: 'Yesterday',
              itemStyle: {
                color: '#94A3B8',
                borderWidth: 1.5, 
                borderType: 'dashed'
              }
            }] : [])
        ]
      },
      grid: { 
        top: 24, 
        bottom: 25,
        left: 40, 
        right: 15,
        containLabel: false
      },
      xAxis: {
        ...xAxisConfig,
        type: 'category', boundaryGap: false,
        data: xAxisDomain, // Use the full domain for the X-axis categories
        axisLine: { show: false }, axisTick: { show: false },
        axisLabel: {
          formatter: (value: string) => {
            if (/^\d{1,2}:00$/.test(value)) {
              return value;
            }
            return '';
          }
        }
      },
      yAxis: {
        ...yAxisConfig,
        type: 'value', max: yAxisMax,
        name: 'kW',
        nameLocation: 'middle', nameGap: 28, 
        nameTextStyle: {
          color: '#64748B',
          fontSize: 9, 
          fontWeight: 400
        },
        axisLine: { show: false }, axisTick: { show: false },
        splitLine: { 
          show: true,
          z: -10, 
          zlevel: -1, 
          lineStyle: {
            color: '#F1F5F9', 
            type: 'dashed',
            width: 1
          }
        },
        axisLabel: {
          fontSize: 9, 
          color: '#64748B',
          margin: 5,
          align: 'right'
        }
      },
      series: finalSeries
    };
  }, [option, data, comparisonData, xAxisDomain]); // Added xAxisDomain to dependency array

  const initializeChart = React.useCallback(() => {
    if (!chartRef.current) return;
    if (isInitialized.current) return;

    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    chartInstance.current = echarts.init(chartRef.current);
    chartInstance.current.setOption(enhancedOption);

    isInitialized.current = true;
  }, [enhancedOption]);

  const handleResize = React.useCallback(() => {
    if (chartInstance.current) {
      chartInstance.current.resize();
    }
  }, []);

  useLayoutEffect(() => {
    initializeChart();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current && typeof chartInstance.current.dispose === 'function') {
        try {
            chartInstance.current.dispose();
        } catch (e) {
            console.error("Error disposing chart:", e);
        }
      }
      chartInstance.current = undefined;
      isInitialized.current = false;
    };
  }, [initializeChart, handleResize]);

  return (
    <div
      ref={chartRef}
      className="w-full h-full min-h-[160px]"
    />
  );
}