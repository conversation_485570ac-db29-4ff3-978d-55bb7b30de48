import React from 'react';
import { EnergySystemData } from './EnergyDonutChart';
import EnergyBreakdownDonutChart from './EnergyBreakdownDonutChart';

export interface EnergySystemBreakdownProps {
  data: EnergySystemData[];
  title?: string;
  subtitle?: string;
  isLoading?: boolean;
  showLabels?: boolean;
  showCards?: boolean;
  cardLayout?: 'compact' | 'detailed' | 'grid' | 'pill';
  cardColumns?: 2 | 3 | 4 | 6;
  height?: number;
  navigateOnClick?: boolean;
  showConsumption?: boolean;
  showPercentage?: boolean;
  innerRadius?: number;
  outerRadius?: number;
}

/**
 * @deprecated Use EnergyBreakdownDonutChart instead for consistent styling
 */
const EnergySystemBreakdown: React.FC<EnergySystemBreakdownProps> = ({
  data,
  title = 'Energy Consumption Breakdown',
  subtitle = 'Current Month',
  isLoading = false,
  showLabels = true,
  showCards = true,
  cardLayout = 'compact',
  cardColumns = 6,
  height = 220,
  navigateOnClick = true,
  showConsumption = false,
  showPercentage = true,
  innerRadius = 55,
  outerRadius = 75
}) => {
  // Use the new component to maintain consistency and reduce duplication
  return (
    <EnergyBreakdownDonutChart
      data={data}
      title={title}
      subtitle={subtitle}
      isLoading={isLoading}
      showLabels={showLabels}
      showCards={showCards}
      cardLayout={cardLayout}
      height={height}
      navigateOnClick={navigateOnClick}
      showConsumption={showConsumption}
      showPercentage={showPercentage}
      innerRadius={innerRadius}
      outerRadius={outerRadius}
    />
  );
};

export default React.memo(EnergySystemBreakdown); 