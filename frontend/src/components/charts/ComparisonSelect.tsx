import React from 'react';
import { Calendar, ChevronDown } from 'lucide-react';

export type ComparisonPeriod = 'none' | 'day' | 'week' | 'month' | 'year';

interface ComparisonSelectProps {
  selectedPeriod: ComparisonPeriod;
  onChange: (period: ComparisonPeriod) => void;
}

const COMPARISON_OPTIONS = [
  { value: 'none', label: 'No Comparison' },
  { value: 'day', label: 'Previous Day' },
  { value: 'week', label: 'Previous Week' },
  { value: 'month', label: 'Previous Month' },
  { value: 'year', label: 'Previous Year' }
] as const;

export function ComparisonSelect({ selectedPeriod, onChange }: ComparisonSelectProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-1.5 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
      >
        <Calendar size={16} className="text-gray-400" />
        <span className="text-sm text-gray-700">
          Compare: {COMPARISON_OPTIONS.find(opt => opt.value === selectedPeriod)?.label}
        </span>
        <ChevronDown size={16} className="text-gray-400" />
      </button>

      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute right-0 mt-1 w-48 bg-white rounded-lg border border-gray-200 shadow-lg z-50 max-h-64 overflow-auto">
            {COMPARISON_OPTIONS.map(option => (
              <button
                key={option.value}
                onClick={() => {
                  onChange(option.value as ComparisonPeriod);
                  setIsOpen(false);
                }}
                className={`w-full flex items-center px-4 py-2 text-sm ${
                  selectedPeriod === option.value
                    ? 'bg-blue-50 text-primary-blue'
                    : 'hover:bg-gray-50 text-gray-700'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
}