import React from 'react';

interface ChartTooltipProps {
  title: string;
  isPeak?: boolean;
  pairs: Array<{ label: string; value: string | number; color?: string }>;
  difference?: { value: string; isPositive: boolean };
}

export function ChartTooltip({ title, isPeak = false, pairs, difference }: ChartTooltipProps) {
  return (
    <div className="bg-white/95 backdrop-blur-sm p-3 rounded-lg border border-blue-100 shadow-lg text-xs max-w-[250px] animate-fadeIn">
      {/* Header */}
      <div className="flex items-center justify-between mb-2 border-b border-gray-100 pb-2">
        <div className="font-medium text-gray-900">
          {title}
          {isPeak && (
            <span className="ml-1 text-[10px] text-blue-500 animate-pulse">
              (Peak Hours Mon-Fri)
            </span>
          )}
        </div>
      </div>
      
      {/* Content */}
      <div className="space-y-1.5">
        {pairs.map((pair, index) => (
          <div 
            key={index} 
            className="flex justify-between items-center animate-slideIn"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <span className="text-gray-500">{pair.label}:</span>
            <span 
              className="font-medium transition-colors duration-200"
              style={{ color: pair.color || '#374151' }}
            >
              {pair.value}
            </span>
          </div>
        ))}
        
        {/* Difference row */}
        {difference && (
          <div className="flex justify-between items-center pt-1.5 mt-1.5 border-t border-gray-100 animate-slideIn" style={{ animationDelay: '200ms' }}>
            <span className="text-gray-500">Difference:</span>
            <span 
              className={`font-medium ${difference.isPositive ? 'text-red-500' : 'text-green-500'}`}
            >
              {difference.isPositive ? '+' : ''}{difference.value}%
            </span>
          </div>
        )}
      </div>
    </div>
  );
}