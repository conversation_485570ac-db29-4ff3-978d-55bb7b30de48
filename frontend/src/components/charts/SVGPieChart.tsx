import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Label, Sector } from 'recharts';
import { UNIFIED_CHART_STYLES, CHART_COLORS } from '../../lib/config/chart-styles';

export interface PieChartDataItem {
  name: string;
  value: number;
}

export interface SVGPieChartProps {
  data: PieChartDataItem[];
  width?: number;
  height?: number;
  onSliceClick?: (item: PieChartDataItem) => void;
}

// Common styling constants for consistency with canvas pie chart
const PIE_STYLE = {
  strokeWidth: 2,
  stroke: '#fff',
  paddingAngle: 2
};

const SVGPieChart: React.FC<SVGPieChartProps> = ({ data, width = 300, height = 300, onSliceClick }) => {
  const [activeIndex, setActiveIndex] = React.useState<number | undefined>(undefined);

  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  const onPieLeave = () => {
    setActiveIndex(undefined);
  };

  const onClick = (data: any) => {
    if (onSliceClick && data?.payload) {
      onSliceClick(data.payload);
    }
  };

  const renderActiveShape = (props: any) => {
    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill } = props;
  
    return (
      <g>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius + 4}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
          stroke={PIE_STYLE.stroke}
          strokeWidth={PIE_STYLE.strokeWidth + 1}
          style={{ filter: 'drop-shadow(0 3px 6px rgba(0,0,0,0.1))' }}
        />
      </g>
    );
  };

  return (
    <PieChart width={width} height={height}>
      <Pie
        data={data}
        cx="50%"
        cy="50%"
        innerRadius={0}
        outerRadius={Math.min(width, height) / 2 - 10}
        fill={UNIFIED_CHART_STYLES.colors.primary}
        dataKey="value"
        paddingAngle={PIE_STYLE.paddingAngle}
        stroke={PIE_STYLE.stroke}
        strokeWidth={PIE_STYLE.strokeWidth}
        activeIndex={activeIndex}
        activeShape={renderActiveShape}
        onMouseEnter={onPieEnter}
        onMouseLeave={onPieLeave}
        onClick={onClick}
      >
        {data.map((_, index) => (
          <Cell 
            key={`cell-${index}`} 
            fill={CHART_COLORS[index % CHART_COLORS.length]} 
          />
        ))}
        <Label
          position="center"
          offset={-15}
          style={{ 
            fill: UNIFIED_CHART_STYLES.colors.text.primary, 
            fontSize: UNIFIED_CHART_STYLES.fonts.size.sm,
            fontWeight: UNIFIED_CHART_STYLES.fonts.weight.medium
          }}
        />
      </Pie>
    </PieChart>
  );
};

export default React.memo(SVGPieChart);
