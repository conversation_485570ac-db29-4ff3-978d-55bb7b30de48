import React, { useRef, useEffect, useState } from 'react';
import * as echarts from 'echarts';
import { COLORS } from '@/lib/constants';
import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

interface PowerDemandChartProps {
  height?: number;
  data?: Array<{ timestamp: string; value: number }>;
  onDateChange?: (date: Date) => void;
}

/**
 * A mock 24-hour power demand line chart with date selection
 */
export function PowerDemandChart({ height = 280, data, onDateChange }: PowerDemandChartProps) {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts>();
  const datePickerRef = useRef<HTMLDivElement>(null);

  // Close date picker on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target as Node)) {
        setShowDatePicker(false);
      }
    };

    if (showDatePicker) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showDatePicker]);

  useEffect(() => {
    let chartData: number[] = [];
    let timeLabels: string[] = [];

    if (data && data.length > 0) {
      // Use real data
      timeLabels = data.map(point => {
        const date = new Date(point.timestamp);
        return `${date.getHours()}:00`;
      });
      chartData = data.map(point => {
        const value = typeof point.value === 'number' ? point.value : parseFloat(point.value);
        return Number.isFinite(value) ? Math.round(value) : 0;
      });
    } else {
      // Generate mock data for a 24-hour period as fallback
      const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`);
      timeLabels = hours;

      // Create a realistic power demand curve with morning and evening peaks
      // Use date as seed for consistent data per day
      const dateSeed = selectedDate.getFullYear() * 10000 +
                       (selectedDate.getMonth() + 1) * 100 +
                       selectedDate.getDate();

      const seededRandom = (seed: number) => {
        const x = Math.sin(seed) * 10000;
        return x - Math.floor(x);
      };

      chartData = hours.map((_, i) => {
        // Base load with date-based variation
        let value = 20 + seededRandom(dateSeed + i * 100) * 5;

        // Morning peak (7-10 AM)
        if (i >= 7 && i <= 10) {
          value += 25 + (i - 7) * 5;
        }

        // Midday plateau (11 AM - 2 PM)
        else if (i >= 11 && i <= 14) {
          value += 40;
        }

        // Afternoon/evening peak (5-8 PM)
        else if (i >= 17 && i <= 20) {
          value += 30 + (i - 17) * 8;
        }

        // Night reduction (9 PM - 6 AM)
        else if (i >= 21 || i <= 6) {
          value += 15;
        }

        // Add date-seeded randomness
        value += seededRandom(dateSeed + i * 200) * 10 - 5;

        // Weekend adjustment (lower consumption)
        const dayOfWeek = selectedDate.getDay();
        if (dayOfWeek === 0 || dayOfWeek === 6) {
          value *= 0.7;
        }

        return Math.max(15, Math.round(value));
      });
    }

    // Initialize chart
    if (chartRef.current) {
      // Dispose existing instance if any
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }

      // Create new chart instance
      chartInstance.current = echarts.init(chartRef.current);

      // Set chart options
      chartInstance.current.setOption({
        tooltip: {
          trigger: 'axis',
          formatter: function(params: any) {
            const time = params[0].name;
            const value = params[0].value;
            return `${time}<br/>Power Demand: <strong>${value} kW</strong>`;
          },
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: '#E2E8F0',
          textStyle: {
            color: '#1E293B'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: timeLabels,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            fontSize: 10,
            color: '#94A3B8',
            interval: 3,
            formatter: (value: string) => value.replace(':00', '')
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          name: 'kW',
          nameTextStyle: {
            color: '#64748B',
            fontSize: 11,
            padding: [0, 0, 10, 0],
            fontWeight: 500
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E2E8F0'
            }
          },
          axisTick: {
            show: true,
            lineStyle: {
              color: '#E2E8F0'
            }
          },
          axisLabel: {
            fontSize: 10,
            color: '#64748B',
            formatter: '{value} kW'
          },
          splitLine: {
            z: -10, // Set negative z-index to push gridlines behind data series
            zlevel: -1, // Use zlevel to ensure gridlines are rendered on a lower canvas layer
            lineStyle: {
              color: '#E2E8F0',
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: 'Power Demand',
            type: 'line',
            smooth: true,
            symbol: 'none',
            sampling: 'average',
            z: 2, // Higher z-index to ensure it's on top
            zlevel: 1, // Higher zlevel to ensure it's rendered on a higher canvas layer
            itemStyle: {
              color: COLORS.primary.blue
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(6, 91, 169, 0.2)' },
                { offset: 1, color: 'rgba(6, 91, 169, 0.02)' }
              ])
            },
            data: chartData
          }
        ]
      });

      // Handle resize
      const handleResize = () => {
        chartInstance.current?.resize();
      };

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        chartInstance.current?.dispose();
      };
    }
  }, [data, selectedDate]);

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
    setShowDatePicker(false);
    if (onDateChange) {
      onDateChange(date);
    }
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
    handleDateChange(newDate);
  };

  const formatDisplayDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
      });
    }
  };

  return (
    <div style={{ width: '100%', height: `${height}px`, position: 'relative' }}>
      {/* Date Navigation Controls */}
      <div className="absolute top-2 right-2 z-50 flex items-center gap-1 bg-white rounded-lg shadow-sm border border-gray-200 px-2 py-1">
        <button
          onClick={() => navigateDate('prev')}
          className="p-1 hover:bg-gray-100 rounded transition-colors"
          title="Previous day"
        >
          <ChevronLeft size={14} className="text-gray-600" />
        </button>

        <button
          onClick={() => setShowDatePicker(!showDatePicker)}
          className="flex items-center gap-1.5 px-2 py-1 hover:bg-gray-100 rounded transition-colors text-xs font-medium text-gray-700"
        >
          <Calendar size={12} />
          <span>{formatDisplayDate(selectedDate)}</span>
        </button>

        <button
          onClick={() => navigateDate('next')}
          className="p-1 hover:bg-gray-100 rounded transition-colors"
          title="Next day"
          disabled={selectedDate.toDateString() === new Date().toDateString()}
        >
          <ChevronRight size={14} className={selectedDate.toDateString() === new Date().toDateString() ? 'text-gray-300' : 'text-gray-600'} />
        </button>
      </div>

      {/* Date Picker Dropdown */}
      {showDatePicker && (
        <div
          ref={datePickerRef}
          className="absolute top-10 right-2 z-[9999]"
          style={{
            zIndex: 9999,
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
          }}
        >
          <div className="bg-white rounded-lg shadow-xl border border-gray-200 p-2 animate-in fade-in-0 zoom-in-95 slide-in-from-top-2 duration-200">
            <DatePicker
              selected={selectedDate}
              onChange={handleDateChange}
              maxDate={new Date()}
              inline
              calendarClassName="power-demand-calendar"
            />
          </div>
        </div>
      )}

      {/* Chart Container */}
      <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
    </div>
  );
}
