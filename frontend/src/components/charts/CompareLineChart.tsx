import React, { useRef, useEffect } from 'react';
import * as echarts from 'echarts';
import type { ChartView } from './types';
import { SYSTEM_ICONS } from '../../lib/constants';

// Derive the meter type from SYSTEM_ICONS keys, consistent with Compare.tsx
type MeterIconKey = keyof typeof SYSTEM_ICONS;

interface CompareLineChartProps {
  data: Array<{ time: string; [key: string]: string | number }>;
  selectedMeters: Array<{
    id: string;
    name: string;
    type: MeterIconKey;
    category: string;
  }>;
  view: ChartView;
}

const CHART_COLORS = [
  ['#065BA9', '#3B82F6'],  // Blues
  ['#059669', '#10B981'],  // Greens
  ['#D97706', '#F59E0B'],  // Ambers
  ['#7C3AED', '#8B5CF6'],  // Purples
  ['#DC2626', '#EF4444'],  // Reds
  ['#0891B2', '#06B6D4'],  // Cyans
  ['#4F46E5', '#6366F1'],  // Indigos
  ['#BE185D', '#EC4899'],  // Pinks
  ['#B45309', '#F97316'],  // Oranges
  ['#064E3B', '#047857']   // Emeralds
];

export function CompareLineChart({ data, selectedMeters, view }: CompareLineChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts>();

  useEffect(() => {
    if (!chartRef.current || !data || !selectedMeters.length) return;

    // Initialize chart
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    // Create series for each selected meter
    const series = selectedMeters.map((meter, index) => {
      const key = `${meter.category.replace(/\s+/g, '_')}_${meter.id}`;
      const [baseColor, lightColor] = CHART_COLORS[index % CHART_COLORS.length];

      // Create data points with null handling and type validation
      const seriesData = data.map(point => {
        const rawValue = point[key];
        
        // Handle different types of values
        let value: number | null = null;
        if (rawValue !== undefined && rawValue !== null) {
          if (typeof rawValue === 'number') {
            value = Number.isFinite(rawValue) ? rawValue : null;
          } else if (typeof rawValue === 'string') {
            const parsed = parseFloat(rawValue);
            value = Number.isFinite(parsed) ? parsed : null;
          } else if (typeof rawValue === 'boolean') {
            value = rawValue ? 1 : 0;
          }
        }
        
        return [point.time, value];
      });

      return {
        name: meter.name,
        type: 'line',
        smooth: false,
        showSymbol: false,
        sampling: 'lttb',
        data: seriesData,
        connectNulls: false, // Don't connect across null values - show gaps
        z: 2,
        zlevel: 1,
        emphasis: {
          focus: 'series',
          scale: false,
          disabled: true
        },
        lineStyle: {
          width: 3,
          color: baseColor,
          opacity: 0.8
        },
        areaStyle: {
          opacity: 0.1,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: baseColor + '20' },
            { offset: 0.5, color: baseColor + '10' },
            { offset: 1, color: baseColor + '05' }
          ])
        }
      };
    });

    // Configure chart options
    const option = {
      animation: false,
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#E2E8F0',
        borderWidth: 1,
        padding: [12, 16],
        textStyle: {
          color: '#1E293B',
          fontSize: 12
        },
        extraCssText: 'backdrop-filter: blur(4px);',
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: 'rgba(148, 163, 184, 0.2)',
            width: 2,
            type: 'dashed'
          }
        },
        formatter: (params: any[]) => {
          const time = params[0].axisValue;
          let content = `<div style="font-weight: 500; margin-bottom: 8px;">${time}</div>`;

          content += '<div style="display: grid; gap: 6px;">';
          params.forEach(param => {
            // Extract the value and handle potential null/undefined values
            const value = param.value && param.value.length > 1 ? param.value[1] : null;
            const displayValue = value !== null && value !== undefined 
              ? value.toLocaleString()
              : '-';
              
            content += `
              <div style="display: flex; align-items: center; justify-content: space-between; gap: 12px;">
                <div style="display: flex; align-items: center; gap: 6px;">
                  <span style="display: inline-block; width: 8px; height: 8px; border-radius: 50%; background: ${param.color};"></span>
                  <span style="color: #475569; font-size: 11px;">${param.seriesName}</span>
                </div>
                <span style="font-weight: 500; color: #1E293B; font-size: 11px;">
                  ${displayValue} ${value !== null && value !== undefined ? (view === 'daily' ? 'kW' : 'kWh') : ''}
                </span>
              </div>
            `;
          });
          content += '</div>';

          return content;
        }
      },
      legend: {
        type: 'scroll',
        orient: 'horizontal',
        top: 0,
        right: 10,
        itemWidth: 12,
        itemHeight: 12,
        itemGap: 20,
        selectedMode: true,
        emphasis: {
          selectorLabel: {
            show: true,
            fontSize: 12,
            color: '#1E293B'
          }
        },
        textStyle: {
          color: '#475569',
          fontSize: 11
        },
        icon: 'circle',
        selector: [
          { type: 'all', title: 'All' }
        ],
        pageIconColor: '#94A3B8',
        pageIconInactiveColor: '#E2E8F0',
        pageIconSize: 12,
        pageTextStyle: {
          color: '#475569'
        }
      },
      grid: {
        top: 50,
        right: 35,
        bottom: 25,
        left: 35,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLine: {
          lineStyle: { color: '#E2E8F0' }
        },
        axisTick: { show: false },
        axisLabel: {
          color: '#94A3B8',
          fontSize: 10,
          interval: view === 'daily' ? 3 : 'auto'
        },
        splitLine: {
          show: true,
          z: -10, // Set negative z-index to push gridlines behind data series
          zlevel: -1, // Use zlevel to ensure gridlines are rendered on a lower canvas layer
          lineStyle: {
            color: '#F1F5F9',
            type: 'dashed'
          }
        }
      },
      yAxis: {
        type: 'value',
        name: 'kW',
        nameTextStyle: {
          color: '#64748B',
          fontSize: 10,
          padding: [0, 0, 0, -24]
        },
        axisLine: {
          show: true,
          lineStyle: { color: '#E2E8F0' }
        },
        axisTick: { show: false },
        axisLabel: {
          color: '#94A3B8',
          fontSize: 10,
          formatter: (value: number) => {
            return value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value;
          }
        },
        splitLine: {
          z: -10, // Set negative z-index to push gridlines behind data series
          zlevel: -1, // Use zlevel to ensure gridlines are rendered on a lower canvas layer
          lineStyle: {
            color: '#F1F5F9',
            type: 'dashed'
          }
        }
      },
      series
    };

    chartInstance.current.setOption(option);

    // Handle resize
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current && typeof chartInstance.current.dispose === 'function') {
        try {
          chartInstance.current.dispose();
        } catch (e) {
          console.error("Error disposing chart:", e);
        }
      }
      chartInstance.current = undefined;
    };
  }, [data, selectedMeters, view]);

  return (
    <div ref={chartRef} className="w-full h-full" />
  );
}