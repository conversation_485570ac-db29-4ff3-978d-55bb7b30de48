import React from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  ComposedChart,
  ReferenceLine,
  ReferenceArea
} from 'recharts';
import { formatNumber } from '../../utils/formatting';

interface DailyLoadChartProps {
  data?: any[];
  comparisonData?: any[];
}

export function DailyLoadChart({ data = [], comparisonData = [] }: DailyLoadChartProps) {
  const now = new Date();
  const currentHour = now.getHours();
  
  // Generate simple test data for 24 hours
  const chartData = Array.from({ length: 24 }, (_, hour) => {
    // Create a realistic load curve
    let todayValue = 200;
    if (hour >= 7 && hour < 9) todayValue = 300 + Math.random() * 100;
    else if (hour >= 9 && hour < 17) todayValue = 400 + Math.random() * 150;
    else if (hour >= 17 && hour < 20) todayValue = 350 + Math.random() * 100;
    else if (hour >= 20 && hour < 23) todayValue = 250 + Math.random() * 50;
    else todayValue = 150 + Math.random() * 50;
    
    const lastWeekValue = todayValue * (0.85 + Math.random() * 0.3);
    
    return {
      time: `${hour}:00`,
      today: hour <= currentHour ? Math.round(todayValue) : null,
      lastWeek: Math.round(lastWeekValue)
    };
  });
  
  // Calculate meaningful metrics
  const currentData = chartData[currentHour];
  const currentPower = currentData?.today || 0;
  const lastWeekPower = currentData?.lastWeek || 0;
  const todayPeak = Math.max(...chartData.filter(d => d.today !== null).map(d => d.today || 0));
  const lastWeekPeak = Math.max(...chartData.map(d => d.lastWeek || 0));
  const peakHour = chartData.find(d => d.today === todayPeak)?.time || '';
  
  // Calculate totals up to current hour
  const todayTotal = chartData
    .filter((_, i) => i <= currentHour)
    .reduce((sum, d) => sum + (d.today || 0), 0);
  const lastWeekTotalSameTime = chartData
    .filter((_, i) => i <= currentHour)
    .reduce((sum, d) => sum + (d.lastWeek || 0), 0);
  const lastWeekFullDay = chartData
    .reduce((sum, d) => sum + (d.lastWeek || 0), 0);
  
  const powerDiff = ((currentPower - lastWeekPower) / lastWeekPower * 100).toFixed(0);
  const totalDiff = ((todayTotal - lastWeekTotalSameTime) / lastWeekTotalSameTime * 100).toFixed(0);
  
  // Determine trend
  const trend = Number(powerDiff) > 10 ? 'high' : Number(powerDiff) < -10 ? 'low' : 'normal';
  
  return (
    <div className="w-full h-full relative">
      {/* Unified metrics bar at top */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-r from-white via-white to-gray-50 shadow-sm">
        <div className="flex items-center justify-between px-4 py-2">
          {/* Left: Real-time metrics group */}
          <div className="flex items-center gap-6 text-sm">
            {/* Live Power */}
            <div className="flex items-baseline gap-1">
              <span className="text-xs text-gray-500">Live Power:</span>
              <span className="font-bold text-gray-900">{currentPower}</span>
              <span className="text-xs text-gray-500">kW</span>
            </div>
            
            {/* Peak Demand */}
            <div className="flex items-baseline gap-1">
              <span className="text-xs text-gray-500">Peak:</span>
              <span className="font-bold text-gray-900">{todayPeak}</span>
              <span className="text-xs text-gray-500">kW</span>
              <span className="text-xs text-gray-400">at {peakHour}</span>
            </div>
          </div>
          
          {/* Right: Energy consumption */}
          <div className="flex items-baseline gap-4 text-sm">
            <div className="flex items-baseline gap-1">
              <span className="text-xs text-gray-500">Today:</span>
              <span className="font-bold text-gray-900">{Math.round(todayTotal).toLocaleString()}</span>
              <span className="text-xs text-gray-500">kWh</span>
            </div>
            <div className="flex items-baseline gap-1 text-gray-600">
              <span className="text-xs text-gray-400">Same day last week:</span>
              <span className="font-medium">{Math.round(lastWeekFullDay).toLocaleString()}</span>
              <span className="text-xs text-gray-400">kWh</span>
            </div>
          </div>
        </div>
      </div>
      
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart 
          data={chartData}
          margin={{ top: 35, right: 10, left: 10, bottom: 5 }}
        >
          <defs>
            <linearGradient id="colorToday" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
              <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.05}/>
            </linearGradient>
          </defs>
          
          <CartesianGrid strokeDasharray="3 3" stroke="#f8f9fa" vertical={false} />
          <XAxis 
            dataKey="time" 
            tick={{ fontSize: 10 }}
            interval={3}
            height={30}
          />
          <YAxis 
            tick={{ fontSize: 10 }}
            width={50}
            label={{ value: 'kW', angle: -90, position: 'insideLeft', style: { fontSize: 10 } }}
          />
          <Tooltip 
            formatter={(value: any) => `${formatNumber(value)} kW`}
            contentStyle={{ 
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              border: '1px solid #e5e7eb',
              borderRadius: '6px',
              fontSize: '12px'
            }}
          />
          <Legend 
            verticalAlign="top"
            align="right"
            height={15}
            wrapperStyle={{ 
              paddingTop: '35px',
              paddingRight: '10px',
              fontSize: '11px' 
            }}
            iconType="line"
          />
          
          {/* Last week line */}
          <Line
            type="monotone"
            dataKey="lastWeek"
            stroke="#94A3B8"
            strokeWidth={2}
            strokeDasharray="5 5"
            dot={false}
            name="Last Week"
          />
          
          {/* Today area */}
          <Area
            type="monotone"
            dataKey="today"
            stroke="#1e40af"
            strokeWidth={2}
            fill="url(#colorToday)"
            name="Today"
            connectNulls={false}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
}