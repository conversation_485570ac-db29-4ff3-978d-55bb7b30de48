import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from 'recharts';
import { useNavigate } from 'react-router-dom';
import { HardDrive } from 'lucide-react';
import { formatNumber } from '../../lib/utils/formatters';
import SystemBreakdownCard from './SystemBreakdownCard';
import { SYSTEM_TYPES } from '../../lib/config/energy-systems';

// Type for system breakdown data
export interface EnergySystemData {
  id: string;
  name: string;
  value: number;
  consumption?: number;
  percentage?: number;
  color: string;
}

export interface EnergyBreakdownDonutChartProps {
  data: EnergySystemData[];
  title?: string;
  subtitle?: string;
  isLoading?: boolean;
  showLabels?: boolean;
  showCards?: boolean;
  cardLayout?: 'compact' | 'detailed' | 'grid' | 'pill';
  height?: number;
  navigateOnClick?: boolean;
  showConsumption?: boolean;
  showPercentage?: boolean;
  innerRadius?: number;
  outerRadius?: number;
  paddingAngle?: number;
  labelFormatter?: (value: number, percentage: number) => string;
  showWrapper?: boolean;
}

const EnergyBreakdownDonutChart: React.FC<EnergyBreakdownDonutChartProps> = ({
  data,
  title = 'Energy Consumption Breakdown',
  subtitle = 'Current Month',
  isLoading = false,
  showLabels = true,
  showCards = true,
  cardLayout = 'pill',
  height = 220,
  navigateOnClick = true,
  showConsumption = false,
  showPercentage = true,
  innerRadius = 55,
  outerRadius = 75,
  paddingAngle = 3,
  labelFormatter,
  showWrapper = true
}) => {
  const navigate = useNavigate();

  // If loading, show placeholder
  if (isLoading) {
    return (
      <div className="p-4 bg-white rounded-xl border border-slate-100 shadow-sm hover:shadow-md transition-all duration-300">
        {(title || subtitle) && (
          <div className="flex items-center justify-between mb-4">
            {title && (
              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-lg bg-gradient-to-br from-blue-50 to-white border border-blue-100/50">
                  <HardDrive size={16} className="text-blue-600" />
                </div>
                <h3 className="text-sm font-medium text-gray-800">{title}</h3>
              </div>
            )}
            {subtitle && (
              <div className="text-[10px] font-medium text-gray-400 uppercase tracking-wide">
                {subtitle}
              </div>
            )}
          </div>
        )}
        <div className="flex items-center justify-center" style={{ height }}>
          <div className="animate-pulse">
            <div className="h-24 w-24 bg-gray-200 rounded-full"></div>
          </div>
        </div>
      </div>
    );
  }

  // If no data, show message
  if (!data || data.length === 0) {
    return (
      <div className="p-4 bg-white rounded-xl border border-slate-100 shadow-sm hover:shadow-md transition-all duration-300">
        {(title || subtitle) && (
          <div className="flex items-center justify-between mb-4">
            {title && (
              <div className="flex items-center gap-2">
                <div className="p-1.5 rounded-lg bg-gradient-to-br from-blue-50 to-white border border-blue-100/50">
                  <HardDrive size={16} className="text-blue-600" />
                </div>
                <h3 className="text-sm font-medium text-gray-800">{title}</h3>
              </div>
            )}
            {subtitle && (
              <div className="text-[10px] font-medium text-gray-400 uppercase tracking-wide">
                {subtitle}
              </div>
            )}
          </div>
        )}
        <div className="flex items-center justify-center" style={{ height }}>
          <span className="text-gray-500">No data available</span>
        </div>
      </div>
    );
  }

  // Prepare data for chart, ensuring all required properties are present
  const chartData = data.map(item => ({
    ...item,
    // Ensure percentage exists or calculate it from value
    percentage: item.percentage !== undefined ? item.percentage : item.value,
    // Use the color provided in the data directly
    color: item.color
  }));

  // Handle click on chart slice
  const handlePieClick = (entry: EnergySystemData) => {
    if (navigateOnClick) {
      navigate(`/analytics?system=${entry.id}`);
    }
  };

  // Default label formatter consistently showing both kWh and percentage
  const defaultLabelFormatter = (value: number, percentage: number) => {
    // Format the consumption value with thousands separator
    const formattedValue = formatNumber(value);
    // Always display both consumption and percentage
    return `${formattedValue} kWh (${percentage}%)`;
  };

  // Use provided formatter or default
  const formatLabel = labelFormatter || defaultLabelFormatter;

  const ChartContent = (
    <>
      {/* Header */}
      {(title || subtitle) && (
        <div className="flex items-center justify-between mb-4">
          {title && (
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-lg bg-gradient-to-br from-blue-50 to-white border border-blue-100/50">
                <HardDrive size={16} className="text-blue-600" />
              </div>
              <h3 className="text-sm font-medium text-gray-800">{title}</h3>
            </div>
          )}
          {subtitle && (
            <div className="text-[10px] font-medium text-gray-400 uppercase tracking-wide">
              {subtitle}
            </div>
          )}
        </div>
      )}
      
      {/* Donut Chart and Systems breakdown cards container */}
      <div className="relative h-full">
        {/* Donut Chart */}
        <div className="relative overflow-hidden group">
          <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                innerRadius={innerRadius}
                outerRadius={outerRadius}
                paddingAngle={paddingAngle}
                dataKey="value"
                onClick={handlePieClick}
                labelLine={showLabels ? { 
                  stroke: '#64748B', 
                  strokeWidth: 0.5, 
                  strokeDasharray: '2 2' 
                } : false}
                label={showLabels ? ({
                  cx, cy, midAngle, innerRadius, outerRadius, index, value
                }) => {
                  const RADIAN = Math.PI / 180;
                  const radius = outerRadius * 1.6;
                  const x = cx + radius * Math.cos(-midAngle * RADIAN);
                  const y = cy + radius * Math.sin(-midAngle * RADIAN);
                  
                  // Get percentage from the data
                  const entry = chartData[index];
                  const percentage = entry.percentage || 0;
                  // Use consumption if available, otherwise use value
                  const displayValue = entry.consumption || value;
                  
                  // Format the label text
                  const labelText = formatLabel(displayValue, percentage);
                  
                  return (
                    <text 
                      x={x} 
                      y={y} 
                      textAnchor={x > cx ? 'start' : 'end'}
                      dominantBaseline="central"
                      fill="#64748B"
                      fontSize={9}
                      fontWeight="500"
                    >
                      {labelText}
                    </text>
                  );
                } : undefined}
              >
                {chartData.map((entry, index) => {
                  return (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={entry.color}
                      stroke="#fff"
                      strokeWidth={1.5}
                      style={{ cursor: navigateOnClick ? 'pointer' : 'default' }}
                    />
                  );
                })}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        {/* Systems breakdown cards */}
        {showCards && (
          <div className="mt-4 flex flex-wrap gap-2 justify-center">
            {chartData.map(system => (
              <SystemBreakdownCard 
                key={system.id} 
                system={system}
                layout={cardLayout}
                navigateOnClick={navigateOnClick}
                showConsumption={showConsumption}
                showPercentage={showPercentage}
              />
            ))}
          </div>
        )}
      </div>
    </>
  );

  // Conditionally render the wrapper
  return showWrapper ? (
    <div className="p-4 bg-white rounded-xl border border-blue-100 shadow-sm hover:shadow-md transition-all duration-300">
      {ChartContent}
    </div>
  ) : (
    ChartContent
  );
};

export default React.memo(EnergyBreakdownDonutChart); 