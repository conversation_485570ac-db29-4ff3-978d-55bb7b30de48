import React from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from 'recharts';
import { useNavigate } from 'react-router-dom';

// Type for system breakdown data
export interface EnergySystemData {
  id: string;
  name: string;
  value: number;
  consumption?: number;
  percentage?: number;
  color: string;
}

// Props for the donut chart
export interface EnergyDonutChartProps {
  data: EnergySystemData[];
  height?: number;
  navigateOnClick?: boolean;
  innerRadius?: number;
  outerRadius?: number;
  paddingAngle?: number;
  showLabels?: boolean;
  customColors?: Record<string, string>;
  labelFormatter?: (value: number, percentage: number) => string;
  isLoading?: boolean;
}

// Local utility function to format numbers
const formatNumber = (value: number, decimalPlaces: number = 0): string => {
  return value.toLocaleString('en-US', {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces
  });
};

const EnergyDonutChart: React.FC<EnergyDonutChartProps> = ({
  data,
  height = 220,
  navigateOnClick = false,
  innerRadius = 55,
  outerRadius = 75,
  paddingAngle = 3,
  showLabels = true,
  customColors,
  labelFormatter,
  isLoading = false
}) => {
  const navigate = useNavigate();

  // If loading, show placeholder
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full" style={{ height }}>
        <div className="animate-pulse">
          <div className="h-24 w-24 bg-gray-200 rounded-full"></div>
        </div>
      </div>
    );
  }

  // If no data, show message
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full" style={{ height }}>
        <span className="text-gray-500">No data available</span>
      </div>
    );
  }

  // Prepare data for chart, ensuring all required properties are present
  const chartData = data.map(item => ({
    ...item,
    // Ensure percentage exists or calculate it from value
    percentage: item.percentage !== undefined ? item.percentage : item.value
  }));

  // Handle click on chart slice
  const handlePieClick = (entry: EnergySystemData) => {
    if (navigateOnClick) {
      navigate(`/analytics?system=${entry.id}`);
    }
  };

  // Default label formatter if not provided
  const defaultLabelFormatter = (value: number, percentage: number) => 
    `${formatNumber(value)} kWh (${percentage}%)`;

  // Use provided formatter or default
  const formatLabel = labelFormatter || defaultLabelFormatter;

  return (
    <div style={{ height, width: '100%' }}>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            innerRadius={innerRadius}
            outerRadius={outerRadius}
            paddingAngle={paddingAngle}
            dataKey="value"
            onClick={handlePieClick}
            labelLine={showLabels ? { 
              stroke: '#64748B', 
              strokeWidth: 0.5, 
              strokeDasharray: '2 2' 
            } : false}
            label={showLabels ? ({
              cx, cy, midAngle, innerRadius, outerRadius, index, value
            }) => {
              const RADIAN = Math.PI / 180;
              const radius = outerRadius * 1.4;
              const x = cx + radius * Math.cos(-midAngle * RADIAN);
              const y = cy + radius * Math.sin(-midAngle * RADIAN);
              
              // Get percentage from the data
              const entry = chartData[index];
              const percentage = entry.percentage || 0;
              const displayValue = entry.consumption || value;
              
              return (
                <text 
                  x={x} 
                  y={y} 
                  textAnchor={x > cx ? 'start' : 'end'}
                  dominantBaseline="central"
                  fill="#64748B"
                  fontSize={9.5}
                  fontWeight="500"
                >
                  {formatLabel(displayValue, percentage)}
                </text>
              );
            } : undefined}
          >
            {chartData.map((entry, index) => {
              return (
                <Cell 
                  key={`cell-${index}`} 
                  fill={entry.color}
                  stroke="#fff"
                  strokeWidth={1}
                  style={{ cursor: navigateOnClick ? 'pointer' : 'default' }}
                />
              );
            })}
          </Pie>
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export default React.memo(EnergyDonutChart); 