import { useState, useEffect, useCallback } from 'react';
import { 
  Bell, 
  Settings, 
  X, 
  AlertTriangle,
  Info,
  ArrowUpRight
} from 'lucide-react';
import { Link } from 'react-router-dom';

// Types
type NotificationSeverity = 'high' | 'medium' | 'low';
type NotificationType = 'alert' | 'info';

interface Notification {
  id: string;
  title: string;
  description: string;
  read: boolean;
  type: NotificationType;
  severity: NotificationSeverity;
  timestamp: string;
  source: string;
}

// Mock data for notifications
const mockNotifications: Notification[] = [
  {
    id: '1',
    title: 'Abnormal Power Consumption',
    description: 'Meter T1-GF-01 is showing 35% higher consumption than normal',
    read: false,
    type: 'alert',
    severity: 'high',
    timestamp: new Date(Date.now() - 1000 * 60 * 32).toISOString(), // 32 minutes ago
    source: 'Power Monitoring'
  },
  {
    id: '2',
    title: 'Meter Offline',
    description: 'Meter T2-3F-03 has been offline for the last 2 hours',
    read: false,
    type: 'alert',
    severity: 'medium',
    timestamp: new Date(Date.now() - 1000 * 60 * 124).toISOString(), // 2 hours ago
    source: 'System Status'
  },
  {
    id: '3',
    title: 'Monthly Report Available',
    description: 'The energy consumption report for March 2025 is now available',
    read: true,
    type: 'info',
    severity: 'low',
    timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12).toISOString(), // 12 hours ago
    source: 'Reporting System'
  }
];

// Notification Dropdown Component
const NotificationDropdown = ({ 
  isOpen, 
  onClose,
  notifications,
  markAsRead,
  onViewAllClick
}: {
  isOpen: boolean;
  onClose: () => void;
  notifications: Notification[];
  markAsRead: (id: string) => void;
  onViewAllClick: () => void;
}) => {
  if (!isOpen) return null;
  
  const unreadCount = notifications.filter(n => !n.read).length;
  
  return (
    <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-50 overflow-hidden border border-gray-200">
      <div className="flex justify-between items-center p-3 border-b">
        <h3 className="font-medium">Notifications ({unreadCount} unread)</h3>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <X size={18} />
        </button>
      </div>
      
      <div className="max-h-96 overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="p-4 text-center text-gray-500">No notifications</div>
        ) : (
          <div>
            {notifications.slice(0, 5).map(notification => (
              <div 
                key={notification.id} 
                className={`p-3 border-b hover:bg-gray-50 ${!notification.read ? 'bg-blue-50' : ''}`}
                onClick={() => markAsRead(notification.id)}
              >
                <div className="flex gap-3">
                  {notification.type === 'alert' ? (
                    <AlertTriangle 
                      size={18} 
                      className={notification.severity === 'high' ? 'text-red-500' : 
                                 notification.severity === 'medium' ? 'text-orange-500' : 'text-yellow-500'} 
                    />
                  ) : (
                    <Info size={18} className="text-blue-500" />
                  )}
                  <div>
                    <p className={`text-sm font-medium ${!notification.read ? 'text-blue-600' : ''}`}>{notification.title}</p>
                    <p className="text-xs text-gray-500 truncate">{notification.description}</p>
                    <p className="text-xs text-gray-400 mt-1">
                      {new Date(notification.timestamp).toLocaleTimeString()} · {notification.source}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div className="p-2 bg-gray-50 flex justify-between items-center">
        <button 
          onClick={onViewAllClick}
          className="text-sm text-blue-600 hover:text-blue-800 font-medium flex items-center"
        >
          View all notifications
          <ArrowUpRight size={14} className="ml-1" />
        </button>
        
        <Link to="/settings/notifications" className="text-sm text-gray-600 hover:text-gray-800 flex items-center">
          <Settings size={14} className="mr-1" /> Settings
        </Link>
      </div>
    </div>
  );
};

// Notification Bell Component
const NotificationBell = ({ 
  notifications,
  onViewAllClick
}: {
  notifications: Notification[];
  onViewAllClick: () => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const unreadCount = notifications.filter(n => !n.read).length;
  
  const handleMarkAsRead = useCallback((id: string) => {
    // API call to mark notification as read
    console.log('Mark as read:', id);
  }, []);
  
  return (
    <div className="relative">
      <button 
        onClick={() => setIsOpen(!isOpen)} 
        className="p-1 rounded-full hover:bg-gray-100 relative"
        aria-label="Notifications"
      >
        <Bell size={20} />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>
      
      <NotificationDropdown 
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        notifications={notifications}
        markAsRead={handleMarkAsRead}
        onViewAllClick={() => {
          setIsOpen(false);
          onViewAllClick();
        }}
      />
    </div>
  );
};

// Interface for the notification system API
interface NotificationSystemAPI {
  NotificationBell: () => JSX.Element;
  markAsRead: (id: string) => void;
  deleteNotification: (id: string) => void;
  getAllNotifications: () => Notification[];
  getUnreadCount: () => number;
}

// The main UnifiedNotificationSystem component
const UnifiedNotificationSystem = (): NotificationSystemAPI => {
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  
  // API call to fetch notifications - simulated with mock data
  useEffect(() => {
    // In a real app, this would fetch notifications from the server
    console.log('Fetching notifications');
    // setNotifications(fetchedNotifications)
  }, []);
  
  // Handle marking notification as read
  const handleMarkAsRead = useCallback((id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true } 
          : notification
      )
    );
    
    // In a real app, this would make an API call to update the notification status
    console.log('Marked notification as read:', id);
  }, []);
  
  // Handle deleting notification
  const handleDelete = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
    
    // In a real app, this would make an API call to delete the notification
    console.log('Deleted notification:', id);
  }, []);
  
  // Handle navigation to the notifications page
  const handleViewAllNotifications = useCallback(() => {
    // In a real app, this would navigate to the notifications page
    window.location.href = '/alert-notifications';
  }, []);
  
  // This is used by the notification bell component in the header
  const notificationBell = useCallback(() => (
    <NotificationBell 
      notifications={notifications}
      onViewAllClick={handleViewAllNotifications}
    />
  ), [notifications, handleViewAllNotifications]);
  
  return {
    // Export the notification bell for use in the header
    NotificationBell: notificationBell,
    // Export functions for direct use by the AlertNotifications page
    markAsRead: handleMarkAsRead,
    deleteNotification: handleDelete,
    getAllNotifications: () => notifications,
    getUnreadCount: () => notifications.filter(n => !n.read).length
  };
};

export default UnifiedNotificationSystem;
