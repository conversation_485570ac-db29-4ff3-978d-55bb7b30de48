import { AlertTriangle, TrendingUp, <PERSON>, <PERSON>, CheckCircle2 } from 'lucide-react';
import { formatDate } from '../../lib/utils/formatters';
import { useNavigate } from 'react-router-dom';

interface Notification {
  id: string;
  type: 'alert' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  source: {
    type: 'system' | 'meter' | 'consumption' | 'device';
    id: string;
    name: string;
  };
}

const mockNotifications: Notification[] = [
  {
    id: '1',
    type: 'alert',
    title: 'Critical Energy Consumption',
    message: 'Tower Building Floor 16 consumption exceeded threshold by 45%',
    timestamp: new Date().toISOString(),
    isRead: false,
    source: {
      type: 'consumption',
      id: 'FL16-A',
      name: 'Floor 16, Tower Building'
    }
  },
  {
    id: '2',
    type: 'warning',
    title: 'Device Disconnected',
    message: 'IoT device M-2234 has been offline for 30 minutes',
    timestamp: new Date(Date.now() - 1800000).toISOString(),
    isRead: false,
    source: {
      type: 'device',
      id: 'M-2234',
      name: 'Energy Meter'
    }
  }
];

const NOTIFICATION_ICONS = {
  alert: AlertTriangle,
  warning: TrendingUp,
  info: Bell,
  success: CheckCircle2,
} as const;

const NOTIFICATION_STYLES = {
  alert: 'bg-red-50 border-red-200',
  warning: 'bg-yellow-50 border-yellow-200',
  info: 'bg-blue-50 border-blue-200',
  success: 'bg-green-50 border-green-200',
} as const;

const NOTIFICATION_ICON_STYLES = {
  alert: 'text-red-600',
  warning: 'text-yellow-600',
  info: 'text-blue-600',
  success: 'text-green-600',
} as const;

interface NotificationPanelProps {
  onClose: () => void;
  onMarkAllRead: () => void;
}

export function NotificationPanel({ onClose, onMarkAllRead }: NotificationPanelProps) {
  const navigate = useNavigate();

  const handleViewAll = () => {
    onClose();
    navigate('/alarms');
  };

  return (
    <div className="w-full bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
      <div className="px-4 py-3 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-gray-900">Notifications</h3>
          <div className="flex items-center gap-3">
            <button
              onClick={onMarkAllRead}
              className="text-xs text-blue-600 hover:text-blue-700 font-medium"
            >
              Mark all read
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <X size={18} />
            </button>
          </div>
        </div>
      </div>

      <div className="max-h-[400px] overflow-y-auto">
        {mockNotifications.map((notification) => {
          const Icon = NOTIFICATION_ICONS[notification.type];
          return (
            <div
              key={notification.id}
              className={`px-4 py-3 hover:bg-gray-50 transition-colors cursor-pointer ${
                !notification.isRead ? 'bg-blue-50/30' : ''
              }`}
            >
              <div className="flex gap-3">
                <div className={`mt-0.5 ${NOTIFICATION_ICON_STYLES[notification.type]}`}>
                  <Icon size={16} />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between gap-2">
                    <p className={`text-sm ${!notification.isRead ? 'font-semibold' : 'font-medium'} text-gray-900`}>
                      {notification.title}
                    </p>
                    {!notification.isRead && (
                      <span className="w-2 h-2 bg-blue-600 rounded-full flex-shrink-0 mt-1.5"></span>
                    )}
                  </div>
                  <p className="mt-0.5 text-xs text-gray-600 line-clamp-2">{notification.message}</p>
                  <div className="mt-1.5 flex items-center gap-2 text-xs text-gray-500">
                    <span>{formatDate(new Date(notification.timestamp))}</span>
                    <span>•</span>
                    <span>{notification.source.name}</span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="px-4 py-2 bg-gray-50 border-t border-gray-100">
        <button
          onClick={handleViewAll}
          className="w-full text-center text-xs text-blue-600 hover:text-blue-700 font-medium py-1"
        >
          View all notifications →
        </button>
      </div>
    </div>
  );
}