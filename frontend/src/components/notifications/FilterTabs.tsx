import React, { memo } from 'react';
import { AlertTriangle, TrendingUp, Bell, CheckCircle2, Calendar, Cpu, Zap, Gauge, Circle } from 'lucide-react';
import { Notification } from '../../pages/AlertNotifications';

interface FilterTabsProps {
  selectedType: 'all' | Notification['type'];
  onSelectType: (type: 'all' | Notification['type']) => void;
  selectedSource: 'all' | Notification['source']['type'];
  onSelectSource: (source: 'all' | Notification['source']['type']) => void;
  timeRange: string;
  onSelectTimeRange: (range: string) => void;
  counts: {
    all: number;
    critical: number;
    warning: number;
    info: number;
    success: number;
    system: number;
    meter: number;
    consumption: number;
    device: number;
  };
}

const FilterTabs = memo(function FilterTabs({
  selectedType,
  onSelectType,
  selectedSource,
  onSelectSource,
  timeRange,
  onSelectTimeRange,
  counts
}: FilterTabsProps) {
  // Type filters configuration
  const typeFilters = [
    { id: 'all', label: 'All', icon: Circle, count: counts.all },
    { id: 'critical', label: 'Critical', icon: AlertTriangle, count: counts.critical, color: 'text-red-600' },
    { id: 'warning', label: 'Warning', icon: TrendingUp, count: counts.warning, color: 'text-yellow-600' },
    { id: 'info', label: 'Info', icon: Bell, count: counts.info, color: 'text-blue-600' },
    { id: 'success', label: 'Success', icon: CheckCircle2, count: counts.success, color: 'text-green-600' }
  ];

  // Source filters configuration
  const sourceFilters = [
    { id: 'all', label: 'All Sources', icon: Circle, count: counts.all },
    { id: 'system', label: 'System', icon: Cpu, count: counts.system },
    { id: 'meter', label: 'Meter', icon: Gauge, count: counts.meter },
    { id: 'consumption', label: 'Consumption', icon: Zap, count: counts.consumption },
    { id: 'device', label: 'Device', icon: Bell, count: counts.device }
  ];

  // Time range filters configuration
  const timeFilters = [
    { id: '1d', label: 'Today' },
    { id: '7d', label: '7 Days' },
    { id: '30d', label: '30 Days' },
    { id: 'all', label: 'All Time' }
  ];

  return (
    <div className="space-y-4">
      {/* Type filters */}
      <div>
        <div className="text-xs font-medium text-gray-500 mb-2">Filter by Type</div>
        <div className="flex flex-wrap gap-2">
          {typeFilters.map(filter => (
            <button
              key={filter.id}
              onClick={() => onSelectType(filter.id as any)}
              className={`inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full ${
                selectedType === filter.id
                  ? filter.id === 'all'
                    ? 'bg-gray-900 text-white'
                    : `bg-${filter.id === 'critical' ? 'red' : filter.id === 'warning' ? 'yellow' : filter.id === 'info' ? 'blue' : 'green'}-100 ${filter.color}`
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
              aria-label={`Filter by ${filter.label}`}
            >
              <filter.icon className={`h-3 w-3 mr-1 ${selectedType === filter.id ? (filter.id === 'all' ? '' : filter.color) : ''}`} />
              {filter.label}
              {filter.count > 0 && (
                <span className={`ml-1.5 py-0.5 px-1.5 text-xs rounded-full ${
                  selectedType === filter.id
                    ? filter.id === 'all'
                      ? 'bg-gray-800'
                      : 'bg-white'
                    : 'bg-gray-200'
                }`}>
                  {filter.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Source filters */}
      <div>
        <div className="text-xs font-medium text-gray-500 mb-2">Filter by Source</div>
        <div className="flex flex-wrap gap-2">
          {sourceFilters.map(filter => (
            <button
              key={filter.id}
              onClick={() => onSelectSource(filter.id as any)}
              className={`inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full ${
                selectedSource === filter.id
                  ? 'bg-gray-900 text-white'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
              aria-label={`Filter by ${filter.label}`}
            >
              <filter.icon className="h-3 w-3 mr-1" />
              {filter.label}
              {filter.count > 0 && filter.id !== 'all' && (
                <span className={`ml-1.5 py-0.5 px-1.5 text-xs rounded-full ${
                  selectedSource === filter.id ? 'bg-gray-800' : 'bg-gray-200'
                }`}>
                  {filter.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Time range filters */}
      <div>
        <div className="text-xs font-medium text-gray-500 mb-2">Time Range</div>
        <div className="flex flex-wrap gap-2">
          {timeFilters.map(filter => (
            <button
              key={filter.id}
              onClick={() => onSelectTimeRange(filter.id)}
              className={`inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full ${
                timeRange === filter.id
                  ? filter.id === '1d'
                    ? 'bg-[#0e7de4] text-white'
                    : 'bg-gray-900 text-white'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
              aria-label={`Set time range to ${filter.label}`}
            >
              <Calendar className="h-3 w-3 mr-1" />
              {filter.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
});

export default FilterTabs;
