import React, { memo, useEffect, useRef } from 'react';
import { Notification } from '../../pages/AlertNotifications';

interface AlertWaveformProps {
  alertType: Notification['type'];
}

// Constants for styling
const COLORS = {
  critical: '#ef4444',
  warning: '#f59e0b',
  info: '#3b82f6',
  success: '#10b981',
};

const AlertWaveform = memo(function AlertWaveform({ alertType }: AlertWaveformProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions considering device pixel ratio for sharper rendering
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    ctx.scale(dpr, dpr);
    
    // Clear canvas
    ctx.clearRect(0, 0, rect.width, rect.height);
    
    // Draw waveform based on alert type
    const color = COLORS[alertType];
    const height = rect.height;
    const width = rect.width;
    
    // Draw grid lines
    ctx.lineWidth = 0.5;
    ctx.strokeStyle = '#e5e7eb';
    
    // Vertical grid lines
    for (let x = 0; x <= width; x += 20) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
    
    // Horizontal grid lines
    for (let y = 0; y <= height; y += 20) {
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
    
    // Center line
    ctx.lineWidth = 1;
    ctx.strokeStyle = '#d1d5db';
    ctx.beginPath();
    ctx.moveTo(0, height / 2);
    ctx.lineTo(width, height / 2);
    ctx.stroke();
    
    // Draw waveform
    ctx.beginPath();
    ctx.lineWidth = 2;
    ctx.strokeStyle = color;
    
    const amplitude = height * 0.4;
    const frequency = alertType === 'critical' ? 0.02 : 0.01;
    const noiseLevel = alertType === 'critical' ? 10 : alertType === 'warning' ? 5 : 2;
    
    // Generate a distorted sine wave based on alert type
    let x = 0;
    ctx.moveTo(x, height / 2 + Math.sin(x * frequency) * amplitude);
    
    while (x < width) {
      // Add noise/distortion for critical and warning alerts
      const noise = (Math.random() - 0.5) * noiseLevel;
      
      // For critical alerts, add occasional spikes
      const spike = alertType === 'critical' && Math.random() > 0.95 ? amplitude * 0.5 * (Math.random() > 0.5 ? 1 : -1) : 0;
      
      const y = height / 2 + Math.sin(x * frequency) * amplitude + noise + spike;
      ctx.lineTo(x, y);
      x++;
    }
    ctx.stroke();
    
    // For critical alerts, add a red fill below the line to indicate severity
    if (alertType === 'critical') {
      const gradient = ctx.createLinearGradient(0, 0, 0, height);
      gradient.addColorStop(0, 'rgba(239, 68, 68, 0.1)');
      gradient.addColorStop(1, 'rgba(239, 68, 68, 0)');
      
      ctx.lineTo(width, height);
      ctx.lineTo(0, height);
      ctx.closePath();
      ctx.fillStyle = gradient;
      ctx.fill();
    }
    
    // Add zero crossing markers for analysis reference
    if (alertType === 'critical' || alertType === 'warning') {
      const zeroCrossings = [];
      let prevY = Math.sin(0) * amplitude;
      
      for (let x = 1; x < width; x += 5) {
        const y = Math.sin(x * frequency) * amplitude;
        if ((prevY < 0 && y >= 0) || (prevY >= 0 && y < 0)) {
          zeroCrossings.push(x);
        }
        prevY = y;
      }
      
      ctx.fillStyle = '#4b5563';
      zeroCrossings.forEach(x => {
        ctx.beginPath();
        ctx.arc(x, height / 2, 2, 0, Math.PI * 2);
        ctx.fill();
      });
    }
    
    // Add time and voltage labels
    ctx.fillStyle = '#6b7280';
    ctx.font = '10px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('0 ms', 5, height - 5);
    ctx.textAlign = 'right';
    ctx.fillText('20 ms', width - 5, height - 5);
    
    // Y-axis labels
    ctx.textAlign = 'left';
    ctx.fillText('+230V', 5, 15);
    ctx.fillText('-230V', 5, height - 15);
    ctx.fillText('0V', 5, height / 2 + 4);
    
  }, [alertType]);
  
  return (
    <div className="relative w-full">
      <canvas 
        ref={canvasRef} 
        className="w-full h-[200px]"
        aria-label={`Voltage waveform for ${alertType} alert`}
      />
    </div>
  );
});

export default AlertWaveform;
