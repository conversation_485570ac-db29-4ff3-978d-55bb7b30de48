import { memo, useEffect, useRef } from 'react';
import { UNIFIED_CHART_STYLES, CANVAS_CONFIG, CHART_COLORS } from '../../lib/config/chart-styles';

interface HarmonicGraphProps {
  alertType: 'critical' | 'warning' | 'info';
}

// Update to use the unified chart styles
const COLORS = {
  critical: CHART_COLORS[6], // danger color
  warning: CHART_COLORS[3], // warning color
  textColor: UNIFIED_CHART_STYLES.colors.text.primary,
  gridColor: UNIFIED_CHART_STYLES.colors.grid,
  labelColor: UNIFIED_CHART_STYLES.colors.text.secondary,
  backgroundColor: UNIFIED_CHART_STYLES.colors.background,
  primaryBlue: CHART_COLORS[0], // primary color
  info: CHART_COLORS[2], // info color
  success: CHART_COLORS[5], // success color
};

// Harmonic frequency labels (fundamental and harmonics)
const HARMONICS = ['50 Hz', '100 Hz', '150 Hz', '200 Hz', '250 Hz', '300 Hz', '350 Hz', '400 Hz', '450 Hz', '500 Hz'];

const HarmonicGraph = memo(function HarmonicGraph({ alertType }: HarmonicGraphProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions considering device pixel ratio for sharper rendering
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    
    // Scale for retina displays
    ctx.scale(dpr, dpr);
    
    // Clear canvas
    ctx.fillStyle = COLORS.backgroundColor;
    ctx.fillRect(0, 0, rect.width, rect.height);
    
    const height = rect.height;
    const width = rect.width;
    
    // Draw grid lines
    const gridSpacingX = width / 11; // 10 harmonics + some padding
    const gridSpacingY = height / 12; // 10 divisions + padding
    
    ctx.strokeStyle = COLORS.gridColor;
    ctx.lineWidth = CANVAS_CONFIG.grid.width;
    ctx.setLineDash(CANVAS_CONFIG.grid.dash);
    
    // Horizontal grid lines
    for (let i = 1; i < 11; i++) {
      const y = height - 30 - (i * gridSpacingY);
      
      ctx.beginPath();
      ctx.moveTo(40, y);
      ctx.lineTo(width - 20, y);
      ctx.stroke();
    }
    
    // Vertical grid lines
    for (let i = 0; i < 10; i++) {
      const x = 40 + (i * gridSpacingX);
      
      ctx.beginPath();
      ctx.moveTo(x, 20);
      ctx.lineTo(x, height - 30);
      ctx.stroke();
    }
    
    // Reset line dash
    ctx.setLineDash([]);
    
    // Draw axes
    ctx.strokeStyle = UNIFIED_CHART_STYLES.colors.axis;
    ctx.lineWidth = 1;
    
    // X-axis
    ctx.beginPath();
    ctx.moveTo(40, height - 30);
    ctx.lineTo(width - 20, height - 30);
    ctx.stroke();
    
    // Y-axis
    ctx.beginPath();
    ctx.moveTo(40, 20);
    ctx.lineTo(40, height - 30);
    ctx.stroke();
    
    // Draw labels
    ctx.font = `${UNIFIED_CHART_STYLES.fonts.weight.normal} ${CANVAS_CONFIG.fontSize.axis}px ${CANVAS_CONFIG.fontFamily}`;
    ctx.fillStyle = UNIFIED_CHART_STYLES.colors.text.label;
    ctx.textAlign = 'center';
    
    // X-axis labels (harmonic frequencies)
    for (let i = 0; i < 10; i++) {
      const x = 40 + (i * gridSpacingX);
      ctx.fillText(HARMONICS[i], x, height - 10);
    }
    
    // Y-axis labels (percentage values)
    ctx.textAlign = 'right';
    for (let i = 0; i <= 10; i += 2) {
      const y = height - 30 - (i * gridSpacingY);
      ctx.fillText(`${i * 10}%`, 35, y + 4);
    }
    
    // Draw title
    ctx.font = `${UNIFIED_CHART_STYLES.fonts.weight.medium} ${CANVAS_CONFIG.fontSize.title}px ${CANVAS_CONFIG.fontFamily}`;
    ctx.fillStyle = UNIFIED_CHART_STYLES.colors.text.primary;
    ctx.textAlign = 'left';
    ctx.fillText('Harmonic Analysis', 40, 15);
    
    // Draw bars
    const barWidth = gridSpacingX * 0.6;
    const maxBarHeight = gridSpacingY * 10;
    
    // Generate random values for harmonics based on alert type
    const harmonicValues = Array(10).fill(0).map((_, i) => {
      if (i === 0) return 100; // Fundamental always 100%
      
      if (alertType === 'critical') {
        // For critical alerts, higher harmonics have higher values
        return Math.min(100, Math.random() * 30 + (i > 5 ? 20 : 5));
      } else if (alertType === 'warning') {
        // For warnings, moderate harmonic values
        return Math.min(100, Math.random() * 20 + (i > 5 ? 10 : 3));
      } else {
        // For info, low harmonic values
        return Math.min(100, Math.random() * 10 + 2);
      }
    });
    
    // Draw each bar
    harmonicValues.forEach((value, i) => {
      const x = 40 + (i * gridSpacingX) - (barWidth / 2);
      const barHeight = (value / 100) * maxBarHeight;
      const y = height - 30 - barHeight;
      
      // Determine color based on harmonic number and alert type
      let barColor;
      if (i === 0) {
        // Fundamental is always primary color
        barColor = COLORS.primaryBlue;
      } else if (alertType === 'critical' && value > 15) {
        barColor = COLORS.critical;
      } else if (alertType === 'warning' && value > 10) {
        barColor = COLORS.warning;
      } else if (value > 5) {
        barColor = COLORS.info;
      } else {
        barColor = COLORS.success;
      }
      
      // Draw bar with rounded top corners
      const radius = CANVAS_CONFIG.barStyle.cornerRadius;
      
      ctx.fillStyle = barColor;
      ctx.beginPath();
      ctx.moveTo(x, height - 30);
      ctx.lineTo(x, y + radius);
      ctx.quadraticCurveTo(x, y, x + radius, y);
      ctx.lineTo(x + barWidth - radius, y);
      ctx.quadraticCurveTo(x + barWidth, y, x + barWidth, y + radius);
      ctx.lineTo(x + barWidth, height - 30);
      ctx.closePath();
      ctx.fill();
      
      // Add value label for significant harmonics
      if (value > 5) {
        ctx.font = `${UNIFIED_CHART_STYLES.fonts.weight.medium} ${CANVAS_CONFIG.fontSize.label}px ${CANVAS_CONFIG.fontFamily}`;
        ctx.fillStyle = UNIFIED_CHART_STYLES.colors.text.primary;
        ctx.textAlign = 'center';
        ctx.fillText(`${Math.round(value)}%`, x + barWidth / 2, y - 5);
      }
    });
    
    // Draw threshold line if alert is active
    if (alertType === 'critical' || alertType === 'warning') {
      const thresholdHeight = height - 30 - (maxBarHeight * (alertType === 'critical' ? 0.2 : 0.15));
      
      // Draw threshold line
      ctx.beginPath();
      ctx.strokeStyle = alertType === 'critical' ? COLORS.critical : COLORS.warning;
      ctx.lineWidth = 2;
      ctx.setLineDash([4, 2]);
      ctx.moveTo(40, thresholdHeight);
      ctx.lineTo(width - 20, thresholdHeight);
      ctx.stroke();
      
      // Add threshold label
      ctx.font = `${UNIFIED_CHART_STYLES.fonts.weight.medium} ${CANVAS_CONFIG.fontSize.label}px ${CANVAS_CONFIG.fontFamily}`;
      ctx.fillStyle = alertType === 'critical' ? COLORS.critical : COLORS.warning;
      ctx.textAlign = 'right';
      ctx.fillText(`${alertType === 'critical' ? 'Critical' : 'Warning'} Threshold`, width - 25, thresholdHeight - 5);
    }
  }, [alertType]);
  
  return (
    <div className="relative w-full">
      <canvas 
        ref={canvasRef} 
        className="w-full h-[240px] rounded-lg bg-white"
        aria-label={`Harmonic analysis graph for ${alertType} alert`}
      />
    </div>
  );
});

export default HarmonicGraph;
