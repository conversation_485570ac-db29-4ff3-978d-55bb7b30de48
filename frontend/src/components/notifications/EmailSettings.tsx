import { memo, useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { Mail, AlertTriangle, CheckCircle, RefreshCw, AlertCircle } from 'lucide-react';
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

// Import types from the *other* EmailAlertSettings component file
// Correctly import types and the constant value
import {
  SEVERITY_LEVELS // Import the constant as a value
} from '../../components/EmailAlerts/EmailAlertSettings';
import type { 
  NotificationPreference, 
  EmailAlertSettings as EmailAlertSettingsData, // Rename imported type
  SeverityLevel // Import SeverityLevel as a type
} from '../../components/EmailAlerts/EmailAlertSettings'; 

// Import mock data utilities
import { 
  getMockEmailSettings, 
  updateMockPreference, 
  sendMockTestEmail,
  runMockAnomalyScan 
} from '@/utils/mockNotifications';

// Flag to use mock data - set to true by default for development
const USE_MOCK_DATA = true;

// Function to get the auth token (replace with your actual implementation)
function getAuthToken(): string | null {
  // Example: Retrieve token from local storage
  return localStorage.getItem('authToken');
}

// Create an Axios instance with default headers
const apiClient = axios.create();

apiClient.interceptors.request.use((config) => {
  const token = getAuthToken();
  if (token) {
    config.headers.Authorization = `Token ${token}`;
  }
  // Remove withCredentials if set, as we are using Token auth
  // delete config.withCredentials; 
  // NOTE: Keep withCredentials for now, might be needed if session auth fallback is desired, but prioritize Token
  return config;
}, (error) => {
  return Promise.reject(error);
});

// --- Remove Mock Data ---
// const MOCK_PREFERENCES: NotificationPreference[] = [...]; // REMOVE
// const MOCK_CHANNELS: NotificationChannel[] = [...]; // REMOVE
// const MOCK_SUBSCRIPTIONS: AlarmSubscription[] = [...]; // REMOVE
// const MOCK_SETTINGS_DATA: EmailAlertSettingsData = {...}; // REMOVE
// --- End Remove Mock Data ---


const EmailSettings = memo(function EmailSettings() {
  const [settings, setSettings] = useState<EmailAlertSettingsData | null>(null);
  const [loading, setLoading] = useState(true); // Keep for initial load
  const [updatingPref, setUpdatingPref] = useState<number | null>(null); // State for specific preference updates
  const [activeTab, setActiveTab] = useState('preferences');
  const [notification, setNotification] = useState({ show: false, message: '', type: 'success' as 'success' | 'error' });
  const [testEmailLoading, setTestEmailLoading] = useState(false); // Specific loading for test email
  const [scanLoading, setScanLoading] = useState(false);
  const [scanResults, setScanResults] = useState<any>(null);
  // const [error, setError] = useState<string | null>(null); // Use showNotification for errors
  // const [success, setSuccess] = useState<string | null>(null); // Use showNotification for success

  // Show notification helper
  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ show: true, message, type });
    // Auto-hide handled by useEffect
  };

  // Fetch settings using API (using the configured apiClient)
  const fetchSettings = useCallback(async () => {
    setLoading(true);
    try {
      if (USE_MOCK_DATA) {
        const mockSettings = await getMockEmailSettings();
        setSettings(mockSettings);
      } else {
        const response = await apiClient.get<EmailAlertSettingsData>(
          '/api/notifications/email-alerts/settings/'
          // No need for { withCredentials: true } here if using Token Auth interceptor
        );
        setSettings(response.data);
      }
    } catch (err) {
      console.error("Failed to load email alert settings:", err);
      showNotification('Failed to load email alert settings. Please try again.', 'error');
      setSettings(null); // Clear potentially stale data
    } finally {
      setLoading(false);
    }
  }, []);

  // Helper function to update preference via API (using apiClient)
  const updatePreferenceApi = useCallback(async (preferenceId: number, updatedFields: Partial<NotificationPreference>) => {
    if (!settings) return;

    const preferenceToUpdate = settings.preferences.find(p => p.id === preferenceId);
    if (!preferenceToUpdate) {
      showNotification('Could not find the preference to update.', 'error');
      return;
    }

    setUpdatingPref(preferenceId);

    const payload = {
      channel_id: preferenceToUpdate.channel.id,
      category: preferenceToUpdate.category,
      ...updatedFields // is_enabled, quiet_hours_start/end, enabled_severities
    };

    try {
      if (USE_MOCK_DATA) {
        const updatedPreference = await updateMockPreference(preferenceId, payload);
        // Update the settings state with the updated preference from the mock response
        setSettings(prevSettings => {
          if (!prevSettings) return null;
          return {
            ...prevSettings,
            preferences: prevSettings.preferences.map(p =>
              p.id === updatedPreference.id ? updatedPreference : p
            )
          };
        });
      } else {
        const response = await apiClient.post<NotificationPreference>(
          '/api/notifications/email-alerts/update_preferences/',
           payload
           // No need for { withCredentials: true } here
         );
        const updatedPreference = response.data;

        // Update the settings state with the updated preference from the API response
        setSettings(prevSettings => {
          if (!prevSettings) return null;
          return {
            ...prevSettings,
            preferences: prevSettings.preferences.map(p =>
              p.id === updatedPreference.id ? updatedPreference : p
            )
          };
        });
      }
      // Use a generic success message or tailor based on updatedFields keys
      let successMsg = 'Preference updated successfully.';
      if ('is_enabled' in updatedFields) {
          successMsg = `${preferenceToUpdate.category_display} notifications ${updatedFields.is_enabled ? 'enabled' : 'disabled'}.`;
      } else if ('quiet_hours_start' in updatedFields || 'quiet_hours_end' in updatedFields) {
          successMsg = `Quiet hours updated for ${preferenceToUpdate.category_display}.`;
      } else if ('enabled_severities' in updatedFields) {
           successMsg = `Alarm severity preference updated for ${preferenceToUpdate.category_display}.`;
      }
      showNotification(successMsg, 'success');

    } catch (err: any) {
      console.error("Failed to update preference:", err);
      showNotification(err.response?.data?.error || 'Failed to update preference. Please try again.', 'error');
      // Optionally revert optimistic UI updates if implemented
    } finally {
      setUpdatingPref(null);
    }
  }, [settings]);

  // Toggle preference enabled/disabled
  const togglePreference = useCallback((preferenceId: number) => {
    const preference = settings?.preferences.find(p => p.id === preferenceId);
    if (!preference) return;

    const isEnabling = !preference.is_enabled;
    const payload: Partial<NotificationPreference> = { is_enabled: isEnabling };

    // When enabling Alarms, default to all severities if none are set yet
    if (preference.category === 'Alarms' && isEnabling && (!preference.enabled_severities || preference.enabled_severities.length === 0)) {
       payload.enabled_severities = [...SEVERITY_LEVELS];
    }

    updatePreferenceApi(preferenceId, payload);

  }, [settings, updatePreferenceApi]);

  // Update quiet hours (Removed)
  // const updateQuietHours = useCallback((preferenceId: number, start: string | null, end: string | null) => {
  //   // Basic validation or debounce could be added here
  //   updatePreferenceApi(preferenceId, { quiet_hours_start: start, quiet_hours_end: end });
  // }, [updatePreferenceApi]); // Only depends on the memoized update function

  // Handle severity checkbox change
  const handleSeverityChange = useCallback((preferenceId: number, severity: SeverityLevel, isChecked: boolean) => {
     const preference = settings?.preferences.find(p => p.id === preferenceId);
     if (!preference || preference.category !== 'Alarms') return;

     // Ensure enabled_severities is an array
     const currentSeverities = Array.isArray(preference.enabled_severities) ? preference.enabled_severities : [];
     const updatedSeverities = isChecked
       ? [...currentSeverities, severity]
       : currentSeverities.filter(s => s !== severity);
     // Filter out duplicates just in case
     const uniqueSeverities = [...new Set(updatedSeverities)];

     updatePreferenceApi(preferenceId, { enabled_severities: uniqueSeverities });

  }, [settings, updatePreferenceApi]); // Depends on settings and the update function


  // Send test email using API (using apiClient)
  const sendTestEmail = async () => {
    setTestEmailLoading(true);
    try {
      if (USE_MOCK_DATA) {
        const mockResponse = await sendMockTestEmail();
        showNotification(mockResponse.message || 'Test email sent successfully!', 'success');
      } else {
        const response = await apiClient.post<{ message: string }>(
          '/api/notifications/email-alerts/test_email/',
          {}
          // No need for { withCredentials: true } here
        );
        showNotification(response.data.message || 'Test email sent successfully!', 'success');
      }
    } catch (err: any) {
      console.error("Failed to send test email:", err);
      showNotification(err.response?.data?.error || 'Failed to send test email. Check logs.', 'error');
    } finally {
      setTestEmailLoading(false);
    }
  };

  // Trigger anomaly scan using API (using apiClient)
  const triggerScan = async (dryRun: boolean) => {
    setScanLoading(true);
    setScanResults(null);
    try {
      if (USE_MOCK_DATA) {
        const mockScanResults = await runMockAnomalyScan(dryRun);
        setScanResults(mockScanResults);
        showNotification(
          dryRun ? `Scan complete: Found ${mockScanResults.total_anomalies ?? 0} anomalies` : `Scan complete: Sent ${mockScanResults.alerts_sent ?? 0} alerts`,
          'success'
        );
      } else {
        const response = await apiClient.post(
          `/api/notifications/email-alerts/trigger_scan/?dry_run=${dryRun}`,
          {}
          // No need for { withCredentials: true } here
        );
        setScanResults(response.data); // Store API response
        showNotification(
          dryRun ? `Scan complete: Found ${response.data.total_anomalies ?? 0} anomalies` : `Scan complete: Sent ${response.data.alerts_sent ?? 0} alerts`,
          'success'
        );
      }
    } catch (err: any) {
      console.error("Failed to trigger scan:", err);
      showNotification(err.response?.data?.error || 'Failed to scan for anomalies. Check logs.', 'error');
    } finally {
      setScanLoading(false);
    }
  };

  // Close notification helper - remains the same
  const closeNotification = () => {
    setNotification({ ...notification, show: false });
  };

  // Fetch settings on mount - remains the same
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  // Auto-hide notification - remains the same
  useEffect(() => {
    if (notification.show) {
      const timer = setTimeout(() => {
        closeNotification();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [notification.show]);

  // Initial Loading State
  if (loading) { // Only show spinner on initial load
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
      </div>
    );
  }

  // Error state if initial fetch failed
   if (!settings) { // If settings are null after loading, assume fetch failed
     return (
       <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg m-4">
         <div className="flex items-center">
           <AlertTriangle className="h-5 w-5 mr-2" />
           <span>Failed to load settings. Please refresh the page or check the console.</span>
         </div>
       </div>
     );
   }

  // --- Render Logic --- (largely unchanged, but update button states)

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header with test email button */}
      <div className="flex flex-col space-y-4 mb-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900\">Email Alert Settings</h2>
          <button
            onClick={sendTestEmail}
            disabled={testEmailLoading} // Use specific loading state
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-[#0e7de4] hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0e7de4] disabled:opacity-50"
            aria-label="Send test email"
          >
            {testEmailLoading ? <RefreshCw className="animate-spin mr-2 h-4 w-4" /> : <Mail className="mr-2 h-4 w-4" />}
            Send Test Email
          </button>
        </div>
        
        <p className="text-sm text-gray-500">
          Configure which types of notifications you want to receive via email, manage quiet hours, 
          and test your alert system.
        </p>
      </div>

      {/* Status Messages */}
      {notification.show && (
        <div className={`my-4 ${
          notification.type === 'success'
            ? 'bg-green-50 border-green-200 text-green-700'
            : 'bg-red-50 border-red-200 text-red-700'
        } px-4 py-3 rounded-lg border`}>
          <div className="flex items-center">
            {notification.type === 'success'
              ? <CheckCircle className="h-5 w-5 mr-2" />
              : <AlertTriangle className="h-5 w-5 mr-2" />}
            <span>{notification.message}</span>
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8" aria-label="Tabs">
          {['preferences', 'subscriptions', 'testing'].map((tab) => {
            const label = tab === 'preferences' 
              ? 'Notification Preferences' 
              : tab === 'subscriptions' 
                ? 'Alarm Subscriptions' 
                : 'Test & Scan';
                
            return (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`py-3 px-1 inline-flex items-center border-b-2 font-medium text-sm ${
                  activeTab === tab
                    ? 'border-[#0e7de4] text-[#0e7de4]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                aria-label={`View ${label} tab`}
              >
                {label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content based on active tab */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Email Notification Preferences Tab */}
        {activeTab === 'preferences' && settings && (
          <div>
             <div className="p-4 border-b border-gray-100">
              <h3 className="text-lg font-medium text-gray-900">Email Notification Preferences</h3>
              <p className="text-sm text-gray-500 mt-1">
                Configure which types of notifications you want to receive via email
              </p>
            </div>
            <div className="p-4">
              {settings.preferences.length > 0 ? (
                settings.preferences.map((preference: NotificationPreference) => (
                  <div key={preference.id} className="mb-6 pb-6 border-b border-gray-200 last:border-b-0 last:mb-0 last:pb-0 relative">
                     {/* Dim overlay while updating specific pref */}
                     {updatingPref === preference.id && (
                       <div className="absolute inset-0 bg-white/50 flex items-center justify-center z-10 rounded-md">
                         <RefreshCw className="h-5 w-5 animate-spin text-primary-blue" />
                       </div>
                     )}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                          <label className="inline-flex items-center cursor-pointer">
                              <input
                                  type="checkbox"
                                  checked={preference.is_enabled}
                                  onChange={() => togglePreference(preference.id)}
                                  disabled={updatingPref === preference.id} // Disable while updating
                                  className="sr-only peer"
                                  aria-label={`Toggle ${preference.category_display} notifications`}
                              />
                               {/* Switch visual */}
                              <div className={`relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#0e7de4] ${updatingPref === preference.id ? 'opacity-50 cursor-not-allowed' : ''}`}></div>
                              <span className="ml-3 text-sm font-medium text-gray-900">{preference.category_display}</span>
                          </label>
                          <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                              {preference.channel.type}
                          </span>
                      </div>
                    </div>

                    {/* Severity Selection (Only for Alarms) */}
                    {preference.category === 'Alarms' && preference.is_enabled && (
                      <div className="mt-3 pl-6 border-l border-gray-200 ml-2 pt-2">
                        <Label className="text-xs font-medium text-gray-600 mb-1.5 block">Notify me for severity levels:</Label>
                        <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                          {SEVERITY_LEVELS.map((severity) => (
                            <div key={severity} className="flex items-center">
                              <Checkbox
                                id={`severity-${preference.id}-${severity}`}
                                checked={preference.enabled_severities?.includes(severity)}
                                onCheckedChange={(checked) => handleSeverityChange(preference.id, severity, !!checked)}
                              />
                              <Label htmlFor={`severity-${preference.id}-${severity}`} className="ml-2 text-sm font-normal capitalize">
                                {severity}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
                  No email notification preferences found.
                </div>
              )}
            </div>
          </div>
        )}

        {/* Alarm Subscriptions Tab */}
        {activeTab === 'subscriptions' && settings && (
          <div>
            <div className="p-4 border-b border-gray-100">
              <h3 className="text-lg font-medium text-gray-900">Alarm Subscriptions</h3>
              <p className="text-sm text-gray-500 mt-1">
                View your current alarm subscriptions and their settings
              </p>
            </div>
            
            <div className="p-4">
              {settings.alarm_subscriptions.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {settings.alarm_subscriptions.map((subscription) => (
                    <div key={subscription.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                      <div className="font-medium">{subscription.name}</div>
                      <div className="flex gap-2 mt-1">
                        <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                          subscription.severity === 'critical' || subscription.severity === 'high'
                            ? 'bg-red-100 text-red-800'
                            : subscription.severity === 'medium'
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-green-100 text-green-800'
                        }`}>
                          {subscription.severity_display}
                        </span>
                        <span className="px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                          {subscription.category_display}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600 mt-2">
                        {subscription.device && `Device: ${subscription.device}`}
                        {subscription.parameter && ` • Parameter: ${subscription.parameter}`}
                      </div>
                      <div className="text-sm text-gray-600">
                        Condition: {subscription.condition} {subscription.threshold_value}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
                  No alarm subscriptions found.
                </div>
              )}
            </div>
          </div>
        )}

        {/* Test and Scan Tab */}
        {activeTab === 'testing' && (
          <div>
            <div className="p-4 border-b border-gray-100">
              <h3 className="text-lg font-medium text-gray-900">Test and Scan</h3>
              <p className="text-sm text-gray-500 mt-1">
                Test email alerts and scan for anomalies
              </p>
            </div>
            
            <div className="p-4">
              <div className="flex flex-wrap gap-3">
                <button
                  onClick={() => triggerScan(true)} // Dry run
                  disabled={scanLoading} // Use general scan loading
                  className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0e7de4] disabled:opacity-50 disabled:cursor-not-allowed"
                  aria-label="Scan for anomalies (dry run)"
                >
                  {scanLoading ? ( // Check general scan loading
                    <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                  ) : (
                    <AlertCircle className="-ml-1 mr-2 h-4 w-4" />
                  )}
                  Scan for Anomalies (Dry Run)
                </button>
                
                <button
                  onClick={() => triggerScan(false)} // Actual run
                  disabled={scanLoading} // Use general scan loading
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-[#0e7de4] hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#0e7de4] disabled:opacity-50 disabled:cursor-not-allowed"
                  aria-label="Scan and send alerts"
                >
                  {scanLoading ? ( // Check general scan loading
                    <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                  ) : (
                    <Mail className="-ml-1 mr-2 h-4 w-4" />
                  )}
                  Scan and Send Alerts
                </button>
              </div>

              {/* Scan Results Display */}
              {scanLoading && !scanResults && ( // Show loading indicator only if no results yet
                <div className="mt-4 flex items-center text-sm text-gray-500">
                    <RefreshCw className="animate-spin mr-2 h-4 w-4" />
                    Scanning...
                </div>
              )}
              {scanResults && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Scan Results</h4>
                  <div className={`p-4 rounded-lg mb-3 ${
                    scanResults.dry_run
                      ? 'bg-blue-50 border border-blue-200 text-blue-700'
                      : 'bg-green-50 border border-green-200 text-green-700'
                  }`}>
                    {scanResults.dry_run
                      ? `Dry run complete. Found ${scanResults.total_anomalies ?? 0} anomalies (${scanResults.grouped_anomalies ?? 0} groups).`
                      : `Scan complete. Sent ${scanResults.alerts_sent ?? 0} alerts (${scanResults.alerts_suppressed ?? 0} suppressed, ${scanResults.errors ?? 0} errors).`}
                  </div>

                  {/* Display alerts that *would* be sent in a dry run */}
                  {scanResults.dry_run && scanResults.would_send && scanResults.would_send.length > 0 && (
                    <div>
                      <div className="text-sm text-gray-700 mb-2">Alerts that would be sent:</div>
                      <div className="grid grid-cols-1 gap-3 max-h-60 overflow-y-auto pr-2">
                        {scanResults.would_send.map((alert: any, index: number) => (
                          <div key={index} className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                            <div className="font-medium">
                              {/* Display severity with color coding */}
                              <span className={`font-bold ${
                                alert.severity?.toLowerCase() === 'critical' ? 'text-red-700' :
                                alert.severity?.toLowerCase() === 'high' ? 'text-orange-600' :
                                alert.severity?.toLowerCase() === 'medium' ? 'text-yellow-600' :
                                alert.severity?.toLowerCase() === 'low' ? 'text-blue-600' : 'text-gray-600'
                              }`}>
                                {alert.severity?.toUpperCase()}:
                              </span> {alert.message}
                            </div>
                            <div className="text-xs text-gray-600 mt-1">
                              Recipients: {alert.recipients?.join(', ') || 'None'} <br/>
                              Device: {alert.device || 'N/A'} {alert.parameter ? `(${alert.parameter})` : ''} Value: {alert.value ?? 'N/A'} <br/>
                              Time: {alert.timestamp ? new Date(alert.timestamp).toLocaleString() : 'N/A'}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

export default EmailSettings;
