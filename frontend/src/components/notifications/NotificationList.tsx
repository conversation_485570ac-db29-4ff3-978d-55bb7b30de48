import React, { memo } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, CheckCircle2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Trash2 } from 'lucide-react';
import { formatDate } from '../../lib/utils/formatters';
import { Notification } from '../../pages/AlertNotifications';

interface NotificationListProps {
  notifications: Notification[];
  selectedNotifications: string[];
  onSelect: (id: string) => void;
  onSelectAll: () => void;
  onMarkAsRead: () => void;
  onDelete: () => void;
  onViewDetails: (notification: Notification) => void;
  selectedNotification: Notification | null;
  loading: boolean;
}

// Notification icon and style mappings
const NOTIFICATION_ICONS = {
  critical: AlertTriangle,
  warning: TrendingUp,
  info: Bell,
  success: CheckCircle2,
} as const;

const NOTIFICATION_STYLES = {
  critical: 'bg-red-50 border-red-200 hover:bg-red-100',
  warning: 'bg-yellow-50 border-yellow-200 hover:bg-yellow-100',
  info: 'bg-blue-50 border-blue-200 hover:bg-blue-100',
  success: 'bg-green-50 border-green-200 hover:bg-green-100',
} as const;

const NOTIFICATION_ICON_STYLES = {
  critical: 'text-red-600',
  warning: 'text-yellow-600',
  info: 'text-blue-600',
  success: 'text-green-600',
} as const;

const NotificationList = memo(function NotificationList({
  notifications,
  selectedNotifications,
  onSelect,
  onSelectAll,
  onMarkAsRead,
  onDelete,
  onViewDetails,
  selectedNotification,
  loading
}: NotificationListProps) {
  if (loading) {
    return (
      <div className="flex justify-center items-center p-8 h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
      </div>
    );
  }

  if (notifications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 h-full text-gray-500">
        <Bell size={32} className="text-gray-300 mb-2" />
        <p className="text-center">No notifications match your filters</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header with bulk actions */}
      <div className="p-3 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={selectedNotifications.length === notifications.length && notifications.length > 0}
              onChange={onSelectAll}
              className="rounded border-gray-300 text-primary-blue focus:ring-primary-blue"
              aria-label="Select all notifications"
            />
            <span className="text-xs text-gray-600">Select All</span>
          </label>
          
          {selectedNotifications.length > 0 && (
            <div className="flex items-center gap-2">
              <button
                onClick={onMarkAsRead}
                className="flex items-center gap-1 px-2 py-1 text-xs text-primary-blue hover:bg-blue-50 rounded-lg"
                aria-label="Mark selected notifications as read"
              >
                <CheckCheck size={14} />
                <span>Mark as Read</span>
              </button>
              <button
                onClick={onDelete}
                className="flex items-center gap-1 px-2 py-1 text-xs text-red-600 hover:bg-red-50 rounded-lg"
                aria-label="Delete selected notifications"
              >
                <Trash2 size={14} />
                <span>Delete</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Notification items */}
      <div className="overflow-y-auto flex-1">
        {notifications.map((notification) => {
          const Icon = NOTIFICATION_ICONS[notification.type];
          const isSelected = selectedNotifications.includes(notification.id);
          const isActive = selectedNotification?.id === notification.id;
          
          return (
            <div 
              key={notification.id}
              className={`flex group border-b border-gray-100 ${
                isActive ? 'bg-blue-50' : 'hover:bg-gray-50'
              }`}
            >
              <div className="p-3 flex items-start gap-3">
                <div className="flex items-center h-5">
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={() => onSelect(notification.id)}
                    className="rounded border-gray-300 text-primary-blue focus:ring-primary-blue"
                    aria-label={`Select notification: ${notification.title}`}
                  />
                </div>
                
                <div 
                  className={`p-2 rounded-full ${NOTIFICATION_ICON_STYLES[notification.type]} bg-opacity-20`}
                >
                  <Icon size={16} />
                </div>
                
                <div 
                  className="flex-1 min-w-0 cursor-pointer"
                  onClick={() => onViewDetails(notification)}
                >
                  <div className="flex items-center justify-between">
                    <h4 className={`text-sm font-medium ${notification.isRead ? 'text-gray-700' : 'text-gray-900'}`}>
                      {notification.title}
                    </h4>
                    <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                      {formatDate(new Date(notification.timestamp))}
                    </span>
                  </div>
                  
                  <p className={`text-xs ${notification.isRead ? 'text-gray-500' : 'text-gray-700'} truncate mt-1`}>
                    {notification.message}
                  </p>
                  
                  <div className="flex items-center gap-2 mt-1">
                    <span 
                      className={`text-xs px-1.5 py-0.5 rounded-full ${
                        notification.type === 'critical' ? 'bg-red-100 text-red-800' :
                        notification.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                        notification.type === 'info' ? 'bg-blue-100 text-blue-800' :
                        'bg-green-100 text-green-800'
                      }`}
                    >
                      {notification.type}
                    </span>
                    <span className="text-xs text-gray-500">
                      {notification.source.name}
                    </span>
                    
                    {(notification.hasWaveform || notification.hasHarmonics) && (
                      <span className="text-xs text-blue-600">
                        {notification.hasWaveform && notification.hasHarmonics 
                          ? 'Waveform + Harmonics' 
                          : notification.hasWaveform 
                            ? 'Waveform' 
                            : 'Harmonics'}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
});

export default NotificationList;
