import React from 'react';
import { Star } from 'lucide-react';

interface StarRatingProps {
  rating: number;
  max?: number;
}

const StarRating: React.FC<StarRatingProps> = ({ rating, max = 5 }) => {
  // Calculate full and partial stars
  const fullStars = Math.floor(rating);
  const partialStar = rating % 1;
  const emptyStars = max - fullStars - (partialStar > 0 ? 1 : 0);

  return (
    <div className="flex">
      {/* Full stars */}
      {Array.from({ length: fullStars }).map((_, i) => (
        <Star
          key={`full-${i}`}
          size={18}
          fill="#fbbf24"
          stroke="#fbbf24"
          className="mr-0.5"
        />
      ))}
      
      {/* Partial star */}
      {partialStar > 0 && (
        <div className="relative">
          {/* Background empty star */}
          <Star
            size={18}
            stroke="#e5e7eb"
            fill="#e5e7eb"
            className="mr-0.5"
          />
          {/* Overlay with filled star clipped to represent partial fill */}
          <div
            className="absolute top-0 left-0 overflow-hidden"
            style={{ width: `${partialStar * 100}%` }}
          >
            <Star
              size={18}
              fill="#fbbf24"
              stroke="#fbbf24"
              className="mr-0.5"
            />
          </div>
        </div>
      )}
      
      {/* Empty stars */}
      {Array.from({ length: emptyStars }).map((_, i) => (
        <Star
          key={`empty-${i}`}
          size={18}
          stroke="#e5e7eb"
          fill="#e5e7eb"
          className="mr-0.5"
        />
      ))}
    </div>
  );
};

export default React.memo(StarRating);
