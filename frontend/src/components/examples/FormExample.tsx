import React, { useState } from 'react';
import { FormField } from '../common/FormField';
import { SelectOption } from '../common/DropdownSelector';
import { Button } from '../ui/Button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '../ui/Card';

/**
 * Example component demonstrating usage of the FormField component
 * with different input types and validation
 */
export const FormExample: React.FC = () => {
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    building: 'building1',
    description: '',
    notifications: true,
    priority: false
  });

  // Validation state
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitted, setSubmitted] = useState(false);

  // Available buildings
  const buildingOptions: SelectOption<string>[] = [
    { id: 'building1', name: 'Main Building' },
    { id: 'building2', name: 'Secondary Building' },
    { id: 'building3', name: 'Office Tower' },
    { id: 'building4', name: 'Warehouse' }
  ];

  // Update form state
  const handleChange = <K extends keyof typeof formData>(field: K, value: typeof formData[K]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error if field was previously in error
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.trim().length < 3) {
      newErrors.name = 'Name must be at least 3 characters';
    }
    
    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    // Description validation
    if (formData.description.trim().length > 200) {
      newErrors.description = 'Description must be less than 200 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      console.log('Form submitted:', formData);
      setSubmitted(true);
      // Here you would typically make an API call with the form data
    }
  };

  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardHeader>
        <CardTitle>Alarm Configuration</CardTitle>
        <CardDescription>Configure alarm settings for the selected building</CardDescription>
      </CardHeader>
      
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {/* Text Input Example */}
          <FormField
            id="name"
            label="Alarm Name"
            type="text"
            value={formData.name}
            onChange={(value) => handleChange('name', value)}
            placeholder="Enter alarm name"
            required
            error={errors.name}
            helpText="Choose a descriptive name for this alarm"
            success={submitted && !errors.name}
          />
          
          {/* Email Input Example */}
          <FormField
            id="email"
            label="Notification Email"
            type="email"
            value={formData.email}
            onChange={(value) => handleChange('email', value)}
            placeholder="<EMAIL>"
            required
            error={errors.email}
            success={submitted && !errors.email}
          />
          
          {/* Dropdown Example */}
          <FormField
            id="building"
            label="Building"
            type="select"
            options={buildingOptions}
            value={formData.building}
            onChange={(value) => handleChange('building', value)}
            required
          />
          
          {/* Textarea Example */}
          <FormField
            id="description"
            label="Description"
            type="textarea"
            value={formData.description}
            onChange={(value) => handleChange('description', value)}
            placeholder="Enter details about this alarm configuration"
            rows={3}
            error={errors.description}
            helpText="Optional: Provide additional context or details"
          />
          
          {/* Checkbox Example */}
          <FormField
            id="notifications"
            label="Enable Email Notifications"
            type="checkbox"
            checked={formData.notifications}
            onChange={(checked) => handleChange('notifications', checked)}
          />
          
          {/* Switch Example */}
          <FormField
            id="priority"
            label="High Priority"
            type="switch"
            checked={formData.priority}
            onChange={(checked) => handleChange('priority', checked)}
            helpText="High priority alarms will trigger immediate SMS alerts"
          />
        </CardContent>
        
        <CardFooter>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline">Cancel</Button>
            <Button type="submit">Save Configuration</Button>
          </div>
        </CardFooter>
      </form>
    </Card>
  );
};

export default FormExample;
