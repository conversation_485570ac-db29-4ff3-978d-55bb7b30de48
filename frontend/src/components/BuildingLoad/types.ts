import { MutableRefObject } from 'react';
import * as echarts from 'echarts';
import { LoadProfileData } from '../../lib/config/load-profile';

export interface ChartProps {
  chartRef: MutableRefObject<HTMLDivElement | null>;
  todayTotal: number;
  lastWeekTotal: number;
  totalDiff: string;
}

export interface ChartConfig {
  option: echarts.EChartsOption;
  theme: {
    colors: string[];
    backgroundColor: string;
    gradients: {
      primary: echarts.graphic.LinearGradient;
      secondary: echarts.graphic.LinearGradient;
    };
  };
}

export interface LoadDataHookResult {
  todayTotal: number;
  lastWeekTotal: number;
  totalDiff: string;
  data: LoadProfileData[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}