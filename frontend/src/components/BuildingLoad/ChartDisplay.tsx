import React from 'react';
import * as echarts from 'echarts';
import { ChartProps } from './types';

const ChartDisplay = React.memo(function ChartDisplay({ 
  chartRef,
  todayTotal,
  lastWeekTotal,
  totalDiff
}: ChartProps) {
  return (
    <div
      ref={chartRef}
      className="w-full h-[180px] min-w-0 will-change-transform overflow-hidden relative focus:outline-none bg-gradient-to-br from-white to-blue-50/10"
    >
      <div className="absolute top-1 right-1 flex items-center gap-2 text-[9px] text-gray-500">
        <div className="flex items-center gap-1">
          <span>Today:</span>
          <span className="font-medium text-primary-blue">{todayTotal.toLocaleString()} kWh</span>
        </div>
        <div className="flex items-center gap-1">
          <span>vs Last Week:</span>
          <span className={`font-medium ${
            parseFloat(totalDiff) > 0 ? 'text-red-500' : 'text-green-500'
          }`}>
            {totalDiff}%
          </span>
        </div>
      </div>
    </div>
  );
});

export { ChartDisplay };