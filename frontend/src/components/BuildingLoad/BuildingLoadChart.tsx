import React, { useRef, useCallback, useLayoutEffect } from 'react';
import * as echarts from 'echarts';
import { ChartDisplay } from './ChartDisplay';
import { useLoadData } from '../../hooks/useLoadData';
import { useChartConfig } from '../../hooks/useChartConfig';
import type { LoadProfileData } from '../../lib/config/load-profile';

interface BuildingLoadProps {
  data?: LoadProfileData[];
}

export function BuildingLoadChart({ data: initialData }: BuildingLoadProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts>();
  const isInitialized = useRef(false);

  const {
    data,
    todayTotal,
    lastWeekTotal,
    totalDiff,
    isLoading,
    error,
    refetch
  } = useLoadData();

  const { option, theme } = useChartConfig(initialData || data);

  const initializeChart = useCallback(() => {
    if (!chartRef.current || isInitialized.current) return;

    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    chartInstance.current = echarts.init(chartRef.current);
    chartInstance.current.setOption(option);
    
    isInitialized.current = true;
  }, [option]);

  const handleResize = useCallback(() => {
    if (chartInstance.current) {
      chartInstance.current.resize();
    }
  }, []);

  useLayoutEffect(() => {
    initializeChart();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = undefined;
        isInitialized.current = false;
      }
    };
  }, [initializeChart, handleResize]);

  if (error) {
    return <div className="text-red-500">Error loading chart data: {error.message}</div>;
  }

  if (isLoading) {
    return <div className="animate-pulse">Loading chart data...</div>;
  }
  
  const containerClasses = `${GRADIENT_STYLES.card} ${BORDER_STYLES.card} ${SHADOW_STYLES.card} ${SHADOW_STYLES.hover.primary} ${ANIMATION_STYLES.transition}`;

  return (
    <div className={containerClasses}>
      <ChartDisplay
        chartRef={chartRef}
        todayTotal={todayTotal}
        lastWeekTotal={lastWeekTotal}
        totalDiff={totalDiff}
      />
    </div>
  );
}