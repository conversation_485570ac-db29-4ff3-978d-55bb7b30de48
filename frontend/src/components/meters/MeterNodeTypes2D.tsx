import React from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Zap, Building, Building2, Factory, Users, Server, AlertTriangle, Wifi, WifiOff, Circle, Snowflake, ExternalLink, Info } from 'lucide-react';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';

// Define a specific type for node status
type NodeStatus = 'active' | 'warning' | 'disconnected';

// Enhanced status indicator component with badge styling
const StatusIndicator: React.FC<{ status: NodeStatus | string | undefined | null }> = ({ status }) => {
  const statusConfig: Record<NodeStatus, { bg: string; text: string; icon: JSX.Element }> = {
    active: {
      bg: 'bg-emerald-50',
      text: 'text-emerald-700',
      icon: <Wifi size={12} className="text-emerald-500 mr-1" />
    },
    warning: {
      bg: 'bg-amber-50',
      text: 'text-amber-700',
      icon: <AlertTriangle size={12} className="text-amber-500 mr-1" />
    },
    disconnected: {
      bg: 'bg-red-50',
      text: 'text-red-700',
      icon: <WifiOff size={12} className="text-red-500 mr-1" />
    }
  };

  // Ensure status is a valid NodeStatus key, otherwise default to disconnected
  const currentValidStatus = (status && statusConfig[status as NodeStatus]) ? status as NodeStatus : 'disconnected';
  const config = statusConfig[currentValidStatus];
  const label = currentValidStatus === 'active' ? 'Active' : currentValidStatus === 'warning' ? 'Warning' : 'Offline';

  return (
    <div className={`flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
      {config.icon}
      <span>{label}</span>
    </div>
  );
};

// Enhanced node styles with modern design and moderate vertical margins
const baseNodeStyle = "bg-gradient-to-br from-white to-gray-50 rounded-md border border-gray-200 shadow-[0_2px_8px_rgba(0,0,0,0.06)] p-4 w-[420px] transition-all duration-200 cursor-pointer hover:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:translate-y-[-2px] my-4"; // Further increased width and padding
const mainNodeStyle = "bg-gradient-to-br from-white to-gray-50 rounded-md border border-gray-200 shadow-[0_2px_8px_rgba(0,0,0,0.06)] p-4 w-[380px] transition-all duration-200 cursor-pointer hover:shadow-[0_4px_12px_rgba(0,0,0,0.08)] hover:translate-y-[-2px] my-4"; // Further increased width and padding

// Status-based top border colors using application color scheme
const statusBorderColors: Record<NodeStatus, string> = {
  active: 'before:block before:absolute before:top-0 before:left-0 before:right-0 before:h-1 before:bg-emerald-500 before:rounded-t-md',
  warning: 'before:block before:absolute before:top-0 before:left-0 before:right-0 before:h-1 before:bg-amber-500 before:rounded-t-md',
  disconnected: 'before:block before:absolute before:top-0 before:left-0 before:right-0 before:h-1 before:bg-red-500 before:rounded-t-md'
};

// Input node component removed as it's now combined with the Main Unit node

// Main Unit node
const MainNode: React.FC<NodeProps> = ({ data }) => {
  const currentStatus = data.status as NodeStatus; // Assert type for data.status
  const statusStyle = statusBorderColors[currentStatus] || statusBorderColors.disconnected; // Fallback to disconnected style
  const isMainPower = data.isMainPower === true;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`${mainNodeStyle} relative ${statusStyle} group`}>
            {/* Only show the left handle if it's not the main power source */}
            {!isMainPower && (
              <Handle
                type="target"
                position={Position.Left}
                className="!bg-blue-600 !w-2 !h-2 !border !border-white !z-10"
              />
            )}

            {/* Header section with icon and title */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="bg-gray-100 p-2.5 rounded-md mr-3">
                  <Zap size={20} className="text-gray-600" />
                </div>
                <div className="font-medium text-gray-800 text-lg">
                  {isMainPower ? 'Main Distribution' : data.label}
                </div>
              </div>
              <StatusIndicator status={currentStatus} />
            </div>

            {/* Metrics section with divider */}
            <div className="pt-3 mt-3 border-t border-gray-100">
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-base text-gray-500 mb-0.5">Total Power</div>
                  <div className="text-blue-600 font-semibold text-2xl">{data.power.toLocaleString()} <span className="text-base font-normal text-gray-500">kW</span></div>
                </div>
                <div className="text-right">
                  <div className="text-base text-gray-500 mb-0.5">Status</div>
                  <div className="text-base font-medium text-gray-700">
                    {currentStatus === 'active' ? 'Operational' : currentStatus === 'warning' ? 'Warning' : 'Offline'}
                  </div>
                </div>
              </div>
            </div>

            {/* External link icon that appears on hover */}
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
              <ExternalLink size={20} className="text-blue-500" />
            </div>

            <Handle
              type="source"
              position={Position.Right}
              className="!bg-blue-600 !w-2 !h-2 !border !border-white !z-10"
            />
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>Click to view main distribution meters</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Tower node
const TowerNode: React.FC<NodeProps> = ({ data, id }) => {
  const currentStatus = data.status as NodeStatus;
  const statusStyle = statusBorderColors[currentStatus] || statusBorderColors.disconnected;
  const towerId = id?.replace('tower', '') || '';

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`${baseNodeStyle} relative ${statusStyle} group`}>
            <Handle
              type="target"
              position={Position.Left}
              className="!bg-blue-600 !w-2 !h-2 !border !border-white !z-10"
            />

            {/* Header section with icon and title */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="bg-gray-100 p-2.5 rounded-md mr-3">
                  <Building size={20} className="text-gray-600" />
                </div>
                <div className="font-medium text-gray-800 text-lg">{data.label}</div>
              </div>
              <StatusIndicator status={currentStatus} />
            </div>

            {/* Metrics section with divider */}
            <div className="pt-3 mt-3 border-t border-gray-100">
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-base text-gray-500 mb-0.5">Power</div>
                  <div className="text-blue-600 font-semibold text-2xl">{data.power.toLocaleString()} <span className="text-base font-normal text-gray-500">kW</span></div>
                </div>
                <div className="text-right">
                  <div className="text-base text-gray-500 mb-0.5">Meters</div>
                  <div className="text-gray-700 font-medium text-2xl">{data.meterCount === 0 ? '-' : data.meterCount}</div>
                </div>
              </div>
            </div>

            {/* External link icon that appears on hover */}
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
              <ExternalLink size={20} className="text-blue-500" />
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>Click to view {data.label} meters</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Tenant node
const TenantNode: React.FC<NodeProps> = ({ data }) => {
  const currentStatus = data.status as NodeStatus;
  const statusStyle = statusBorderColors[currentStatus] || statusBorderColors.disconnected;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`${baseNodeStyle} relative ${statusStyle} group`}>
            <Handle
              type="target"
              position={Position.Left}
              className="!bg-blue-600 !w-2 !h-2 !border !border-white !z-10"
            />

            {/* Header section with icon and title */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="bg-gray-100 p-2.5 rounded-md mr-3">
                  <Users size={20} className="text-gray-600" />
                </div>
                <div className="font-medium text-gray-800 text-lg">{data.label}</div>
              </div>
              <StatusIndicator status={currentStatus} />
            </div>

            {/* Metrics section with divider */}
            <div className="pt-3 mt-3 border-t border-gray-100">
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-base text-gray-500 mb-0.5">Power</div>
                  <div className="text-blue-600 font-semibold text-2xl">{data.power.toLocaleString()} <span className="text-base font-normal text-gray-500">kW</span></div>
                </div>
                <div className="text-right">
                  <div className="text-base text-gray-500 mb-0.5">Meters</div>
                  <div className="text-gray-700 font-medium text-2xl">{data.meterCount === 0 ? '-' : data.meterCount}</div>
                </div>
              </div>
            </div>

            {/* External link icon that appears on hover */}
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
              <ExternalLink size={20} className="text-blue-500" />
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>Click to view all tenant meters across towers</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Chiller Plant node
const PlantNode: React.FC<NodeProps> = ({ data }) => {
  const currentStatus = data.status as NodeStatus;
  const statusStyle = statusBorderColors[currentStatus] || statusBorderColors.disconnected;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`${baseNodeStyle} relative ${statusStyle} group`}>
            <Handle
              type="target"
              position={Position.Left}
              className="!bg-blue-600 !w-2 !h-2 !border !border-white !z-10"
            />

            {/* Header section with icon and title */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="bg-gray-100 p-2.5 rounded-md mr-3">
                  <Snowflake size={20} className="text-gray-600" />
                </div>
                <div className="font-medium text-gray-800 text-lg">{data.label}</div>
              </div>
              <StatusIndicator status={currentStatus} />
            </div>

            {/* Metrics section with divider */}
            <div className="pt-3 mt-3 border-t border-gray-100">
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-base text-gray-500 mb-0.5">Power</div>
                  <div className="text-blue-600 font-semibold text-2xl">{data.power.toLocaleString()} <span className="text-base font-normal text-gray-500">kW</span></div>
                </div>
                <div className="text-right">
                  <div className="text-base text-gray-500 mb-0.5">Meters</div>
                  <div className="text-gray-700 font-medium text-2xl">{data.meterCount === 0 ? '-' : data.meterCount}</div>
                </div>
              </div>
            </div>

            {/* External link icon that appears on hover */}
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
              <ExternalLink size={20} className="text-blue-500" />
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>Click to view chiller plant meters</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Data Center node
const DataCenterNode: React.FC<NodeProps> = ({ data }) => {
  const currentStatus = data.status as NodeStatus;
  const statusStyle = statusBorderColors[currentStatus] || statusBorderColors.disconnected;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`${baseNodeStyle} relative ${statusStyle} group`}>
            <Handle
              type="target"
              position={Position.Left}
              className="!bg-blue-600 !w-2 !h-2 !border !border-white !z-10"
            />

            {/* Header section with icon and title */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <div className="bg-gray-100 p-2.5 rounded-md mr-3">
                  <Server size={20} className="text-gray-600" />
                </div>
                <div className="flex items-center gap-2">
                  <div className="font-medium text-gray-800 text-lg">{data.label || 'Data Center & Others'}</div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info size={16} className="text-blue-500" />
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-xs">
                        <p className="text-sm">
                          <strong>Virtual Meter (Calculated)</strong><br/>
                          This value is automatically calculated as:<br/>
                          Total Power - (Tower A + Tower B + Tower C + Tenants + Chiller Plant)
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
              <StatusIndicator status={currentStatus} />
            </div>

            {/* Metrics section with divider */}
            <div className="pt-3 mt-3 border-t border-gray-100">
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-base text-gray-500 mb-0.5">Power</div>
                  <div className="text-blue-600 font-semibold text-2xl">{data.power.toLocaleString()} <span className="text-base font-normal text-gray-500">kW</span></div>
                </div>
                <div className="text-right">
                  <div className="text-base text-gray-500 mb-0.5">Type</div>
                  <div className="flex items-center justify-end">
                    <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded-full">Virtual</span>
                  </div>
                </div>
              </div>
            </div>

            {/* External link icon that appears on hover */}
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
              <ExternalLink size={20} className="text-blue-500" />
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>This is a virtual meter showing calculated consumption from other unmeasured loads</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Export node types
const nodeTypes = {
  main: MainNode,
  tower: TowerNode,
  tenant: TenantNode,
  plant: PlantNode,
  datacenter: DataCenterNode
};

export { nodeTypes as meterNodeTypes };
