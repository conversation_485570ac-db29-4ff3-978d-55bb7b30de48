import React from 'react';
import { Wifi, WifiOff, AlertTriangle } from 'lucide-react';

const StatusLegend: React.FC = () => {
  return (
    <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg shadow-sm p-3 flex space-x-4">
      <div className="flex items-center">
        <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
        <span className="text-sm text-gray-700 flex items-center">
          <Wifi size={14} className="mr-1 text-green-600" /> Active
        </span>
      </div>
      
      <div className="flex items-center">
        <div className="w-3 h-3 rounded-full bg-yellow-500 animate-pulse mr-2"></div>
        <span className="text-sm text-gray-700 flex items-center">
          <AlertTriangle size={14} className="mr-1 text-yellow-600" /> Warning
        </span>
      </div>
      
      <div className="flex items-center">
        <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
        <span className="text-sm text-gray-700 flex items-center">
          <WifiOff size={14} className="mr-1 text-red-600" /> Offline
        </span>
      </div>
    </div>
  );
};

export default React.memo(StatusLegend);
