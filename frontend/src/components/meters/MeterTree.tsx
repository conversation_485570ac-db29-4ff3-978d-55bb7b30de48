import React from 'react';
import { TreeNode } from './types';
import { ChevronRight } from 'lucide-react';
import clsx from 'clsx';

interface MeterTreeProps {
  nodes: TreeNode[];
  expandedNodes: string[];
  selectedNodeId: string | null;
  onSelectNode: (node: TreeNode) => void;
  onToggleNode: (nodeId: string) => void;
  ariaLabel?: string; // For accessibility
  selectedMeterView?: string; // Add prop for context
}

export const MeterTree: React.FC<MeterTreeProps> = ({
  nodes,
  expandedNodes,
  selectedNodeId,
  onSelectNode,
  onToggleNode,
  ariaLabel = "Meter Hierarchy",
  selectedMeterView
}) => {
  // Recursive render function
  const renderNode = (node: TreeNode, level = 0): React.ReactNode => {

    const isSelected = selectedNodeId === node.id;
    const isExpanded = expandedNodes.includes(node.id);
    const hasChildren = node.children && node.children.length > 0;

    return (
      <li
        key={node.id}
        role="treeitem"
        aria-selected={isSelected}
        aria-expanded={hasChildren ? isExpanded : undefined}
        className={clsx(
          'border-b border-gray-100 last:border-b-0',
          isSelected && 'bg-blue-50'
        )}
      >
        <div
          className="flex items-center"
          style={{ paddingLeft: `${level * 8}px` }}
        >
          {hasChildren ? (
            <button
              type="button"
              aria-label={isExpanded ? 'Collapse' : 'Expand'}
              className="flex items-center justify-center p-1 focus:outline-none"
              onClick={(e) => {
                e.stopPropagation();
                onToggleNode(node.id);
              }}
            >
              <ChevronRight
                size={14}
                className={clsx(
                  'transition-transform duration-200',
                  isExpanded ? 'rotate-90' : '',
                  isSelected ? 'text-blue-600' : 'text-gray-400'
                )}
              />
            </button>
          ) : (
            <div className="w-6"></div> // Placeholder for alignment
          )}

          <button
            className={clsx(
              'flex-1 flex items-center px-2 py-1 text-xs text-left rounded-sm',
              isSelected ? 'text-blue-600 font-medium' : 'text-gray-700 hover:text-blue-600'
            )}
            onClick={() => {
              onSelectNode(node);
              // If node has children, also toggle expansion when clicking on the node name
              if (hasChildren) {
                onToggleNode(node.id);
              }
            }}
          >
            {/* Meter status dot for leaf nodes (meters) */}
            {(!node.children || node.children.length === 0) && (
              <span
                className={clsx(
                  'inline-block w-2 h-2 rounded-full mr-2',
                  node.status?.online === false ? 'bg-red-500' : 'bg-green-500'
                )}
                title={node.status?.online === false ? 'Offline' : 'Online'}
              />
            )}
            <span className="flex-1 truncate">{node.name}</span>
          </button>
        </div>

        {/* Render children if expanded */}
        {hasChildren && isExpanded && (
          <ul role="group" className="ml-2">
            {(node.children || []).map(child => renderNode(child, level + 1))}
          </ul>
        )}
      </li>
    );
  };

  return (
    <ul className="px-1 py-1" role="tree" aria-label={ariaLabel}>
      {nodes.map(node => renderNode(node))}
    </ul>
  );
};