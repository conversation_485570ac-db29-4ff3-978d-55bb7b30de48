import React, { useState, useEffect, useCallback } from 'react';
import { 
  Zap, 
  Activity, 
  ArrowUp, 
  ArrowDown, 
  Percent, 
  Trash2, 
  PlusCircle, 
  AlertTriangle, 
  CheckCircle, 
  Loader2,
  AlertCircle
} from 'lucide-react';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui"
import { Input } from "@/components/ui"
import { Button } from "@/components/ui"
import { ToggleSwitch } from "@/components/ui"
import { Label } from "@/components/ui"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui"
import { Badge } from '@/components/ui/badge';

// Import API functions and types
import {
    getThresholdsForMeter,
    addThresholdForMeter,
    updateThreshold,
    deleteThreshold
} from '../../lib/api/meters';
import type { Threshold, ThresholdInput } from '../../types/models';
import type { TreeNode } from './types'; 

interface MeterThresholdsProps {
  node: TreeNode | null; 
}

const parameterOptions = [
    { value: 'power_demand', label: 'Power Demand (kW)', icon: Zap },
    { value: 'voltage', label: 'Voltage (V)', icon: Activity },
    { value: 'current', label: 'Current (A)', icon: ArrowDown }, 
    { value: 'power_factor', label: 'Power Factor', icon: Percent },
    { value: 'energy_consumption', label: 'Energy (kWh)', icon: ArrowUp } 
];

const conditionOptions = [
    { value: '>', label: '>' },
    { value: '<', label: '<' },
    { value: '>=', label: '>=' },
    { value: '<=', label: '<=' },
];

const severityOptions = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'critical', label: 'Critical' },
];

// Helper functions for threshold display
const getParameterLabel = (parameter: string) => {
  const option = parameterOptions.find(opt => opt.value === parameter);
  return option ? option.label : parameter;
};

const getParameterUnit = (parameter: string) => {
  switch (parameter) {
    case 'power_demand':
      return 'kW';
    case 'voltage':
      return 'V';
    case 'current':
      return 'A';
    case 'power_factor':
      return '';
    case 'energy_consumption':
      return 'kWh';
    case 'temperature':
      return '°C';
    default:
      return '';
  }
};

const getConditionLabel = (condition: string) => {
  switch (condition) {
    case '>':
      return 'Greater than';
    case '<':
      return 'Less than';
    case '>=':
      return 'Greater than or equal to';
    case '<=':
      return 'Less than or equal to';
    case '==':
      return 'Equal to';
    default:
      return condition;
  }
};

const getSeverityVariant = (severity: string) => {
  switch (severity.toLowerCase()) {
    case 'critical':
      return 'destructive';
    case 'high':
      return 'default'; // Using default instead of warning since it's not in the available types
    case 'medium':
      return 'secondary';
    case 'low':
      return 'outline';
    default:
      return 'default';
  }
};

const MeterThresholds: React.FC<MeterThresholdsProps> = ({ node }) => {
  const selectedMeterId = node?.id; 
  const [thresholds, setThresholds] = useState<Threshold[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Add specific loading states for different operations
  const [isAddingThreshold, setIsAddingThreshold] = useState<boolean>(false);
  const [isUpdatingThreshold, setIsUpdatingThreshold] = useState<number | null>(null);
  const [isDeletingThreshold, setIsDeletingThreshold] = useState<number | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string | undefined>>({});

  // State for the "Add Threshold" form
  const [newThreshold, setNewThreshold] = useState<Omit<ThresholdInput, 'meter'>>({
    parameter: 'power_demand',
    condition: '>',
    value: 0,
    severity: 'medium',
    enabled: true,
  });

  // Callback to fetch thresholds for the selected meter
  const fetchThresholds = useCallback(async () => {
    if (!selectedMeterId) {
      setThresholds([]); 
      return;
    }
    setLoading(true);
    setError(null);
    setSuccess(null);
    try {
      console.log(`Fetching thresholds for meter: ${selectedMeterId}`);
      const data = await getThresholdsForMeter(selectedMeterId);
      console.log('Fetched thresholds:', data);
      setThresholds(data || []);
    } catch (err: any) {
      console.error('Error fetching meter thresholds:', err);
      console.error('Error response data:', err.response?.data);
      console.error('Error status:', err.response?.status);
      const errorMessage = err.response?.data?.detail || 'Failed to load thresholds. Please try again.';
      setError(errorMessage);
      setThresholds([]); 
    } finally {
      setLoading(false);
    }
  }, [selectedMeterId]);

  // Fetch thresholds when the selected meter changes
  useEffect(() => {
    fetchThresholds();
  }, [fetchThresholds]);

  // Auto-clear success message after 5 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  // Validate form before submission
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (newThreshold.value === undefined || newThreshold.value === null) {
      errors.value = 'Value is required';
    }
    
    // Add parameter-specific validations
    if (newThreshold.parameter === 'power_factor' && (newThreshold.value < -1 || newThreshold.value > 1)) {
      errors.value = 'Power Factor must be between -1 and 1';
    }
    
    if (newThreshold.parameter === 'voltage' && newThreshold.value <= 0) {
      errors.value = 'Voltage must be greater than 0';
    }
    
    if (newThreshold.parameter === 'current' && newThreshold.value < 0) {
      errors.value = 'Current cannot be negative';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handler for adding a new threshold
  const handleAddThreshold = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!selectedMeterId) {
        setError("No meter selected to add threshold to.");
        return;
    }
    
    // Validate form before submission
    if (!validateForm()) {
      return;
    }
    
    setIsAddingThreshold(true);
    setError(null);
    setSuccess(null);
    try {
      await addThresholdForMeter(selectedMeterId, newThreshold);
      // Refresh the list after adding
      await fetchThresholds();
      setSuccess('Threshold added successfully!');
      // Reset form
      setNewThreshold({
        parameter: 'power_demand',
        condition: '>',
        value: 0,
        severity: 'medium',
        enabled: true,
      });
      setFormErrors({});
    } catch (err: any) {
      console.error('Error adding threshold:', err);
      const errorMessage = err.response?.data?.detail || 'Failed to add threshold. Please try again.';
      setError(errorMessage);
    } finally {
      setIsAddingThreshold(false);
    }
  };

  // Handler for updating a threshold (specifically toggling enabled status)
  const handleToggleThreshold = async (thresholdId: number, currentEnabledStatus: boolean) => {
    if (!selectedMeterId) return;
    setIsUpdatingThreshold(thresholdId); 
    setError(null);
    setSuccess(null);
    try {
      await updateThreshold(selectedMeterId, thresholdId, { enabled: !currentEnabledStatus });
      // Refresh list to show the change
      await fetchThresholds();
      setSuccess('Threshold status updated.');
    } catch (err: any) {
      console.error('Error toggling threshold:', err);
      const errorMessage = err.response?.data?.detail || 'Failed to update threshold status.';
      setError(errorMessage);
    } finally {
      setIsUpdatingThreshold(null);
    }
  };

  // Handler for deleting a threshold
  const handleDeleteThreshold = async (thresholdId: number) => {
    if (!selectedMeterId) return;
    if (!window.confirm('Are you sure you want to delete this threshold?')) return;

    setIsDeletingThreshold(thresholdId); 
    setError(null);
    setSuccess(null);
    try {
      await deleteThreshold(selectedMeterId, thresholdId);
      // Refresh list after deleting
      await fetchThresholds();
      setSuccess('Threshold deleted successfully.');
    } catch (err: any) {
      console.error('Error deleting threshold:', err);
      const errorMessage = err.response?.data?.detail || 'Failed to delete threshold. Please try again.';
      setError(errorMessage);
    } finally {
      setIsDeletingThreshold(null);
    }
  };

  // Render error message if present
  const renderError = () => {
    if (!error) return null;
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  };

  // Render success message if present
  const renderSuccess = () => {
    if (!success) return null;
    return (
      <Alert variant="default" className="bg-green-100 border-green-300 text-green-800">
        <CheckCircle className="h-4 w-4" />
        <AlertTitle>Success</AlertTitle>
        <AlertDescription>{success}</AlertDescription>
      </Alert>
    );
  };

  if (!selectedMeterId) {
    return <div className="p-4 text-center text-gray-500">Select a meter node to view and configure thresholds.</div>;
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle>Thresholds for {node?.name || selectedMeterId}</CardTitle>
      </CardHeader>
      <CardContent className="flex-grow overflow-y-auto space-y-6">
        {/* Display Messages */} 
        {renderError()}
        {renderSuccess()}
        {/* Add Threshold Form */} 
        <form onSubmit={handleAddThreshold} className="space-y-4 p-4 border rounded-md bg-gray-50">
           <h3 className="text-lg font-semibold mb-2">Add New Threshold</h3>
           <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
             <div>
               <Label htmlFor="parameter">Parameter</Label>
               <Select 
                 value={newThreshold.parameter} 
                 onValueChange={(value) => setNewThreshold({...newThreshold, parameter: value as any})}
               >
                 <SelectTrigger id="parameter">
                   <SelectValue placeholder="Select parameter" />
                 </SelectTrigger>
                 <SelectContent>
                   {parameterOptions.map(option => (
                     <SelectItem key={option.value} value={option.value}>
                       <div className="flex items-center">
                         <option.icon className="mr-2 h-4 w-4" />
                         <span>{option.label}</span>
                       </div>
                     </SelectItem>
                   ))}
                 </SelectContent>
               </Select>
               {formErrors.parameter && <p className="text-red-500 text-sm mt-1">{formErrors.parameter}</p>}
             </div>
             
             <div>
               <Label htmlFor="condition">Condition</Label>
               <Select 
                 value={newThreshold.condition} 
                 onValueChange={(value) => setNewThreshold({...newThreshold, condition: value as any})}
               >
                 <SelectTrigger id="condition">
                   <SelectValue placeholder="Select condition" />
                 </SelectTrigger>
                 <SelectContent>
                   {conditionOptions.map(option => (
                     <SelectItem key={option.value} value={option.value}>
                       {option.label}
                     </SelectItem>
                   ))}
                 </SelectContent>
               </Select>
             </div>
             
             <div>
               <Label htmlFor="value">Value</Label>
               <Input 
                 id="value" 
                 type="number" 
                 value={newThreshold.value.toString()} 
                 onChange={(e) => setNewThreshold({...newThreshold, value: parseFloat(e.target.value) || 0})}
                 className={formErrors.value ? "border-red-500" : ""}
               />
               {formErrors.value && <p className="text-red-500 text-sm mt-1">{formErrors.value}</p>}
             </div>
           </div>
           
           <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
             <div>
               <Label htmlFor="severity">Severity</Label>
               <Select 
                 value={newThreshold.severity} 
                 onValueChange={(value) => setNewThreshold({...newThreshold, severity: value as any})}
               >
                 <SelectTrigger id="severity">
                   <SelectValue placeholder="Select severity" />
                 </SelectTrigger>
                 <SelectContent>
                   {severityOptions.map(option => (
                     <SelectItem key={option.value} value={option.value}>
                       {option.label}
                     </SelectItem>
                   ))}
                 </SelectContent>
               </Select>
             </div>
             
             <div className="flex items-center space-x-2 mt-8">
               <ToggleSwitch
                 checked={newThreshold.enabled}
                 onChange={() => setNewThreshold({...newThreshold, enabled: !newThreshold.enabled})}
                 label="Enabled"
               />
             </div>
           </div>
           
           <div className="mt-4 flex justify-end">
             <Button type="submit" disabled={isAddingThreshold}>
               {isAddingThreshold ? (
                 <>
                   <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                   Adding...
                 </>
               ) : (
                 <>
                   <PlusCircle className="mr-2 h-4 w-4" />
                   Add Threshold
                 </>
               )}
             </Button>
           </div>
         </form>

        {/* Threshold List */} 
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Configured Thresholds</h3>
          {loading ? (
            <div className="p-8 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-primary" />
              <p className="text-gray-500">Loading thresholds...</p>
            </div>
          ) : thresholds.length > 0 ? (
            <div className="space-y-2">
              {thresholds.map((threshold) => (
                <div key={threshold.id} className="flex items-center justify-between p-3 border rounded-md">
                  <div className="flex-grow">
                    <div className="flex items-center space-x-2">
                      <Badge variant={getSeverityVariant(threshold.severity)}>{threshold.severity}</Badge>
                      <span className="font-medium">{getParameterLabel(threshold.parameter)}</span>
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {getConditionLabel(threshold.condition)} {threshold.value} {getParameterUnit(threshold.parameter)}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ToggleSwitch
                      checked={threshold.enabled} 
                      onChange={() => handleToggleThreshold(threshold.id, !threshold.enabled)}
                      label="Enabled"
                      disabled={isUpdatingThreshold === threshold.id}
                    />
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={() => handleDeleteThreshold(threshold.id)}
                      disabled={isDeletingThreshold === threshold.id}
                    >
                      {isDeletingThreshold === threshold.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="h-4 w-4 text-red-500" />
                      )}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center border rounded-md bg-gray-50">
              <AlertCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
              <p className="text-gray-500">No thresholds configured for this meter yet.</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default MeterThresholds;
