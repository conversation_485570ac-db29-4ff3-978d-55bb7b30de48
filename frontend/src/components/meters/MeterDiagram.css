/* ReactFlow specific styles */
.react-flow {
  height: 100% !important;
  width: 100% !important;
  background: white;
  border: none !important;
}

.react-flow__container {
  height: 100% !important;
  width: 100% !important;
}

.react-flow__renderer {
  height: 100% !important;
  width: 100% !important;
}

.react-flow__pane {
  height: 100% !important;
  width: 100% !important;
}

/* Node styles */
.react-flow__node {
  transition: all 0.2s ease;
  cursor: pointer;
}

.react-flow__node:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  filter: brightness(1.05);
}

.react-flow__node:active {
  transform: translateY(0);
  filter: brightness(0.95);
}

/* Edge styles */
.react-flow__edge-path {
  stroke-width: 1.5;
}

/* Remove any bottom borders */
.react-flow__edge {
  border-bottom: none !important;
}

.react-flow__container {
  border-bottom: none !important;
}

.react-flow__edge.animated .react-flow__edge-path {
  stroke-dasharray: 5;
  animation: flowAnimation 1s infinite linear;
}

@keyframes flowAnimation {
  from {
    stroke-dashoffset: 10;
  }
  to {
    stroke-dashoffset: 0;
  }
}

/* Controls */
.react-flow__controls {
  bottom: 20px;
  right: 20px;
  left: auto;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.react-flow__controls-button {
  background-color: white;
  border: none;
  border-bottom: 1px solid #f0f0f0;
  box-sizing: content-box;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 16px;
  height: 16px;
  padding: 5px;
  color: #3B82F6;
}

.react-flow__controls-button:hover {
  background-color: #f5f7ff;
}

/* Background */
.react-flow__background {
  background-size: 20px 20px;
  background-image: radial-gradient(#e5e7eb 0.5px, transparent 0.5px);
  opacity: 0.5;
}

/* Remove bottom border line */
.react-flow__pane {
  border-bottom: none !important;
}

/* Remove all borders from the diagram */
.react-flow__renderer {
  border: none !important;
}

.react-flow__edge-path {
  stroke-width: 1.5;
  stroke-dasharray: none;
}

/* Remove x-axis line */
.react-flow__edge-path[d*="M0"] {
  display: none !important;
}

/* View Toggle */
.react-flow__view-toggle-container {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  width: auto;
  z-index: 10;
  pointer-events: all;
}

.react-flow__view-toggle {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  background-color: white;
  border-radius: 4px;
  border: none; /* Remove border */
}

/* Make React Flow handles (dots) invisible by default, visible on node hover/focus */
.react-flow__handle {
  opacity: 0;
  transition: opacity 0.2s;
}

.react-flow__node:hover .react-flow__handle,
.react-flow__node:focus .react-flow__handle,
.react-flow__node.selected .react-flow__handle {
  opacity: 0.4; /* Subtle, adjust as needed */
}
