import React from 'react';
import Live<PERSON><PERSON><PERSON><PERSON> from './LiveMeterChart';
import { Tab } from '@headlessui/react';

// Meter types and their corresponding units
const METER_UNITS = {
  'Lighting': 'kW',
  'Power': 'kW',
  'Receptacle': 'kW',
  'airSide': 'kW',
  'Load Center': 'kW',
  'Emergency': 'kW',
  'Drainage Pumps': 'kW',
  'Water Treatment': 'kW'
};

interface LiveMeterReadingsProps {
  meterId: string;
  meterName?: string;
  meterType?: string;
}

const LiveMeterReadings: React.FC<LiveMeterReadingsProps> = ({
  meterId,
  meterName = meterId,
  meterType = 'Power'
}) => {
  // Determine the unit based on meter type, but we'll use fixed units for the tabs
  // This is just for reference if needed in future extensions
  const _unit = METER_UNITS[meterType as keyof typeof METER_UNITS] || 'kW';

  return (
    <div className="bg-white rounded-lg shadow p-4 mb-6">
      <h2 className="text-xl font-semibold mb-4">{meterName} Live Readings</h2>
      
      <Tab.Group>
        <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1 mb-4">
          <Tab 
            className={({ selected }: { selected: boolean }) =>
              `w-full rounded-lg py-2.5 text-sm font-medium leading-5 
              ${selected 
                ? 'bg-white text-blue-700 shadow' 
                : 'text-blue-500 hover:bg-white/[0.12] hover:text-blue-600'
              }`
            }
          >
            Power (kW)
          </Tab>
          <Tab 
            className={({ selected }: { selected: boolean }) =>
              `w-full rounded-lg py-2.5 text-sm font-medium leading-5 
              ${selected 
                ? 'bg-white text-blue-700 shadow' 
                : 'text-blue-500 hover:bg-white/[0.12] hover:text-blue-600'
              }`
            }
          >
            Energy (kWh)
          </Tab>
        </Tab.List>
        
        <Tab.Panels>
          <Tab.Panel>
            <LiveMeterChart 
              meterId={meterId} 
              title={`${meterName} - Real-time Power Demand`}
              unit="kW"
              color="#3B82F6" // Blue
              maxDataPoints={100}
            />
          </Tab.Panel>
          <Tab.Panel>
            <LiveMeterChart 
              meterId={meterId} 
              title={`${meterName} - Energy Consumption`}
              unit="kWh"
              color="#10B981" // Green
              maxDataPoints={24} // Fewer points for energy consumption (e.g., hourly)
            />
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
      
      <div className="mt-4 text-sm text-gray-500">
        <p>Data updates in real-time from Tridium Niagara via Supabase.</p>
      </div>
    </div>
  );
};

export default LiveMeterReadings;
