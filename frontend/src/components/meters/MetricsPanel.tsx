import React from 'react';
import { Activity, Zap } from 'lucide-react';
import { COLORS } from '../../constants/colors';

interface MetricsPanelProps {
  totalMeters: number;
  activeMeters: number;
  warningMeters: number;
  disconnectedMeters: number;
  totalPower: number;
}

// Component for displaying system-wide metrics
const MetricsPanel: React.FC<MetricsPanelProps> = ({
  totalMeters,
  activeMeters,
  warningMeters,
  disconnectedMeters,
  totalPower
}) => {
  const activePercentage = Math.round((activeMeters / totalMeters) * 100);
  const warningPercentage = Math.round((warningMeters / totalMeters) * 100);
  const disconnectedPercentage = Math.round((disconnectedMeters / totalMeters) * 100);

  return (
    <div className="absolute top-4 right-4 bg-white border border-gray-200 rounded-lg shadow-sm p-4 w-72">
      <h3 className="text-lg font-semibold text-gray-800 mb-2 flex items-center">
        <Activity size={18} className="mr-2 text-blue-600" />
        System Metrics
      </h3>
      
      <div className="mb-3">
        <div className="flex justify-between text-sm mb-1">
          <span className="text-gray-600">Total System Load</span>
          <span className="font-semibold text-gray-800">{totalPower.toLocaleString()} kW</span>
        </div>
        <div className="w-full bg-gray-100 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-full h-2" 
            style={{ width: '100%' }}
          ></div>
        </div>
      </div>

      <div className="mb-3">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Total Meters</span>
          <span className="font-semibold text-gray-800">{totalMeters}</span>
        </div>
        
        <div className="grid grid-cols-3 gap-2 mt-2">
          <div className="text-center p-2 bg-green-50 rounded-md">
            <div className="text-green-600 font-medium">{activePercentage}%</div>
            <div className="text-xs text-gray-600">Active</div>
          </div>
          <div className="text-center p-2 bg-yellow-50 rounded-md">
            <div className="text-yellow-600 font-medium">{warningPercentage}%</div>
            <div className="text-xs text-gray-600">Warning</div>
          </div>
          <div className="text-center p-2 bg-red-50 rounded-md">
            <div className="text-red-600 font-medium">{disconnectedPercentage}%</div>
            <div className="text-xs text-gray-600">Offline</div>
          </div>
        </div>
      </div>

      <div className="text-xs text-gray-500 mt-2">
        Last updated: {new Date().toLocaleString('th-TH', { 
          timeZone: 'Asia/Bangkok',
          hour: '2-digit',
          minute: '2-digit',
          day: '2-digit',
          month: 'short',
          year: 'numeric'
        })}
      </div>
    </div>
  );
};

export default React.memo(MetricsPanel);
