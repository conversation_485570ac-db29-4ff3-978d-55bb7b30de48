import { useState, useEffect, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import ViewToggle, { ViewMode } from '../common/ViewToggle';
import './MeterDiagram.css';
// Removed service function imports as data fetching is moved to parent

// MeterGroupData and helper functions (getNestedValue, getStatusFromPower) are removed
// as they are now managed by MeterDiagramPage.tsx

// VIEW_METER_GROUP_CONFIG is removed as it's now in MeterDiagramPage.tsx

interface MeterGroupData {
    id: string;
    label: string;
    meterCount: number;
    power: number;
    status: 'active' | 'warning' | 'disconnected';
}

// Props for InfoSidebar - defined here to be used by the exported MeterInfoSidebar
// This component will receive its data from MeterDiagramPage
interface InfoSidebarProps {
  meterGroupsData: MeterGroupData[];
  totalMetersCount: number;
  loading: boolean;
  error?: string | null;
}

// Component for the main SVG diagram (remains mostly static as per current structure)
const DiagramSvg = memo(() => {
  const navigate = useNavigate();
  // State for image loading
  const [imageLoaded, setImageLoaded] = useState(false);

  // Navigation handlers
  const handleAreaClick = (system: string) => {
    // For tower systems, navigate to Meters page with tower filter
    if (system.startsWith('tower-')) {
      navigate(`/meters?view=${system}`);
    } else if (system === 'main') {
      // Main Distribution (combined with Power Input)
      navigate('/meters?view=main');
    } else if (system === 'tenant') {
      navigate('/meters?view=tenant');
    } else if (system === 'chiller-plant') {
      navigate('/meters?view=chiller-plant');
    } else if (system === 'data-center-others') {
      navigate('/meters?view=data-center-others');
    } else {
      navigate(`/meter-detail?system=${system}`);
    }
  };

  // Preload image for faster rendering
  useEffect(() => {
    const img = new Image();
    img.src = '/assets/images/MeterDiagramLayout.svg';
    img.onload = () => setImageLoaded(true);
  }, []);
  if (!imageLoaded) {
    return <div className="text-gray-500">Loading diagram...</div>;
  }
  return (
    <div className="flex items-center h-full justify-start pl-16 relative">
      <img
        src="/assets/images/MeterDiagramLayout.svg"
        alt="Electricity Meter Diagram"
        className="w-[90%] h-[90%] object-contain"
        decoding="async"
        {...{ fetchpriority: "high" }}
      />

      {/* Clickable areas overlaid on the SVG - positions are approximate and would need adjustment */}
      <div className="absolute inset-0 w-[90%] h-[90%]">
        {/* Main Distribution (Combined with Power Input) */}
        <div
          className="absolute cursor-pointer hover:bg-blue-500/10 transition-colors rounded-md"
          style={{ top: '40%', left: '20%', width: '20%', height: '10%' }}
          onClick={() => handleAreaClick('main')}
          title="Click to view main distribution meters"
        />

        {/* Tower A */}
        <div
          className="absolute cursor-pointer hover:bg-blue-500/10 transition-colors rounded-md"
          style={{ top: '20%', left: '60%', width: '15%', height: '10%' }}
          onClick={() => handleAreaClick('tower-a')}
          title="Click to view Tower A meters"
        />

        {/* Tower B */}
        <div
          className="absolute cursor-pointer hover:bg-blue-500/10 transition-colors rounded-md"
          style={{ top: '35%', left: '60%', width: '15%', height: '10%' }}
          onClick={() => handleAreaClick('tower-b')}
          title="Click to view Tower B meters"
        />

        {/* Tower C */}
        <div
          className="absolute cursor-pointer hover:bg-blue-500/10 transition-colors rounded-md"
          style={{ top: '50%', left: '60%', width: '15%', height: '10%' }}
          onClick={() => handleAreaClick('tower-c')}
          title="Click to view Tower C meters"
        />

        {/* Tenant */}
        <div
          className="absolute cursor-pointer hover:bg-blue-500/10 transition-colors rounded-md"
          style={{ top: '65%', left: '60%', width: '15%', height: '10%' }}
          onClick={() => handleAreaClick('tenant')}
          title="Click to view all tenant meters across towers"
        />

        {/* Chiller Plant */}
        <div
          className="absolute cursor-pointer hover:bg-blue-500/10 transition-colors rounded-md"
          style={{ top: '80%', left: '60%', width: '15%', height: '10%' }}
          onClick={() => handleAreaClick('chiller-plant')}
          title="Click to view chiller plant meters"
        />

        {/* Data Center & Others */}
        <div
          className="absolute cursor-pointer hover:bg-blue-500/10 transition-colors rounded-md"
          style={{ top: '95%', left: '60%', width: '15%', height: '10%' }}
          onClick={() => handleAreaClick('data-center-others')}
          title="Click to view Data Center & Others meters"
        />
      </div>
    </div>
  );
});

// Component for the combined metrics and status info sidebar
// This is now a presentational component receiving all data via props.
const InfoSidebar = memo(({ meterGroupsData, totalMetersCount, loading, error }: InfoSidebarProps) => {

  if (loading) {
    return <div className="p-3 text-sm text-gray-500">Loading system metrics...</div>;
  }

  if (error) {
    return <div className="p-3 text-sm text-red-500">Error loading data: {error}</div>;
  }

  const mainPower = meterGroupsData.find(g => g.id === 'main')?.power || 0;
  const activeMeters = meterGroupsData.filter(g => g.status === 'active').length;
  const warningMeters = meterGroupsData.filter(g => g.status === 'warning').length;
  const offlineMeters = meterGroupsData.filter(g => g.status === 'disconnected').length;
  const totalGroups = meterGroupsData.length;

  const activePercent = totalGroups > 0 ? Math.round((activeMeters / totalGroups) * 100) : 0;
  const warningPercent = totalGroups > 0 ? Math.round((warningMeters / totalGroups) * 100) : 0;
  const offlinePercent = totalGroups > 0 ? Math.round((offlineMeters / totalGroups) * 100) : 0;

  return (
    <div className="space-y-4">
      {/* Power Summary Panel */}
      <div className="bg-gradient-to-br from-blue-50/50 to-transparent rounded-lg p-4 border border-blue-100">
        <h3 className="text-sm font-semibold text-gray-800 mb-3 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-activity mr-2 text-blue-600">
            <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
          </svg>
          Power Summary
        </h3>
        <div className="space-y-3">
          <div className="flex justify-between items-center p-3 bg-white rounded-lg border border-gray-100">
            <span className="text-sm text-gray-600">Total System Load</span>
            <span className="text-lg font-bold text-gray-900">{mainPower.toLocaleString()} kW</span>
          </div>
          <div className="flex justify-between items-center p-3 bg-white rounded-lg border border-gray-100">
            <span className="text-sm text-gray-600">Total Meters</span>
            <span className="text-lg font-bold text-gray-900">{totalMetersCount}</span>
          </div>
        </div>
        
        <div className="mt-4">
          <div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">System Status</div>
          <div className="grid grid-cols-3 gap-2">
            <div className="text-center p-3 bg-green-50 rounded-lg border border-green-100">
              <div className="text-2xl font-bold text-green-700">{activePercent}%</div>
              <div className="text-xs text-green-600 mt-1">Active</div>
            </div>
            <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-100">
              <div className="text-2xl font-bold text-yellow-700">{warningPercent}%</div>
              <div className="text-xs text-yellow-600 mt-1">Warning</div>
            </div>
            <div className="text-center p-3 bg-red-50 rounded-lg border border-red-100">
              <div className="text-2xl font-bold text-red-700">{offlinePercent}%</div>
              <div className="text-xs text-red-600 mt-1">Offline</div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Meter Groups Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-4 py-3 bg-gradient-to-r from-gray-50 to-transparent border-b border-gray-100">
          <h3 className="text-sm font-semibold text-gray-800">Meter Groups</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Group</th>
                <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Meters</th>
                <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Power</th>
              </tr>
            </thead>
              <tbody className="divide-y divide-gray-200">
                {meterGroupsData.map((group, idx) => (
                  <tr
                    key={group.id}
                    className={
                      group.status === 'warning'
                        ? 'bg-yellow-50 hover:bg-yellow-100 transition-colors'
                        : group.status === 'disconnected'
                        ? 'bg-red-50 hover:bg-red-100 transition-colors'
                        : 'hover:bg-gray-50 transition-colors'
                    }
                  >
                    <td className="px-4 py-3">
                      <div className="flex items-center">
                        <div className={`w-2 h-2 rounded-full mr-2 flex-shrink-0 ${
                          group.status === 'active' ? 'bg-green-500' :
                          group.status === 'warning' ? 'bg-yellow-500 animate-pulse' : 'bg-red-500'
                        }`}></div>
                        <span className="font-medium text-gray-900">{group.label}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 text-right text-gray-600">
                      {group.meterCount > 0 ? group.meterCount : '-'}
                    </td>
                    <td className="px-4 py-3 text-right font-semibold text-gray-900">
                      {group.power.toLocaleString()} kW
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
      </div>

    </div>
  );
});

interface MeterDiagramViewComponentProps {
  viewMode?: ViewMode;
  onViewChange?: (view: ViewMode) => void;
  onNodeSelect?: (nodeId: string) => void;
}

function MeterDiagramViewComponent({ viewMode, onViewChange, onNodeSelect }: MeterDiagramViewComponentProps) {
  return (
    <div style={{ width: '100%', height: '100%', position: 'relative' }}>
      <DiagramSvg />
      <div className="react-flow__view-toggle-container">
        <ViewToggle
          currentView={viewMode || '3d'}
          onChange={onViewChange || (() => {})}
          className="react-flow__view-toggle"
        />
      </div>
      {/* The InfoSidebar is now rendered by MeterDiagramPage and passed data directly */}
    </div>
  );
}

// Export the InfoSidebar for use in MeterDiagramPage.tsx.
// It receives its data directly from MeterDiagramPage.
export const MeterInfoSidebar = InfoSidebar;

export const MeterDiagramView = memo(MeterDiagramViewComponent);