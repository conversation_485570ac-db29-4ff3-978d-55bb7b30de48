import React, { useState, useEffect, useMemo } from 'react';
import { Zap, AlertTriangle, BarChart2, Activity, Gauge, TrendingUp, Info } from 'lucide-react';
import { formatNumber } from '../../lib/utils/formatters';
import RuleDetailDrawer from '@/components/alarms/RuleDetailDrawer';
import type { TreeNode } from './types';
import { useToast } from '@/components/ui/ToastProvider';
import { useAutoRefreshContext } from '../../lib/contexts/AutoRefreshContext';
import { getMeterImagePath, DEFAULT_METER_IMAGE } from '../../lib/config/meter-images';
import { fetchStatisticalData, HistoricalDataQuery, fetchEnergyData } from '../../services/timescaleServiceMock';
import type { StatisticalRecord, EnergyRecord, HistoricalDataRecord as TimescaleHistoricalDataRecord } from '../../services/timescaleServiceMock';
import { fetchDeviceById, DeviceDetails } from '../../services/deviceServiceMock'; // Using mock-enabled device service
import { fetchSiteData } from '../../services/siteServiceMock'; // Import fetchSiteData
import { DateTime } from 'luxon'; // Import Luxon for timezone handling

// Import chart components directly (temporary fix)
import { DailyLoadChart } from '../charts/DailyLoadChart';
import { MonthlyLoadChart } from '../charts/MonthlyLoadChart';

// Simple chart skeleton loader
const ChartSkeleton = () => (
  <div className="animate-pulse flex flex-col w-full h-full">
    <div className="h-4 bg-gray-200 rounded w-1/4 mb-3"></div>
    <div className="flex-1 bg-gray-200 rounded"></div>
  </div>
);

interface MeterDetailsProps {
  node: TreeNode | null;
}

const MeterDetails = ({ node }: MeterDetailsProps) => {
  const { showToast } = useToast();

  const [isRuleDrawerOpen, setIsRuleDrawerOpen] = React.useState(false);
  const [meterImage, setMeterImage] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'basic' | 'advanced' | 'power_quality'>('basic');

  // State for site timezone
  const [siteTimezone, setSiteTimezone] = useState<string | null>(null);
  const [isSiteTimezoneLoading, setIsSiteTimezoneLoading] = useState<boolean>(true);
  const [siteTimezoneError, setSiteTimezoneError] = useState<string | null>(null);

  // State for Real-time Power Demand chart
  const [realtimePowerData, setRealtimePowerData] = useState<{ time: string; demand: number; }[]>([]);
  const [isRealtimePowerLoading, setIsRealtimePowerLoading] = useState(true);
  const [realtimePowerError, setRealtimePowerError] = useState<string | null>(null);

  // State for Daily Energy Consumption chart (now fetching daily data)
  const [dailyEnergyApiResult, setDailyEnergyApiResult] = useState<TimescaleHistoricalDataRecord<EnergyRecord> | null>(null);
  const [isDailyEnergyLoading, setIsDailyEnergyLoading] = useState(true);
  const [dailyEnergyError, setDailyEnergyError] = useState<string | null>(null);
  const [dailyEnergyForChart, setDailyEnergyForChart] = useState<{ time: string; demand: number; }[]>([]);

  // State for Device Details (Status & Metrics)
  const [deviceDetailsData, setDeviceDetailsData] = useState<DeviceDetails | null>(null);
  const [isDeviceDetailsLoading, setIsDeviceDetailsLoading] = useState(true);
  const [deviceDetailsError, setDeviceDetailsError] = useState<string | null>(null);

  // Handle Create Rule success - called when rule is created
  const handleRuleSuccess = () => {
    showToast({
      message: "Rule created successfully!",
      type: 'success',
    });
    setIsRuleDrawerOpen(false);
  };

  // Image upload functionality has been removed
  // We now use a fixed image for all meters

  // Reset loading state when node changes to show loading indicator
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    setLoading(true);
    const timer = setTimeout(() => setLoading(false), 100); // Reduced from 500ms to 100ms
    return () => clearTimeout(timer);
  }, [node?.id]); // Only reset loading when node changes

  // Load the meter image when component mounts
  useEffect(() => {
    // Get the image from our simplified store
    const image = getMeterImagePath();
    if (image) {
      setMeterImage(image);
    }
  }, []);


  // Get refresh state from context
  const { isAutoRefreshEnabled, refreshInterval } = useAutoRefreshContext();

  // Use refresh interval only if auto-refresh is enabled
  const effectiveRefreshInterval = isAutoRefreshEnabled ? refreshInterval : 0; // 0 means no auto-refresh

  // Generate stable mock phase data
  const mockPhaseData = useMemo(() => {
    // Use a seed based on node ID for consistent values per meter
    const seed = node?.id ? node.id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) : 1;
    const seededRandom = (min: number, max: number, offset: number) => {
      const x = Math.sin(seed + offset) * 10000;
      return min + (x - Math.floor(x)) * (max - min);
    };
    
    // Get base power value, ensure it's a valid number
    const rawPower = deviceDetailsData?.latest_data?.power;
    const basePower = (typeof rawPower === 'number' && !isNaN(rawPower)) ? rawPower : 450;
    
    // Generate phase power distribution with realistic imbalance
    const l1Factor = 0.33 + seededRandom(-0.01, 0.01, 1);
    const l2Factor = 0.34 + seededRandom(-0.01, 0.01, 2);
    const l3Factor = 1 - l1Factor - l2Factor; // Ensure total equals 100%
    
    // Calculate phase powers
    const l1Power = basePower * l1Factor;
    const l2Power = basePower * l2Factor;
    const l3Power = basePower * l3Factor;
    
    // Generate THD values within normal operating ranges
    const voltageThd = {
      l1: seededRandom(2.1, 3.6, 3),
      l2: seededRandom(2.3, 3.8, 4),
      l3: seededRandom(2.0, 3.5, 5),
    };
    
    const currentThd = {
      l1: seededRandom(5.5, 9.5, 6),
      l2: seededRandom(6.0, 10.0, 7),
      l3: seededRandom(5.8, 9.8, 8),
    };
    
    return {
      phasePower: {
        l1: isNaN(l1Power) ? 148.5 : l1Power,
        l2: isNaN(l2Power) ? 153.0 : l2Power,
        l3: isNaN(l3Power) ? 148.5 : l3Power,
      },
      voltageThd,
      currentThd,
    };
  }, [deviceDetailsData?.latest_data?.power, node?.id]); // Regenerate when power or node changes

  // Fetch site timezone using fetchAllSites with expand metadata
  useEffect(() => {
    const loadSiteTimezone = async () => {
      setIsSiteTimezoneLoading(true);
      
      try {
        const siteData = await fetchSiteData('set');
        
        if (siteData && siteData.metadata && siteData.metadata.timezone) {
          setSiteTimezone(siteData.metadata.timezone);
        } else {
          // Fallback to default timezone
          setSiteTimezone('Asia/Bangkok');
        }
      } catch (error) {
        console.error("Failed to fetch site timezone:", error);
        // Fallback to default timezone
        setSiteTimezone('Asia/Bangkok');
        setSiteTimezoneError(error instanceof Error ? error.message : "Failed to fetch site timezone");
      } finally {
        setIsSiteTimezoneLoading(false);
      }
    };
    loadSiteTimezone();
  }, []);

  // Helper function to create DateTime in site timezone and convert to UTC
  const createSiteDateTime = (year: number, month: number, day: number, hour: number = 0, minute: number = 0, second: number = 0): DateTime => {
    return DateTime.fromObject(
      { year, month, day, hour, minute, second },
      { zone: siteTimezone || 'Asia/Bangkok' }
    ).toUTC();
  };

  // Get current date for data fetching - this will be re-evaluated based on site's timezone
  // const currentYear = today.getFullYear(); // Old
  // const currentMonth = today.getMonth(); // Old

  // Fetch real-time power data using TimescaleDB 15-minute table
  useEffect(() => {
    if (!node?.id || !siteTimezone) {
      setRealtimePowerData([]);
      setIsRealtimePowerLoading(false);
      setRealtimePowerError(null);
      return;
    }

    const fetchPowerData = async () => {
      setIsRealtimePowerLoading(true);
      setRealtimePowerError(null);
      try {
        // Get current date in site timezone
        const nowInSiteZone = DateTime.now().setZone(siteTimezone || 'Asia/Bangkok');
        
        // Start time is 00:00:00 of today in site timezone, converted to UTC
        const startTimeForQuery = createSiteDateTime(
          nowInSiteZone.year, 
          nowInSiteZone.month, 
          nowInSiteZone.day, 
          0, 0, 0
        );
        
        // End time is now in UTC
        const endTimeForQuery = DateTime.now().toUTC();

        const query: HistoricalDataQuery = {
          table_name: "statistic_data_15min",
          site_id: "set",
          device_id: node.id,
          datapoints: ["power"],
          start_timestamp: startTimeForQuery.toISO()!,
          end_timestamp: endTimeForQuery.toISO()!,
        };
        console.log('[MeterDetails] Real-time Power Request Body:', JSON.stringify(query, null, 2));

        const apiResultArray = await fetchStatisticalData(query);

        // Check if apiResultArray has a data property (API wrapper) or is directly an array
        const dataArray = Array.isArray(apiResultArray) ? apiResultArray : (apiResultArray as any)?.data;
        
        if (dataArray && dataArray.length > 0) {
          const powerDataHistoricalRecord = dataArray.find((r: any) => r.datapoint === "power");

                      if (powerDataHistoricalRecord && powerDataHistoricalRecord.records) {
              const transformedData = powerDataHistoricalRecord.records.map((statRecord: any) => {
                const utcDate = new Date(statRecord.timestamp);
                const localTimeString = utcDate.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit', hour12: false });
                return {
                  time: localTimeString,
                  demand: statRecord.mean_value
                };
              });
              setRealtimePowerData(transformedData);
          } else {
            setRealtimePowerData([]);
          }
        } else {
          setRealtimePowerData([]);
        }
        
      } catch (error) {
        console.error("Failed to fetch real-time power data:", error);
        const errorMessage = error instanceof Error ? 
          error.message : 
          `Failed to fetch real-time power data for ${node.id}.`;
        setRealtimePowerError(errorMessage);
        setRealtimePowerData([]);
      } finally {
        setIsRealtimePowerLoading(false);
      }
    };

    fetchPowerData();
  }, [node?.id, siteTimezone]); // Added siteTimezone dependency

  // Fetch daily energy data using TimescaleDB 1-day table
  useEffect(() => {
    if (!node?.id || !siteTimezone) {
      setDailyEnergyApiResult(null);
      setIsDailyEnergyLoading(false);
      setDailyEnergyError(null);
      return;
    }

    const fetchMonthEnergy = async () => {
      setIsDailyEnergyLoading(true);
      setDailyEnergyError(null);
      setDailyEnergyApiResult(null); // Reset previous data

      try {
        // Get current date in site timezone
        const nowInSiteZone = DateTime.now().setZone(siteTimezone || 'Asia/Bangkok');
        
        // Calendar month range: from 1st day of current month to 1st day of next month
        // This ensures we get complete daily data for the current calendar month
        const startOfMonthUTC = createSiteDateTime(nowInSiteZone.year, nowInSiteZone.month, 1, 0, 0, 0);
        
        // Start of next month in site timezone, converted to UTC
        const startOfNextMonthUTC = createSiteDateTime(
          nowInSiteZone.month === 12 ? nowInSiteZone.year + 1 : nowInSiteZone.year,
          nowInSiteZone.month === 12 ? 1 : nowInSiteZone.month + 1,
          1, 0, 0, 0
        );

        const query: HistoricalDataQuery = {
          table_name: "energy_data_1day", 
          site_id: "set", 
          device_id: node.id,
          datapoints: ["daily_energy"], 
          start_timestamp: startOfMonthUTC.toISO()!,
          end_timestamp: startOfNextMonthUTC.toISO()!, 
        };

        console.log('[MeterDetails] Daily Energy Request Body:', JSON.stringify(query, null, 2));
        
        const apiResultArray = await fetchEnergyData(query);

        // Check if apiResultArray has a data property (API wrapper) or is directly an array  
        const energyDataArray = Array.isArray(apiResultArray) ? apiResultArray : (apiResultArray as any)?.data;
        
        if (energyDataArray && energyDataArray.length > 0) {
          const energyDataRecord = energyDataArray.find((r: any) => r.datapoint === "daily_energy");
          if (energyDataRecord) {
            setDailyEnergyApiResult(energyDataRecord);
          } else {
            setDailyEnergyApiResult(null);
          }
        } else {
          setDailyEnergyApiResult(null);
        }
        
      } catch (error) {
        console.error("Failed to fetch daily energy data:", error);
        const errorMessage = error instanceof Error ? 
          `API Error fetching ${node.id}: ${error.message}` : 
          "An unknown error occurred while fetching daily energy.";
        setDailyEnergyError(errorMessage);
        setDailyEnergyApiResult(null);
      } finally {
        setIsDailyEnergyLoading(false);
      }
    };

    fetchMonthEnergy();
  }, [node?.id, siteTimezone]); // Added siteTimezone dependency

  // Generate daily breakdown for the energy chart from API daily records
  useEffect(() => {
    if (dailyEnergyApiResult && dailyEnergyApiResult.records && dailyEnergyApiResult.records.length > 0) {
      // Create a complete array for all 31 days (monthly chart expects full range)
      const fullMonthData = Array.from({ length: 31 }, (_, i) => ({
        time: String(i + 1),
        demand: 0, // Default to 0 for days without data
      }));

      // Fill in actual data from API
      dailyEnergyApiResult.records.forEach(record => {
        // Convert UTC timestamp to site timezone to get the correct day
        const utcDate = DateTime.fromISO(record.timestamp);
        const siteDate = utcDate.setZone(siteTimezone || 'Asia/Bangkok');
        const dayOfMonth = siteDate.day; // Get day in site timezone
        
        console.log(`[MeterDetails] Energy Data Transformation:`, {
          originalTimestamp: record.timestamp,
          siteTimezone: siteTimezone,
          utcDate: utcDate.toISO(),
          siteDate: siteDate.toISO(),
          dayOfMonth: dayOfMonth,
          value: record.value
        });
        
        // Place data at correct position (dayOfMonth - 1 for 0-based array index)
        if (dayOfMonth >= 1 && dayOfMonth <= 31) {
          fullMonthData[dayOfMonth - 1] = {
            time: String(dayOfMonth),
            demand: record.value,
          };
        }
      });
      
      console.log(`[MeterDetails] Final transformed chart data:`, fullMonthData);
      setDailyEnergyForChart(fullMonthData);
    } else {
      setDailyEnergyForChart([]); // Clear data if no API data
    }
  }, [dailyEnergyApiResult, siteTimezone]); // Added siteTimezone dependency

  // Fetch device details (for status and metrics)
  useEffect(() => {
    if (!node?.id) {
      setDeviceDetailsData(null);
      setIsDeviceDetailsLoading(false);
      setDeviceDetailsError(null);
      return;
    }

    let isActive = true; // To prevent state updates on unmounted component
    const fetchDetails = async () => {
      if (!isActive) return;
      setIsDeviceDetailsLoading(true);
      setDeviceDetailsError(null);
      try {
        const data = await fetchDeviceById(node.id, ['latest_data']);
        if (isActive) {
          setDeviceDetailsData(data);
        }
      } catch (error) {
        if (isActive) {
          console.error(`Failed to fetch device details for ${node.id}:`, error);
          setDeviceDetailsError(error instanceof Error ? error.message : "An unknown error occurred while fetching device details.");
          setDeviceDetailsData(null);
        }
      } finally {
        if (isActive) {
          setIsDeviceDetailsLoading(false);
        }
      }
    };

    fetchDetails(); // Initial fetch

    let intervalId: NodeJS.Timeout | undefined = undefined;
    if (effectiveRefreshInterval > 0) {
      intervalId = setInterval(fetchDetails, effectiveRefreshInterval);
    }

    return () => {
      isActive = false;
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [node?.id, effectiveRefreshInterval]); // Removed timezone dependencies

  // Overall loading state that combines component loading and data loading - optimized
  const isChartLoading = loading || isRealtimePowerLoading;
  const isEnergyChartLoading = loading || isDailyEnergyLoading;
  const isStatusLoading = loading || isDeviceDetailsLoading;



  // Don't render the detailed status cards during initial loading
  // This improves perceived performance significantly
  const useReducedLoading = true;

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center gap-2 border-b border-[#EDEFF9] pb-1 mb-3">
        <Zap className="text-primary-blue" size={16} aria-hidden="true" />
        <h2 className="text-base md:text-lg font-semibold text-primary-blue" data-component-name="MeterDetails">
          Meter Details{node?.name ? `: ${node.name}` : ''}
        </h2>
      </div>
      {/* Main Content / Placeholder */}
      {(!node) ? (
        <div className="flex-1 flex flex-col items-center justify-center text-center">
          <div className="w-32 h-32 mb-4 opacity-60 select-none" aria-hidden="true">
            <svg viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="16" y="32" width="96" height="64" rx="12" fill="#EDEFF9"/><rect x="40" y="56" width="48" height="16" rx="4" fill="#A3BFFA"/><rect x="56" y="80" width="16" height="8" rx="2" fill="#C7D2FE"/></svg>
          </div>
          <p className="text-sm text-gray-600 truncate">Select a meter to view details</p>
        </div>
      ) : (
        <div className="flex flex-col md:flex-row gap-6 fade-in">
          {/* Left: Charts */}
          <div className="flex-1 flex flex-col gap-6 min-w-0">
            {/* Power Demand Chart */}
            <div className={`bg-white border border-[#EDEFF9] rounded-lg shadow-md p-4 relative min-h-[260px] ${deviceDetailsData?.latest_data?.status?.value !== 1 ? 'pointer-events-none' : ''}`}
                 role="img"
                 aria-label="Chart showing Real-time Power Demand">
              <div className="mb-2 flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Zap className="text-primary-blue" size={14} />
                  <h3 className="text-sm font-semibold">Real-time Power Demand</h3>
                </div>
                <div className="relative group">
                  <input
                    type="date"
                    value={new Date().toISOString().split('T')[0]}
                    onChange={(e) => {
                      // TODO: Implement date change logic
                      console.log('Date changed to:', e.target.value);
                    }}
                    className="pl-8 pr-3 py-1.5 text-xs bg-gradient-to-r from-blue-50 to-white border border-blue-200 rounded-lg 
                               focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent
                               hover:border-blue-300 transition-all duration-200 cursor-pointer
                               text-gray-700 font-medium"
                  />
                  <svg 
                    className="absolute left-2.5 top-1/2 transform -translate-y-1/2 w-3.5 h-3.5 text-blue-500 pointer-events-none"
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <div className="h-[240px] relative">
                {isChartLoading ? (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-50/70 animate-pulse">
                    <p className="text-xs text-gray-400">Loading chart...</p>
                  </div>
                ) : realtimePowerError ? (
                  <div className="absolute inset-0 flex items-center justify-center text-center p-4">
                    <p className="text-xs text-red-500">Error loading power data: {realtimePowerError}</p>
                  </div>
                ) : realtimePowerData.length === 0 ? (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <p className="text-xs text-gray-400">No power data available for the selected period.</p>
                  </div>
                ) : (
                  <DailyLoadChart data={realtimePowerData} comparisonData={[]} />
                )}
                {deviceDetailsData?.latest_data?.status?.value !== 1 && (
                  <div className="absolute inset-0 flex items-center justify-center bg-white/70 z-10 pointer-events-none select-none">
                    <span className="text-2xl font-bold text-red-400 opacity-60 rotate-[-10deg]">OFFLINE</span>
                  </div>
                )}
              </div>
            </div>
            {/* Energy Consumption Bar Chart */}
            <div className={`bg-white border border-[#EDEFF9] rounded-lg shadow-md p-4 relative min-h-[260px] ${deviceDetailsData?.latest_data?.status?.value !== 1 ? 'pointer-events-none' : ''}`}
                 role="img"
                 aria-label="Chart showing Monthly Energy Consumption">
              <div className="mb-2 flex items-center">
                <div className="flex items-center gap-2">
                  <BarChart2 className="text-primary-blue" size={14} />
                  <h3 className="text-sm font-semibold" data-component-name="MeterDetails">
                    Daily Energy Consumption (This Month)
                  </h3>
                </div>
              </div>
              <div className="h-[240px] relative">
                {isEnergyChartLoading ? (
                  <div className="absolute inset-0 flex items-center justify-center bg-gray-50/70 animate-pulse">
                    <p className="text-xs text-gray-400">Loading chart...</p>
                  </div>
                ) : dailyEnergyError ? (
                  <div className="absolute inset-0 flex items-center justify-center text-center p-4">
                    <p className="text-xs text-red-500">Error loading energy data: {dailyEnergyError}</p>
                  </div>
                ) : dailyEnergyForChart.length === 0 ? (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <p className="text-xs text-gray-400">No energy data available for this month.</p>
                  </div>
                ) : (
                  <MonthlyLoadChart data={dailyEnergyForChart} comparisonData={[]} />
                )}
                {deviceDetailsData?.latest_data?.status?.value !== 1 && (
                  <div className="absolute inset-0 flex items-center justify-center bg-white/70 z-10 pointer-events-none select-none">
                    <span className="text-2xl font-bold text-red-400 opacity-60 rotate-[-10deg]">OFFLINE</span>
                  </div>
                )}
              </div>
            </div>
          </div>
          {/* Right: Status & Metrics */}
          <div className="bg-white border border-[#EDEFF9] rounded-lg shadow-md p-4 space-y-3 max-w-[340px] w-full relative flex flex-col">
            <h3 className="text-xs font-semibold text-gray-700 tracking-wide uppercase mb-2">Status & Metrics</h3>

            {/* Meter Image */}
            <div className="border border-dashed border-gray-300 rounded-lg p-2 bg-gray-50 mb-2">
              <div className="aspect-video bg-gray-100 flex items-center justify-center overflow-hidden relative max-h-[120px] mx-auto w-3/4">
                <img
                  src={meterImage || DEFAULT_METER_IMAGE}
                  alt={`${node?.name || 'Meter'} image`}
                  className="object-cover w-full h-full"
                />
              </div>
              <div className="text-xs text-gray-400 text-center mt-1">
                {node?.type === 'chillerPlant' ? 'Chiller' : node?.type === 'airSide' ? 'AHU' : 'Power Meter'}
                {node?.id ? ` #${node.id}` : ''}
              </div>
            </div>

            {useReducedLoading && isStatusLoading ? (
              <div className="animate-pulse flex flex-col space-y-4">
                <div className="h-6 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="flex-1 flex flex-col gap-6 min-w-0">
                    <div className="bg-gray-100 rounded-lg h-[260px]"></div>
                    <div className="bg-gray-100 rounded-lg h-[260px]"></div>
                  </div>
                  <div className="w-full md:w-80 lg:w-96 shrink-0">
                    <div className="bg-gray-100 rounded-lg h-full min-h-[400px]"></div>
                  </div>
                </div>
              </div>
            ) : (
              <div>
                {/* Status Section */}
                <div className="p-3 bg-gray-50 rounded-md border border-gray-100">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <span className={`w-2.5 h-2.5 rounded-full ${deviceDetailsData?.latest_data?.status?.value === 1 ? 'bg-green-500' : 'bg-red-500'}`} />
                      <span className={`text-sm font-medium ${deviceDetailsData?.latest_data?.status?.value === 1 ? 'text-green-700' : 'text-red-700'}`}>
                        {deviceDetailsData?.latest_data?.status?.value === 1 ? 'Online' : 'Offline'}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">
                      {deviceDetailsData?.latest_data?.power?.updated_at ? `Updated: ${new Date(deviceDetailsData.latest_data.power.updated_at).toLocaleTimeString()}` : 'Updated: N/A'}
                    </span>
                  </div>
                </div>

                {/* Divider */}
                <div className="border-t border-gray-100 my-3"></div>

                {/* Tabbed Interface for Electrical Parameters */}
                <div className="space-y-3">
                  {/* Tab Navigation */}
                  <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-4" aria-label="Tabs">
                      <button
                        onClick={() => setActiveTab('basic')}
                        className={`py-1.5 px-1 border-b-2 font-medium text-xs ${
                          activeTab === 'basic'
                            ? 'border-primary-blue text-primary-blue'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center gap-1.5">
                          <Gauge size={14} />
                          <span>Basic</span>
                        </div>
                      </button>
                      <button
                        onClick={() => setActiveTab('advanced')}
                        className={`py-1.5 px-1 border-b-2 font-medium text-xs ${
                          activeTab === 'advanced'
                            ? 'border-primary-blue text-primary-blue'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center gap-1.5">
                          <Activity size={14} />
                          <span>3-Phase</span>
                        </div>
                      </button>
                      <button
                        onClick={() => setActiveTab('power_quality')}
                        className={`py-1.5 px-1 border-b-2 font-medium text-xs ${
                          activeTab === 'power_quality'
                            ? 'border-primary-blue text-primary-blue'
                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center gap-1.5">
                          <TrendingUp size={14} />
                          <span>Power Quality</span>
                        </div>
                      </button>
                    </nav>
                  </div>

                  {/* Tab Content */}
                  <div className="min-h-[400px]">
                    {activeTab === 'basic' && (
                      <div className="space-y-4 animate-fade-in">
                        {/* Critical Status Alert */}
                        {deviceDetailsData?.latest_data?.power_factor_total?.value && 
                         deviceDetailsData.latest_data.power_factor_total.value < 0.85 && (
                          <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
                            <div className="flex items-center gap-2">
                              <AlertTriangle size={16} className="text-amber-600" />
                              <span className="text-sm text-amber-800 font-medium">
                                Power Factor Warning: Below 0.85 - Thai utility penalty may apply
                              </span>
                            </div>
                          </div>
                        )}

                        {/* Power Summary */}
                        <div>
                          <span className="block text-xs font-medium text-gray-700 mb-2">Power Summary</span>
                          <div className="bg-white border border-gray-100 rounded-md p-3">
                            <div className="space-y-2.5">
                              <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-500">Total Power</span>
                                <span className="font-medium">
                                  {typeof deviceDetailsData?.latest_data?.power?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.power.value , { decimals: 1 }) 
                                    : '-'} <span className="text-xs text-gray-500">kW</span>
                                </span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-500">Power Factor</span>
                                <span className="font-medium">
                                  {typeof deviceDetailsData?.latest_data?.power_factor?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.power_factor.value, { decimals: 3 }) 
                                    : '-'}
                                </span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-500">Avg Phase Voltage</span>
                                <span className="font-medium">
                                  {(typeof deviceDetailsData?.latest_data?.voltage_l1?.value === 'number' && 
                                    typeof deviceDetailsData?.latest_data?.voltage_l2?.value === 'number' && 
                                    typeof deviceDetailsData?.latest_data?.voltage_l3?.value === 'number')
                                    ? formatNumber((deviceDetailsData.latest_data.voltage_l1.value + 
                                                   deviceDetailsData.latest_data.voltage_l2.value + 
                                                   deviceDetailsData.latest_data.voltage_l3.value) / 3, { decimals: 1 }) 
                                    : '-'} <span className="text-xs text-gray-500">V</span>
                                </span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-500">Total Current</span>
                                <span className="font-medium">
                                  {(typeof deviceDetailsData?.latest_data?.current_l1?.value === 'number' && 
                                    typeof deviceDetailsData?.latest_data?.current_l2?.value === 'number' && 
                                    typeof deviceDetailsData?.latest_data?.current_l3?.value === 'number')
                                    ? formatNumber(deviceDetailsData.latest_data.current_l1.value + 
                                                  deviceDetailsData.latest_data.current_l2.value + 
                                                  deviceDetailsData.latest_data.current_l3.value, { decimals: 1 }) 
                                    : '-'} <span className="text-xs text-gray-500">A</span>
                                </span>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-500">Energy Import</span>
                                <span className="font-medium">
                                  {typeof deviceDetailsData?.latest_data?.cumulative_energy?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.cumulative_energy.value, { decimals: 0 }) 
                                    : '-'} <span className="text-xs text-gray-500">kWh</span>
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {activeTab === 'advanced' && (
                      <div className="space-y-4 animate-fade-in">
                        {/* 3-Phase Power Distribution */}
                        <div>
                          <span className="block text-xs font-medium text-gray-700 mb-2">Phase Power Distribution</span>
                          <div className="bg-white border border-gray-100 rounded-md p-3">
                            <div className="grid grid-cols-3 gap-4 text-xs">
                              <div className="text-center">
                                <div className="text-gray-500 mb-1">L1</div>
                                <div className="font-medium text-sm">
                                  {mockPhaseData?.phasePower?.l1 ? mockPhaseData.phasePower.l1.toFixed(1) : '148.5'} kW
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-gray-500 mb-1">L2</div>
                                <div className="font-medium text-sm">
                                  {mockPhaseData?.phasePower?.l2 ? mockPhaseData.phasePower.l2.toFixed(1) : '153.0'} kW
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-gray-500 mb-1">L3</div>
                                <div className="font-medium text-sm">
                                  {mockPhaseData?.phasePower?.l3 ? mockPhaseData.phasePower.l3.toFixed(1) : '148.5'} kW
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Voltage Measurements */}
                        <div>
                          <span className="block text-xs font-medium text-gray-700 mb-2">Voltage Measurements</span>
                          <div className="bg-white border border-gray-100 rounded-md p-3">
                            <div className="space-y-3">
                              {/* Line-to-Line Voltages */}
                              <div>
                                <div className="text-xs text-gray-500 mb-1">Line-to-Line (380V nominal)</div>
                                <div className="grid grid-cols-3 gap-4 text-xs">
                                  <div className="text-center">
                                    <div className="text-gray-500">L1-L2</div>
                                    <div className="font-medium">
                                      {typeof deviceDetailsData?.latest_data?.voltage_l1l2?.value === 'number' 
                                        ? formatNumber(deviceDetailsData.latest_data.voltage_l1l2.value, { decimals: 1 }) 
                                        : '-'} V
                                    </div>
                                  </div>
                                  <div className="text-center">
                                    <div className="text-gray-500">L2-L3</div>
                                    <div className="font-medium">
                                      {typeof deviceDetailsData?.latest_data?.voltage_l2l3?.value === 'number' 
                                        ? formatNumber(deviceDetailsData.latest_data.voltage_l2l3.value, { decimals: 1 }) 
                                        : '-'} V
                                    </div>
                                  </div>
                                  <div className="text-center">
                                    <div className="text-gray-500">L3-L1</div>
                                    <div className="font-medium">
                                      {typeof deviceDetailsData?.latest_data?.voltage_l3l1?.value === 'number' 
                                        ? formatNumber(deviceDetailsData.latest_data.voltage_l3l1.value, { decimals: 1 }) 
                                        : '-'} V
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Line-to-Neutral Voltages */}
                              <div>
                                <div className="text-xs text-gray-500 mb-1">Line-to-Neutral (220V nominal)</div>
                                <div className="grid grid-cols-3 gap-4 text-xs">
                                  <div className="text-center">
                                    <div className="text-gray-500">L1-N</div>
                                    <div className="font-medium">
                                      {typeof deviceDetailsData?.latest_data?.voltage_l1?.value === 'number' 
                                        ? formatNumber(deviceDetailsData.latest_data.voltage_l1.value, { decimals: 1 }) 
                                        : '-'} V
                                    </div>
                                  </div>
                                  <div className="text-center">
                                    <div className="text-gray-500">L2-N</div>
                                    <div className="font-medium">
                                      {typeof deviceDetailsData?.latest_data?.voltage_l2?.value === 'number' 
                                        ? formatNumber(deviceDetailsData.latest_data.voltage_l2.value, { decimals: 1 }) 
                                        : '-'} V
                                    </div>
                                  </div>
                                  <div className="text-center">
                                    <div className="text-gray-500">L3-N</div>
                                    <div className="font-medium">
                                      {typeof deviceDetailsData?.latest_data?.voltage_l3?.value === 'number' 
                                        ? formatNumber(deviceDetailsData.latest_data.voltage_l3.value, { decimals: 1 }) 
                                        : '-'} V
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Current Measurements */}
                        <div>
                          <span className="block text-xs font-medium text-gray-700 mb-2">Current Measurements</span>
                          <div className="bg-white border border-gray-100 rounded-md p-3">
                            <div className="grid grid-cols-3 gap-4 text-xs">
                              <div className="text-center">
                                <div className="text-gray-500">L1</div>
                                <div className="font-medium">
                                  {typeof deviceDetailsData?.latest_data?.current_l1?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.current_l1.value, { decimals: 1 }) 
                                    : '-'} A
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-gray-500">L2</div>
                                <div className="font-medium">
                                  {typeof deviceDetailsData?.latest_data?.current_l2?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.current_l2.value, { decimals: 1 }) 
                                    : '-'} A
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-gray-500">L3</div>
                                <div className="font-medium">
                                  {typeof deviceDetailsData?.latest_data?.current_l3?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.current_l3.value, { decimals: 1 }) 
                                    : '-'} A
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Power Triangle */}
                        <div>
                          <span className="block text-xs font-medium text-gray-700 mb-2">Power Triangle</span>
                          <div className="bg-white border border-gray-100 rounded-md p-3">
                            <div className="grid grid-cols-3 gap-4 text-sm">
                              <div className="text-center">
                                <div className="text-xs text-gray-500">Real Power</div>
                                <div className="font-medium">
                                  {typeof deviceDetailsData?.latest_data?.power?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.power.value , { decimals: 1 }) 
                                    : '-'} kW
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-xs text-gray-500">Reactive Power</div>
                                <div className="font-medium">
                                  {typeof deviceDetailsData?.latest_data?.reactive_power?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.reactive_power.value , { decimals: 1 }) 
                                    : '-'} kVAR
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-xs text-gray-500">Apparent Power</div>
                                <div className="font-medium">
                                  {typeof deviceDetailsData?.latest_data?.apparent_power?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.apparent_power.value , { decimals: 1 }) 
                                    : '-'} kVA
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {activeTab === 'power_quality' && (
                      <div className="space-y-4 animate-fade-in">
                        {/* Power Quality Alerts */}
                        {((deviceDetailsData?.latest_data?.voltage_thd_l1?.value && deviceDetailsData.latest_data.voltage_thd_l1.value > 5) ||
                          (deviceDetailsData?.latest_data?.current_thd_l1?.value && deviceDetailsData.latest_data.current_thd_l1.value > 15)) && (
                          <div className="bg-red-50 border border-red-200 rounded-md p-3">
                            <div className="flex items-center gap-2">
                              <AlertTriangle size={16} className="text-red-600" />
                              <span className="text-sm text-red-800 font-medium">
                                High THD Detected - May cause equipment damage
                              </span>
                            </div>
                          </div>
                        )}

                        {/* System Quality */}
                        <div>
                          <span className="block text-xs font-medium text-gray-700 mb-2">System Quality</span>
                          <div className="bg-white border border-gray-100 rounded-md p-3">
                            <div className="space-y-2.5">
                              <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-500">Frequency</span>
                                <span className="font-medium">
                                  {typeof deviceDetailsData?.latest_data?.frequency?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.frequency.value, { decimals: 2 }) 
                                    : '-'} <span className="text-xs text-gray-500">Hz</span>
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Voltage THD by Phase */}
                        <div>
                          <span className="block text-xs font-medium text-gray-700 mb-2">Voltage THD by Phase</span>
                          <div className="bg-white border border-gray-100 rounded-md p-3">
                            <div className="grid grid-cols-3 gap-4 text-xs">
                              <div className="text-center">
                                <div className="text-gray-500 mb-1">L1</div>
                                <div className="font-medium text-sm">
                                  {typeof deviceDetailsData?.latest_data?.thd_voltage_l1?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.thd_voltage_l1.value, { decimals: 2 }) 
                                    : mockPhaseData?.voltageThd?.l1 ? mockPhaseData.voltageThd.l1.toFixed(2) : '2.85'} %
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-gray-500 mb-1">L2</div>
                                <div className="font-medium text-sm">
                                  {typeof deviceDetailsData?.latest_data?.thd_voltage_l2?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.thd_voltage_l2.value, { decimals: 2 }) 
                                    : mockPhaseData?.voltageThd?.l2 ? mockPhaseData.voltageThd.l2.toFixed(2) : '3.12'} %
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-gray-500 mb-1">L3</div>
                                <div className="font-medium text-sm">
                                  {typeof deviceDetailsData?.latest_data?.thd_voltage_l3?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.thd_voltage_l3.value, { decimals: 2 }) 
                                    : mockPhaseData?.voltageThd?.l3 ? mockPhaseData.voltageThd.l3.toFixed(2) : '2.47'} %
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Current THD by Phase */}
                        <div>
                          <span className="block text-xs font-medium text-gray-700 mb-2">Current THD by Phase</span>
                          <div className="bg-white border border-gray-100 rounded-md p-3">
                            <div className="grid grid-cols-3 gap-4 text-xs">
                              <div className="text-center">
                                <div className="text-gray-500 mb-1">L1</div>
                                <div className="font-medium text-sm">
                                  {typeof deviceDetailsData?.latest_data?.thd_current_l1?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.thd_current_l1.value, { decimals: 2 }) 
                                    : mockPhaseData?.currentThd?.l1 ? mockPhaseData.currentThd.l1.toFixed(2) : '7.23'} %
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-gray-500 mb-1">L2</div>
                                <div className="font-medium text-sm">
                                  {typeof deviceDetailsData?.latest_data?.thd_current_l2?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.thd_current_l2.value, { decimals: 2 }) 
                                    : mockPhaseData?.currentThd?.l2 ? mockPhaseData.currentThd.l2.toFixed(2) : '8.45'} %
                                </div>
                              </div>
                              <div className="text-center">
                                <div className="text-gray-500 mb-1">L3</div>
                                <div className="font-medium text-sm">
                                  {typeof deviceDetailsData?.latest_data?.thd_current_l3?.value === 'number' 
                                    ? formatNumber(deviceDetailsData.latest_data.thd_current_l3.value, { decimals: 2 }) 
                                    : mockPhaseData?.currentThd?.l3 ? mockPhaseData.currentThd.l3.toFixed(2) : '6.92'} %
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                      </div>
                    )}
                  </div>
                </div>
                
                {/* Divider before action button */}
                <div className="border-t border-gray-100 my-3"></div>
                
                {/* Create Alarm Rule Button - Positioned below power quality */}
                <button
                  className="w-full flex items-center justify-center gap-2 px-3 py-1.5 rounded-md transition-all duration-150 text-sm font-medium disabled:opacity-50 bg-primary-blue text-white border border-blue-600 shadow-sm hover:bg-blue-700 active:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 cursor-pointer"
                  onClick={() => setIsRuleDrawerOpen(true)}
                  aria-haspopup="dialog"
                  aria-controls="rule-drawer"
                  disabled={!node}
                  title={node ? `Create rule for ${node.name}` : 'Select a meter to create a rule'}
                  data-component-name="MeterDetails"
                >
                  <AlertTriangle size={16} className="text-white" />
                  <span>Create Alarm Rule</span>
                </button>
              </div>
            )}
          </div>
        </div>
      )}
      {/* Alarm Rule Drawer */}
      <RuleDetailDrawer
        isOpen={isRuleDrawerOpen}
        onClose={() => {
          setIsRuleDrawerOpen(false);
          // Don't show success message when just closing the drawer
        }}
        onCreate={() => {
          // Only show success message when a rule is actually created
          handleRuleSuccess();
        }}
        rule={null}
        isCreating={true}
        presetMeterIds={node ? [Number(node.id)] : undefined}
      />
    </div>
  );
};

export default MeterDetails;