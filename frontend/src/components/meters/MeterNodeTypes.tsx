import React from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { Wifi, WifiOff, AlertTriangle, Zap, Box, PanelTop, Activity } from 'lucide-react'; // Import icons
import { MeterNodeData } from '../../lib/api/meters'; // Import the type for node data

// Helper to get status indicator
const StatusIndicator = ({ status }: { status?: 'online' | 'offline' | 'warning' }) => {
  if (status === 'online') return <Wifi size={12} className="text-green-600" />;
  if (status === 'offline') return <WifiOff size={12} className="text-red-600" />;
  if (status === 'warning') return <AlertTriangle size={12} className="text-yellow-600" />;
  return null;
};

// Helper to get type icon
const TypeIcon = ({ type }: { type?: string }) => {
    let IconComponent = Activity;
    if (type === 'main') IconComponent = Zap;
    if (type === 'panel') IconComponent = PanelTop;
    if (type === 'submeter') IconComponent = Box;
    // Add more types as needed
    return <IconComponent size={16} className="text-blue-700 mr-2 flex-shrink-0" />;
}

// --- Refined Styles --- 
// Clean node style - similar to dashboard cards
const nodeStyle = "bg-white border border-gray-200/80 rounded-lg shadow-sm p-3 text-xs hover:shadow-md transition-shadow duration-200 min-w-[180px]";
// Status colors for left border
const statusBorderColors = {
  online: 'border-l-green-500',
  offline: 'border-l-red-500',
  warning: 'border-l-yellow-500',
  default: 'border-l-gray-300' 
};

// --- Custom Node Components ---

// General Meter Node
const MeterNode: React.FC<NodeProps<MeterNodeData['data']>> = ({ data }) => {
  const status = data.status || 'default';
  const borderClass = statusBorderColors[status as keyof typeof statusBorderColors] || statusBorderColors.default;

  return (
    <div className={`${nodeStyle} ${borderClass} border-l-4`}> {/* Thicker status border */}
      <Handle type="target" position={Position.Top} className="!bg-gray-300 !w-4 !h-1 !border-none !rounded-sm !top-[-3px]" />
      <div className="flex items-center mb-1">
        <TypeIcon type={data.meterType} />
        <strong className="text-gray-800 flex-1 truncate text-sm font-medium" title={data.label}>{data.label}</strong>
        <div className="ml-2"><StatusIndicator status={data.status} /></div>
      </div>
      {data.reading !== undefined && (
        <div className="text-gray-600 font-semibold text-sm pl-8"> {/* Larger, bolder reading */}
          {data.reading.toLocaleString()} kW
        </div>
      )}
      <Handle type="source" position={Position.Bottom} className="!bg-gray-300 !w-4 !h-1 !border-none !rounded-sm !bottom-[-3px]"/>
    </div>
  );
};

// Input Node (Utility Grid)
const InputNode: React.FC<NodeProps<MeterNodeData['data']>> = ({ data }) => {
  return (
    <div className={`${nodeStyle} border-l-blue-600 border-l-4 bg-blue-50/30`}> {/* Specific style */}
       <div className="flex items-center justify-center">
          <Zap size={16} className="text-blue-700 mr-1.5"/>
          <strong className="text-gray-800 text-sm font-medium">{data.label}</strong>
       </div>
       <Handle type="source" position={Position.Bottom} className="!bg-gray-300 !w-4 !h-1 !border-none !rounded-sm !bottom-[-3px]"/>
    </div>
  );
};

// Output Node (Panel)
const OutputNode: React.FC<NodeProps<MeterNodeData['data']>> = ({ data }) => {
  return (
    <div className={`${nodeStyle} border-l-gray-400 border-l-4 bg-gray-100/50`}> {/* Specific style */}
      <Handle type="target" position={Position.Top} className="!bg-gray-300 !w-4 !h-1 !border-none !rounded-sm !top-[-3px]" />
       <div className="flex items-center">
         <PanelTop size={16} className="text-gray-500 mr-1.5"/>
         <strong className="text-gray-700 text-sm font-medium">{data.label}</strong>
       </div>
      <Handle type="source" position={Position.Bottom} className="!bg-gray-300 !w-4 !h-1 !border-none !rounded-sm !bottom-[-3px]"/>
    </div>
  );
};

// Export map of node types
export const meterNodeTypes = {
  default: MeterNode, // Use general component as default
  meterNode: MeterNode, 
  input: InputNode,
  output: OutputNode,
  // Add specific types if more differentiation is needed later
  // main: MainMeterNode, 
  // panel: PanelNode, 
  // submeter: SubmeterNode
}; 