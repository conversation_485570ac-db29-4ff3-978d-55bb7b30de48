import React, { useState, useEffect, useRef } from 'react';
import { supabase } from '../../lib/supabaseClient';
import { RealtimeChannel } from '@supabase/supabase-js';
import { Line<PERSON>hart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface MeterReading {
  id: number;
  meter_tridium_id: string;
  timestamp: string;
  value: number;
  unit: string;
  created_at?: string;
}

interface LiveMeterChartProps {
  meterId: string;
  title?: string;
  unit?: string; // Expected unit (kW or kWh)
  height?: number;
  width?: number;
  color?: string;
  maxDataPoints?: number; // Maximum number of data points to display
}

const LiveMeterChart: React.FC<LiveMeterChartProps> = ({
  meterId,
  title = 'Live Meter Data',
  unit = 'kW',
  height = 300,
  width = '100%',
  color = '#8884d8',
  maxDataPoints = 50
}) => {
  const [readings, setReadings] = useState<MeterReading[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Fixed interval update mechanism to prevent too frequent refreshes
  const pendingReadings = useRef<MeterReading[]>([]);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Function to apply pending updates in a batch
  const applyPendingUpdates = () => {
    if (pendingReadings.current.length === 0) {
      return;
    }

    // Update the state with all pending readings
    setReadings(currentReadings => {
      // Combine current readings with pending readings
      const updatedReadings = [...currentReadings, ...pendingReadings.current];
      // Limit to maxDataPoints
      return updatedReadings.slice(-maxDataPoints);
    });

    // Clear pending readings
    pendingReadings.current = [];
  };

  useEffect(() => {
    // Fetch initial data
    const fetchInitialData = async () => {
      try {
        setLoading(true);
        const { data, error: fetchError } = await supabase
          .from('meter_readings')
          .select('*')
          .eq('meter_tridium_id', meterId)
          .eq('unit', unit)
          .order('timestamp', { ascending: false })
          .limit(maxDataPoints);

        if (fetchError) {
          console.error('Error fetching initial readings:', fetchError);
          setError('Could not load initial data.');
        } else if (data) {
          // Reverse to show oldest first for time series visualization
          setReadings(data.reverse());
        }
      } catch (err) {
        console.error('Unexpected error:', err);
        setError('An unexpected error occurred.');
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();

    // Set up fixed interval updates (every 15 seconds)
    intervalRef.current = setInterval(applyPendingUpdates, 15000);

    // Set up Realtime subscription
    const channel: RealtimeChannel = supabase
      .channel(`meter_readings_${meterId}_${unit}`) // Unique channel name per meter and unit
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'meter_readings',
          filter: `meter_tridium_id=eq.${meterId}` // Only listen for this meter's readings
        },
        (payload) => {
          console.log('New reading received!', payload);
          const newReading = payload.new as MeterReading;

          // Only add readings with the matching unit
          if (newReading.unit === unit) {
            // Just add to pending readings - updates will happen on fixed interval
            pendingReadings.current.push(newReading);
          }
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log(`Subscribed to readings for meter ${meterId} (${unit})!`);
        }
        if (status === 'CHANNEL_ERROR') {
          console.error(`Subscription error for meter ${meterId}:`, err);
          setError('Realtime connection error.');
        }
        if (status === 'TIMED_OUT') {
          console.warn(`Subscription timed out for meter ${meterId}`);
          setError('Realtime connection timed out.');
        }
      });

    // Cleanup function
    return () => {
      console.log(`Unsubscribing from readings for meter ${meterId}`);
      supabase.removeChannel(channel);

      // Clear the update interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [meterId, unit, maxDataPoints]);

  // Format data for chart
  const chartData = readings.map((reading) => ({
    timestamp: new Date(reading.timestamp).toLocaleTimeString(),
    value: reading.value,
  }));

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  // Render appropriate chart based on unit
  // Per user preference: Power (kW) as line chart, Energy (kWh) as bar chart
  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <h2 className="text-lg font-semibold mb-4">{title}</h2>
      <div style={{ height, width }}>
        <ResponsiveContainer width="100%" height="100%">
          {unit === 'kWh' ? (
            // Bar chart for Energy (kWh)
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="value" name={unit} fill={color} />
            </BarChart>
          ) : (
            // Line chart for Power (kW) and other units
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="value"
                name={unit}
                stroke={color}
                activeDot={{ r: 8 }}
              />
            </LineChart>
          )}
        </ResponsiveContainer>
      </div>
      {readings.length === 0 && (
        <div className="text-center text-gray-500 mt-4">
          No data available. Waiting for readings...
        </div>
      )}
    </div>
  );
};

export default LiveMeterChart;
