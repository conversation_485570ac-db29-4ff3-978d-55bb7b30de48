import { Gauge } from 'lucide-react';

interface MeterStatsProps { 
  stats: {
    total: number;
    online: number;
    offline: number;
    alerts: number;
  };
  // Removed unused selectedView and onViewSelect
}

export function MeterStats({ stats }: MeterStatsProps) { // Removed unused props from destructuring
  return (
    <div className="flex items-center gap-4">
      {/* Total Meters */}
      <div className="flex items-center gap-2 px-3 py-1.5 rounded-lg text-xs bg-blue-50 text-primary-blue">
        <Gauge size={14} />
        <span className="font-medium">{stats.total}</span>
        <span>Total</span>
      </div>
    </div>
  );
}