import React, { useEffect, useState } from 'react';
import React<PERSON>low, {
  ReactFlowProvider,
  Node,
  Edge,
  Background,
  ConnectionLineType,
  useNodesState,
  useEdgesState,
  MarkerType,
} from 'reactflow';
import { useNavigate } from 'react-router-dom';
import 'reactflow/dist/style.css';
import './MeterDiagram.css';
import { meterNodeTypes } from './MeterNodeTypes2D';
import { fetchDeviceById, fetchChildDeviceRelationsById } from '../../lib/services/meterDiagramService';
import type { DeviceDetails } from '../../services/deviceService';
import StatusLegend from './StatusLegend';
import { 
  getAllMainMetersFlat,
  getAllTowerBuildingMeters,
  getAllPodiumBuildingMeters,
  getAllCarParkBuildingMeters
} from '../../lib/config/building/meters';

// Helper to determine status (Restored)
const getStatusFromPower = (power: number | undefined | null): string => {
  if (power === undefined || power === null) return 'disconnected';
  if (power === 0) return 'warning';
  return 'active';
};

// Helper to get meter count based on group ID
const getMeterCountForGroup = (groupId: string): number => {
  switch(groupId) {
    case 'main':
      return 172; // Main meters from getAllMainMetersFlat()
    case 'towerA':
      return 222; // Tower A has 222 meters (from tower-building-meters.ts)
    case 'towerB':
      return 0; // Tower B meters not configured yet
    case 'towerC':
      return 0; // Tower C meters not configured yet
    case 'tenant':
      // Count tenant meters from main configuration
      return getAllMainMetersFlat().filter(m => 
        m.type === 'tenant' || (m as any).groupName?.includes('TENANT')
      ).length;
    case 'plant':
      // Count chiller plant meters
      return getAllMainMetersFlat().filter(m => 
        m.type === 'chillerPlant' || (m as any).groupName?.includes('CHILLER')
      ).length;
    case 'dataCenter':
      // Virtual meter - calculated from other meters
      return 0; // This is a calculated value, not actual meters
    default:
      return 0;
  }
};

// Configuration for the meter groups.
// IMPORTANT: Replace 'YOUR_DEVICE_ID_FOR_...' with actual device_ids from your backend.
// IMPORTANT: Confirm the key for power in latest_data (e.g., latest_data.overall_active_power)
const METER_GROUP_CONFIG = [
  {
    id: 'main',
    deviceId: 'C-BEDB1', // TODO: Replace with actual device_id
    label: 'Main Unit',
    type: 'main',
    position: { x: 50, y: 450 },
    isMainPower: true,
  },
  {
    id: 'towerA',
    deviceId: 'tower_a', // TODO: Replace with actual device_id
    label: 'Tower Building',
    type: 'tower',
    position: { x: 650, y: 0 },
  },
  {
    id: 'towerB',
    deviceId: 'tower_b', // TODO: Replace with actual device_id
    label: 'Podium Building',
    type: 'tower',
    position: { x: 650, y: 180 },
  },
  {
    id: 'towerC',
    deviceId: 'tower_c', // TODO: Replace with actual device_id
    label: 'Car Park Building',
    type: 'tower',
    position: { x: 650, y: 360 },
  },
  {
    id: 'tenant',
    deviceId: 'tower_a_6th_floor_tenants', // TODO: Replace with actual device_id
    label: 'Tenant',
    type: 'tenant',
    position: { x: 650, y: 540 },
  },
  {
    id: 'plant',
    deviceId: 'chiller_plant', // TODO: Replace with actual device_id
    label: 'Chiller Plant',
    type: 'plant',
    position: { x: 650, y: 720 },
  },
  {
    id: 'dataCenter',
    deviceId: 'CB-EXD', // TODO: Replace with actual device_id
    label: 'Data Center & Others',
    type: 'datacenter',
    position: { x: 650, y: 900 },
  },
];

// Initial edges with improved styling - structure remains, styles might adapt
const BASE_INITIAL_EDGES: Omit<Edge, 'id' | 'source' | 'target'> = {
  type: 'smoothstep',
  style: { strokeWidth: 2, opacity: 0.8 },
  sourceHandle: 'right',
  targetHandle: 'left',
};

const createInitialEdges = (nodes: Node[]): Edge[] => {
  const mainNode = nodes.find(n => n.id === 'main');
  if (!mainNode) return [];

  return METER_GROUP_CONFIG.filter(group => group.id !== 'main').map(group => {
    const targetNode = nodes.find(n => n.id === group.id);
    // Ensure power is treated as number, default to undefined if not available for status check
    const power = typeof targetNode?.data?.power === 'number' ? targetNode.data.power : undefined;
    const status = getStatusFromPower(power);

    let edgeStyle = { ...BASE_INITIAL_EDGES.style, stroke: 'url(#blue-gradient)' };
    let animated = true;

    if (status === 'warning') {
      edgeStyle.stroke = 'url(#amber-gradient)';
    } else if (status === 'disconnected') {
      edgeStyle.stroke = 'url(#red-gradient)';
      edgeStyle.strokeDasharray = '5 3';
      animated = false;
    } else if (group.id === 'dataCenter') { // Specific case for dataCenter if needed
        edgeStyle.stroke = 'url(#purple-gradient)';
    }

    return {
      id: `e-main-${group.id}`,
      source: 'main',
      target: group.id,
      ...BASE_INITIAL_EDGES,
      animated,
      style: edgeStyle,
    };
  });
};

interface FlowProps {
  // Removed viewMode and onViewChange as they are not used in this simplified setup for now
}

function Flow({}: FlowProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState<Node[]>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null); // For global errors, not individual node errors
  const navigate = useNavigate();

  useEffect(() => {
    const fetchDataForAllGroups = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const nodePromises = METER_GROUP_CONFIG.map(async (groupConfig) => {
          let powerValue: number | undefined | null = undefined;
          let nodeStatus = 'disconnected';
          let fetchedMeterCount = 0;

          try {
            const deviceDetails: DeviceDetails = await fetchDeviceById(groupConfig.deviceId, ['latest_data']);
            powerValue = deviceDetails.latest_data?.power?.value as number | undefined | null;
            
            // Special handling for Data Center & Others (virtual meter)
            if (groupConfig.id === 'dataCenter' || deviceDetails.is_virtual) {
              nodeStatus = "active"; // Virtual meters are always active when data is available
            } else {
              // For physical meters, determine status based on power
              nodeStatus = getStatusFromPower(powerValue);
            }

            try {
              const relations = await fetchChildDeviceRelationsById(groupConfig.deviceId);
              fetchedMeterCount = relations.length || getMeterCountForGroup(groupConfig.id);
            } catch (relationsError) {
              console.warn(`Failed to fetch relations for device ${groupConfig.deviceId}:`, relationsError);
              fetchedMeterCount = getMeterCountForGroup(groupConfig.id); // Use config-based count as fallback
            }

          } catch (individualDeviceError) {
            console.warn(`Failed to fetch device details for ${groupConfig.deviceId}:`, individualDeviceError);
            // Node status remains 'disconnected', powerValue undefined, meterCount will be attempted or default
             try {
              const relations = await fetchChildDeviceRelationsById(groupConfig.deviceId);
              fetchedMeterCount = relations.length || getMeterCountForGroup(groupConfig.id);
            } catch (relationsError) {
              console.warn(`Failed to fetch relations for device ${groupConfig.deviceId} (after device fetch error):`, relationsError);
              fetchedMeterCount = getMeterCountForGroup(groupConfig.id); // Use config-based count as fallback
            }
          }

          return {
            id: groupConfig.id,
            type: groupConfig.type,
            data: {
              label: groupConfig.label,
              status: nodeStatus, // Based on successful deviceDetails fetch, not power value directly for node status
              power: powerValue !== undefined && powerValue !== null ? parseFloat(powerValue.toFixed(2)) : 0,
              meterCount: fetchedMeterCount, // Use dynamically fetched count
              isMainPower: groupConfig.isMainPower || false,
            },
            position: groupConfig.position,
          } as Node;
        });

        const fetchedNodes = await Promise.all(nodePromises);
        
        setNodes(fetchedNodes);
        setEdges(createInitialEdges(fetchedNodes));

      } catch (err) {
        // This catch block is for more global errors
        console.error('Global error fetching meter group data:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred while loading data.');
        // Fallback: Set all nodes to disconnected based on config if a global error occurs
        const errorNodesPromises = METER_GROUP_CONFIG.map(async (groupConfig) => {
            let fetchedMeterCountOnError = getMeterCountForGroup(groupConfig.id); // Use config count
            try {
                const relations = await fetchChildDeviceRelationsById(groupConfig.deviceId);
                if (relations.length > 0) {
                    fetchedMeterCountOnError = relations.length;
                }
            } catch (relationsError) {
                console.warn(`Fallback: Failed to fetch relations for device ${groupConfig.deviceId}:`, relationsError);
                // Keep using config-based count
            }
            return {
                id: groupConfig.id,
                type: groupConfig.type,
                data: {
                    label: groupConfig.label,
                    status: 'disconnected',
                    power: 0,
                    meterCount: fetchedMeterCountOnError, // Use config-based count or fetched count
                    isMainPower: groupConfig.isMainPower || false,
                },
                position: groupConfig.position,
            } as Node;
        });
        const errorNodes = await Promise.all(errorNodesPromises);
        setNodes(errorNodes);
        setEdges(createInitialEdges(errorNodes));
      } finally {
        setIsLoading(false);
      }
    };

    fetchDataForAllGroups();
    // Optional: Refresh interval
    // const intervalId = setInterval(fetchDataForAllGroups, 30000);
    // return () => clearInterval(intervalId);
  }, [setNodes, setEdges]);


  const handleNodeClick = (event: React.MouseEvent, node: Node) => {
    // Prevent event propagation to avoid triggering other click handlers
    event.stopPropagation();

    // Navigate based on node type and id
    switch(node.type) {
      case 'tower':
        // Extract tower identifier (e.g., "towerA" -> "tower-a")
        const towerId = node.id.replace('tower', '').toLowerCase();
        // Navigate to Meters page with the tower filter applied
        navigate(`/meters?view=tower-${towerId}`);
        break;
      case 'plant':
        // Navigate to Meters page with chiller-plant filter
        navigate('/meters?view=chiller-plant');
        break;
      case 'tenant':
        // Navigate to Meters page with tenant filter (using equipment type)
        navigate('/meters?view=tenant');
        break;
      case 'datacenter':
        // Navigate to Meters page with dataCenterOthers filter
        navigate('/meters?view=data-center-others');
        break;
      case 'main':
        navigate('/meters?view=main');
        break;
      default:
        console.log(`No navigation defined for node type: ${node.type}`);
    }
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-full">Loading live meter data...</div>;
  }

  // Display global error message if one occurred, but still attempt to render nodes (which might be in disconnected state)
  if (error && nodes.every((n: Node) => n.data && typeof n.data.status === 'string' && n.data.status === 'disconnected')) {
    return <div className="flex justify-center items-center h-full text-red-500">Error: {error} <br/> Displaying static diagram with all items disconnected.</div>;
  }

  return (
    <ReactFlow
      nodes={nodes}
      edges={edges}
      onNodesChange={onNodesChange}
      onEdgesChange={onEdgesChange}
      onNodeClick={handleNodeClick}
      nodeTypes={meterNodeTypes}
      connectionLineType={ConnectionLineType.SmoothStep}
      fitView
      fitViewOptions={{ padding: 0.3 }} // More padding for better spacing
      minZoom={0.65}
      maxZoom={0.65}
      defaultViewport={{ x: 0, y: 0, zoom: 0.65 }} // Zoomed out to 65%
      proOptions={{ hideAttribution: true }}
      style={{ background: 'white' }}
      nodesDraggable={false}
      nodesConnectable={false}
      elementsSelectable={true}
      zoomOnScroll={false}
      panOnScroll={false}
      panOnDrag={false}
      preventScrolling={false}
    >
      {/* Define gradients for edges */}
      <svg style={{ width: 0, height: 0 }}>
        <defs>
          <linearGradient id="blue-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#60A5FA" />
            <stop offset="100%" stopColor="#3B82F6" />
          </linearGradient>
          <linearGradient id="amber-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#FBBF24" />
            <stop offset="100%" stopColor="#F59E0B" />
          </linearGradient>
          <linearGradient id="red-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#F87171" />
            <stop offset="100%" stopColor="#EF4444" />
          </linearGradient>
          <linearGradient id="purple-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#A78BFA" />
            <stop offset="100%" stopColor="#8B5CF6" />
          </linearGradient>
        </defs>
      </svg>
      <Background color="#f1f5f9" gap={24} size={0.5} />
      <StatusLegend />
    </ReactFlow>
  );
}

// Main component with provider
interface SimpleMeterDiagramProps {
  viewMode: '2d'; // Kept for consistency with MeterDiagramPage, though not directly used by Flow's new data fetching
  onViewChange: (view: '2d') => void; // Kept for consistency
}

export default function SimpleMeterDiagram({ viewMode: _viewMode, onViewChange: _onViewChange }: SimpleMeterDiagramProps) {
  // The viewMode and onViewChange props are passed down but not directly used in the Flow component's
  // data fetching logic itself. They could be used for other UI elements if needed.
  return (
    <div style={{ width: '100%', height: '100%' }}>
      <ReactFlowProvider>
        <Flow />
      </ReactFlowProvider>
    </div>
  );
}
