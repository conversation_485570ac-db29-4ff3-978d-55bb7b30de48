import React, { useState, useEffect, useMemo } from 'react';
import { 
  LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, 
  <PERSON>ltip, Legend, ResponsiveContainer 
} from 'recharts';
import { 
  Calendar, ArrowUpRight, ArrowDownRight, Info, 
  ChevronDown, ChevronUp, Download, AlertTriangle
} from 'lucide-react';
import { format } from 'date-fns';
import { ComparisonSelect, type ComparisonPeriod } from '../charts/ComparisonSelect';
import { formatNumber } from '../../lib/utils/formatters';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"; 
import MeterThresholds from './MeterThresholds'; 
import type { TreeNode } from './types'; 
import { Button } from '@/components/ui/Button';
import RuleDetailDrawer from '@/components/alarms/RuleDetailDrawer';

// Sample mock data function - replace with actual API call
const fetchMeterData = async (_meterId: string | undefined, _date: Date, _comparisonPeriod: string) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Add a check for meterId
  if (!_meterId) {
    return { current: [], comparison: [] };
  }
  
  // Generate current data
  const current = Array.from({ length: 24 }, (_, i) => {
    // Create a pattern that peaks during work hours
    let value = 10 + Math.sin((i - 6) * 0.5) * 40;
    if (i >= 8 && i <= 17) value += 30;
    // Add some randomness
    value += Math.random() * 10;
    // Ensure non-negative
    value = Math.max(0, value);
    
    return {
      time: `${i}:00`,
      value: Math.round(value * 10) / 10
    };
  });
  
  // Generate comparison data with some variation
  const comparison = current.map(item => {
    const variation = (Math.random() * 0.4) - 0.2; // -20% to +20%
    return {
      time: item.time,
      value: Math.round(item.value * (1 + variation) * 10) / 10
    };
  });
  
  return { current, comparison };
};

// Function to calculate percentage change
const calculatePercentageChange = (previous: number, current: number): number => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - previous) / previous) * 100);
};

interface MeterDetailViewProps {
  // meterId: string; 
  node: TreeNode | null; 
}

// UI color constants used for styling charts and indicators
const COLORS = {
  current: '#3B82F6',
  previous: '#D1D5DB',
  increase: '#EF4444',
  decrease: '#10B981',
  neutral: '#6B7280'
};

export const MeterDetailView: React.FC<MeterDetailViewProps> = ({ node }) => {
  const meterId = node?.id;
  // State
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [comparisonPeriod, setComparisonPeriod] = useState<ComparisonPeriod>('day');
  const [chartType, setChartType] = useState<'line' | 'bar'>('line');
  const [showInsights, setShowInsights] = useState(true);
  const [meterData, setMeterData] = useState<{ 
    current: Array<{ time: string; value: number }>;
    comparison: Array<{ time: string; value: number }>;
  }>({ current: [], comparison: [] });
  const [isLoading, setIsLoading] = useState(true);
  const [isRuleDrawerOpen, setIsRuleDrawerOpen] = useState(false);
  const [ruleDrawerMeterId, setRuleDrawerMeterId] = useState<number | null>(null);

  const handleOpenCreateRule = () => {
    if (!node?.id) return;
    let numericId = Number(node.id);
    if (isNaN(numericId)) {
      numericId = node.id.split('').reduce((acc, c) => acc + c.charCodeAt(0), 0);
    }
    setRuleDrawerMeterId(numericId);
    setIsRuleDrawerOpen(true);
  };

  const handleRuleCreated = (newRule: any) => {
    if (newRule.meter_ids && newRule.meter_ids.includes(ruleDrawerMeterId)) {
      // No direct list to update here; could emit event or console log for now
      console.log('Rule created for current meter', newRule);
    }
  };

  // Fetch meter data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const data = await fetchMeterData(meterId, selectedDate, comparisonPeriod);
        setMeterData(data);
      } catch (error) {
        console.error('Error fetching meter data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [meterId, selectedDate, comparisonPeriod]);
  
  // Calculate insights
  const insights = useMemo(() => {
    if (!meterData.current.length || !meterData.comparison.length) return [];
    
    // Calculate total consumption
    const currentTotal = meterData.current.reduce((sum, item) => sum + item.value, 0);
    const previousTotal = meterData.comparison.reduce((sum, item) => sum + item.value, 0);
    const totalChange = calculatePercentageChange(previousTotal, currentTotal);
    
    // Find peak values
    const currentPeak = Math.max(...meterData.current.map(item => item.value));
    const previousPeak = Math.max(...meterData.comparison.map(item => item.value));
    const peakChange = calculatePercentageChange(previousPeak, currentPeak);
    
    // Find peak times
    const currentPeakItem = meterData.current.find(item => item.value === currentPeak);
    const previousPeakItem = meterData.comparison.find(item => item.value === previousPeak);
    
    // Calculate off-peak average (lowest 25% of values)
    const sortedCurrent = [...meterData.current].sort((a, b) => a.value - b.value);
    const sortedPrevious = [...meterData.comparison].sort((a, b) => a.value - b.value);
    const offPeakCount = Math.floor(meterData.current.length * 0.25);
    const currentOffPeakAvg = sortedCurrent.slice(0, offPeakCount).reduce((sum, item) => sum + item.value, 0) / offPeakCount;
    const previousOffPeakAvg = sortedPrevious.slice(0, offPeakCount).reduce((sum, item) => sum + item.value, 0) / offPeakCount;
    const offPeakChange = calculatePercentageChange(previousOffPeakAvg, currentOffPeakAvg);
    
    return [
      {
        title: 'Total consumption',
        current: `${formatNumber(currentTotal)} kWh`,
        previous: `${formatNumber(previousTotal)} kWh`,
        change: totalChange,
        icon: totalChange > 0 ? ArrowUpRight : ArrowDownRight,
        color: totalChange > 0 ? 'rose-600' : 'emerald-600'
      },
      {
        title: 'Peak consumption',
        current: `${formatNumber(currentPeak)} kWh at ${currentPeakItem?.time || 'N/A'}`,
        previous: `${formatNumber(previousPeak)} kWh at ${previousPeakItem?.time || 'N/A'}`,
        change: peakChange,
        icon: peakChange > 0 ? ArrowUpRight : ArrowDownRight,
        color: peakChange > 0 ? 'rose-600' : 'emerald-600'
      },
      {
        title: 'Off-peak average',
        current: `${formatNumber(currentOffPeakAvg)} kWh`,
        previous: `${formatNumber(previousOffPeakAvg)} kWh`,
        change: offPeakChange,
        icon: offPeakChange > 0 ? ArrowUpRight : ArrowDownRight,
        color: offPeakChange > 0 ? 'rose-600' : 'emerald-600'
      }
    ];
  }, [meterData]);
  
  // Combined chart data
  const chartData = useMemo(() => {
    return meterData.current.map((item, index) => ({
      time: item.time,
      current: item.value,
      previous: meterData.comparison[index]?.value || 0
    }));
  }, [meterData]);
  
  // Handle null node case
  if (!node) {
    return <div className="p-4 text-center text-gray-500">Select a meter to view details.</div>;
  }
  
  return (
    <div className="space-y-3">
      {/* Header */}
      <div className="flex justify-between items-center mb-3">
        <h2 className="text-lg font-medium text-gray-900">Meter Performance</h2>
        <div className="flex items-center gap-4">
          <Button
            variant="default"
            size="default"
            className="px-4 py-2 text-sm font-medium bg-blue-600 text-white hover:bg-blue-700 shadow-md flex items-center gap-1.5"
            onClick={handleOpenCreateRule}
            style={{background: '#2563EB', color: 'white', fontWeight: 'bold'}}
          >
            <AlertTriangle size={14} />
            Create Alarm Rule
          </Button>
          <div className="flex gap-2">
            <button 
              onClick={() => setChartType('line')}
              className={`px-2.5 py-1.5 rounded-md text-xs font-medium ${
                chartType === 'line' 
                  ? 'bg-blue-50 text-blue-600 border border-blue-200' 
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              aria-pressed={chartType === 'line'}
            >
              Line
            </button>
            <button 
              onClick={() => setChartType('bar')}
              className={`px-2.5 py-1.5 rounded-md text-xs font-medium ${
                chartType === 'bar' 
                  ? 'bg-blue-50 text-blue-600 border border-blue-200' 
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              aria-pressed={chartType === 'bar'}
            >
              Bar
            </button>
            <button
              onClick={() => {
                // Export functionality would go here
                console.log('Export data', meterData);
              }}
              className="px-2.5 py-1.5 rounded-md text-xs font-medium text-gray-600 hover:text-gray-800 flex items-center gap-1"
              title="Export meter data"
            >
              <Download size={14} />
              Export
            </button>
          </div>
        </div>
      </div>
      
      {/* Date Selection and Comparison Controls */}
      <div className="flex flex-wrap justify-between items-center gap-3 bg-white p-3 rounded-xl border border-gray-100 shadow-sm mb-3">
        <div className="flex items-center gap-2">
          <button
            onClick={() => setSelectedDate(new Date())}
            className="px-3 py-1.5 bg-blue-600 text-white rounded-lg text-sm font-medium flex items-center gap-1"
          >
            Today
          </button>
          <button
            className="px-3 py-1.5 border border-gray-200 rounded-lg text-sm text-gray-700 flex items-center gap-1"
            onClick={() => {
              // Date picker would go here
              const yesterday = new Date();
              yesterday.setDate(yesterday.getDate() - 1);
              setSelectedDate(yesterday);
            }}
          >
            <Calendar size={14} />
            {format(selectedDate, 'MMM d, yyyy')}
          </button>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Compare with:</span>
          <ComparisonSelect 
            selectedPeriod={comparisonPeriod}
            onChange={(value: ComparisonPeriod) => setComparisonPeriod(value)}
          />
        </div>
      </div>
      
      {/* Tabs Component */}
      <Tabs defaultValue="performance" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-3">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="thresholds">Thresholds</TabsTrigger>
        </TabsList>
        
        {/* Performance Tab Content */}
        <TabsContent value="performance" className="mt-3 space-y-3">
          {/* Chart */}
          <div className="bg-white p-3 rounded-xl border border-gray-100 shadow-sm">
            <div className="h-64">
              {isLoading ? (
                <div className="h-full flex items-center justify-center">
                  <p className="text-gray-500">Loading data...</p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  {chartType === 'line' ? (
                    <LineChart
                      data={chartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis 
                        dataKey="time" 
                        fontSize={10}
                        tickLine={false}
                        axisLine={{ stroke: '#E5E7EB' }}
                        dy={8}
                      />
                      <YAxis 
                        fontSize={10}
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `${formatNumber(Number(value))}`}
                      />
                      <Tooltip 
                        formatter={(value) => [`${formatNumber(Number(value))} kWh`, '']}
                        labelFormatter={(label) => `Time: ${label}`}
                        contentStyle={{
                          fontSize: '12px',
                          borderRadius: '6px',
                          border: '1px solid #E5E7EB',
                          boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                        }}
                      />
                      <Legend 
                        verticalAlign="top" 
                        align="right" 
                        iconType="circle"
                        wrapperStyle={{ 
                          paddingBottom: 10, 
                          fontSize: '12px',
                          fontWeight: 500
                        }}
                      />
                      {comparisonPeriod !== 'none' && (
                        <Line 
                          type="monotone" 
                          dataKey="previous" 
                          name={`Previous (${comparisonPeriod})`} 
                          stroke={COLORS.previous}
                          strokeDasharray="5 5"
                        />
                      )}
                      <Line 
                        type="monotone" 
                        dataKey="current" 
                        name="Current" 
                        stroke={COLORS.current}
                        strokeWidth={2}
                      />
                    </LineChart>
                  ) : (
                    <BarChart
                      data={chartData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis 
                        dataKey="time" 
                        fontSize={10}
                        tickLine={false}
                        axisLine={{ stroke: '#E5E7EB' }}
                        dy={8}
                      />
                      <YAxis 
                        fontSize={10}
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `${formatNumber(Number(value))}`}
                      />
                      <Tooltip 
                        formatter={(value) => [`${formatNumber(Number(value))} kWh`, '']}
                        labelFormatter={(label) => `Time: ${label}`}
                        contentStyle={{
                          fontSize: '12px',
                          borderRadius: '6px',
                          border: '1px solid #E5E7EB',
                          boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                        }}
                      />
                      <Legend 
                        verticalAlign="top" 
                        align="right" 
                        iconType="circle"
                        wrapperStyle={{ 
                          paddingBottom: 10, 
                          fontSize: '12px',
                          fontWeight: 500
                        }}
                      />
                      {comparisonPeriod !== 'none' && (
                        <Bar 
                          dataKey="previous" 
                          name={`Previous (${comparisonPeriod})`} 
                          fill={COLORS.previous}
                          radius={[4, 4, 0, 0]}
                        />
                      )}
                      <Bar 
                        dataKey="current" 
                        name="Current" 
                        fill={COLORS.current}
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  )}
                </ResponsiveContainer>
              )}
            </div>
          </div>
          
          {/* Insights Panel */}
          <div className="bg-white rounded-xl border border-gray-100 shadow-sm overflow-hidden">
            <button
              onClick={() => setShowInsights(!showInsights)}
              className="w-full px-4 py-3 flex justify-between items-center border-b border-gray-100"
            >
              <div className="flex items-center gap-2">
                <Info size={16} className="text-blue-600" />
                <h3 className="font-medium text-gray-900">Performance Insights</h3>
              </div>
              {showInsights ? (
                <ChevronUp size={16} className="text-gray-500" />
              ) : (
                <ChevronDown size={16} className="text-gray-500" />
              )}
            </button>
            
            {showInsights && (
              <div className="p-4 divide-y divide-gray-100">
                {insights.map((insight, index) => (
                  <div key={index} className="py-3 first:pt-0 last:pb-0">
                    <div className="flex justify-between items-start">
                      <h4 className="text-sm text-gray-600">{insight.title}</h4>
                      <div className="flex items-center gap-1">
                        <insight.icon size={14} className={`text-${insight.color}`} />
                        <span className={`text-xs font-medium text-${insight.color}`}>
                          {insight.change > 0 ? '+' : ''}{insight.change}%
                        </span>
                      </div>
                    </div>
                    <div className="mt-1 grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs text-gray-500">Current</p>
                        <p className="text-sm font-medium text-gray-900">{insight.current}</p>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Previous</p>
                        <p className="text-sm font-medium text-gray-900">{insight.previous}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </TabsContent>
        
        {/* Thresholds Tab Content */}
        <TabsContent value="thresholds" className="mt-3">
          <MeterThresholds node={node} />
        </TabsContent>
      </Tabs>
      {/* Rule Detail Drawer */}
      <RuleDetailDrawer
        isOpen={isRuleDrawerOpen}
        onClose={() => setIsRuleDrawerOpen(false)}
        rule={null}
        isCreating={true}
        presetMeterIds={ruleDrawerMeterId !== null ? [ruleDrawerMeterId] : undefined}
        onCreate={handleRuleCreated}
      />
    </div>
  );
};

export default MeterDetailView;
