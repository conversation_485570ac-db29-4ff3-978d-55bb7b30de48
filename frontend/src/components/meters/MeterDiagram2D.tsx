import React, { useCallback, useState, useEffect } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  ConnectionLineType,
  ReactFlowProvider,
} from 'reactflow';
import 'reactflow/dist/style.css';
import './MeterDiagram.css';
import dagre from 'dagre';
import { meterNodeTypes } from './MeterNodeTypes2D';

// Static data for meter groups (same as in MeterDiagramView.tsx)
const meterGroups = {
  main: { label: 'Main Unit', meterCount: 0, power: 1354, status: 'active' },
  towerA: { label: 'Tower Building', meterCount: 112, power: 425, status: 'active' },
  towerB: { label: 'Podium Building', meterCount: 138, power: 380, status: 'active' },
  towerC: { label: 'Car Park Building', meterCount: 95, power: 215, status: 'active' },
  tenant: { label: 'Tenant', meterCount: 68, power: 180, status: 'warning' },
  plant: { label: 'Plant', meterCount: 42, power: 0, status: 'disconnected' },
  dataCenter: { label: 'Data Center', meterCount: 0, power: 154, status: 'active' }
};

// Using only 'All Systems' view

// Node width and height for layout calculation
const nodeWidth = 420; // Further increased to match the actual node width
const nodeHeight = 130; // Further increased height to better fit content

// Helper function to create a dagre graph for automatic layout
const getLayoutedElements = (nodes: Node[], edges: Edge[], direction = 'LR') => {
  const dagreGraph = new dagre.graphlib.Graph();
  dagreGraph.setDefaultEdgeLabel(() => ({}));
  dagreGraph.setGraph({
    rankdir: direction,
    nodesep: 120, // Further increased horizontal spacing for larger nodes
    ranksep: 170, // Further increased vertical spacing for larger nodes
    edgesep: 60, // Further increased edge spacing
  });

  // Set node dimensions
  nodes.forEach((node) => {
    dagreGraph.setNode(node.id, { width: nodeWidth, height: nodeHeight });
  });

  // Add edges to the graph
  edges.forEach((edge) => {
    dagreGraph.setEdge(edge.source, edge.target);
  });

  // Calculate layout
  dagre.layout(dagreGraph);

  // Apply layout to nodes
  const layoutedNodes = nodes.map((node) => {
    const nodeWithPosition = dagreGraph.node(node.id);
    return {
      ...node,
      position: {
        x: nodeWithPosition.x - nodeWidth / 2,
        y: nodeWithPosition.y - nodeHeight / 2,
      },
    };
  });

  return { nodes: layoutedNodes, edges };
};

// Create initial nodes based on meter groups
const createInitialNodes = () => {
  const nodes: Node[] = [
    {
      id: 'input',
      type: 'input',
      data: {
        label: 'Power Input',
        status: 'active',
        power: meterGroups.main.power,
        systemType: 'all'
      },
      position: { x: 0, y: 0 },
    },
    {
      id: 'main',
      type: 'main',
      data: {
        label: meterGroups.main.label,
        status: meterGroups.main.status,
        power: meterGroups.main.power,
        systemType: 'all'
      },
      position: { x: 0, y: 0 },
    },
    {
      id: 'towerA',
      type: 'tower',
      data: {
        label: meterGroups.towerA.label,
        status: meterGroups.towerA.status,
        power: meterGroups.towerA.power,
        meterCount: meterGroups.towerA.meterCount,
        systemType: 'all'
      },
      position: { x: 0, y: 0 },
    },
    {
      id: 'towerB',
      type: 'tower',
      data: {
        label: meterGroups.towerB.label,
        status: meterGroups.towerB.status,
        power: meterGroups.towerB.power,
        meterCount: meterGroups.towerB.meterCount,
        systemType: 'all'
      },
      position: { x: 0, y: 0 },
    },
    {
      id: 'towerC',
      type: 'tower',
      data: {
        label: meterGroups.towerC.label,
        status: meterGroups.towerC.status,
        power: meterGroups.towerC.power,
        meterCount: meterGroups.towerC.meterCount,
        systemType: 'all'
      },
      position: { x: 0, y: 0 },
    },
    {
      id: 'tenant',
      type: 'tenant',
      data: {
        label: meterGroups.tenant.label,
        status: meterGroups.tenant.status,
        power: meterGroups.tenant.power,
        meterCount: meterGroups.tenant.meterCount,
        systemType: 'all'
      },
      position: { x: 0, y: 0 },
    },
    {
      id: 'plant',
      type: 'plant',
      data: {
        label: meterGroups.plant.label,
        status: meterGroups.plant.status,
        power: meterGroups.plant.power,
        meterCount: meterGroups.plant.meterCount,
        systemType: 'chillerPlant'
      },
      position: { x: 0, y: 0 },
    },
    {
      id: 'dataCenter',
      type: 'datacenter',
      data: {
        label: meterGroups.dataCenter.label,
        status: meterGroups.dataCenter.status,
        power: meterGroups.dataCenter.power,
        meterCount: meterGroups.dataCenter.meterCount,
        systemType: 'equipment'
      },
      position: { x: 0, y: 0 },
    },
  ];

  return nodes;
};

// Create initial edges based on meter relationships
const createInitialEdges = () => {
  const edges: Edge[] = [
    {
      id: 'e-input-main',
      source: 'input',
      target: 'main',
      type: 'smoothstep',
      animated: true,
      style: { stroke: '#3B82F6', strokeWidth: 2 },
      sourceHandle: 'right',
      targetHandle: 'left',
    },
    {
      id: 'e-main-towerA',
      source: 'main',
      target: 'towerA',
      type: 'smoothstep',
      animated: true,
      style: { stroke: '#3B82F6', strokeWidth: 2 },
      sourceHandle: 'right',
      targetHandle: 'left',
    },
    {
      id: 'e-main-towerB',
      source: 'main',
      target: 'towerB',
      type: 'smoothstep',
      animated: true,
      style: { stroke: '#3B82F6', strokeWidth: 2 },
      sourceHandle: 'right',
      targetHandle: 'left',
    },
    {
      id: 'e-main-towerC',
      source: 'main',
      target: 'towerC',
      type: 'smoothstep',
      animated: true,
      style: { stroke: '#3B82F6', strokeWidth: 2 },
      sourceHandle: 'right',
      targetHandle: 'left',
    },
    {
      id: 'e-main-tenant',
      source: 'main',
      target: 'tenant',
      type: 'smoothstep',
      animated: true,
      style: { stroke: '#F59E0B', strokeWidth: 2 },
      sourceHandle: 'right',
      targetHandle: 'left',
    },
    {
      id: 'e-main-plant',
      source: 'main',
      target: 'plant',
      type: 'smoothstep',
      animated: false, // Not animated if disconnected
      style: { stroke: '#EF4444', strokeWidth: 2 },
      sourceHandle: 'right',
      targetHandle: 'left',
    },
    {
      id: 'e-main-dataCenter',
      source: 'main',
      target: 'dataCenter',
      type: 'smoothstep',
      animated: true,
      style: { stroke: '#3B82F6', strokeWidth: 2 },
      sourceHandle: 'right',
      targetHandle: 'left',
    },
  ];

  return edges;
};

// Props interface
interface MeterDiagram2DProps {}

// Main component
const MeterDiagram2D: React.FC<MeterDiagram2DProps> = () => {
  // Initialize nodes and edges - using useMemo to prevent recreation on every render
  const initialNodes = React.useMemo(() => createInitialNodes(), []);
  const initialEdges = React.useMemo(() => createInitialEdges(), []);

  // Apply layout to initial elements - using useMemo to prevent recreation on every render
  const { nodes: layoutedNodes, edges: layoutedEdges } = React.useMemo(
    () => getLayoutedElements(initialNodes, initialEdges),
    [initialNodes, initialEdges]
  );

  // Set up state for nodes and edges
  const [nodes, setNodes, onNodesChange] = useNodesState(layoutedNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(layoutedEdges);

  // Initialize nodes and edges
  useEffect(() => {
    // Use a stable reference to layoutedNodes and layoutedEdges
    const updateNodesAndEdges = () => {
      // Show all nodes
      setNodes(layoutedNodes.map(node => ({
        ...node,
        hidden: false
      })));
      // Show all edges
      setEdges(layoutedEdges.map(edge => ({
        ...edge,
        hidden: false
      })));
    };

    // Call the update function
    updateNodesAndEdges();

    // Set up a timer to ensure the diagram is rendered properly
    const timer = setTimeout(() => {
      updateNodesAndEdges();
    }, 100);

    return () => clearTimeout(timer);
  }, [layoutedNodes, layoutedEdges, setNodes, setEdges]);

  // No need for handleSystemChange as we receive selectedSystem as a prop

  return (
    <div className="h-full w-full" style={{ height: '100%', width: '100%' }}>
      {/* React Flow diagram */}
      <div className="flex-1" style={{ height: '100%' }}>
        <div style={{ width: '100%', height: '100%', position: 'relative' }}>
          <ReactFlowProvider>
            <ReactFlow
              key="flow-all"
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              nodeTypes={meterNodeTypes}
              connectionLineType={ConnectionLineType.SmoothStep}
              fitView
              fitViewOptions={{ padding: 0.05 }} // Extremely minimal padding to maximize node size
              attributionPosition="bottom-left"
              minZoom={0.5}
              maxZoom={1.5}
              defaultZoom={1.0} // Maximum zoom to fill container
              proOptions={{ hideAttribution: true }}
              style={{ background: 'white' }}
              nodesDraggable={false}
              nodesConnectable={false}
              elementsSelectable={false}
            >
              <Background color="#f8fafc" gap={16} />
              <Controls showInteractive={false} />
            </ReactFlow>
          </ReactFlowProvider>
        </div>
      </div>
    </div>
  );
};

export default MeterDiagram2D;
