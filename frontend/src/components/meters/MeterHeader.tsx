import { useState, useRef, useEffect } from 'react';
import { Building2, WifiOff, AlertTriangle, ChevronDown } from 'lucide-react';
import { MeterStats } from './MeterStats';

// Define the specific view types managed by MetersPage
type TowerView = 'main' | 'tower_a' | 'tower_b' | 'tower_c';

interface MeterHeaderProps {
  selectedMeterView: TowerView; // Use the specific tower view type
  onViewChange: (view: TowerView) => void; // Update parameter type
  stats: {
    total: number;
    online: number;
    offline: number;
    alerts: number;
  };
  selectedView: 'all' | 'online' | 'offline' | 'alerts';
  onViewSelect: (view: 'all' | 'online' | 'offline' | 'alerts') => void;
}

export function MeterHeader({
  selectedMeterView,
  onViewChange,
  stats,
  selectedView,
  onViewSelect
}: MeterHeaderProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get view display name
  const getViewName = () => {
    switch (selectedMeterView) {
      case 'main': return 'Main Meters';
      case 'tower_a': return 'Tower Building';
      case 'tower_b': return 'Podium Building';
      case 'tower_c': return 'Car Park Building';
      default: return 'Select View';
    }
  };

  // Get view icon
  const ViewIcon = Building2;

  return (
    <div className="flex items-center gap-2 flex-shrink-0">
      {/* Building View Dropdown */}
      <div className="relative" ref={dropdownRef}>
        <button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="flex items-center gap-1 px-2 py-1 rounded-lg text-xs transition-all duration-200 text-primary-blue bg-blue-50 hover:bg-blue-100/70"
        >
          <ViewIcon size={14} />
          <span>{getViewName()}</span>
          <ChevronDown size={12} className={`transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
        </button>

        {isDropdownOpen && (
          <div className="absolute z-10 mt-1 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
            <div className="py-1">
              {[
                { key: 'main', label: 'Main Meters' },
                { key: 'tower_a', label: 'Tower Building' },
                { key: 'tower_b', label: 'Podium Building' },
                { key: 'tower_c', label: 'Car Park Building' }
              ].map(view => (
                <button
                  key={view.key}
                  onClick={() => {
                    onViewChange(view.key as TowerView);
                    setIsDropdownOpen(false);
                  }}
                  className={`w-full text-left px-4 py-2 text-xs flex items-center gap-2 ${
                    selectedMeterView === view.key ? 'bg-blue-50 text-primary-blue' : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <ViewIcon size={14} />
                  <span>{view.label}</span>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}