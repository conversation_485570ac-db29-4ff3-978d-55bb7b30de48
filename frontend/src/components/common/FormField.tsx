import * as React from 'react';
import { cn } from '@/lib/utils';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Checkbox } from '../ui/checkbox';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { AlertCircle, Check, Info } from 'lucide-react';
import DropdownSelector, { SelectOption } from './DropdownSelector';

// Base props shared by all field types
interface BaseFieldProps {
  id: string;
  label: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  helpText?: string;
  className?: string;
  labelClassName?: string;
  inputContainerClassName?: string;
  inputClassName?: string;
  errorClassName?: string;
  helpClassName?: string;
  autoFocus?: boolean;
  success?: boolean;
}

// Type-specific props for different field types
interface TextFieldProps extends BaseFieldProps {
  type: 'text' | 'email' | 'password' | 'number' | 'date';
  value: string | number;
  onChange: (value: string) => void;
  placeholder?: string;
  min?: number;
  max?: number;
  step?: number;
}

interface TextareaFieldProps extends BaseFieldProps {
  type: 'textarea';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
}

interface CheckboxFieldProps extends BaseFieldProps {
  type: 'checkbox';
  checked: boolean;
  onChange: (checked: boolean) => void;
}

interface SwitchFieldProps extends BaseFieldProps {
  type: 'switch';
  checked: boolean;
  onChange: (checked: boolean) => void;
}

interface SelectFieldProps<T extends string = string> extends BaseFieldProps {
  type: 'select';
  options: SelectOption<T>[];
  value: T;
  onChange: (value: T) => void;
  placeholder?: string;
}

// Union of all possible props based on type
type FormFieldProps<T extends string = string> = 
  | TextFieldProps 
  | TextareaFieldProps 
  | CheckboxFieldProps
  | SwitchFieldProps
  | SelectFieldProps<T>;

/**
 * FormField - A unified form field component
 * 
 * This component standardizes form inputs across the application with consistent styling,
 * error handling, help text, and accessibility features.
 * 
 * @example
 * <FormField
 *   id="email"
 *   label="Email Address"
 *   type="email"
 *   value={email}
 *   onChange={setEmail}
 *   required
 *   error={emailError}
 *   helpText="We'll never share your email with anyone else."
 * />
 */
export function FormField<T extends string = string>(props: FormFieldProps<T>) {
  const { 
    id, 
    label, 
    required, 
    disabled, 
    error, 
    helpText, 
    className,
    labelClassName,
    inputContainerClassName,
    inputClassName,
    errorClassName,
    helpClassName,
    success,
    autoFocus
  } = props;

  // Generate a unique ID for the field if none is provided
  const fieldId = id || React.useId();
  
  // Is this a checkbox or switch? These have different layouts
  const isToggle = props.type === 'checkbox' || props.type === 'switch';

  // Main container classes
  const containerClasses = cn(
    "space-y-2 w-full",
    className
  );

  // Input container classes
  const inputWrapperClasses = cn(
    isToggle ? "flex items-center gap-2" : "",
    inputContainerClassName
  );

  // Label classes
  const labelClasses = cn(
    "text-sm font-medium text-gray-700",
    required && "after:content-['*'] after:ml-0.5 after:text-red-500",
    isToggle && "order-2",
    labelClassName
  );

  // Shared input classes
  const sharedInputClasses = cn(
    "transition duration-200",
    error && "border-red-500 focus:ring-red-500 focus:border-red-500",
    success && "border-green-500 focus:ring-green-500 focus:border-green-500",
    inputClassName
  );

  // Error classes
  const errorClasses = cn(
    "text-sm text-red-500 flex items-center gap-1 mt-1",
    errorClassName
  );

  // Help text classes
  const helpClasses = cn(
    "text-xs text-gray-500 flex items-center gap-1 mt-1",
    helpClassName
  );

  // Render the appropriate input based on type
  const renderInput = () => {
    switch (props.type) {
      case 'text':
      case 'email':
      case 'password':
      case 'number':
      case 'date':
        return (
          <Input
            id={fieldId}
            type={props.type}
            value={props.value}
            onChange={(e) => props.onChange(e.target.value)}
            placeholder={props.placeholder}
            disabled={disabled}
            required={required}
            className={sharedInputClasses}
            min={props.min}
            max={props.max}
            step={props.step}
            autoFocus={autoFocus}
            aria-invalid={!!error}
            aria-describedby={error ? `${fieldId}-error` : helpText ? `${fieldId}-help` : undefined}
          />
        );
      
      case 'textarea':
        return (
          <Textarea
            id={fieldId}
            value={props.value}
            onChange={(e) => props.onChange(e.target.value)}
            placeholder={props.placeholder}
            disabled={disabled}
            required={required}
            className={sharedInputClasses}
            rows={props.rows || 4}
            autoFocus={autoFocus}
            aria-invalid={!!error}
            aria-describedby={error ? `${fieldId}-error` : helpText ? `${fieldId}-help` : undefined}
          />
        );
      
      case 'checkbox':
        return (
          <Checkbox
            id={fieldId}
            checked={props.checked}
            onCheckedChange={props.onChange}
            disabled={disabled}
            className={cn("h-4 w-4", sharedInputClasses)}
            aria-invalid={!!error}
            aria-describedby={error ? `${fieldId}-error` : helpText ? `${fieldId}-help` : undefined}
          />
        );
      
      case 'switch':
        return (
          <Switch
            id={fieldId}
            checked={props.checked}
            onCheckedChange={props.onChange}
            disabled={disabled}
            className={sharedInputClasses}
            aria-invalid={!!error}
            aria-describedby={error ? `${fieldId}-error` : helpText ? `${fieldId}-help` : undefined}
          />
        );
      
      case 'select':
        return (
          <DropdownSelector
            options={props.options}
            selectedValue={props.value}
            onChange={props.onChange}
            className={sharedInputClasses}
            label={props.placeholder}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <div className={containerClasses}>
      {/* For most inputs, label comes first */}
      {!isToggle && (
        <Label
          htmlFor={fieldId}
          className={labelClasses}
        >
          {label}
        </Label>
      )}

      {/* Input wrapper */}
      <div className={inputWrapperClasses}>
        {/* For checkboxes and switches, input comes first, then label */}
        {isToggle && (
          <>
            {renderInput()}
            <Label
              htmlFor={fieldId}
              className={labelClasses}
            >
              {label}
            </Label>
          </>
        )}
        
        {/* For text inputs and others */}
        {!isToggle && renderInput()}
      </div>

      {/* Error message */}
      {error && (
        <div className={errorClasses} id={`${fieldId}-error`} role="alert">
          <AlertCircle size={14} />
          <span>{error}</span>
        </div>
      )}

      {/* Success message */}
      {!error && success && (
        <div className="text-sm text-green-500 flex items-center gap-1 mt-1">
          <Check size={14} />
          <span>Looks good!</span>
        </div>
      )}

      {/* Help text */}
      {!error && helpText && (
        <div className={helpClasses} id={`${fieldId}-help`}>
          <Info size={14} />
          <span>{helpText}</span>
        </div>
      )}
    </div>
  );
}

export default FormField;
