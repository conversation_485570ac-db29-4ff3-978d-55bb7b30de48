import { RefreshCw } from 'lucide-react';
import { Button } from '../ui/Button';
import { cn } from "@/lib/utils";

interface AutoRefreshProps {
  isEnabled: boolean;
  onToggle: () => void;
  interval?: number; // Refresh interval in milliseconds
}

export function AutoRefresh({ isEnabled, onToggle, interval = 15000 }: AutoRefreshProps) {
  return (
    <Button
      variant={isEnabled ? "default" : "secondary"}
      onClick={onToggle}
      className={cn(
        "flex items-center gap-2 h-8 px-3 py-1.5 rounded-md bg-white/70 hover:bg-white/90 border border-gray-100",
        isEnabled
          ? "text-primary-blue"
          : "text-gray-500"
      )}
    >
      <RefreshCw size={14} className={isEnabled ? 'animate-spin' : ''} />
      <span className="text-[11px]">Auto-refresh</span>
    </Button>
  );
}