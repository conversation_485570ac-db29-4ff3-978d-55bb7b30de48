import React from 'react';
import { Square } from 'lucide-react';

export type ViewMode = '2d' | '3d';

interface ViewToggleProps {
  currentView: ViewMode;
  onChange: (view: ViewMode) => void;
  className?: string;
}

/**
 * A toggle component for switching between 2D and 3D views
 */
export const ViewToggle: React.FC<ViewToggleProps> = ({
  currentView,
  onChange,
  className = ''
}) => {
  return (
    <div className={`inline-flex items-center border border-gray-300 rounded-md overflow-hidden ${className}`}>
      <button
        className={`flex items-center justify-center px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
          currentView === '2d'
            ? 'bg-blue-50 text-blue-700'
            : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
        }`}
        onClick={() => onChange('2d')}
        aria-pressed={currentView === '2d'}
        title="Switch to 2D flat view"
      >
        <Square size={16} className="mr-1.5" />
        <span>2D</span>
      </button>
      <button
        className={`flex items-center justify-center px-3 py-1.5 text-sm font-medium transition-colors ${
          currentView === '3d'
            ? 'bg-blue-600 text-white'
            : 'bg-white text-gray-700 hover:bg-gray-50'
        }`}
        onClick={() => onChange('3d')}
        aria-pressed={currentView === '3d'}
        title="Switch to 3D perspective view"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" className="mr-1.5">
          <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
          <polyline points="3.27 6.96 12 12.01 20.73 6.96" />
          <line x1="12" y1="22.08" x2="12" y2="12" />
        </svg>
        <span>3D</span>
      </button>
    </div>
  );
};

export default ViewToggle;
