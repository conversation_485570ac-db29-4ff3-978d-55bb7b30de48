import React from 'react';

interface ErrorDisplayProps {
  message: string;
  onRetry?: () => void;
  className?: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ message, onRetry, className = '' }) => {
  return (
    <div 
      className={`border border-red-300 bg-red-50 p-4 rounded-lg shadow-sm text-red-700 ${className}`}
      role="alert"
    >
      <div className="flex justify-between items-center">
        <div>
          <p className="font-semibold text-red-800">Error</p>
          <p className="text-sm">{message}</p>
        </div>
        {onRetry && (
          <button 
            onClick={onRetry} 
            className="ml-4 px-4 py-1.5 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out"
            aria-label="Retry loading data"
          >
            Retry
          </button>
        )}
      </div>
    </div>
  );
};

export default ErrorDisplay;
