import React from 'react';

export const SoftwareVersion: React.FC = () => {
  // Get current date for version
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2); // Last 2 digits of year
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const version = `v1-${year}.${month}.0`;

  return (
    <div className="fixed bottom-2 right-2 text-[10px] text-gray-400 select-none pointer-events-none">
      {version}
    </div>
  );
};