import React, { useState, useEffect } from 'react';
import { Database, Server, X } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { apiClient } from '@/lib/api/enhancedApiClient';
import { cn } from '@/lib/utils';

export const FloatingMockDataToggle: React.FC = () => {
  const [useMockData, setUseMockData] = useState(apiClient.isUsingMockData());
  const [isExpanded, setIsExpanded] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  const handleToggle = (checked: boolean) => {
    setUseMockData(checked);
    // Use temporary override instead of master setting
    apiClient.setTemporaryMockDataOverride(checked);
    
    // Show tooltip briefly
    setShowTooltip(true);
    setTimeout(() => setShowTooltip(false), 2000);
    
    // Reload the page to apply the change, preserving the current path
    setTimeout(() => {
      // Preserve current path and reload
      const currentPath = window.location.pathname + window.location.search + window.location.hash;
      window.location.href = currentPath;
    }, 500);
  };

  useEffect(() => {
    // Get current state from API client
    setUseMockData(apiClient.isUsingMockData());
  }, []);

  return (
    <>
      {/* Floating button/toggle */}
      <div className="fixed bottom-4 right-4 z-50">
        {isExpanded ? (
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-3 animate-fade-in">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="text-sm font-semibold text-gray-700">Data Source</h3>
              <button
                onClick={() => setIsExpanded(false)}
                className="ml-auto p-0.5 hover:bg-gray-100 rounded transition-colors"
                aria-label="Close"
              >
                <X size={14} className="text-gray-500" />
              </button>
            </div>
            
            <div className="flex items-center gap-3">
              <Server className={cn(
                "w-5 h-5 transition-colors",
                !useMockData ? 'text-blue-600' : 'text-gray-400'
              )} />
              
              <Switch
                checked={useMockData}
                onCheckedChange={handleToggle}
                className="data-[state=checked]:bg-amber-600"
                aria-label="Toggle mock data"
              />
              
              <Database className={cn(
                "w-5 h-5 transition-colors",
                useMockData ? 'text-amber-600' : 'text-gray-400'
              )} />
            </div>
            
            <div className="mt-2 text-xs text-gray-600">
              Currently using: <span className="font-medium">{useMockData ? 'Mock Data' : 'Live API'}</span>
            </div>
            
            <div className="mt-1 text-[10px] text-gray-500">
              Temporary switch - Settings control remains unchanged
            </div>
          </div>
        ) : (
          <button
            onClick={() => setIsExpanded(true)}
            className={cn(
              "p-3 rounded-full shadow-lg transition-all duration-200 opacity-70 hover:opacity-100",
              "hover:scale-105 active:scale-95",
              useMockData 
                ? "bg-amber-50 hover:bg-amber-100 border-2 border-amber-300" 
                : "bg-blue-50 hover:bg-blue-100 border-2 border-blue-300"
            )}
            aria-label="Toggle data source"
          >
            {useMockData ? (
              <Database className="w-5 h-5 text-amber-600" />
            ) : (
              <Server className="w-5 h-5 text-blue-600" />
            )}
          </button>
        )}
      </div>

      {/* Tooltip */}
      {showTooltip && (
        <div className="fixed bottom-20 right-4 z-50 animate-fade-in">
          <div className="bg-gray-900 text-white text-xs px-3 py-2 rounded-lg shadow-lg">
            Switching to {useMockData ? 'Mock Data' : 'Live API'}...
          </div>
        </div>
      )}
    </>
  );
};