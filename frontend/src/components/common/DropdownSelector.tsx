import * as React from 'react';
import { ChevronDown } from 'lucide-react';

export interface SelectOption<T extends string> {
  id: T;
  name: string;
  icon?: React.ReactNode;
  color?: string;
}

interface DropdownSelectorProps<T extends string> {
  options: SelectOption<T>[];
  selectedValue: T;
  onChange: (value: T) => void;
  className?: string;
  width?: string; // For controlling dropdown width
  buttonClassName?: string; // For additional button styling
  label?: string; // Optional accessible label
}

/**
 * A unified dropdown selector component for consistent UI patterns
 * Used for selecting from a list of options with optional icons
 */
function DropdownSelector<T extends string>({
  options,
  selectedValue,
  onChange,
  className = '',
  width = '100%',
  buttonClassName = '',
  label
}: DropdownSelectorProps<T>) {
  const [isOpen, setIsOpen] = React.useState(false);
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // Find the selected option
  const selectedOption = options.find(opt => opt.id === selectedValue) || options[0];

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle selection
  const handleSelect = (id: T) => {
    onChange(id);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {label && <span className="sr-only">{label}</span>}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center justify-between gap-2 px-4 py-2 bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:shadow-md hover:border-blue-200 rounded-lg transition-all duration-300 ${buttonClassName}`}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-label={label}
      >
        <div className="flex items-center">
          {selectedOption.icon && (
            <span className="mr-2">
              {React.isValidElement(selectedOption.icon) 
                ? React.cloneElement(selectedOption.icon, {
                    size: selectedOption.icon.props.size || 16,
                    color: '#4b5563',
                    style: { color: '#4b5563' }
                  })
                : selectedOption.icon
              }
            </span>
          )}
          <span className="text-sm font-semibold truncate" style={{ color: '#1f2937' }}>{selectedOption.name}</span>
        </div>
        <ChevronDown size={16} className={`text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div 
          className="absolute right-0 z-50 mt-2 bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg py-1 max-h-64 overflow-auto focus:outline-none border border-[#EDEFF9]"
          style={{ width, zIndex: 50 }}
        >
          <ul role="listbox" className="py-1">
            {options.map((option) => {
              const isSelected = option.id === selectedValue;
              return (
                <li
                  key={option.id}
                  role="option"
                  aria-selected={isSelected}
                  onClick={() => handleSelect(option.id)}
                  className={`cursor-pointer text-sm px-4 py-2.5 hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 ${
                    isSelected ? 'bg-blue-50 text-blue-700 font-semibold' : 'text-gray-700'
                  }`}
                >
                  <div className="flex items-center">
                    {option.icon && (
                      <span className="mr-2">
                        {React.cloneElement(option.icon as React.ReactElement, {
                          color: isSelected ? '#2563eb' : (option.icon as React.ReactElement).props.color
                        })}
                      </span>
                    )}
                    {option.name}
                  </div>
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
}

export default DropdownSelector;
