import React from 'react';
import { ToggleSwitch } from '../ui/ToggleSwitch';
import { ChartDataType } from './ChartTypeToggle';

interface ChartTypeToggleSwitchProps {
  chartType: ChartDataType;
  onChange: (type: ChartDataType) => void;
  className?: string;
  size?: 'sm' | 'md';
  disabled?: boolean;
}

/**
 * Toggle switch component for switching between energy and power chart views
 */
const ChartTypeToggleSwitch: React.FC<ChartTypeToggleSwitchProps> = ({
  chartType,
  onChange,
  className = '',
  size = 'md',
  disabled = false
}) => {
  const isEnergyChart = chartType === 'energy';
  const isPowerChart = chartType === 'power';

  // Handle toggle change
  const handleToggleChange = (checked: boolean) => {
    if (!disabled) {
      onChange(checked ? 'power' : 'energy');
    }
  };

  return (
    <div className={`flex items-center space-x-2 ${className} ${disabled ? 'opacity-60' : ''}`}>
      <span className={`text-${size === 'sm' ? 'xs' : 'sm'} text-gray-600`}>Energy (kWh)</span>
      <ToggleSwitch
        checked={chartType === 'power'}
        onChange={handleToggleChange}
        size={size}
        disabled={disabled}
        aria-label="Toggle between energy and power data"
      />
      <span className={`text-${size === 'sm' ? 'xs' : 'sm'} ${disabled ? 'text-gray-400' : 'text-gray-600'}`}>Power (kW)</span>
    </div>
  );
};

export default ChartTypeToggleSwitch;
