import React from 'react';
import { BuildingType } from '../../types/analytics';
import DropdownSelector, { SelectOption } from './DropdownSelector';

// Define a more specific type for the building prop
interface BuildingOption {
  id: BuildingType;
  name: string;
}

interface BuildingSelectorProps {
  selectedBuilding: BuildingType;
  onBuildingChange: (id: BuildingType) => void;
  buildings: BuildingOption[];
  className?: string;
}

const BuildingSelector: React.FC<BuildingSelectorProps> = ({
  selectedBuilding,
  onBuildingChange,
  buildings,
  className = ''
}) => {
  // Convert to the format expected by DropdownSelector
  const options: SelectOption<BuildingType>[] = buildings.map(building => ({
    id: building.id,
    name: building.name
  }));

  return (
    <DropdownSelector
      options={options}
      selectedValue={selectedBuilding}
      onChange={onBuildingChange}
      className={className}
      label="Select Building"
    />
  );
};

export default BuildingSelector;
