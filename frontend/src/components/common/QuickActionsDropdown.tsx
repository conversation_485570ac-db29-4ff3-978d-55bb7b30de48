import React from 'react';
import { useRef, useState } from 'react';
import { MoreVertical, Edit, Trash, KeyRound, UserX } from 'lucide-react';

interface QuickActionsDropdownProps {
  onEdit: () => void;
  onDelete: () => void;
  onChangePassword?: () => void;
  onDeactivate?: () => void;
}

export function QuickActionsDropdown({ onEdit, onDelete, onChangePassword, onDeactivate }: QuickActionsDropdownProps) {
  const [open, setOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown on outside click
  function handleClickOutside(e: MouseEvent) {
    if (dropdownRef.current && !dropdownRef.current.contains(e.target as Node)) {
      setOpen(false);
    }
  }

  // Attach/detach event listener
  React.useEffect(() => {
    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [open]);

  return (
    <div className="relative inline-block text-left" ref={dropdownRef}>
      <button
        className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500/50 text-gray-500 hover:text-blue-600 transition-all"
        onClick={() => setOpen((v) => !v)}
        aria-label="Open quick actions"
        type="button"
      >
        <MoreVertical size={18} />
      </button>
      {open && (
        <div className="origin-top-right absolute right-0 mt-2 w-44 rounded-lg shadow-lg bg-white border border-gray-100 z-10 overflow-hidden">
          <div className="py-1">
            <button
              onClick={() => { setOpen(false); onEdit(); }}
              className="w-full flex items-center gap-2 px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
              type="button"
            >
              <Edit size={15} className="text-blue-500" /> Edit
            </button>
            {onChangePassword && (
              <button
                onClick={() => { setOpen(false); onChangePassword(); }}
                className="w-full flex items-center gap-2 px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 transition-colors"
                type="button"
              >
                <KeyRound size={15} className="text-blue-500" /> Change Password
              </button>
            )}
            {onDeactivate && (
              <button
                onClick={() => { setOpen(false); onDeactivate(); }}
                className="w-full flex items-center gap-2 px-4 py-2.5 text-sm text-gray-700 hover:bg-yellow-50 transition-colors"
                type="button"
              >
                <UserX size={15} className="text-amber-500" /> Deactivate
              </button>
            )}
            <button
              onClick={() => { setOpen(false); onDelete(); }}
              className="w-full flex items-center gap-2 px-4 py-2.5 text-sm text-gray-700 hover:bg-red-50 border-t border-gray-100 transition-colors"
              type="button"
            >
              <Trash size={15} className="text-red-500" /> Delete
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
