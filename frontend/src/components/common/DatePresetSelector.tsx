import * as React from 'react';
import { Clock, ChevronDown } from 'lucide-react';
import {
  startOfMonth,
  startOfYear,
  subDays,
  subMonths,
  isSameDay,
  isSameMonth,
  getDay,
  setDate
} from 'date-fns';
import { cn } from '@/lib/utils';

// Import from ViewType - same as in AnalyticsControls
export type ViewType = 'day' | 'week' | 'month' | 'year' | 'multi-year';

// Define date presets
export interface DatePreset {
  label: string;
  getDate: () => Date;
  view: ViewType;
  description?: string;
  id?: string; // Unique identifier for preset
}

export interface DatePresetSelectorProps {
  onPresetSelect: (preset: DatePreset) => void;
  selectedDate: Date;
  selectedView: ViewType;
  className?: string;
  customPresets?: DatePreset[]; // Allow custom presets to be passed
  buttonClassName?: string;
  dropdownClassName?: string;
  presetItemClassName?: string;
  label?: string; // Custom label for button
}

/**
 * A reusable component for selecting common date presets
 * Provides quick access to predefined time periods like Today, This Week, etc.
 */
export const DatePresetSelector: React.FC<DatePresetSelectorProps> = ({
  onPresetSelect,
  selectedDate,
  selectedView,
  className = '',
  customPresets = [],
  buttonClassName = '',
  dropdownClassName = '',
  presetItemClassName = '',
  label
}) => {
  const [isPresetsOpen, setIsPresetsOpen] = React.useState(false);
  const presetsDropdownRef = React.useRef<HTMLDivElement>(null);

  // Helper function to get start of week
  const getStartOfWeek = React.useCallback((date: Date = new Date()): Date => {
    const today = new Date(date);
    const dayOfWeek = getDay(today); // 0 = Sunday, 1 = Monday, etc.
    // Adjust to Monday (1) as start of week
    // If today is Sunday (0), go back 6 days to previous Monday
    // Otherwise, go back (dayOfWeek - 1) days
    return setDate(today, today.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1));
  }, []);

  // Define default date presets
  const DEFAULT_PRESETS: DatePreset[] = React.useMemo(() => [
    {
      id: 'today',
      label: 'Today',
      getDate: () => new Date(),
      view: 'day',
      description: 'View data for today'
    },
    {
      id: 'yesterday',
      label: 'Yesterday',
      getDate: () => subDays(new Date(), 1),
      view: 'day',
      description: 'View data for yesterday'
    },
    {
      id: 'this-week',
      label: 'This Week',
      getDate: () => getStartOfWeek(),
      view: 'week',
      description: 'View data for the current week (Monday to Sunday)'
    },
    {
      id: 'this-month',
      label: 'This Month',
      getDate: () => startOfMonth(new Date()),
      view: 'month',
      description: 'View data for the current month'
    },
    {
      id: 'last-month',
      label: 'Last Month',
      getDate: () => startOfMonth(subMonths(new Date(), 1)),
      view: 'month',
      description: 'View data for the previous month'
    },
    {
      id: 'year-to-date',
      label: 'Year to Date',
      getDate: () => startOfYear(new Date()),
      view: 'year',
      description: 'View data from the beginning of the year until today'
    },
    {
      id: 'five-year',
      label: '5-Year Overview',
      getDate: () => new Date(),
      view: 'multi-year',
      description: 'View data across a 5-year period centered on the current year'
    }
  ], [getStartOfWeek]);

  // Combine default and custom presets
  const DATE_PRESETS = React.useMemo(() => {
    // If custom presets are provided, add them to default presets
    // Filter out any default presets that have the same ID as custom presets
    const defaultPresetsToUse = customPresets.length > 0
      ? DEFAULT_PRESETS.filter(
          defaultPreset => !customPresets.some(
            customPreset => customPreset.id === defaultPreset.id
          )
        )
      : DEFAULT_PRESETS;

    return [...defaultPresetsToUse, ...customPresets];
  }, [DEFAULT_PRESETS, customPresets]);

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (presetsDropdownRef.current && !presetsDropdownRef.current.contains(event.target as Node)) {
        setIsPresetsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle pressing Escape key to close dropdown
  React.useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isPresetsOpen) {
        setIsPresetsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isPresetsOpen]);

  // Handle preset selection
  const handlePresetSelect = (preset: DatePreset) => {
    onPresetSelect(preset);
    setIsPresetsOpen(false);
  };

  // Check if a preset is currently active
  const isPresetActive = React.useCallback((preset: DatePreset): boolean => {
    const presetDate = preset.getDate();

    // Different comparison based on view type
    switch (preset.view) {
      case 'day':
        return isSameDay(selectedDate, presetDate) && selectedView === 'day';

      case 'month':
        return isSameMonth(selectedDate, presetDate) && selectedView === 'month';

      case 'week':
        if (preset.id === 'this-week') {
          return isSameDay(selectedDate, getStartOfWeek()) && selectedView === 'week';
        }
        return false;

      case 'year':
        if (preset.id === 'year-to-date') {
          return isSameDay(selectedDate, startOfYear(new Date())) && selectedView === 'year';
        }
        return false;

      case 'multi-year':
        if (preset.id === 'five-year') {
          // We only need to check if the view is set to multi-year
          // and that the selected date's year is the same as the current year
          const currentYear = new Date().getFullYear();
          return selectedDate.getFullYear() === currentYear && selectedView === 'multi-year';
        }
        return false;

      default:
        return false;
    }
  }, [selectedDate, selectedView, getStartOfWeek]);

  // Find active preset, if any
  const activePreset = React.useMemo(() => {
    return DATE_PRESETS.find(preset => isPresetActive(preset));
  }, [DATE_PRESETS, isPresetActive]);

  // Handle keyboard navigation in dropdown
  const handleKeyDown = (e: React.KeyboardEvent, index: number) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      const nextFocusElement = document.getElementById(`preset-item-${index + 1}`);
      if (nextFocusElement) nextFocusElement.focus();
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      const prevFocusElement = document.getElementById(`preset-item-${index - 1}`);
      if (prevFocusElement) prevFocusElement.focus();
    }
  };

  return (
    <div className={cn("relative", className)} ref={presetsDropdownRef}>
      <button
        onClick={() => setIsPresetsOpen(!isPresetsOpen)}
        className={cn(
          "flex items-center gap-2 px-4 py-2 bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:shadow-md hover:border-blue-200 rounded-lg transition-all duration-300",
          buttonClassName
        )}
        aria-label="Open date presets"
        aria-expanded={isPresetsOpen}
        aria-haspopup="listbox"
      >
        <Clock size={16} className="text-blue-500" />
        <span className="text-sm font-semibold text-gray-800">
          {label || activePreset?.label || 'Quick Select'}
        </span>
        <ChevronDown
          size={14}
          className={cn(
            "text-gray-500 transition-transform",
            isPresetsOpen ? 'rotate-180' : ''
          )}
        />
      </button>

      {isPresetsOpen && (
        <div
          className={cn(
            "absolute left-0 z-[9999] mt-2 w-48 bg-white rounded-xl shadow-xl border border-gray-200 py-2 animate-in fade-in-0 zoom-in-95 slide-in-from-top-2 duration-200",
            dropdownClassName
          )}
          role="listbox"
          aria-labelledby="date-preset-dropdown"
          style={{
            zIndex: 9999,
            position: 'absolute',
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
          }}
        >
          {DATE_PRESETS.map((preset, index) => (
            <button
              id={`preset-item-${index}`}
              key={preset.id || preset.label}
              onClick={() => handlePresetSelect(preset)}
              className={cn(
                "flex items-center w-full px-4 py-2.5 text-sm hover:bg-blue-50 hover:text-blue-700 transition-all duration-200",
                isPresetActive(preset) ? 'bg-blue-50 text-blue-700 font-semibold' : 'text-gray-700',
                presetItemClassName
              )}
              title={preset.description}
              role="option"
              aria-selected={isPresetActive(preset)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              tabIndex={0}
            >
              {preset.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default DatePresetSelector;
