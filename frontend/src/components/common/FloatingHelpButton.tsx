import React, { useState } from 'react';
import { HelpCircle, BookOpen, PresentationIcon, X, MessageCircle, Sparkles } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export const FloatingHelpButton: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [showWhatsComingModal, setShowWhatsComingModal] = useState(false);
  const navigate = useNavigate();

  const handleManualClick = () => {
    navigate('/manual');
    setIsOpen(false);
  };

  const handleTrainingClick = () => {
    navigate('/training');
    setIsOpen(false);
  };

  const handleWhatsComingClick = () => {
    setShowWhatsComingModal(true);
    setIsOpen(false);
  };

  return (
    <div className="fixed bottom-4 left-4 z-50">
      {/* Expanded menu - Light theme */}
      {isOpen && (
        <div className="absolute bottom-12 left-0 animate-in slide-in-from-bottom-2 fade-in duration-200">
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-1 min-w-[220px]">
            <div className="p-2">
              <div className="text-xs font-medium text-gray-500 uppercase tracking-wider px-3 py-1.5">
                Help & Resources
              </div>
            </div>
            
            <button
              onClick={handleManualClick}
              className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-all group"
            >
              <BookOpen size={16} className="text-gray-500 group-hover:text-blue-600" />
              <div className="text-left">
                <div className="font-medium">User Manual</div>
                <div className="text-xs text-gray-500">Browse documentation</div>
              </div>
            </button>
            
            <button
              onClick={handleTrainingClick}
              className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-all group"
            >
              <PresentationIcon size={16} className="text-gray-500 group-hover:text-blue-600" />
              <div className="text-left">
                <div className="font-medium">Training Slides</div>
                <div className="text-xs text-gray-500">Interactive tutorials</div>
              </div>
            </button>
            
            <button
              onClick={handleWhatsComingClick}
              className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-all group"
            >
              <Sparkles size={16} className="text-gray-500 group-hover:text-[#FFC000]" />
              <div className="text-left">
                <div className="font-medium">What's Coming</div>
                <div className="text-xs text-gray-500">Development roadmap</div>
              </div>
            </button>
            
            <div className="border-t border-gray-200 my-1"></div>
            
            <button
              className="w-full flex items-center gap-3 px-3 py-2.5 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-all group opacity-50 cursor-not-allowed"
            >
              <MessageCircle size={16} className="text-gray-400" />
              <div className="text-left">
                <div className="font-medium">Contact Support</div>
                <div className="text-xs text-gray-500">Coming soon</div>
              </div>
            </button>
          </div>
        </div>
      )}

      {/* Main help button - More subtle design */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          relative flex items-center justify-center w-9 h-9 rounded-full
          bg-gray-100 text-gray-400 border border-gray-200
          shadow-sm hover:shadow
          hover:bg-gray-50 hover:text-gray-600 hover:border-gray-300
          transition-all duration-200
          ${isOpen ? 'scale-95' : ''}
        `}
      >
        <span className="relative z-10">
          {isOpen ? (
            <X size={14} className="transition-transform duration-200" />
          ) : (
            <HelpCircle size={14} className="transition-transform duration-200" />
          )}
        </span>
      </button>

      {/* What's Coming Modal */}
      {showWhatsComingModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden animate-in fade-in zoom-in duration-200">
            <div style={{ backgroundColor: '#FFC000' }} className="sticky top-0 p-4 text-black">
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-xl font-bold flex items-center gap-2">
                    <Sparkles size={24} />
                    What's Coming
                  </h2>
                  <p className="text-sm opacity-80 mt-1">Development Roadmap & Tasks</p>
                </div>
                <button
                  onClick={() => setShowWhatsComingModal(false)}
                  className="p-1 hover:bg-black/10 rounded transition-colors"
                >
                  <X size={20} />
                </button>
              </div>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(80vh-100px)]">
              <div className="space-y-6">
                {/* Priority Tasks */}
                <section>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                    Priority Tasks
                  </h3>
                  <div className="space-y-2">
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <h4 className="font-medium text-red-800">📸 Capture Software Screenshots</h4>
                      <p className="text-sm text-red-700 mt-1">
                        Replace all placeholder images in training slides with actual screenshots from the live system
                      </p>
                      <ul className="text-xs text-red-600 mt-2 space-y-1 ml-4">
                        <li>• Dashboard overview screenshots</li>
                        <li>• Analytics page with real data visualizations</li>
                        <li>• Meter management interface</li>
                        <li>• Alarm configuration screens</li>
                        <li>• Report generation examples</li>
                      </ul>
                    </div>
                  </div>
                </section>

                {/* Training Materials */}
                <section>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                    Training Materials Update
                  </h3>
                  <div className="space-y-2">
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <h4 className="font-medium text-yellow-800">Update All Training Slides</h4>
                      <ul className="text-sm text-yellow-700 mt-2 space-y-1">
                        <li>✓ Replace placeholder content with actual system features</li>
                        <li>✓ Add step-by-step guides for common tasks</li>
                        <li>✓ Include troubleshooting sections</li>
                        <li>✓ Create quick reference cards</li>
                      </ul>
                    </div>
                  </div>
                </section>

                {/* UI/UX Improvements */}
                <section>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                    UI/UX Enhancements
                  </h3>
                  <div className="space-y-2">
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>• Modernize date/time picker designs</li>
                        <li>• Improve color consistency across reports</li>
                        <li>• Enhance export functionality visibility</li>
                        <li>• Add keyboard shortcuts guide</li>
                      </ul>
                    </div>
                  </div>
                </section>

                {/* Documentation */}
                <section>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                    Documentation Tasks
                  </h3>
                  <div className="space-y-2">
                    <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                      <ul className="text-sm text-green-700 space-y-1">
                        <li>• Create user onboarding checklist</li>
                        <li>• Document API integration guides</li>
                        <li>• Write troubleshooting FAQ</li>
                      </ul>
                    </div>
                  </div>
                </section>
              </div>
              
              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <p className="text-xs text-gray-600 text-center">
                  Last updated: {new Date().toLocaleDateString()} | Contact development team for updates
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};