import React, { ButtonHTMLAttributes, ReactNode } from 'react';

export type ButtonVariant = 'primary' | 'secondary' | 'tertiary' | 'outline' | 'ghost';
export type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  isActive?: boolean;
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
  children: ReactNode;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'secondary',
  size = 'md',
  isActive = false,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  children,
  className = '',
  ...props
}) => {
  // Base styles
  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-all duration-200 focus:outline-none';

  // Size styles
  const sizeStyles = {
    sm: 'text-xs px-2 py-1 gap-1',
    md: 'text-sm px-3 py-1.5 gap-1.5',
    lg: 'text-base px-4 py-2 gap-2',
  };

  // Variant styles
  const variantStyles = {
    primary: `bg-blue-600 text-white hover:bg-blue-700 ${isActive ? 'bg-blue-700 shadow-inner' : ''}`,
    secondary: `bg-gray-100 text-gray-700 hover:bg-gray-200 ${isActive ? 'bg-white text-gray-800 shadow-sm' : ''}`,
    tertiary: `bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 ${isActive ? 'bg-gray-50 shadow-inner' : ''}`,
    outline: `bg-transparent border border-gray-300 text-gray-700 hover:bg-gray-50 ${isActive ? 'bg-gray-50 border-gray-400 shadow-inner' : ''}`,
    ghost: `bg-transparent text-gray-600 hover:bg-gray-100 ${isActive ? 'bg-gray-100 text-gray-800' : ''}`,
  };

  // Active state styles
  const activeStyles = isActive ? 'font-medium' : '';

  // Width styles
  const widthStyles = fullWidth ? 'w-full' : '';

  // Combine all styles
  const buttonStyles = `${baseStyles} ${sizeStyles[size]} ${variantStyles[variant]} ${activeStyles} ${widthStyles} ${className}`;

  return (
    <button className={buttonStyles} {...props}>
      {icon && iconPosition === 'left' && <span className="button-icon">{icon}</span>}
      {children}
      {icon && iconPosition === 'right' && <span className="button-icon">{icon}</span>}
    </button>
  );
};

export default Button;
