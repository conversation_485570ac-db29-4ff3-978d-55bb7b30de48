import React from 'react';

export type ChartDataType = 'energy' | 'power';

interface ChartTypeToggleProps {
  chartType: ChartDataType;
  onChange: (type: ChartDataType) => void;
  className?: string;
}

/**
 * Reusable toggle component for switching between energy and power chart views
 */
const ChartTypeToggle: React.FC<ChartTypeToggleProps> = ({ 
  chartType, 
  onChange,
  className = ''
}) => {
  const isEnergyChart = chartType === 'energy';
  const isPowerChart = chartType === 'power';

  return (
    <div className={`flex items-center border border-gray-300 rounded-md overflow-hidden ${className}`}>
      <button
        className={`px-3 py-1.5 text-sm font-medium ${
          isEnergyChart 
            ? 'bg-blue-600 text-white' 
            : 'bg-white text-gray-700 hover:bg-gray-50'
        }`}
        onClick={() => onChange('energy')}
        aria-pressed={isEnergyChart}
        aria-label="Show energy data"
      >
        Energy (kWh)
      </button>
      <button
        className={`px-3 py-1.5 text-sm font-medium ${
          isPowerChart 
            ? 'bg-blue-600 text-white' 
            : 'bg-white text-gray-700 hover:bg-gray-50'
        }`}
        onClick={() => onChange('power')}
        aria-pressed={isPowerChart}
        aria-label="Show power data"
      >
        Power (kW)
      </button>
    </div>
  );
};

export default ChartTypeToggle;
