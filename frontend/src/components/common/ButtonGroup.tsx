import React from 'react';

interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'compact';
}

const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  className = '',
  variant = 'default'
}) => {
  const baseStyles = 'inline-flex rounded-lg';
  const variantStyles = {
    default: 'bg-gray-100 p-1 gap-1',
    compact: 'bg-gray-100 p-0.5 gap-0.5'
  };
  
  return (
    <div 
      className={`${baseStyles} ${variantStyles[variant]} ${className}`}
      data-component-name="ButtonGroup"
    >
      {children}
    </div>
  );
};

export default ButtonGroup;
