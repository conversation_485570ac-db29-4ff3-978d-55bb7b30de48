import React, { useState, useRef, useEffect } from 'react';
import { Download, ChevronDown, FileText } from 'lucide-react';

export interface ExportButtonProps {
  onExportCSV: () => void;
  onExportPDF?: () => void;
  className?: string;
  buttonSize?: 'sm' | 'md';
}

const ExportButton: React.FC<ExportButtonProps> = ({
  onExportCSV,
  onExportPDF,
  className = '',
  buttonSize = 'md'
}) => {
  const [isExportOpen, setIsExportOpen] = useState(false);
  const exportRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close export dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (exportRef.current && !exportRef.current.contains(event.target as Node)) {
        setIsExportOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle export options
  const handleExport = (format: 'pdf' | 'csv') => {
    // Close the dropdown
    setIsExportOpen(false);
    
    if (format === 'csv') {
      onExportCSV();
    } else if (format === 'pdf' && onExportPDF) {
      onExportPDF();
    }
  };

  const buttonClasses = buttonSize === 'sm' 
    ? "ml-auto flex items-center gap-1 mt-2 text-xs text-blue-600 hover:text-blue-800 transition-colors px-2 py-1 rounded hover:bg-blue-50"
    : "flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-md shadow-sm hover:bg-blue-50 hover:border-blue-300 transition-all duration-200";

  return (
    <div className={`relative ${className}`} ref={exportRef}>
      <button 
        onClick={() => setIsExportOpen(!isExportOpen)}
        className={buttonClasses}
      >
        <Download size={buttonSize === 'sm' ? 14 : 16} className={`text-blue-500 transition-transform duration-200 ${isExportOpen ? 'transform rotate-180' : ''}`} />
        Export
        <ChevronDown size={buttonSize === 'sm' ? 14 : 16} className={`text-gray-500 transition-transform duration-200 ${isExportOpen ? 'rotate-180' : ''}`} />
      </button>

      {isExportOpen && (
        <div className={`absolute right-0 mt-2 ${buttonSize === 'sm' ? 'w-40' : 'w-48'} bg-white border border-gray-200 rounded-md shadow-lg z-10 animate-fadeIn`}>
          <div className="py-2">
            {onExportPDF && (
              <button 
                onClick={() => handleExport('pdf')}
                className="flex items-center gap-2 w-full px-4 py-2.5 text-sm text-gray-700 hover:bg-red-50 transition-colors duration-150"
              >
                <FileText size={buttonSize === 'sm' ? 14 : 16} className="text-red-500 drop-shadow-sm" />
                <span>Export as PDF</span>
              </button>
            )}
            <button 
              onClick={() => handleExport('csv')}
              className="flex items-center gap-2 w-full px-4 py-2.5 text-sm text-gray-700 hover:bg-green-50 transition-colors duration-150"
            >
              <FileText size={buttonSize === 'sm' ? 14 : 16} className="text-green-500 drop-shadow-sm" />
              <span>Export as CSV</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExportButton;
