import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { RefreshCw, Settings, Mail, AlertTriangle } from 'lucide-react';
import { format } from 'date-fns';

// Mock data for development
const MOCK_NOTIFICATIONS = {
  results: [
    {
      id: 1,
      subject: 'Consumption Spike Alert',
      body: 'Unusual energy consumption detected for Device A',
      status: 'SENT',
      category: 'consumption_spike',
      sent_at: new Date().toISOString(),
      data: {
        anomaly_type: 'consumption_spike',
        severity: 'high',
        device_name: 'Device A',
        parameter_name: 'Energy Consumption',
        value: 150
      }
    },
    {
      id: 2,
      subject: 'Device Offline Alert',
      body: 'Device B has been offline for more than 30 minutes',
      status: 'SENT',
      category: 'device_offline',
      sent_at: new Date(Date.now() - 3600000).toISOString(),
      data: {
        anomaly_type: 'device_offline',
        severity: 'critical',
        device_name: 'Device B',
        parameter_name: 'Connection Status',
        value: 0
      }
    },
    {
      id: 3,
      subject: 'Power Quality Alert',
      body: 'Power quality issues detected for Device C',
      status: 'SENT',
      category: 'power_quality',
      sent_at: new Date(Date.now() - 7200000).toISOString(),
      data: {
        anomaly_type: 'power_quality',
        severity: 'medium',
        device_name: 'Device C',
        parameter_name: 'Power Factor',
        value: 0.85
      }
    }
  ]
};

interface Notification {
  id: number;
  subject: string;
  body: string;
  status: string;
  category: string;
  sent_at: string;
  data: {
    anomaly_type: string;
    severity: string;
    device_name?: string;
    parameter_name?: string;
    value?: number;
  };
}

const EmailAlertDashboard: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // Fetch recent email notifications
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/notifications/?channel_type=email&limit=10');
      setNotifications(response.data.results);
      setError(null);
    } catch (err: any) {
      console.error('Error fetching email notifications:', err);
      // Use mock data in development
      setNotifications(MOCK_NOTIFICATIONS.results);
      setError('Could not fetch notifications from server. Using mock data for development.');
    } finally {
      setLoading(false);
    }
  };

  // Navigate to email alert settings
  const goToSettings = () => {
    navigate('/settings/email-alerts');
  };

  // Format date
  const formatDate = (dateString: string): string => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy HH:mm');
    } catch (err) {
      return dateString;
    }
  };

  // Get severity class
  const getSeverityClass = (severity: string): string => {
    if (!severity) return 'bg-gray-100 text-gray-800';
    
    switch (severity.toLowerCase()) {
      case 'high':
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Load notifications on component mount
  useEffect(() => {
    fetchNotifications();
  }, []);

  return (
    <div className="mb-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Email Alerts Dashboard</h2>
        <div className="flex space-x-2">
          <button
            onClick={fetchNotifications}
            disabled={loading}
            className="p-2 rounded-full text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue disabled:opacity-50"
            title="Refresh"
          >
            <RefreshCw className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <button
            onClick={goToSettings}
            className="p-2 rounded-full text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue"
            title="Email Alert Settings"
          >
            <Settings className="h-5 w-5" />
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {/* Recent Email Alerts */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-medium">Recent Email Alerts</h3>
          </div>
          <div className="p-4">
            {loading ? (
              <div className="flex justify-center items-center min-h-[200px]">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
              </div>
            ) : error ? (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  <span>{error}</span>
                </div>
              </div>
            ) : notifications.length === 0 ? (
              <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
                <div className="flex items-center">
                  <Mail className="h-5 w-5 mr-2" />
                  <span>No recent email alerts found.</span>
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Subject
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Device
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Parameter
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Severity
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {notifications.map((notification) => (
                      <tr key={notification.id} className="hover:bg-gray-50">
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(notification.sent_at)}
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-900">
                          {notification.subject}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                          {notification.data?.device_name || 'N/A'}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                          {notification.data?.parameter_name || 'N/A'}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getSeverityClass(notification.data?.severity)}`}>
                            {notification.data?.severity || 'N/A'}
                          </span>
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            notification.status === 'SENT'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {notification.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-medium">Quick Actions</h3>
          </div>
          <div className="p-4">
            <div className="flex flex-wrap gap-3">
              <button
                onClick={goToSettings}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue"
              >
                <Settings className="-ml-1 mr-2 h-4 w-4" />
                Manage Email Alert Settings
              </button>
              <button
                onClick={() => navigate('/settings/email-alerts/test')}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue"
              >
                <Mail className="-ml-1 mr-2 h-4 w-4" />
                Send Test Email
              </button>
              <button
                onClick={() => navigate('/settings/email-alerts/scan')}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-blue hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue"
              >
                <AlertTriangle className="-ml-1 mr-2 h-4 w-4" />
                Scan for Anomalies
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailAlertDashboard;
