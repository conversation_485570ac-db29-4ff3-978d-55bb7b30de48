import React, { useState, useEffect } from 'react';
import { CheckCir<PERSON>, AlertTriangle, RefreshCw, Mail, Clock } from 'lucide-react';
import axios from 'axios';

// Define Severity levels array
export const SEVERITY_LEVELS = ['critical', 'high', 'medium', 'low'] as const;
export type SeverityLevel = typeof SEVERITY_LEVELS[number];

// Mock data for development
const MOCK_SETTINGS = {
  preferences: [
    {
      id: 1,
      channel: {
        id: 1,
        name: 'Email',
        type: 'email',
      },
      category: 'consumption_spike',
      category_display: 'Consumption Spike',
      is_enabled: true,
      quiet_hours_start: '08:00',
      quiet_hours_end: '17:00',
    },
    {
      id: 2,
      channel: {
        id: 1,
        name: 'Email',
        type: 'email',
      },
      category: 'device_offline',
      category_display: 'Device Offline',
      is_enabled: true,
      quiet_hours_start: '08:00',
      quiet_hours_end: '17:00',
    },
  ],
  channels: [
    {
      id: 1,
      name: '<PERSON><PERSON>',
      type: 'email',
      description: 'Email notifications',
    },
  ],
  alarm_subscriptions: [
    {
      id: 1,
      name: 'Consumption Spike',
      category: 'consumption_spike',
      category_display: 'Consumption Spike',
      severity: 'high',
      severity_display: 'High',
      device: 'Device 1',
      parameter: 'Power Demand',
      condition: '>',
      threshold_value: 500,
    },
    {
      id: 2,
      name: 'Device Offline',
      category: 'device_offline',
      category_display: 'Device Offline',
      severity: 'critical',
      severity_display: 'Critical',
      device: 'Device 2',
      parameter: 'Parameter 2',
      condition: '<',
      threshold_value: 5,
    },
  ],
};

export interface NotificationPreference {
  id: number;
  channel: NotificationChannel;
  category: string;
  category_display: string;
  is_enabled: boolean;
  enabled_severities: SeverityLevel[] | null;
}

export interface NotificationChannel {
  id: number;
  name: string;
  type: string;
  description: string;
}

export interface AlarmSubscription {
  id: number;
  name: string;
  category: string;
  category_display: string;
  severity: string;
  severity_display: string;
  device: string | null;
  parameter: string | null;
  condition: string;
  threshold_value: number;
}

export interface EmailAlertSettings {
  preferences: NotificationPreference[];
  channels: NotificationChannel[];
  alarm_subscriptions: AlarmSubscription[];
}

const EmailAlertSettings: React.FC = () => {
  const [settings, setSettings] = useState<EmailAlertSettings | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [notification, setNotification] = useState<{
    show: boolean;
    message: string;
    type: 'success' | 'error' | 'info';
  }>({
    show: false,
    message: '',
    type: 'info',
  });
  const [scanLoading, setScanLoading] = useState<boolean>(false);
  const [scanResults, setScanResults] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<'preferences' | 'subscriptions' | 'testing'>('preferences');

  // Fetch email alert settings
  const fetchSettings = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a production environment, this would be an actual API call
      // For now, we'll use mock data directly to avoid 404 errors
      // setSettings(MOCK_SETTINGS); // Commented out mock settings usage
      
      // Uncomment this when the API is available
      // const response = await axios.get('/api/notifications/email-alerts/settings/');
      // setSettings(response.data);
    } catch (err: any) {
      console.error('Error fetching email alert settings:', err);
      
      // Use mock data in development (Removed quiet hours)
      // setSettings(MOCK_SETTINGS); // Ensure this is commented out or removed
      setError('Could not fetch settings from server. Using mock data for development.');
    } finally {
      setLoading(false);
    }
  };

  // Update notification preference
  const updatePreference = async (preference: NotificationPreference, newValue: boolean) => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      const payload = {
        channel_id: preference.channel.id,
        category: preference.category,
        is_enabled: newValue,
      };
      
      // In a production environment, this would be an actual API call
      // For now, we'll update the local state directly
      
      // Uncomment this when the API is available
      // await axios.post('/api/notifications/email-alerts/update_preferences/', payload);
      
      // Update local state (Removed quiet hours from payload)
      setSettings(prevSettings => {
        if (!prevSettings) return prevSettings;
        return {
          ...prevSettings,
          preferences: prevSettings.preferences.map(p => 
            p.id === preference.id ? { ...p, is_enabled: newValue } : p
          )
        };
      });
      
      setSuccess(`Successfully ${newValue ? 'enabled' : 'disabled'} ${preference.category_display} notifications`);
      
      // Show notification
      setNotification({
        show: true,
        message: `Successfully ${newValue ? 'enabled' : 'disabled'} ${preference.category_display} notifications`,
        type: 'success'
      });
      
      // Hide notification after 3 seconds
      setTimeout(() => {
        setNotification(prev => ({ ...prev, show: false }));
      }, 3000);
      
    } catch (err: any) {
      console.error('Error updating notification preference:', err);
      
      // Update local state anyway for development purposes
      setSettings(prevSettings => {
        if (!prevSettings) return prevSettings;
        return {
          ...prevSettings,
          preferences: prevSettings.preferences.map(p => 
            p.id === preference.id ? { ...p, is_enabled: newValue } : p
          )
        };
      });
      
      setSuccess(`Successfully ${newValue ? 'enabled' : 'disabled'} ${preference.category_display} notifications`);
    } finally {
      setLoading(false);
    }
  };

  // Update quiet hours
  const updateQuietHours = async (preference: NotificationPreference, start: string | null, end: string | null) => {
    // This function is no longer needed and can be removed or fully commented out
    /* setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      const payload = {
        channel_id: preference.channel.id,
        category: preference.category,
        is_enabled: preference.is_enabled, 
        quiet_hours_start: start,
        quiet_hours_end: end,
      };
      
      // In a production environment, this would be an actual API call
      // For now, we'll update the local state directly
      
      // Uncomment this when the API is available
      // await axios.post('/api/notifications/email-alerts/update_preferences/', payload);
      
      // Update local state
      setSettings(prevSettings => {
        if (!prevSettings) return prevSettings;
        return {
          ...prevSettings,
          preferences: prevSettings.preferences.map(p => 
            p.id === preference.id ? { ...p, quiet_hours_start: start, quiet_hours_end: end } : p
          )
        };
      });
      
      setSuccess(`Successfully updated quiet hours for ${preference.category_display} notifications`);
      
    } catch (err: any) {
      console.error('Error updating quiet hours:', err);
      setError('Could not update quiet hours.');
    } finally {
      setLoading(false);
    } */
  };

  // Send test email
  const sendTestEmail = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      // In a production environment, this would be an actual API call
      // For now, we'll simulate a successful test email send
      
      // Uncomment this when the API is available
      // await axios.post('/api/notifications/email-alerts/send_test/');
      
      setSuccess('Test email sent successfully!');
      
      // Show notification
      setNotification({
        show: true,
        message: 'Test email sent successfully!',
        type: 'success'
      });
      
      // Hide notification after 3 seconds
      setTimeout(() => {
        setNotification(prev => ({ ...prev, show: false }));
      }, 3000);
      
    } catch (err: any) {
      console.error('Error sending test email:', err);
      
      setError('API error occurred, but test email simulation was successful for development purposes.');
    } finally {
      setLoading(false);
    }
  };

  // Trigger anomaly scan
  const triggerScan = async (dryRun: boolean = true) => {
    setScanLoading(true);
    try {
      // In a production environment, this would be an actual API call
      // For now, we'll simulate a successful scan
      
      // Uncomment this when the API is available
      // const response = await axios.post(
      //   `/api/notifications/email-alerts/trigger_scan/?dry_run=${dryRun}&type=all`
      // );
      
      setScanResults({
        dry_run: dryRun,
        total_anomalies: 10,
        grouped_anomalies: 5,
        alerts_sent: 3,
        alerts_suppressed: 2,
        errors: 1,
        would_send: [
          {
            severity: 'High',
            message: 'Anomaly detected',
            recipients: ['<EMAIL>', '<EMAIL>']
          }
        ]
      });
      
      setNotification({
        show: true,
        message: dryRun
          ? `Scan completed: Found ${10} anomalies`
          : `Scan completed: Sent ${3} alerts`,
        type: 'info',
      });
      
      // Hide notification after 3 seconds
      setTimeout(() => {
        setNotification(prev => ({ ...prev, show: false }));
      }, 3000);
      
    } catch (err: any) {
      setNotification({
        show: true,
        message: err.message || 'Failed to trigger anomaly scan',
        type: 'error',
      });
      console.error('Error triggering anomaly scan:', err);
    } finally {
      setScanLoading(false);
    }
  };

  // Toggle notification preference
  const togglePreference = (preference: NotificationPreference) => {
    const updatedPreference = {
      ...preference,
      is_enabled: !preference.is_enabled,
    };
    updatePreference(updatedPreference, !preference.is_enabled);
  };

  // Close notification
  const closeNotification = () => {
    setNotification({ ...notification, show: false });
  };

  // Load settings on component mount
  useEffect(() => {
    fetchSettings();
    
    // Clear any error messages after 5 seconds
    const timer = setTimeout(() => {
      if (error) setError(null);
      if (success) setSuccess(null);
    }, 5000);
    
    return () => clearTimeout(timer);
  }, []);

  // Auto-hide notification after 5 seconds
  useEffect(() => {
    if (notification.show) {
      const timer = setTimeout(() => {
        closeNotification();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [notification.show]);

  if (loading && !settings) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
      </div>
    );
  }

  if (error && !settings) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mt-2">
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Tabs */}
      <div className="flex flex-col space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">Email Alert Settings</h2>
          <button
            onClick={sendTestEmail}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-blue hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue disabled:opacity-50"
          >
            <Mail className="mr-2 h-4 w-4" />
            Send Test Email
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('preferences')}
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === 'preferences'
                ? 'border-b-2 border-primary-blue text-primary-blue'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Notification Preferences
          </button>
          <button
            onClick={() => setActiveTab('subscriptions')}
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === 'subscriptions'
                ? 'border-b-2 border-primary-blue text-primary-blue'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Alarm Subscriptions
          </button>
          <button
            onClick={() => setActiveTab('testing')}
            className={`py-2 px-4 text-sm font-medium ${
              activeTab === 'testing'
                ? 'border-b-2 border-primary-blue text-primary-blue'
                : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Test & Scan
          </button>
        </div>
      </div>

      {/* Status Messages */}
      {success && (
        <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            <span>{success}</span>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            <span>{error}</span>
          </div>
        </div>
      )}

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Email Notification Preferences Tab */}
        {activeTab === 'preferences' && (
          <div>
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium">Email Notification Preferences</h3>
              <p className="text-sm text-gray-500 mt-1">Configure which types of notifications you want to receive via email</p>
            </div>
            <div className="p-4">
              {settings?.preferences.map((preference) => (
                <div key={preference.id} className="mb-4 pb-4 border-b border-gray-200 last:border-b-0 last:mb-0 last:pb-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <label className="inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={preference.is_enabled}
                          onChange={() => togglePreference(preference)}
                          className="sr-only peer"
                        />
                        <div className="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-blue"></div>
                        <span className="ml-3 text-sm font-medium text-gray-900">{preference.category_display}</span>
                      </label>
                      <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                        {preference.channel.type}
                      </span>
                    </div>
                  </div>
                </div>
              ))}

              {settings?.preferences.length === 0 && (
                <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
                  No email notification preferences found.
                </div>
              )}
            </div>
          </div>
        )}

        {/* Alarm Subscriptions Tab */}
        {activeTab === 'subscriptions' && (
          <div>
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium">Alarm Subscriptions</h3>
              <p className="text-sm text-gray-500 mt-1">View your current alarm subscriptions and their settings</p>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {settings?.alarm_subscriptions.map((subscription) => (
                  <div key={subscription.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="font-medium">{subscription.name}</div>
                    <div className="flex gap-2 mt-1">
                      <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                        subscription.severity === 'critical' || subscription.severity === 'high'
                          ? 'bg-red-100 text-red-800'
                          : subscription.severity === 'medium'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {subscription.severity_display}
                      </span>
                      <span className="px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                        {subscription.category_display}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 mt-2">
                      {subscription.device && `Device: ${subscription.device}`}
                      {subscription.parameter && ` • Parameter: ${subscription.parameter}`}
                    </div>
                    <div className="text-sm text-gray-600">
                      Condition: {subscription.condition} {subscription.threshold_value}
                    </div>
                  </div>
                ))}
              </div>

              {settings?.alarm_subscriptions.length === 0 && (
                <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg">
                  No alarm subscriptions found.
                </div>
              )}
            </div>
          </div>
        )}

        {/* Test and Scan Tab */}
        {activeTab === 'testing' && (
          <div>
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-lg font-medium">Test and Scan</h3>
              <p className="text-sm text-gray-500 mt-1">Test email alerts and scan for anomalies</p>
            </div>
            <div className="p-4">
              <div className="flex flex-wrap gap-3">
                <button
                  onClick={() => triggerScan(true)}
                  disabled={scanLoading}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {scanLoading && scanResults?.dry_run ? (
                    <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                  ) : (
                    <AlertTriangle className="-ml-1 mr-2 h-4 w-4" />
                  )}
                  Scan for Anomalies (Dry Run)
                </button>
                <button
                  onClick={() => triggerScan(false)}
                  disabled={scanLoading}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-blue hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-blue disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {scanLoading && !scanResults?.dry_run ? (
                    <RefreshCw className="animate-spin -ml-1 mr-2 h-4 w-4" />
                  ) : (
                    <Mail className="-ml-1 mr-2 h-4 w-4" />
                  )}
                  Scan and Send Alerts
                </button>
              </div>

              {scanResults && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Scan Results</h4>
                  <div className={`p-4 rounded-lg mb-3 ${
                    scanResults.dry_run 
                      ? 'bg-blue-50 border border-blue-200 text-blue-700' 
                      : 'bg-green-50 border border-green-200 text-green-700'
                  }`}>
                    {scanResults.dry_run
                      ? `Found ${scanResults.total_anomalies} anomalies (grouped into ${scanResults.grouped_anomalies})`
                      : `Sent ${scanResults.alerts_sent} alerts (${scanResults.alerts_suppressed} suppressed, ${scanResults.errors} errors)`}
                  </div>

                  {scanResults.dry_run && scanResults.would_send && (
                    <div>
                      <div className="text-sm text-gray-700 mb-2">Alerts that would be sent:</div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {scanResults.would_send.map((alert: any, index: number) => (
                          <div key={index} className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                            <div className="font-medium">
                              <span className={`${
                                alert.severity.toLowerCase() === 'high' 
                                  ? 'text-red-600' 
                                  : alert.severity.toLowerCase() === 'medium'
                                  ? 'text-yellow-600'
                                  : 'text-green-600'
                              }`}>
                                {alert.severity.toUpperCase()}:
                              </span> {alert.message}
                            </div>
                            <div className="text-sm text-gray-600">
                              Recipients: {alert.recipients.join(', ')}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailAlertSettings;
