import React from 'react';

interface DashboardLayoutProps {
  mainColumn: React.ReactNode;
  sideColumn: React.ReactNode;
}

export function DashboardLayout({ mainColumn, sideColumn }: DashboardLayoutProps) {
  return (
    <div className="grid grid-cols-12 gap-3 h-full">
      <div className="col-span-12 lg:col-span-8 space-y-3 min-h-0 min-w-0 h-full">
        {mainColumn}
      </div>
      <div className="col-span-12 lg:col-span-4 space-y-3 min-h-0 min-w-0 h-full">
        {sideColumn}
      </div>
    </div>
  );
}