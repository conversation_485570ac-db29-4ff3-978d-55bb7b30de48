import React from 'react';
import { TrendingUp, TrendingDown, AlertTriangle } from 'lucide-react';
import type { FloorData } from '../../types';

interface FloorStatsProps {
  floorData: FloorData;
  timeRange: 'daily' | 'weekly' | 'monthly';
}

export function FloorStats({ floorData, timeRange }: FloorStatsProps) {
  const trends = {
    consumption: {
      value: 12.5,
      isIncrease: true,
    },
    peakDemand: {
      value: 8.3,
      isIncrease: false,
    },
    co2: {
      value: 15.2,
      isIncrease: true,
    },
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <div className="p-4 bg-white rounded-xl border border-[#EDEFF9]">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Energy Consumption</span>
          <div className={`flex items-center gap-1 text-xs ${
            trends.consumption.isIncrease ? 'text-red-500' : 'text-green-500'
          }`}>
            {trends.consumption.isIncrease ? (
              <TrendingUp size={16} />
            ) : (
              <TrendingDown size={16} />
            )}
            {trends.consumption.value}%
          </div>
        </div>
        <div className="flex items-baseline gap-2">
          <span className="text-2xl font-bold text-[#065BA9]">
            {floorData.consumption.toLocaleString()}
          </span>
          <span className="text-sm text-gray-500">kWh</span>
        </div>
        <div className="mt-2 flex items-center gap-2">
          <div className="flex-1 h-2 bg-gray-100 rounded-full overflow-hidden">
            <div 
              className="h-full bg-[#065BA9]" 
              style={{ width: `${(floorData.consumption / 20000) * 100}%` }}
            />
          </div>
          <span className="text-xs text-gray-500">
            {((floorData.consumption / 20000) * 100).toFixed(1)}%
          </span>
        </div>
      </div>

      <div className="p-4 bg-white rounded-xl border border-[#EDEFF9]">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Peak Demand</span>
          <div className={`flex items-center gap-1 text-xs ${
            trends.peakDemand.isIncrease ? 'text-red-500' : 'text-green-500'
          }`}>
            {trends.peakDemand.isIncrease ? (
              <TrendingUp size={16} />
            ) : (
              <TrendingDown size={16} />
            )}
            {trends.peakDemand.value}%
          </div>
        </div>
        <div className="flex items-baseline gap-2">
          <span className="text-2xl font-bold text-[#065BA9]">221.8</span>
          <span className="text-sm text-gray-500">kW</span>
        </div>
        <div className="mt-2 flex items-center gap-2">
          <div className="flex-1 h-2 bg-gray-100 rounded-full overflow-hidden">
            <div 
              className="h-full bg-[#065BA9]" 
              style={{ width: '75%' }}
            />
          </div>
          <span className="text-xs text-gray-500">75%</span>
        </div>
      </div>

      <div className="p-4 bg-white rounded-xl border border-[#EDEFF9]">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">CO₂ Emissions</span>
          <div className={`flex items-center gap-1 text-xs ${
            trends.co2.isIncrease ? 'text-red-500' : 'text-green-500'
          }`}>
            {trends.co2.isIncrease ? (
              <TrendingUp size={16} />
            ) : (
              <TrendingDown size={16} />
            )}
            {trends.co2.value}%
          </div>
        </div>
        <div className="flex items-baseline gap-2">
          <span className="text-2xl font-bold text-[#065BA9]">7,781.9</span>
          <span className="text-sm text-gray-500">kgCO₂e</span>
        </div>
        <div className="mt-2 flex items-center gap-2">
          <div className="flex-1 h-2 bg-gray-100 rounded-full overflow-hidden">
            <div 
              className="h-full bg-[#065BA9]" 
              style={{ width: '85%' }}
            />
          </div>
          <span className="text-xs text-gray-500">85%</span>
        </div>
      </div>
    </div>
  );
}