import React from 'react';
import { Card } from '../ui/Card'; 
import { SectionTitle } from '../ui/SectionTitle';

interface DashboardSectionProps {
  title: string;
  icon?: React.ElementType;
  subtitle?: string;
  rightContent?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  compact?: boolean;
}

export function DashboardSection({
  title,
  icon,
  subtitle,
  rightContent,
  children,
  className = '',
  compact = false
}: DashboardSectionProps) {
  return (
    <Card className={`p-3.5 ${className}`}>
      <SectionTitle
        title={title}
        icon={icon}
        subtitle={subtitle}
        rightContent={rightContent}
        className={compact ? 'mb-1' : 'mb-3'}
      />
      {children}
    </Card>
  );
}