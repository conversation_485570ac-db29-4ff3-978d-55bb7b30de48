import React from 'react';
import { Alert<PERSON>riangle, WifiOff, TrendingUp } from 'lucide-react';

interface Alert {
  id: string;
  type: 'anomaly' | 'disconnected' | 'high-consumption';
  message: string;
  timestamp: string;
  officeId: string;
}

interface FloorAlertsProps {
  alerts: Alert[];
}

export function FloorAlerts({ alerts }: FloorAlertsProps) {
  const alertIcons = {
    anomaly: <AlertTriangle className="text-red-500" size={16} />,
    disconnected: <WifiOff className="text-gray-500" size={16} />,
    'high-consumption': <TrendingUp className="text-orange-500" size={16} />,
  };

  const alertColors = {
    anomaly: 'bg-red-50 border-red-200',
    disconnected: 'bg-gray-50 border-gray-200',
    'high-consumption': 'bg-orange-50 border-orange-200',
  };

  return (
    <div className="bg-white rounded-xl border border-[#EDEFF9] p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Alerts</h3>
      <div className="space-y-3">
        {alerts.map((alert) => (
          <div 
            key={alert.id}
            className={`p-3 rounded-lg border ${alertColors[alert.type]}`}
          >
            <div className="flex items-start gap-3">
              <div className="mt-0.5">{alertIcons[alert.type]}</div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">{alert.message}</p>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-xs text-gray-500">Office {alert.officeId}</span>
                  <span className="text-xs text-gray-500">
                    {new Date(alert.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}