import React from 'react';
import { X, Zap, Fan, Lightbulb, Power, AlertTriangle, WifiOff, ChevronLeft } from 'lucide-react';
import * as echarts from 'echarts';
import { formatNumber } from '../../lib/utils/formatters';
import { TEXT_STYLES, GRADIENT_STYLES, SHADOW_STYLES, BORDER_STYLES } from '../../lib/config/ui';

interface FloorAnalyticsPanelProps {
  floorNumber: number;
  onClose: () => void;
}

interface PowerQualityMetrics {
  voltage: number;
  current: number;
  powerFactor: number;
  frequency: number;
  thd: number;
}

const SYSTEM_TYPES = {
  hvac: { icon: Fan, color: 'text-blue-500', label: 'HVAC' },
  lighting: { icon: Lightbulb, color: 'text-amber-500', label: 'Lighting' },
  equipment: { icon: Power, color: 'text-purple-500', label: 'Equipment' }
} as const;

export function FloorAnalyticsPanel({ floorNumber, onClose }: FloorAnalyticsPanelProps) {
  const chartRef = React.useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = React.useState(false);
  const [powerQuality, setPowerQuality] = React.useState<PowerQualityMetrics>({
    voltage: 230.5,
    current: 125.3,
    powerFactor: 0.92,
    frequency: 50.0,
    thd: 2.8
  });

  const systemBreakdown = {
    hvac: 45,
    lighting: 30,
    equipment: 25
  };

  React.useEffect(() => {
    // Trigger animation after mount
    requestAnimationFrame(() => {
      setIsVisible(true);
    });
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300); // Wait for animation to complete
  };

  React.useLayoutEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);
    const now = new Date();
    const times = Array.from({ length: 96 }, (_, i) => {
      const time = new Date(now.getTime() - (95 - i) * 15 * 60 * 1000);
      return time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
    });

    const data = times.map(() => Math.random() * 100 + 150);

    const option = {
      grid: {
        top: 20,
        right: 20,
        bottom: 30,
        left: 50,
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: 'rgba(96, 165, 250, 0.2)',
            width: 2,
            type: 'dashed'
          }
        },
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#E2E8F0',
        borderWidth: 1,
        padding: [8, 12],
        textStyle: {
          color: '#374151',
          fontSize: 12
        },
        formatter: (params: any) => {
          const value = params[0].value;
          return `
            <div style="font-weight: 500; margin-bottom: 4px;">
              ${params[0].name}
            </div>
            <div style="display: flex; justify-content: space-between; gap: 12px;">
              <span style="color: #6B7280;">Power Demand:</span>
              <span style="font-weight: 500;">${value.toFixed(1)} kW</span>
            </div>
          `;
        }
      },
      xAxis: {
        type: 'category',
        data: times,
        axisLine: {
          lineStyle: {
            color: '#E2E8F0'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          interval: 11,
          color: '#94A3B8',
          fontSize: 10
        }
      },
      yAxis: {
        type: 'value',
        name: 'kW',
        nameTextStyle: {
          color: '#64748B',
          fontSize: 10,
          padding: [0, 0, 0, 30]
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E2E8F0'
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          z: -10, // Set negative z-index to push gridlines behind data series
          zlevel: -1, // Use zlevel to ensure gridlines are rendered on a lower canvas layer
          lineStyle: {
            color: '#E2E8F0',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#94A3B8',
          fontSize: 10
        }
      },
      series: [{
        data: data,
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 3,
          color: '#3B82F6'
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(59, 130, 246, 0.2)' },
            { offset: 1, color: 'rgba(59, 130, 246, 0.02)' }
          ])
        }
      }]
    };

    chart.setOption(option);

    const handleResize = () => chart.resize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.dispose();
    };
  }, []);

  // Simulate real-time updates
  React.useEffect(() => {
    const timer = setInterval(() => {
      setPowerQuality(prev => ({
        ...prev,
        voltage: prev.voltage + (Math.random() - 0.5) * 0.2,
        current: prev.current + (Math.random() - 0.5) * 0.5,
        powerFactor: Math.min(1, Math.max(0.8, prev.powerFactor + (Math.random() - 0.5) * 0.01)),
        frequency: prev.frequency + (Math.random() - 0.5) * 0.02,
        thd: Math.max(0, prev.thd + (Math.random() - 0.5) * 0.1)
      }));
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  return (
    <>
      {/* Backdrop */}
      <div
        className={`fixed inset-0 bg-black/20 backdrop-blur-sm transition-opacity duration-300 z-[9998] ${
          isVisible ? 'opacity-100' : 'opacity-0'
        }`}
        onClick={handleClose}
      />

      {/* Panel */}
      <div
        className={`fixed top-0 right-0 h-screen w-[800px] bg-white shadow-2xl transform transition-transform duration-300 ease-out z-[9999] ${
          isVisible ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <button
              onClick={handleClose}
              className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <ChevronLeft size={20} className="text-gray-500" />
            </button>
            <h2 className="text-lg font-semibold text-gray-900">
              Floor {floorNumber} Analytics
            </h2>
          </div>
          <button
            onClick={handleClose}
            className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 space-y-6 overflow-auto h-[calc(100vh-65px)]">
          {/* Real-time Power Demand */}
          <div className={`p-4 rounded-xl ${GRADIENT_STYLES.card} ${BORDER_STYLES.card} ${SHADOW_STYLES.card}`}>
            <h3 className={TEXT_STYLES.title.base}>Real-time Power Demand</h3>
            <div ref={chartRef} className="w-full h-[300px]" />
          </div>

          {/* System Breakdown */}
          <div className="grid grid-cols-3 gap-6">
            {Object.entries(SYSTEM_TYPES).map(([type, { icon: Icon, color, label }]) => (
              <div
                key={type}
                className={`p-6 rounded-xl ${GRADIENT_STYLES.card} ${BORDER_STYLES.card} ${SHADOW_STYLES.card} hover:shadow-lg transition-shadow duration-200`}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Icon size={24} className={color} />
                    <span className={TEXT_STYLES.label}>{label}</span>
                  </div>
                  <span className="text-lg font-medium text-primary-blue">
                    {systemBreakdown[type as keyof typeof systemBreakdown]}%
                  </span>
                </div>
                <div className="h-3 bg-gray-100 rounded-full overflow-hidden">
                  <div
                    className="h-full bg-gradient-to-r from-blue-500 to-blue-600"
                    style={{ width: `${systemBreakdown[type as keyof typeof systemBreakdown]}%` }}
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Power Quality Metrics */}
          <div className={`p-6 rounded-xl ${GRADIENT_STYLES.card} ${BORDER_STYLES.card} ${SHADOW_STYLES.card}`}>
            <h3 className={TEXT_STYLES.title.base}>Power Quality Metrics</h3>
            <div className="mt-6 grid grid-cols-5 gap-6">
              <div>
                <div className="text-sm text-gray-500 mb-2">Voltage</div>
                <div className="text-xl font-medium text-primary-blue">
                  {powerQuality.voltage.toFixed(1)} V
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500 mb-2">Current</div>
                <div className="text-xl font-medium text-primary-blue">
                  {powerQuality.current.toFixed(1)} A
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500 mb-2">Power Factor</div>
                <div className="text-xl font-medium text-primary-blue">
                  {powerQuality.powerFactor.toFixed(2)}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500 mb-2">Frequency</div>
                <div className="text-xl font-medium text-primary-blue">
                  {powerQuality.frequency.toFixed(1)} Hz
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500 mb-2">THD</div>
                <div className="text-xl font-medium text-primary-blue">
                  {powerQuality.thd.toFixed(1)}%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}