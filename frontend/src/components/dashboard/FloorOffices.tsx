import React, { memo } from 'react';
import { AlertTriangle, WifiOff } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import type { FloorData } from '../../types';

interface FloorOfficesProps {
  floorNumber: number;
  offices: Array<{
    id: string;
    consumption: number;
    hasAnomaly?: boolean;
    isDisconnected?: boolean;
  }>;
}

function getConsumptionColor(consumption: number): string {
  if (consumption === 0) return '#EDEFF9';
  if (consumption < 20) return '#CFE5FA';
  if (consumption < 40) return '#8BC6FF';
  if (consumption < 60) return '#55A6F2';
  return '#065BA9';
}

const OfficeBox = memo(function OfficeBox({ consumption, hasAnomaly, isDisconnected, onClick }: { consumption: number; hasAnomaly?: boolean; isDisconnected?: boolean; onClick?: () => void }) {
  const bgColor = getConsumptionColor(consumption);
  const textColor = consumption < 20 ? '#55A6F2' : 'white';
  const size = '48px';

  return (
    <div className="flex flex-col items-center gap-1 group relative">
      {(hasAnomaly || isDisconnected) && (
        <div className="w-5 h-5 mb-0.5">
          <div className="relative">
            {hasAnomaly && (
              <div className="absolute inset-0 bg-[#EF4337] rounded-full border-[0.5px] border-white flex items-center justify-center">
                <AlertTriangle size={12} className="text-white" />
              </div>
            )}
            {isDisconnected && (
              <div className="absolute inset-0 bg-gradient-to-br from-[#BDBEC8] to-[#788796] rounded-full border-[0.5px] border-white flex items-center justify-center">
                <WifiOff size={12} className="text-white" />
              </div>
            )}
          </div>
        </div>
      )}
      <div style={{ width: size, height: size }}>
        <div className={`w-full h-full p-2 rounded-md flex flex-col justify-center items-center transition-transform duration-200 hover:scale-105`}
             style={{ backgroundColor: bgColor }}
             onClick={onClick}>
          {consumption > 0 && (
            <div className="text-center">
              <span className={`text-[13px] font-bold tracking-[0.52px]`} style={{ color: textColor }}>
                {consumption.toFixed(1)}
                <br />
              </span>
              <span className={`text-[9px] font-bold tracking-[0.36px]`} style={{ color: textColor }}>
                kWh
              </span>
            </div>
          )}
        </div>
      </div>
      <div className="text-center text-[#788796] text-[11px] font-normal">Office ##</div>
      
      {/* Tooltip */}
      <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
        <div className="font-semibold">Office ##</div>
        <div>Consumption: {consumption.toFixed(1)} kWh</div>
        {hasAnomaly && <div className="text-red-300">Anomaly Detected</div>}
        {isDisconnected && <div className="text-gray-300">IoT Disconnected</div>}
      </div>
    </div>
  );
});

export const FloorOffices = memo(function FloorOffices({ floorNumber, offices }: FloorOfficesProps) {
  const navigate = useNavigate();

  return (
    <div className="grid grid-cols-8 md:grid-cols-10 lg:grid-cols-12 gap-x-2 gap-y-4">
      {offices.map((office) => (
        <OfficeBox
          key={office.id}
          consumption={office.consumption}
          hasAnomaly={office.hasAnomaly}
          isDisconnected={office.isDisconnected}
          onClick={() => navigate(`/floor-analytics?building=A&floor=${floorNumber}`)}
        />
      ))}
    </div>
  );
});