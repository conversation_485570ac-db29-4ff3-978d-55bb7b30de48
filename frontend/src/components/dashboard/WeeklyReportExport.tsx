import React, { useState, useRef, useEffect } from 'react';
import { Calendar, Download, FileText, TrendingUp, AlertCircle, BarChart3, Building } from 'lucide-react';
import { Button } from '../ui/Button';
import { format, startOfWeek, endOfWeek, subWeeks, eachDayOfInterval } from 'date-fns';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { formatNumberWithCommas } from '../../utils/formatting';
import type { BuildingId } from '../../types';

interface WeeklyReportData {
  period: {
    start: Date;
    end: Date;
  };
  summary: {
    totalConsumption: number;
    totalCost: number;
    avgDailyConsumption: number;
    peakDemand: number;
    peakDemandTime: string;
    comparisonToPrevWeek: number;
  };
  buildingBreakdown: {
    building: string;
    consumption: number;
    cost: number;
    percentage: number;
  }[];
  systemBreakdown: {
    system: string;
    consumption: number;
    percentage: number;
  }[];
  dailyConsumption: {
    date: string;
    consumption: number;
    cost: number;
  }[];
}

interface WeeklyReportExportProps {
  data?: any; // Dashboard data
  selectedBuilding?: BuildingId | 'all';
}

export const WeeklyReportExport: React.FC<WeeklyReportExportProps> = ({ 
  data,
  selectedBuilding = 'all' 
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [reportData, setReportData] = useState<WeeklyReportData | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const generateReportData = (): WeeklyReportData => {
    const now = new Date();
    const weekStart = startOfWeek(now, { weekStartsOn: 1 }); // Monday
    const weekEnd = endOfWeek(now, { weekStartsOn: 1 }); // Sunday
    const prevWeekStart = subWeeks(weekStart, 1);
    const prevWeekEnd = subWeeks(weekEnd, 1);

    // Calculate summary metrics with type validation
    const rawTotalConsumption = data?.totalEnergy;
    const totalConsumption = typeof rawTotalConsumption === 'number' 
      ? rawTotalConsumption 
      : (parseFloat(rawTotalConsumption) || 4521.5);
    
    const totalCost = totalConsumption * 4.5; // THB 4.5 per kWh
    const avgDailyConsumption = totalConsumption / 7;
    
    const rawPeakDemand = data?.peakDemand;
    const peakDemand = typeof rawPeakDemand === 'number'
      ? rawPeakDemand
      : (parseFloat(rawPeakDemand) || 756.2);
      
    const comparisonToPrevWeek = -5.2; // % reduction

    // Building breakdown
    const buildingBreakdown = [
      { building: 'Tower Building', consumption: 1805.2, cost: 8123.4, percentage: 40 },
      { building: 'Podium Building', consumption: 1532.9, cost: 6898.1, percentage: 34 },
      { building: 'Car Park Building', consumption: 1183.4, cost: 5325.3, percentage: 26 },
    ];

    // System breakdown
    const systemBreakdown = [
      { system: 'Chiller Plant', consumption: 1808.6, percentage: 40 },
      { system: 'Air Side', consumption: 904.3, percentage: 20 },
      { system: 'Light & Power', consumption: 678.2, percentage: 15 },
      { system: 'Data Center', consumption: 542.6, percentage: 12 },
      { system: 'Elevators', consumption: 361.7, percentage: 8 },
      { system: 'Others', consumption: 226.1, percentage: 5 },
    ];

    // Daily consumption
    const days = eachDayOfInterval({ start: weekStart, end: weekEnd });
    const dailyConsumption = days.map((day, index) => {
      const baseConsumption = 600 + Math.random() * 200;
      const weekendFactor = index >= 5 ? 0.7 : 1; // Lower on weekends
      const consumption = baseConsumption * weekendFactor;
      return {
        date: format(day, 'EEE, MMM d'),
        consumption: Math.round(consumption * 10) / 10,
        cost: Math.round(consumption * 4.5 * 10) / 10,
      };
    });


    return {
      period: { start: weekStart, end: weekEnd },
      summary: {
        totalConsumption,
        totalCost,
        avgDailyConsumption,
        peakDemand,
        peakDemandTime: '2025-05-23 14:30',
        comparisonToPrevWeek,
      },
      buildingBreakdown,
      systemBreakdown,
      dailyConsumption,
    };
  };

  const generatePDF = async () => {
    setIsGenerating(true);
    setIsDropdownOpen(false); // Close dropdown
    const reportData = generateReportData();
    setReportData(reportData);

    try {
      // Create PDF
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pageWidth = pdf.internal.pageSize.getWidth();
      const margin = 15;
      let yPosition = margin;

      // Header with Thai stock exchange yellow color
      pdf.setFillColor(255, 192, 0); // Thai stock exchange yellow (#FFC000)
      pdf.rect(0, 0, pageWidth, 35, 'F');
      
      // Title section - dark text on yellow background
      pdf.setTextColor(0, 0, 0); // Black text for contrast
      pdf.setFontSize(20);
      pdf.setFont('helvetica', 'bold');
      pdf.text('ENERGY MANAGEMENT SYSTEM', margin, 18);
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'normal');
      pdf.text('Weekly Performance Report', margin, 28);
      
      // Date range - right aligned
      pdf.setFontSize(10);
      const dateText = `${format(reportData.period.start, 'MMMM d')} - ${format(reportData.period.end, 'MMMM d, yyyy')}`;
      const dateWidth = pdf.getStringUnitWidth(dateText) * pdf.internal.getFontSize() / pdf.internal.scaleFactor;
      pdf.text(dateText, pageWidth - margin - dateWidth, 28);

      yPosition = 45;

      // Executive Summary
      pdf.setTextColor(204, 154, 0); // Darker shade of #FFC000 for headers
      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'bold');
      pdf.text('EXECUTIVE SUMMARY', margin, yPosition);
      yPosition += 8;

      // Summary box with border
      pdf.setFillColor(248, 250, 252);
      pdf.setDrawColor(200, 200, 200);
      pdf.rect(margin, yPosition, pageWidth - 2 * margin, 38, 'FD');
      
      // Summary metrics in professional grid layout
      pdf.setTextColor(50, 50, 50);
      pdf.setFontSize(9);
      const col1 = margin + 8;
      const col2 = pageWidth / 2 + 5;
      let summaryY = yPosition + 10;
      
      // Labels in gray, values in black
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(100, 100, 100);
      pdf.text('Total Energy Consumption:', col1, summaryY);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${formatNumberWithCommas(reportData.summary.totalConsumption)} kWh`, col1 + 48, summaryY);
      
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(100, 100, 100);
      pdf.text('Total Cost:', col2, summaryY);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`THB ${formatNumberWithCommas(Math.round(reportData.summary.totalCost))}`, col2 + 25, summaryY);
      summaryY += 10;
      
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(100, 100, 100);
      pdf.text('Average Daily Consumption:', col1, summaryY);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${formatNumberWithCommas(Math.round(reportData.summary.avgDailyConsumption))} kWh`, col1 + 48, summaryY);
      
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(100, 100, 100);
      pdf.text('Peak Demand:', col2, summaryY);
      pdf.setTextColor(0, 0, 0);
      pdf.setFont('helvetica', 'bold');
      pdf.text(`${formatNumberWithCommas(reportData.summary.peakDemand)} kW`, col2 + 30, summaryY);
      summaryY += 10;
      
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(100, 100, 100);
      pdf.text('Week-over-Week Change:', col1, summaryY);
      const changeColor = reportData.summary.comparisonToPrevWeek < 0 ? [0, 150, 0] : [200, 0, 0];
      pdf.setTextColor(...changeColor);
      pdf.setFont('helvetica', 'bold');
      const changePrefix = reportData.summary.comparisonToPrevWeek > 0 ? '+' : '';
      pdf.text(`${changePrefix}${reportData.summary.comparisonToPrevWeek}%`, col1 + 48, summaryY);

      yPosition += 48;

      // Building Performance
      pdf.setTextColor(204, 154, 0); // Darker shade of #FFC000 for headers
      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'bold');
      pdf.text('BUILDING PERFORMANCE', margin, yPosition);
      yPosition += 8;

      // Building table header with Thai stock exchange yellow
      pdf.setFillColor(255, 192, 0); // Thai stock exchange yellow (#FFC000)
      pdf.setTextColor(0, 0, 0); // Black text for contrast
      pdf.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
      pdf.setFontSize(9);
      pdf.setFont('helvetica', 'bold');
      pdf.text('Building', margin + 5, yPosition + 5.5);
      pdf.text('Consumption (kWh)', margin + 50, yPosition + 5.5);
      pdf.text('Cost (THB)', margin + 100, yPosition + 5.5);
      pdf.text('Share', margin + 145, yPosition + 5.5);
      yPosition += 8;

      // Building data rows with alternating colors
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(50, 50, 50);
      reportData.buildingBreakdown.forEach((building, index) => {
        // Alternating row colors
        if (index % 2 === 0) {
          pdf.setFillColor(248, 250, 252);
          pdf.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
        }
        
        pdf.text(building.building, margin + 5, yPosition + 5.5);
        pdf.text(formatNumberWithCommas(building.consumption), margin + 50, yPosition + 5.5);
        pdf.text(formatNumberWithCommas(building.cost), margin + 100, yPosition + 5.5);
        pdf.text(`${building.percentage}%`, margin + 145, yPosition + 5.5);
        yPosition += 8;
      });

      yPosition += 10;

      // System Breakdown
      pdf.setTextColor(204, 154, 0); // Darker shade of #FFC000 for headers
      pdf.setFontSize(14);
      pdf.setFont('helvetica', 'bold');
      pdf.text('SYSTEM ENERGY DISTRIBUTION', margin, yPosition);
      yPosition += 8;

      // System table header with Thai stock exchange yellow
      pdf.setFillColor(255, 192, 0); // Thai stock exchange yellow (#FFC000)
      pdf.setTextColor(0, 0, 0); // Black text for contrast
      pdf.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
      pdf.setFontSize(9);
      pdf.setFont('helvetica', 'bold');
      pdf.text('System', margin + 5, yPosition + 5.5);
      pdf.text('Consumption (kWh)', margin + 70, yPosition + 5.5);
      pdf.text('Percentage', margin + 145, yPosition + 5.5);
      yPosition += 8;

      // System data rows with alternating colors
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(50, 50, 50);
      reportData.systemBreakdown.forEach((system, index) => {
        // Alternating row colors
        if (index % 2 === 0) {
          pdf.setFillColor(248, 250, 252);
          pdf.rect(margin, yPosition, pageWidth - 2 * margin, 8, 'F');
        }
        
        pdf.text(system.system, margin + 5, yPosition + 5.5);
        pdf.text(formatNumberWithCommas(system.consumption), margin + 70, yPosition + 5.5);
        pdf.text(`${system.percentage}%`, margin + 145, yPosition + 5.5);
        yPosition += 8;
      });

      // No need for second page - all content fits on one page
      yPosition += 10;

      // Professional footer with line
      const footerY = pdf.internal.pageSize.getHeight() - 15;
      pdf.setDrawColor(200, 200, 200);
      pdf.line(margin, footerY - 5, pageWidth - margin, footerY - 5);
      
      pdf.setFontSize(8);
      pdf.setTextColor(120, 120, 120);
      pdf.setFont('helvetica', 'normal');
      pdf.text(
        `Generated on ${format(new Date(), 'MMMM d, yyyy')} at ${format(new Date(), 'HH:mm')}`,
        margin,
        footerY
      );
      
      // Right-aligned footer text
      const footerText = 'Energy Management System | Weekly Performance Report';
      const footerWidth = pdf.getStringUnitWidth(footerText) * 8 / pdf.internal.scaleFactor;
      pdf.text(
        footerText,
        pageWidth - margin - footerWidth,
        footerY
      );
      
      // Page number (if we add multi-page support later)
      const pageText = 'Page 1';
      const pageWidth2 = pdf.getStringUnitWidth(pageText) * 8 / pdf.internal.scaleFactor;
      pdf.text(
        pageText,
        (pageWidth - pageWidth2) / 2,
        footerY
      );

      // Save PDF
      pdf.save(`EMS_Weekly_Report_${format(new Date(), 'yyyy-MM-dd')}.pdf`);
    } catch (error) {
      console.error('Error generating PDF:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const generateCSV = () => {
    setIsDropdownOpen(false); // Close dropdown
    const reportData = generateReportData();
    
    // Create CSV content
    let csv = 'Energy Management System - Weekly Report\n';
    csv += `Period: ${format(reportData.period.start, 'MMM d')} - ${format(reportData.period.end, 'MMM d, yyyy')}\n\n`;
    
    // Summary section
    csv += 'SUMMARY\n';
    csv += `Total Consumption,${reportData.summary.totalConsumption},kWh\n`;
    csv += `Total Cost,${formatNumberWithCommas(Math.round(reportData.summary.totalCost))},THB\n`;
    csv += `Average Daily Consumption,${reportData.summary.avgDailyConsumption},kWh\n`;
    csv += `Peak Demand,${reportData.summary.peakDemand},kW\n`;
    csv += `vs Previous Week,${reportData.summary.comparisonToPrevWeek},%\n\n`;
    
    // Building breakdown
    csv += 'BUILDING BREAKDOWN\n';
    csv += 'Building,Consumption (kWh),Cost (THB),Share (%)\n';
    reportData.buildingBreakdown.forEach(b => {
      csv += `${b.building},${b.consumption},${b.cost},${b.percentage}\n`;
    });
    csv += '\n';
    
    // System breakdown
    csv += 'SYSTEM BREAKDOWN\n';
    csv += 'System,Consumption (kWh),Share (%)\n';
    reportData.systemBreakdown.forEach(s => {
      csv += `${s.system},${s.consumption},${s.percentage}\n`;
    });
    csv += '\n';
    
    // Daily consumption
    csv += 'DAILY CONSUMPTION\n';
    csv += 'Date,Consumption (kWh),Cost (THB)\n';
    reportData.dailyConsumption.forEach(d => {
      csv += `${d.date},${d.consumption},${d.cost}\n`;
    });
    
    // Create blob and download
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `EMS_Weekly_Report_${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="relative group" ref={dropdownRef}>
      <button
        className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 shadow-sm"
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
      >
        <Calendar size={16} className="text-gray-600" />
        <span className="text-sm font-medium text-gray-700">Weekly Report</span>
        <svg className={`w-3 h-3 text-gray-500 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {isDropdownOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-xl z-50 py-2 animate-fadeIn">
        <div className="px-3 py-2 border-b border-gray-100">
          <h3 className="text-sm font-semibold text-gray-800">Export Weekly Report</h3>
          <p className="text-xs text-gray-500 mt-1">Generate management report for the past week</p>
        </div>
        
        <div className="py-1">
          <button
            onClick={generatePDF}
            disabled={isGenerating}
            className="w-full flex items-center gap-3 px-4 py-2.5 hover:bg-gray-50 transition-colors text-left"
          >
            <FileText size={18} className="text-red-500" />
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-700">Export as PDF</div>
              <div className="text-xs text-gray-500">Full formatted report</div>
            </div>
            {isGenerating && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
            )}
          </button>
          
          <button
            onClick={generateCSV}
            className="w-full flex items-center gap-3 px-4 py-2.5 hover:bg-gray-50 transition-colors text-left"
          >
            <FileText size={18} className="text-green-500" />
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-700">Export as CSV</div>
              <div className="text-xs text-gray-500">Data for further analysis</div>
            </div>
          </button>
        </div>
        
        <div className="px-4 py-2 bg-gray-50 border-t border-gray-100">
          <p className="text-xs font-medium text-gray-600 mb-1">Report includes:</p>
          <ul className="text-xs text-gray-500 space-y-0.5">
            <li>• Executive summary & KPIs</li>
            <li>• Building performance metrics</li>
            <li>• System energy breakdown</li>
            <li>• Critical alerts & recommendations</li>
          </ul>
        </div>
      </div>
      )}
    </div>
  );
};