import React from 'react';
import { TEXT_STYLES } from '../../lib/config/ui';
import { formatNumber } from '../../lib/utils/formatters';

interface BillingOverviewProps {
  currentAmount: number;
  dueDate: Date;
  pendingBills: number;
  paidBills: number;
}

export function BillingOverview({ currentAmount, dueDate, pendingBills, paidBills }: BillingOverviewProps) {
  return (
    <div className="space-y-3">
      <div className="grid grid-cols-1 gap-3">
        <div className="p-3 rounded-lg bg-gradient-to-br from-blue-50/60 via-blue-50/40 to-white border border-blue-100/80 hover:shadow-[0_2px_4px_rgba(14,126,228,0.1)] transition-all duration-200">
          <div className="flex flex-col">
            <div className="flex items-center justify-between mb-3">
              <span className={TEXT_STYLES.label}>Current Billing Cycle</span>
              <span className="text-xs text-gray-500">
                Due {dueDate.toLocaleDateString('en-US', { day: 'numeric', month: 'short' })}
              </span>
            </div>
            <div className="flex items-baseline gap-1">
              <span className="text-sm font-bold text-primary-blue">
                ฿{formatNumber(currentAmount, { decimals: 0 })}
              </span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          {/* Pending Bills */}
          <div className="p-3 rounded-lg bg-gradient-to-br from-yellow-50/60 via-yellow-50/40 to-white border border-yellow-100/80 hover:shadow-[0_2px_4px_rgba(234,179,8,0.1)] transition-all duration-200">
            <div className="flex flex-col">
              <span className={TEXT_STYLES.label}>Pending Bills</span>
              <div className="flex items-baseline gap-1">
                <span className="text-sm font-medium text-yellow-700">{pendingBills} unpaid</span>
              </div>
            </div>
          </div>

          {/* Paid Bills */}
          <div className="p-3 rounded-lg bg-gradient-to-br from-green-50/60 via-green-50/40 to-white border border-green-100/80 hover:shadow-[0_2px_4px_rgba(34,197,94,0.1)] transition-all duration-200">
            <div className="flex flex-col">
              <span className={TEXT_STYLES.label}>Paid Bills</span>
              <div className="flex items-baseline gap-1">
                <span className="text-sm font-medium text-green-700">{paidBills} paid</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}