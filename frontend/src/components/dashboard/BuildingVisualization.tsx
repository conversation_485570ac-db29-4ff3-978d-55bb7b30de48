import React from 'react';
import { ChevronDown } from 'lucide-react';
import type { BuildingId, FloorData } from '../../types';
import { COLORS, CONSUMPTION_THRESHOLDS, BUILDINGS } from '../../lib/constants';

interface BuildingVisualizationProps {
  selectedBuilding: BuildingId;
  selectedFloor: number;
  floorData?: FloorData[];
  onBuildingChange: (building: BuildingId) => void;
  onFloorSelect: (floor: number) => void;
}

function getConsumptionStatus(consumption: number): FloorData['status'] {
  if (consumption === 0) return 'no-data';
  if (consumption < CONSUMPTION_THRESHOLDS.low) return 'low';
  if (consumption < CONSUMPTION_THRESHOLDS.medium) return 'medium';
  if (consumption < CONSUMPTION_THRESHOLDS.high) return 'high';
  return 'critical';
}

function getStatusColor(status: FloorData['status']): string {
  return COLORS.consumption[status];
}
export function BuildingVisualization({
  selectedBuilding,
  selectedFloor,
  floorData = [],
  onBuildingChange,
  onFloorSelect,
}: BuildingVisualizationProps) {
  const building = BUILDINGS[selectedBuilding];
  const floors = Array.from(
    { length: building.floors + (building.hasBasement ? 1 : 0) },
    (_, i) => {
      if (i === building.floors) return 'B';
      return building.floors - i;
    }
  );

  const getFloorColor = (floor: number) => {
    const data = floorData.find(d => d.floorNumber === floor);
    if (!data) return COLORS.consumption['no-data'];
    return getStatusColor(getConsumptionStatus(data.consumption));
  };

  const getFloorLabel = (floor: number | 'B') => {
    const usage = building.floorUsage[floor];
    return `Floor ${floor}${usage ? ` - ${usage}` : ''}`;
  };

  return (
    <div className="w-full h-full relative">
      {/* Building Visualization */}
      <div className="absolute left-[100px] top-[37px] w-[320px] h-[598px]">
        <div className="absolute inset-0 bg-white border border-[#B4B4B4] bg-gradient-to-b from-white to-white shadow-[0_0_20px_rgba(120,135,150,0.1)]" />
        <div 
          className="absolute left-[50px] top-[260px] w-[160px] h-[96px] transition-colors duration-300"
          style={{
            backgroundColor: `${getFloorColor(selectedFloor)}33`,
            borderWidth: 3,
            borderStyle: 'solid',
            borderColor: getFloorColor(selectedFloor)
          }}
        />
      </div>

      {/* Floor Selector */}
      <div className="absolute left-1 top-3.5 flex flex-col gap-1">
        {floors.map((floor) => (
          <div key={floor} className="flex items-center gap-2">
            <button
              onClick={() => typeof floor === 'number' && onFloorSelect(floor)}
              className={`w-8 py-0.5 px-2 rounded-xl border flex items-center justify-center transition-all duration-200 group ${
                floor === selectedFloor
                  ? 'bg-white border-[#065BA9] text-[#065BA9] shadow-[1px_3px_20px_rgba(57,124,221,0.3)] backdrop-blur-lg'
                  : 'bg-background-main border-primary-dark-grey text-primary-dark-grey hover:bg-white hover:border-primary-blue hover:text-primary-blue'
              }`}
              title={getFloorLabel(floor)}
            >
              <span className="text-[9px] font-normal">
                {floor === 'B' ? 'B' : `FL.${floor}`}
              </span>
            </button>
            <div className="text-[9px] text-gray-500 hidden lg:block">
              {building.floorUsage[floor]}
            </div>
          </div>
        ))}
      </div>

      {/* Building Selector */}
      <div className="absolute right-0 top-2.5">
        <button
          onClick={() => onBuildingChange(selectedBuilding === 'A' ? 'B' : 'A')}
          className="h-8 px-3 bg-background-main rounded-lg border border-background-white-blue flex items-center gap-2 hover:bg-white transition-colors duration-200"
        >
          <span className="text-xs text-primary-dark-grey">
            {BUILDINGS[selectedBuilding].name}
          </span>
          <ChevronDown size={16} className="text-primary-blue" />
        </button>
      </div>
    </div>
  );
}