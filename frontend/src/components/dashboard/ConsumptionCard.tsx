import React from 'react';
import { formatNumber, formatPercentage } from '../../lib/utils/formatters';
import type { EnergyConsumption } from '../../types';
import { TEXT_STYLES } from '../../lib/config/ui';

interface ConsumptionCardProps {
  data: EnergyConsumption;
}

export function ConsumptionCard({ data }: ConsumptionCardProps) {
  return (
    <div className="animate-fadeIn shrink-0">
      <div className="grid grid-cols-3 gap-2">
      <div className="bg-gradient-to-br from-blue-50/80 via-blue-50/50 to-white rounded-xl p-4 border border-blue-100 hover:shadow-[0_2px_8px_rgba(14,126,228,0.15)] transition-shadow duration-300 group">
        <div className="flex items-start gap-3">
          <div className="flex-1">
            <p className={TEXT_STYLES.label}>Consumption</p>
            <div className="flex items-baseline gap-1 truncate">
              <p className={TEXT_STYLES.value.primary}>{formatNumber(data.total, { decimals: 0 })}</p>
              <p className={TEXT_STYLES.unit}>kWh</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-br from-blue-50/80 via-blue-50/50 to-white rounded-xl p-4 border border-blue-100 hover:shadow-[0_2px_8px_rgba(14,126,228,0.15)] transition-shadow duration-300 group">
        <div className="flex items-start gap-3">
          <div className="flex-1">
            <p className={TEXT_STYLES.label}>Peak</p>
            <div className="flex items-baseline gap-1">
              <p className={TEXT_STYLES.value.primary}>{formatNumber(data.peak, { decimals: 0 })}</p>
              <p className={TEXT_STYLES.unit}>kW</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-br from-blue-50/80 via-blue-50/50 to-white rounded-xl p-4 border border-blue-100 hover:shadow-[0_2px_8px_rgba(14,126,228,0.15)] transition-shadow duration-300 group">
        <div className="flex items-start gap-3">
          <div className="flex-1">
            <p className={TEXT_STYLES.label}>Electricity Cost</p>
            <div className="flex items-baseline gap-1">
              <p className={TEXT_STYLES.value.primary}>
                ฿{formatNumber(data.cost, { decimals: 0 })}
              </p>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
}