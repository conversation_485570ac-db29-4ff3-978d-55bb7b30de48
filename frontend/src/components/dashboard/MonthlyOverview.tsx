import { formatNumber } from '../../lib/utils/formatters';

export interface MonthlyOverviewProps {
  energy: {
    consumption: number | null;
    peak: number | null;
    cost: number | null;
  };
  billing?: {
    currentAmount: number;
    dueDate: Date;
    paidBills: number;
  };
}

export function MonthlyOverview({ energy, billing }: MonthlyOverviewProps) {
  return (
    <div className="space-y-5">
      <div className="flex flex-col gap-2">
        <EnergyConsumptionCards energy={energy} billing={billing} />
      </div>
    </div>
  );
}

// Separated component for energy consumption cards
function EnergyConsumptionCards({ 
  energy, 
  billing 
}: { 
  energy: MonthlyOverviewProps['energy'], 
  billing?: MonthlyOverviewProps['billing'] 
}) {
  return (
    <div className="grid grid-cols-3 gap-2">
      <div className="p-2 bg-white rounded-lg border border-[#EDEFF9] hover:shadow-lg transition-shadow duration-300">
        <div className="flex items-center gap-1 mb-1">
          <h3 className="text-[10px] font-medium text-gray-700">Consumption</h3>
        </div>
        <div className="flex items-baseline gap-1">
          <span className="text-sm font-bold text-primary-blue">
            {energy.consumption === null ? '-' : formatNumber(energy.consumption, { decimals: 0 })}
          </span>
          <span className="text-[9px] text-gray-500">kWh</span>
        </div>
      </div>

      <div className="p-2 bg-white rounded-lg border border-[#EDEFF9] hover:shadow-lg transition-shadow duration-300">
        <div className="flex items-center gap-1 mb-1">
          <h3 className="text-[10px] font-medium text-gray-700">Peak Demand</h3>
        </div>
        <div className="flex items-baseline gap-1">
          <span className="text-sm font-bold text-primary-blue">
            {energy.peak === null ? '-' : formatNumber(energy.peak, { decimals: 1 })}
          </span>
          <span className="text-[9px] text-gray-500">kW</span>
        </div>
      </div>

      <div className="p-2 bg-white rounded-lg border border-[#EDEFF9] hover:shadow-lg transition-shadow duration-300">
        <div className="flex items-center gap-1 mb-1">
          <h3 className="text-[10px] font-medium text-gray-700">Cost</h3>
        </div>
        <div className="flex items-baseline gap-1">
          <span className="text-sm font-bold text-primary-blue">
            {energy.cost === null ? '-' : `฿${formatNumber(energy.cost, { decimals: 0 })}`}
          </span>
        </div>
      </div>
    </div>
  );
}

export default MonthlyOverview;