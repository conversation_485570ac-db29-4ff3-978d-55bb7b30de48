import { useState, useEffect, useCallback } from 'react';
import { MAIN_METER_LIST } from '../../../lib/config/building/meters/main-meter-list';

// Types for meter groups
export interface Meter {
  id: string;
  name: string;
  type: 'physical' | 'virtual';
  location?: string;
}

export interface MeterGroupMeter {
  id: string;
  operation: '+' | '-';
}

export interface MeterGroup {
  id: string;
  name: string;
  description?: string;
  meters: MeterGroupMeter[];
}

/**
 * Custom hook to manage meter groups
 *
 * This hook provides state and functions for creating, updating, and deleting
 * meter groups, as well as managing the meter selection process.
 */
export function useMeterGroups() {
  // State for meter groups and meters
  const [meterGroups, setMeterGroups] = useState<MeterGroup[]>([]);
  const [meters, setMeters] = useState<Meter[]>([]);

  // State for the form
  const [groupName, setGroupName] = useState('');
  const [groupDescription, setGroupDescription] = useState('');
  const [selectedMeters, setSelectedMeters] = useState<string[]>([]);
  const [operations, setOperations] = useState<Record<string, '+' | '-'>>({});

  // Load meters and meter groups from localStorage on mount
  useEffect(() => {
    const storedGroups = localStorage.getItem('meterGroups');
    if (storedGroups) {
      try {
        setMeterGroups(JSON.parse(storedGroups));
      } catch (error) {
        console.error('Error parsing stored meter groups:', error);
      }
    }

    // Load meter data from static config
    const actualMeters: Meter[] = Object.values(MAIN_METER_LIST).flatMap(cat =>
      cat.meters.map(m => ({
        id: m.id,
        name: m.name,
        type: 'physical',
        location: cat.name
      }))
    );

    setMeters(actualMeters);
  }, []);

  // Save meter groups to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('meterGroups', JSON.stringify(meterGroups));
  }, [meterGroups]);

  // Toggle meter selection
  const toggleMeterSelection = useCallback((meterId: string) => {
    setSelectedMeters(prev => {
      if (prev.includes(meterId)) {
        // Remove meter from selection
        const newOperations = { ...operations };
        delete newOperations[meterId];
        setOperations(newOperations);
        return prev.filter(id => id !== meterId);
      } else {
        // Add meter to selection with default '+' operation
        const newOperations = { ...operations };
        newOperations[meterId] = '+';
        setOperations(newOperations);
        return [...prev, meterId];
      }
    });
  }, [operations]);

  // Create a new meter group
  const createGroup = useCallback((groupData: Omit<MeterGroup, 'id'>) => {
    const newGroup: MeterGroup = {
      id: `group-${Date.now()}`,
      ...groupData
    };

    setMeterGroups(prev => [...prev, newGroup]);
  }, []);

  // Update an existing meter group
  const updateGroup = useCallback((groupId: string, groupData: Omit<MeterGroup, 'id'>) => {
    setMeterGroups(prev =>
      prev.map(group =>
        group.id === groupId
          ? { ...group, ...groupData }
          : group
      )
    );
  }, []);

  // Delete a meter group
  const deleteGroup = useCallback((groupId: string) => {
    setMeterGroups(prev => prev.filter(group => group.id !== groupId));
  }, []);

  return {
    meterGroups,
    meters,
    createGroup,
    updateGroup,
    deleteGroup,
    selectedMeters,
    toggleMeterSelection,
    setSelectedMeters,
    operations,
    setOperations,
    groupName,
    setGroupName,
    groupDescription,
    setGroupDescription
  };
}
