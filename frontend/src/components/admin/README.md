# Admin Components

This directory contains components for the Admin section of the application, which provides system configuration capabilities for administrators.

## Components

### MeterGroupConfiguration

The `MeterGroupConfiguration` component allows administrators to create and manage meter groups for analytics and other system functions. It provides:

- Creation of meter groups with addition/subtraction operations
- Management of existing meter groups (edit, delete)
- Search functionality for finding meters
- Simple validation to prevent errors

## Hooks

### useMeterGroups

The `useMeterGroups` hook provides state management and functions for working with meter groups:

- Loading and saving meter groups to localStorage
- Creating, updating, and deleting meter groups
- Managing meter selection and operations

## Future Improvements

1. Connect to real meter data API instead of using mock data
2. Integrate groups into analytics and other relevant pages
3. Implement more robust validation
4. Add more advanced filtering options for meters
5. Provide visualization of the meter group structure

## Usage

The Admin section is only accessible to users with administrator privileges. The `AdminRoute` component in `src/components/auth` ensures that only authorized users can access these pages.

```tsx
// Example usage in routes
<Route path="/admin" element={<AdminRoute><Admin /></AdminRoute>} />
```
