import React, { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { Plus, Search, Save, Trash, Edit, Move, Gauge, X, RefreshCw } from 'lucide-react';
import { useMeterGroups, Meter } from './hooks/useMeterGroups';

/**
 * MeterGroupConfiguration component
 *
 * Allows administrators to create and manage meter groups for analytics
 * and other system functions.
 */
const MeterGroupConfiguration: React.FC = () => {
  // State for the component
  const [searchTerm, setSearchTerm] = useState('');
  const [editingGroupId, setEditingGroupId] = useState<string | null>(null);
  const [draggedMeter, setDraggedMeter] = useState<Meter | null>(null);
  const [isDraggingOver, setIsDraggingOver] = useState(false);

  // Custom hook to manage meter groups
  const {
    meterGroups,
    meters,
    createGroup,
    updateGroup,
    deleteGroup,
    selectedMeters,
    toggleMeterSelection,
    setSelectedMeters,
    operations,
    setOperations,
    groupName,
    setGroupName,
    groupDescription,
    setGroupDescription
  } = useMeterGroups();

  // Suggest names based on selection
  const nameSuggestions = useMemo(() => {
    const suggestions: string[] = [];
    if (selectedMeters.length > 0) {
      const names = selectedMeters.map(id => meters.find(m => m.id === id)?.name || '').filter(n => n);
      if (names.length === 1) suggestions.push(names[0]);
      else suggestions.push(`${names[0]} & ${names.length - 1} Others`);
      const locs = Array.from(new Set(selectedMeters.map(id => meters.find(m => m.id === id)?.location)));
      if (locs.length === 1 && locs[0]) suggestions.unshift(`${locs[0]} Group`);
    }
    suggestions.push('New Meter Group');
    return suggestions.slice(0, 3);
  }, [selectedMeters, meters]);

  // Filter meters based on search term
  const filteredMeters = meters.filter(meter =>
    meter.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    meter.id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Collapsible categories state
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  // Group meters by category (location)
  const groupedMeters = useMemo(() => {
    const groups: Record<string, Meter[]> = {};
    filteredMeters.forEach(m => {
      const cat = m.location || 'Other';
      groups[cat] = groups[cat] || [];
      groups[cat].push(m);
    });
    return groups;
  }, [filteredMeters]);

  // Handle creating or updating a group
  const handleSaveGroup = useCallback(() => {
    if (!groupName.trim()) {
      alert('Please enter a group name');
      return;
    }

    if (selectedMeters.length === 0) {
      alert('Please select at least one meter');
      return;
    }

    const groupData = {
      name: groupName,
      description: groupDescription,
      meters: selectedMeters.map(id => ({
        id,
        operation: operations[id] || '+'
      }))
    };

    if (editingGroupId) {
      updateGroup(editingGroupId, groupData);
      setEditingGroupId(null);
    } else {
      createGroup(groupData);
    }

    // Reset form
    setGroupName('');
    setGroupDescription('');
    setSelectedMeters([]);
    setOperations({});
  }, [
    groupName,
    groupDescription,
    selectedMeters,
    operations,
    editingGroupId,
    createGroup,
    updateGroup,
    setGroupName,
    setGroupDescription,
    setSelectedMeters,
    setOperations
  ]);

  // Disable Save until name and meters are selected
  const isSaveDisabled = !groupName.trim() || selectedMeters.length === 0;

  // Handle editing an existing group
  const handleEditGroup = useCallback((groupId: string) => {
    // reset search to show all meters
    setSearchTerm('');
    const group = meterGroups.find(g => g.id === groupId);
    if (!group) return;

    setEditingGroupId(groupId);
    setGroupName(group.name);
    setGroupDescription(group.description || '');

    const selectedIds: string[] = [];
    const ops: Record<string, '+' | '-'> = {};

    group.meters.forEach(meter => {
      selectedIds.push(meter.id);
      ops[meter.id] = meter.operation;
    });

    setSelectedMeters(selectedIds);
    setOperations(ops);
  }, [meterGroups, setGroupName, setGroupDescription, setSelectedMeters, setOperations, setSearchTerm]);

  // Handle canceling the edit
  const handleCancelEdit = useCallback(() => {
    setEditingGroupId(null);
    setGroupName('');
    setGroupDescription('');
    setSelectedMeters([]);
    setOperations({});
  }, [setGroupName, setGroupDescription, setSelectedMeters, setOperations]);

  // Handle drag start from available meters
  const handleDragStart = useCallback((e: React.DragEvent<HTMLDivElement>, meter: Meter) => {
    setDraggedMeter(meter);
    e.dataTransfer.effectAllowed = 'copy';
    e.dataTransfer.setData('text/plain', meter.id); // Set data for compatibility

    // Create a simple ghost image
    const ghostElement = document.createElement('div');
    ghostElement.classList.add('bg-white', 'px-3', 'py-2', 'rounded-md', 'shadow-sm', 'border', 'border-gray-200');
    ghostElement.style.fontSize = '14px';
    ghostElement.style.fontWeight = '400';
    ghostElement.style.color = '#065BA9'; // primary-blue
    ghostElement.innerHTML = `<div style="display: flex; align-items: center; gap: 6px;">
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="12" y1="5" x2="12" y2="19"></line>
        <line x1="5" y1="12" x2="19" y2="12"></line>
      </svg>
      ${meter.name}
    </div>`;

    document.body.appendChild(ghostElement);
    ghostElement.style.position = 'absolute';
    ghostElement.style.top = '-1000px';
    ghostElement.style.left = '0';
    ghostElement.style.zIndex = '9999';

    // Use a better position for the drag image
    e.dataTransfer.setDragImage(ghostElement, 20, 20);

    setTimeout(() => {
      document.body.removeChild(ghostElement);
    }, 0);
  }, []);

  // Handle drag over for the selected meters container
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (draggedMeter && !selectedMeters.includes(draggedMeter.id)) {
      setIsDraggingOver(true);
    }
  }, [draggedMeter, selectedMeters]);

  // Handle drag leave for the selected meters container
  const handleDragLeave = useCallback(() => {
    setIsDraggingOver(false);
  }, []);

  // Handle drag end
  const handleDragEnd = useCallback(() => {
    setDraggedMeter(null);
    setIsDraggingOver(false);
  }, []);

  // Handle drop on the selected meters container
  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (draggedMeter && !selectedMeters.includes(draggedMeter.id)) {
      toggleMeterSelection(draggedMeter.id);
    }
    setDraggedMeter(null);
    setIsDraggingOver(false);
  }, [draggedMeter, selectedMeters, toggleMeterSelection]);

  // Scroll form into view on edit
  useEffect(() => {
    if (editingGroupId) {
      document.getElementById('group-form')?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [editingGroupId]);

  return (
    <div className="space-y-6 pb-20"> {/* Added pb-20 for padding at bottom to account for fixed buttons */}
      {/* Header with description */}
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Meter Group Configuration</h2>
        <p className="text-gray-600">
          Create and manage meter groups for analytics and reporting. Groups can combine multiple meters
          with addition and subtraction operations.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left column: Group creation form */}
        <div className="space-y-4">
          <div id="group-form" className="bg-blue-50 p-4 rounded-lg border border-blue-100">
            <h3 className="font-medium text-blue-800 mb-2">
              {editingGroupId ? 'Edit Meter Group' : 'Create New Meter Group'}
            </h3>

            {/* Group name */}
            <div className="space-y-3 mb-4">
              <div>
                <label htmlFor="groupName" className="block text-sm font-medium text-gray-700 mb-1">
                  Group Name
                </label>
                <input
                  type="text"
                  id="groupName"
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                  className="block w-full max-w-md px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter group name"
                />
                {nameSuggestions.length > 0 && (
                  <div className="mt-2 text-sm text-gray-600">
                    Suggested:
                    {nameSuggestions.map(name => (
                      <button key={name} type="button" onClick={() => setGroupName(name)} className="underline text-blue-600 ml-2">
                        {name}
                      </button>
                    ))}
                  </div>
                )}
                {!groupName.trim() && <p className="mt-1 text-red-600 text-sm">Name can’t be blank.</p>}
              </div>
            </div>

            {/* Search meters */}
            <div className="mb-3">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="block w-full max-w-md pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Search meters..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Meter selection: dual-list UI */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium">Available Meters</h4>
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <Move className="h-3 w-3" />
                    <span>Drag to select</span>
                  </div>
                </div>
                <div className="relative">
                  {searchTerm ? (
                    <ul className="absolute z-10 bg-white border rounded shadow max-h-60 w-full overflow-auto">
                      {filteredMeters.slice(0, 20).map(m => (
                        <li
                          key={m.id}
                          className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 group cursor-grab transition-colors duration-150"
                          draggable="true"
                          onDragStart={(e) => handleDragStart(e, m)}
                          onDragEnd={handleDragEnd}
                          title="Drag to add to selected meters"
                        >
                          <div className="flex items-center gap-2">
                            <Move className="h-4 w-4 text-gray-400 group-hover:text-primary-blue" />
                            <div>
                              <div className="text-sm font-medium">{m.name}</div>
                              <div className="text-xs text-gray-500">{m.id}</div>
                            </div>
                          </div>
                          <button onClick={() => toggleMeterSelection(m.id)} className="text-blue-600 hover:text-blue-800">
                            <Plus className="h-4 w-4" />
                          </button>
                        </li>
                      ))}
                      {filteredMeters.length > 20 && (
                        <li className="text-center text-sm text-gray-500 py-2">Showing first 20 results...</li>
                      )}
                    </ul>
                  ) : (
                    Object.entries(groupedMeters).map(([cat, ms]) => (
                      <div key={cat} className="mb-2">
                        <button
                          type="button"
                          onClick={() => setExpandedCategories(prev => ({ ...prev, [cat]: !prev[cat] }))}
                          className={`w-full text-left px-3 py-2.5 rounded-md border transition-all duration-200 font-medium ${expandedCategories[cat] ? 'bg-gradient-to-r from-blue-100 to-blue-50 border-blue-200 shadow-md text-blue-800' : 'bg-gradient-to-r from-blue-50/80 to-white border-blue-100/50 shadow-sm text-gray-700 hover:bg-blue-50/70'}`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div className="p-1 rounded-full bg-blue-100/80">
                                <Gauge className="h-3.5 w-3.5 text-primary-blue" />
                              </div>
                              <span>{cat}</span>
                            </div>
                            <span className={`text-xs px-2 py-0.5 rounded-full ${expandedCategories[cat] ? 'bg-blue-200/70 text-blue-800' : 'bg-blue-100/50 text-blue-700'}`}>{ms.length}</span>
                          </div>
                        </button>
                        {expandedCategories[cat] && (
                          <div className="border rounded-md bg-white max-h-60 overflow-y-auto mt-1">
                            {ms.map(m => (
                              <div
                                key={m.id}
                                className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 group cursor-grab transition-colors duration-150"
                                draggable="true"
                                onDragStart={(e) => handleDragStart(e, m)}
                                onDragEnd={handleDragEnd}
                                title="Drag to add to selected meters"
                              >
                                <div className="flex items-center gap-2">
                                  <Move className="h-4 w-4 text-gray-400 group-hover:text-primary-blue" />
                                  <div>
                                    <div className="text-sm font-medium">{m.name}</div>
                                    <div className="text-xs text-gray-500">{m.id}</div>
                                  </div>
                                </div>
                                <button onClick={() => toggleMeterSelection(m.id)} className="text-blue-600 hover:text-blue-800">
                                  <Plus className="h-4 w-4" />
                                </button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </div>
              <div>
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium">Selected Meters</h4>
                  <div className="text-xs text-gray-500 flex items-center gap-1">
                    <Move className="h-3 w-3" />
                    <span>Drop target</span>
                  </div>
                </div>
                <div
                  className={`border rounded-md bg-white max-h-60 overflow-y-auto transition-colors duration-200 relative ${isDraggingOver ? 'border-primary-blue bg-blue-50/10' : selectedMeters.length > 0 ? 'border-gray-200' : 'border-dashed border-gray-300'}`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  {/* Minimal drop indicator when dragging over */}
                  {draggedMeter && !selectedMeters.includes(draggedMeter.id) && isDraggingOver && (
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none z-10">
                      <div className="bg-white border border-gray-200 rounded-md px-3 py-1.5 shadow-sm transition-opacity duration-150">
                        <div className="flex items-center gap-2 text-primary-blue text-sm">
                          <Plus className="h-3.5 w-3.5" />
                          <span>Add meter</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedMeters.length > 0 ? selectedMeters.map((id) => {
                    const meter = meters.find(m => m.id === id);
                    return (
                      <div
                        key={id}
                        className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 transition-all duration-150 group"
                      >
                        <div className="flex items-center gap-2">
                          <div>
                            <div className="text-sm font-medium">{meter?.name || id}</div>
                            <div className="text-xs text-gray-500">{id}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <select
                            value={operations[id] || '+'}
                            onChange={(e) => {
                              const newOps = { ...operations, [id]: e.target.value as '+' | '-' };
                              setOperations(newOps);
                            }}
                            className="border border-gray-300 rounded-md p-1"
                          >
                            <option value="+">+</option>
                            <option value="-">-</option>
                          </select>
                          <button
                            onClick={() => toggleMeterSelection(id)}
                            className="text-red-600 hover:text-red-800"
                            title="Remove meter"
                            onDragOver={(e) => e.stopPropagation()}
                          >
                            <Trash className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    );
                  }) : (
                    <div className="p-6 text-center">
                      <div className="flex flex-col items-center justify-center gap-2 py-8">
                        <Move className="h-6 w-6 text-gray-300 mb-1" />
                        <p className="text-sm text-gray-500">Drag meters here to add</p>
                      </div>
                    </div>
                  )}
                </div>
                {selectedMeters.length === 0 && <p className="mt-1 text-red-600 text-sm">Pick at least one meter.</p>}
              </div>
            </div>

            {/* Placeholder for spacing where buttons used to be */}
            <div className="h-16"></div>

            {/* Fixed position action buttons at the bottom */}
            <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 p-3 z-10 shadow-sm">
              <div className="container mx-auto flex justify-end space-x-3 max-w-7xl">
                {editingGroupId && (
                  <button
                    type="button"
                    onClick={handleCancelEdit}
                    className="inline-flex items-center px-4 py-2 border border-gray-200 text-sm font-medium rounded-md text-gray-600 bg-white hover:bg-gray-50 transition-colors duration-150"
                    title="Cancel editing and discard changes"
                  >
                    <X className="h-4 w-4 mr-1.5 text-gray-400" />
                    Cancel Edit
                  </button>
                )}
                <button
                  type="button"
                  onClick={() => { setSelectedMeters([]); setOperations({}); }}
                  className={`inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md transition-colors duration-150 ${selectedMeters.length === 0 ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed' : 'border-gray-200 text-gray-600 bg-white hover:bg-gray-50'}`}
                  title="Clear all selected meters"
                  disabled={selectedMeters.length === 0}
                >
                  <RefreshCw className={`h-4 w-4 mr-1.5 ${selectedMeters.length === 0 ? 'text-gray-300' : 'text-gray-400'}`} />
                  Clear All
                </button>
                <button
                  type="button"
                  onClick={handleSaveGroup}
                  disabled={isSaveDisabled}
                  className={`inline-flex items-center px-5 py-2 border shadow-sm text-sm font-medium rounded-md transition-all duration-150 ${
                    isSaveDisabled
                      ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                      : 'bg-primary-blue text-white border-primary-blue hover:bg-blue-700'
                  }`}
                >
                  <Save className="h-4 w-4 mr-1.5" />
                  {editingGroupId ? 'Update Group' : 'Save Group'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Right column: Existing groups */}
        <div>
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
              <h3 className="text-sm font-medium text-gray-700">Existing Meter Groups</h3>
            </div>

            <div className="divide-y divide-gray-200 max-h-[500px] overflow-y-auto">
              {meterGroups.length > 0 ? (
                meterGroups.map((group) => (
                  <div key={group.id} className="p-4 hover:bg-gray-50 cursor-pointer" onClick={() => handleEditGroup(group.id)}>
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">{group.name}</h4>
                        {group.description && (
                          <p className="text-xs text-gray-500 mt-1">{group.description}</p>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={(e) => { e.stopPropagation(); handleEditGroup(group.id); }}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => { e.stopPropagation(); if (window.confirm(`Are you sure you want to delete the group "${group.name}"?`)) deleteGroup(group.id); }}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash className="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    <div className="mt-2">
                      <h5 className="text-xs font-medium text-gray-700 mb-1">Meters:</h5>
                      <ul className="text-xs text-gray-600 space-y-1 pl-4">
                        {group.meters.map((meter, index) => (
                          <li key={meter.id} className="flex items-center">
                            <span>{index === 0 ? '' : meter.operation} </span>
                            <span>{meters.find(m => m.id === meter.id)?.name || meter.id}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-sm text-gray-500">
                  No meter groups defined yet
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MeterGroupConfiguration;
