import React, { useState, useRef } from 'react';
import { Upload, FolderOpen, Check, X, Download, Image, Camera } from 'lucide-react';

interface UploadedImage {
  id: string;
  name: string;
  category: string;
  file: File;
  preview: string;
  savedPath?: string;
}

const categories = [
  { value: 'dashboard', label: 'Dashboard', folder: 'public/images/dashboard' },
  { value: 'analytics', label: 'Analytics', folder: 'public/images/analytics' },
  { value: 'meters', label: 'Meters', folder: 'public/images/meters' },
  { value: 'alarms', label: 'Alarms', folder: 'public/images/alarms' },
  { value: 'reports', label: 'Reports', folder: 'public/images/reports' },
  { value: 'settings', label: 'Settings', folder: 'public/images/settings' },
  { value: 'manual', label: 'Manual/Documentation', folder: 'public/images/manual' },
  { value: 'training', label: 'Training Materials', folder: 'public/images/training' },
  { value: 'general', label: 'General UI', folder: 'public/images/general' },
];

export default function ScreenshotUploadManager() {
  // Load saved images from localStorage on component mount
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>(() => {
    const saved = localStorage.getItem('screenshot-upload-manager-images');
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (e) {
        console.error('Failed to parse saved images:', e);
        return [];
      }
    }
    return [];
  });
  const [selectedCategory, setSelectedCategory] = useState('dashboard');
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Save images to localStorage whenever they change
  React.useEffect(() => {
    localStorage.setItem('screenshot-upload-manager-images', JSON.stringify(uploadedImages));
  }, [uploadedImages]);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    if (e.target.files && e.target.files[0]) {
      handleFiles(e.target.files);
    }
  };

  const handleFiles = (files: FileList) => {
    const newImages: UploadedImage[] = [];
    
    Array.from(files).forEach((file) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const preview = e.target?.result as string;
          newImages.push({
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            name: file.name,
            category: selectedCategory,
            file: file,
            preview: preview,
          });
          
          if (newImages.length === files.length) {
            setUploadedImages(prev => [...prev, ...newImages]);
          }
        };
        reader.readAsDataURL(file);
      }
    });
  };

  const removeImage = (id: string) => {
    setUploadedImages(prev => prev.filter(img => img.id !== id));
  };

  const generateSaveInstructions = () => {
    const instructions = uploadedImages.map(img => {
      const category = categories.find(c => c.value === img.category);
      const folder = category?.folder || 'public/images';
      const fileName = img.name.toLowerCase().replace(/\s+/g, '-');
      return {
        originalName: img.name,
        savePath: `${folder}/${fileName}`,
        category: category?.label || 'Unknown',
      };
    });

    const instructionsText = instructions.map(inst => 
      `- Save "${inst.originalName}" to "${inst.savePath}"`
    ).join('\n');

    // Create a text file with instructions
    const blob = new Blob([`Screenshot Organization Instructions\n${'='.repeat(40)}\n\n${instructionsText}\n\nFolder Structure:\n${categories.map(c => `- ${c.folder}/`).join('\n')}`], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'screenshot-organization-instructions.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  const clearAllImages = () => {
    if (window.confirm('Are you sure you want to clear all uploaded images? This cannot be undone.')) {
      setUploadedImages([]);
      localStorage.removeItem('screenshot-upload-manager-images');
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-b pb-4">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
          <Camera className="w-5 h-5" />
          Screenshot Upload Manager
        </h2>
        <p className="text-gray-600 mt-1">
          Upload manual screenshots and organize them into proper folders
        </p>
      </div>

      {/* Category Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Category for Next Upload
        </label>
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {categories.map(cat => (
            <option key={cat.value} value={cat.value}>
              {cat.label} → {cat.folder}/
            </option>
          ))}
        </select>
      </div>

      {/* Upload Area */}
      <div
        className={`relative ${dragActive ? 'bg-blue-50' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleChange}
          className="hidden"
        />
        
        <div
          onClick={() => fileInputRef.current?.click()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <Upload size={40} className="mx-auto mb-3 text-gray-400" />
          <p className="text-base font-medium text-gray-700 mb-1">
            Drop screenshots here or click to upload
          </p>
          <p className="text-sm text-gray-500">
            Support for PNG, JPG, and other image formats
          </p>
        </div>
      </div>

      {/* Uploaded Images */}
      {uploadedImages.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium">Uploaded Screenshots ({uploadedImages.length})</h3>
            <div className="flex gap-2">
              <button
                onClick={generateSaveInstructions}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2 text-sm"
              >
                <Download size={14} />
                Generate Instructions
              </button>
              <button
                onClick={clearAllImages}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center gap-2 text-sm"
              >
                <X size={14} />
                Clear All
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {uploadedImages.map(img => {
              const category = categories.find(c => c.value === img.category);
              return (
                <div key={img.id} className="border rounded-lg overflow-hidden bg-gray-50">
                  <div className="aspect-video relative">
                    <img
                      src={img.preview}
                      alt={img.name}
                      className="w-full h-full object-cover"
                    />
                    <button
                      onClick={() => removeImage(img.id)}
                      className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                    >
                      <X size={14} />
                    </button>
                  </div>
                  <div className="p-3">
                    <p className="font-medium text-sm text-gray-900 truncate">{img.name}</p>
                    <p className="text-xs text-gray-600 mt-1">
                      <FolderOpen size={12} className="inline mr-1" />
                      {category?.folder}/
                    </p>
                    <span className="inline-block mt-2 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
                      {category?.label}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="border-t pt-6">
        <h3 className="text-base font-medium mb-3">How to Use</h3>
        <ol className="list-decimal list-inside space-y-1 text-sm text-gray-700">
          <li>Take manual screenshots of each feature/page</li>
          <li>Select the appropriate category from the dropdown</li>
          <li>Upload your screenshots (drag & drop or click)</li>
          <li>Review the suggested folder organization</li>
          <li>Click "Generate Instructions" to get a text file with save locations</li>
          <li>Save each image to the recommended folder in the project</li>
        </ol>

        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-900 text-sm mb-2">Folder Structure</h4>
          <div className="text-xs text-blue-800 font-mono space-y-1">
            {categories.map(cat => (
              <div key={cat.value}>
                {cat.folder}/
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}