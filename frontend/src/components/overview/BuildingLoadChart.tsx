import React from 'react';
import { DailyLoadChart } from '../charts/DailyLoadChartSimple';
import { WeeklyLoadChart } from '../charts/WeeklyLoadChartRecharts';
import { MonthlyLoadChart } from '../charts/MonthlyLoadChartRecharts';
import { YearlyLoadChart } from '../charts/YearlyLoadChartRecharts';
import { CompareLineChart } from '../charts/CompareLineChart';
import type { ChartView } from '../charts/types';
import type { LoadProfileData } from '../../lib/config/load-profile';
import { getUnitForView } from '../../lib/config/units';

// Define the specific meter types expected by CompareLineChart based on linter error
type ExpectedMeterType = "chillerPlant" | "airSide" | "light_power" | "data_center_others" | "evCharger" | "escalator_elevator";

// Empty array to trigger mock data in chart when no real data is available
const DEFAULT_DATA: LoadProfileData[] = [];

export interface BuildingLoadChartProps {
  data?: LoadProfileData[];
  comparisonData?: LoadProfileData[];
  onBarClick?: (date: Date) => void;
  selectedMeters?: Array<{ id: string; name: string; type: ExpectedMeterType; category: string }>;
  view?: ChartView;
  showComparison?: boolean;
  isComparison?: boolean;
  selectedSystem?: string | null;
  years?: number[];
  todayTotalKWh?: number | null;
  yesterdayTotalKWh?: number | null;
  isLoadingTotals?: boolean;
  errorTotals?: string | null;
}

function BuildingLoadChart({ 
  data = [], 
  comparisonData = [], 
  onBarClick,
  selectedMeters = [],
  view = 'daily', 
  showComparison = false,
  isComparison = false,
  selectedSystem,
  years,
  todayTotalKWh,
  yesterdayTotalKWh,
  isLoadingTotals,
  errorTotals
}: BuildingLoadChartProps) {
  // Directly use or default the data props
  const safeData = React.useMemo(() => data?.length ? data : DEFAULT_DATA, [data]);
  const safeComparisonData = React.useMemo(() => comparisonData?.length ? comparisonData : DEFAULT_DATA, [comparisonData]);

  // Render the appropriate chart based on view type
  const chart = React.useMemo(() => {
    return renderChartByType({
      view,
      safeData,
      safeComparisonData,
      years,
      selectedMeters,
      isComparison
    });
  }, [view, safeData, safeComparisonData, years, selectedMeters, isComparison]);

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* Chart container with proper overflow control */}
      <div className="flex-1 min-h-0 overflow-hidden">
        {chart}
      </div>
      
      {/* Removed comparison panel - all metrics are now above the chart */}
    </div>
  );
}

// Helper function to render the appropriate chart
function renderChartByType({ 
  view, 
  safeData, 
  safeComparisonData, 
  years,
  selectedMeters,
  isComparison
}: { 
  view: ChartView;
  safeData: LoadProfileData[];
  safeComparisonData: LoadProfileData[];
  years?: number[];
  selectedMeters: Array<{ id: string; name: string; type: ExpectedMeterType; category: string }>;
  isComparison: boolean;
}) {
  if (isComparison && selectedMeters.length > 0) {
    // For comparison mode with selected meters
    return (
      <CompareLineChart 
        data={safeData as any}
        selectedMeters={selectedMeters}
        view={view}
      />
    );
  }
  
  if (isComparison && selectedMeters.length === 0) {
    return <div className="flex justify-center items-center h-full text-gray-500">Select meters to compare</div>;
  }

  // Regular chart views
  switch (view) {
    case 'daily':
      return <DailyLoadChart data={safeData} comparisonData={safeComparisonData} />;
    case 'weekly':
      return <WeeklyLoadChart data={safeData} comparisonData={safeComparisonData} />;
    case 'monthly':
      return <MonthlyLoadChart data={safeData} comparisonData={safeComparisonData} />;
    case 'yearly':
    case 'multi-year':
      return <YearlyLoadChart 
        data={safeData} 
        comparisonData={safeComparisonData}
        years={years}
      />;
    default:
      return <DailyLoadChart data={safeData} comparisonData={safeComparisonData} />;
  }
}

// Separate component for the comparison panel
function ComparisonPanel({ 
  todayTotal, 
  yesterdayTotal, 
  isLoading,
  error
}: { 
  todayTotal?: number | null; 
  yesterdayTotal?: number | null; 
  isLoading?: boolean;
  error?: string | null;
}) {
  // Get current time for display
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const hoursProgress = ((hours * 60 + minutes) / (24 * 60)) * 100;
  
  // Calculate projected total for today based on current consumption rate
  const projectedTotal = todayTotal && hoursProgress > 0 
    ? (todayTotal / hoursProgress) * 100 
    : null;
  
  // Calculate difference
  const difference = todayTotal && yesterdayTotal 
    ? todayTotal - (yesterdayTotal * hoursProgress / 100)
    : null;
  const percentDiff = yesterdayTotal && difference !== null
    ? (difference / (yesterdayTotal * hoursProgress / 100)) * 100
    : null;

  if (isLoading) {
    return (
      <div className="mt-2 px-4">
        <div className="animate-pulse h-16 bg-gray-100 rounded-lg"></div>
      </div>
    );
  }

  if (error) {
    return null; // Silently fail - don't distract from the main chart
  }

  return (
    <div className="px-4 py-1.5 bg-gray-50 border-t">
      <div className="flex items-center justify-between text-xs text-gray-600">
        <span>
          Full day total: <span className="font-semibold text-gray-800">
            {typeof todayTotal === 'number' ? Math.round(todayTotal).toLocaleString() : '-'} kWh
          </span>
        </span>
        <span>
          Yesterday: <span className="font-semibold text-gray-800">
            {typeof yesterdayTotal === 'number' ? Math.round(yesterdayTotal).toLocaleString() : '-'} kWh
          </span>
        </span>
      </div>
    </div>
  );
}

export { BuildingLoadChart };