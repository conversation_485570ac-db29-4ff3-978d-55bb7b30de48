import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Right, Building2, <PERSON><PERSON><PERSON><PERSON>ff, <PERSON><PERSON>ge, Zap, Fan, Lightbulb, Cog, PlugZap, BatteryCharging as Charg<PERSON><PERSON><PERSON>, Snowflake } from 'lucide-react';
import { BUILDINGS } from '../../lib/constants';
import { TEXT_STYLES, GRADIENT_STYLES, SHADOW_STYLES, BORDER_STYLES, ANIMATION_STYLES } from '../../lib/config/ui';

const METER_TYPES = {
  lighting: { icon: Lightbulb, label: 'Lighting', count: 24 },
  chiller: { icon: Snowflake, label: 'Chiller Plant', count: 18 },
  airside: { icon: Fan, label: 'Air Side', count: 16 },
  infrastructure: { icon: Cog, label: 'Infrastructure', count: 12 },
  solar: { icon: Zap, label: 'Solar', count: 8 },
  ev: { icon: ChargingPod, label: 'EV Charger', count: 6 }
} as const;

// Calculate total meters
const totalMeters = Object.values(METER_TYPES).reduce((sum, { count }) => sum + count, 0);

interface AlertSection {
  title: string;
  icon: typeof AlertTriangle;
  description: string;
  alerts: {
    urgent: number;
    high: number;
    low: number;
    total: number;
  };
  details?: {
    category: string;
    status: 'critical' | 'warning' | 'normal';
    value: string;
    threshold: string;
  }[];
}

interface SystemAlertsProps {
  sections: AlertSection[];
  onViewAll?: () => void;
}

export function SystemAlerts({ sections, onViewAll }: SystemAlertsProps) {
  return (
    <div className={`w-full p-5 ${GRADIENT_STYLES.card} ${BORDER_STYLES.card} ${SHADOW_STYLES.card} backdrop-blur-sm hover:shadow-[0_16px_24px_-8px_rgba(14,125,228,0.15)] transition-all duration-300`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <h3 className={TEXT_STYLES.title.base}>Meter Status</h3>
          <span className="text-[10px] text-gray-400">Last updated: {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
        </div>
        {onViewAll && (
          <button
            onClick={onViewAll}
            className="flex items-center gap-1 text-xs text-gray-400 hover:text-gray-600 transition-colors group whitespace-nowrap hover:scale-105"
          >
            View all meters
            <ArrowRight size={16} className="transition-transform group-hover:translate-x-1" />
          </button>
        )}
      </div>

      <div className="space-y-4">
        {sections.map((section) => (
          <React.Fragment key={section.title}>
            {section.details && (
              <div className="grid grid-cols-2 gap-3">
                {section.details.map((detail, idx) => (
                  <div 
                    key={`${detail.category}-${detail.status}-${idx}`} 
                    className={`flex-1 flex items-center justify-between p-3 rounded-lg transition-all duration-200 ${
                      detail.value.includes('Online')
                        ? 'bg-gradient-to-br from-blue-50/60 via-blue-50/40 to-white border border-blue-100/80 hover:shadow-[0_4px_12px_rgba(14,126,228,0.15)] hover:scale-[1.02]'
                        : 'bg-gradient-to-br from-red-50/60 via-red-50/40 to-white border border-red-100/80 hover:shadow-[0_4px_12px_rgba(239,68,68,0.15)] hover:scale-[1.02]'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-2.5 h-2.5 rounded-full ${
                        detail.value.includes('Online')
                          ? 'bg-primary-blue shadow-[0_0_12px_rgba(14,126,228,0.3)] animate-[pulse_2s_ease-in-out_infinite]'
                          : 'bg-red-500 shadow-[0_0_12px_rgba(239,68,68,0.3)] animate-[pulse_1s_ease-in-out_infinite]'
                      }`} />
                      <div className="group relative">
                        <span className="text-sm font-medium text-gray-900">{detail.value.replace(' / Online', '').replace(' / Offline', '')}</span>
                        <div className="absolute left-0 -top-12 w-48 p-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10">
                          {detail.value.includes('Online') 
                            ? 'Meters are actively reporting data and functioning normally'
                            : 'Meters are currently disconnected and require attention'}
                        </div>
                      </div>
                    </div>
                    <span className={`text-xs px-2 py-0.5 rounded-full transition-all duration-200 ${
                      detail.value.includes('Online')
                        ? 'bg-blue-100/50 text-primary-blue font-medium shadow-[0_2px_8px_rgba(14,126,228,0.15)]'
                        : 'bg-red-100/50 text-red-800 font-medium shadow-[0_2px_8px_rgba(239,68,68,0.15)]'
                    }`}>
                      {detail.threshold}
                    </span>
                  </div>
                ))}
              </div>
            )}

            {/* Meter Type Distribution */}
            <div className="mt-3">
              <div className="text-sm text-gray-500 mb-4 flex items-center justify-between">
                <span>Distribution</span>
                <span className="text-xs text-gray-400">{totalMeters} meters</span>
              </div>
              <div className="grid grid-cols-2 gap-3" key="meter-distribution">
                {Object.entries(METER_TYPES).map(([type, { icon: Icon, label, count }]) => (
                  <div
                    key={type}
                    className="p-3 bg-gradient-to-br from-blue-50/60 via-blue-50/40 to-white rounded-lg border border-blue-100/80 hover:shadow-[0_4px_12px_rgba(14,126,228,0.15)] hover:scale-[1.02] transition-all duration-200 group relative"
                    title={`${label} Meters`}
                  >
                    <div className="flex items-center gap-3">
                      <div className="p-1.5 rounded-lg bg-white/50 group-hover:bg-blue-100/50 transition-colors duration-200">
                        <Icon size={14} className="text-primary-blue" />
                      </div>
                      <div className="flex flex-col">
                       <span className={TEXT_STYLES.label}>{label}</span>
                        <div className="flex items-baseline gap-1">
                         <span className="text-sm font-medium text-primary-blue">{count}</span>
                         <span className={TEXT_STYLES.unit}>meters</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}