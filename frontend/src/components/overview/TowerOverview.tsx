import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Building2, ArrowR<PERSON>, AlertTriangle, TrendingDown, TrendingUp } from 'lucide-react';
import type { BuildingId } from '../../types';
import { formatNumber } from '../../lib/utils/formatters';
import { buildingData } from '../../lib/data/dashboard';

interface TowerOverviewProps {
  building: BuildingId;
  customData?: {
    consumption: number | null;
    lastYearConsumption: number | null;
    percentChange: number | null;
    timeFrame: 'weekly' | 'monthly' | 'yearly';
  };
}

export function TowerOverview({ building, customData }: TowerOverviewProps) {
  const navigate = useNavigate();

  const handleCardClick = () => {
    navigate(`/meters?building=${building.toLowerCase()}`, {
      state: { selectedMeterView: `tower-${building.toLowerCase()}` }
    });
  };
  
  const handleAlertsClick = (event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent card click
    navigate(`/alarms?building=${building.toLowerCase()}&status=active`);
  };

  const handleMetersClick = (event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent card click
    navigate(`/meters?building=${building.toLowerCase()}&status=disconnected`);
  };

  // Use customData if provided, otherwise fallback to buildingData or defaults
  const lastYearConsumption = customData?.lastYearConsumption;
  const percentChange = customData?.percentChange;
  const timeFrame = customData?.timeFrame || 'weekly'; // Default to weekly if not specified

  const buildingDefaultConsumption = buildingData[building]?.consumption ?? 0;
  let consumptionToDisplay: string;

  if (customData !== undefined) {
    // Real data was attempted/provided
    if (customData.consumption === null) {
      consumptionToDisplay = '-'; // Real data is null (error or no data), so show "-"
    } else if (typeof customData.consumption === 'number') {
      consumptionToDisplay = formatNumber(customData.consumption, { decimals: 0 }); // Real data is a number
    } else {
      // Fallback for safety, though types should prevent customData.consumption from being neither number nor null
      consumptionToDisplay = formatNumber(buildingDefaultConsumption, { decimals: 0 });
    }
  } else {
    // No customData provided at all, use mock/default from buildingData
    consumptionToDisplay = formatNumber(buildingDefaultConsumption, { decimals: 0 });
  }
  
  const offlineMetersCount = buildingData[building].offlineMeters || 0;
  const anomaliesCount = buildingData[building].anomalies || 0;

  // Calculate progress percentage - uses customData.consumption if available and numeric, otherwise 0
  const numericConsumptionForProgress = (customData !== undefined && typeof customData.consumption === 'number') ? customData.consumption : 0;
  const progressPercentage = (typeof lastYearConsumption === 'number' && lastYearConsumption > 0)
                            ? Math.min(100, (numericConsumptionForProgress / lastYearConsumption) * 100)
                            : 0;

  // Determine if consumption increased or decreased compared to last year
  const isDecrease = typeof percentChange === 'number' && percentChange < 0;

  // Format the percentage change for display, show '-' if not available
  const formattedPercentChange = typeof percentChange === 'number' 
                                ? `${percentChange < 0 ? '' : '+'}${percentChange.toFixed(1)}%` 
                                : '-';

  const displayLastYearConsumption = typeof lastYearConsumption === 'number' 
                                      ? formatNumber(lastYearConsumption, { decimals: 0 })
                                      : '-';

  return (
    <button
      onClick={handleCardClick} // Main card click navigates to meter details
      className={`w-full p-3 bg-white rounded-xl border border-blue-100 hover:shadow-[0_8px_16px_-4px_rgba(14,125,228,0.08)] transition-all duration-300 group text-left`}
    >
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-1 rounded-lg bg-gradient-to-br from-blue-100/80 via-blue-100/50 to-white border border-blue-200/50">
              <Building2 size={14} className="text-primary-blue transition-transform group-hover:scale-110 duration-300" />
            </div>
            <h4 className="text-xs font-medium text-gray-500">
              {building === 'A' ? 'Tower Building' : building === 'B' ? 'Podium Building' : 'Car Park Building'}
            </h4>
          </div>
          {/* Keep View Details as part of the main button click area for now */}
          <div className="flex items-center gap-1 text-xs text-gray-400 group-hover:text-primary-blue transition-colors">
            View Details
            <ArrowRight size={14} className="transform transition-transform group-hover:translate-x-1.5 duration-300" />
          </div>
        </div>

        {/* Stats Grid */}
        <div>
          {/* Energy Consumption */}
          <div className="p-1.5 rounded-lg bg-white border border-blue-100 group-hover:shadow-[0_2px_8px_rgba(14,126,228,0.15)] transition-all duration-300">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-1.5">
                <span className="text-[10px] text-gray-500 font-medium">
                  {timeFrame === 'weekly' ? 'Weekly' : 'Monthly'} Consumption
                </span>
              </div>
              
              {/* Year-over-year comparison - only show if lastYearConsumption is a valid number AND not zero */}
              {(typeof lastYearConsumption === 'number' && lastYearConsumption !== 0) && (
                <div className={`flex items-center gap-0.5 text-[10px] font-medium ${isDecrease && typeof percentChange === 'number' ? 'text-green-600' : (typeof percentChange === 'number' && percentChange > 0 ? 'text-red-500' : 'text-gray-500')}`}>
                  {/* Only show icon if percentChange is a valid number and not zero */}
                  {(typeof percentChange === 'number' && percentChange !== 0) ? (
                    isDecrease ? (
                      <TrendingDown size={12} className="transition-transform group-hover:scale-110 duration-300" />
                    ) : (
                      <TrendingUp size={12} className="transition-transform group-hover:scale-110 duration-300" />
                    )
                  ) : null} 
                  <span>{formattedPercentChange}</span>
                </div>
              )}
            </div>
            
            <div className="flex items-baseline gap-1 mb-2">
              <span className="text-sm font-bold text-primary-blue">
                {consumptionToDisplay}
              </span>
              <span className="text-[9px] text-gray-500">kWh</span>
            </div>
            
            {/* Progress bar comparing to last year - only show if lastYearConsumption is a valid number AND not zero */}
            {(typeof lastYearConsumption === 'number' && lastYearConsumption !== 0) && (
              <div className="mt-1.5 space-y-1">
                <div className="flex items-center justify-between text-[9px] text-gray-500">
                  <span>vs same {timeFrame} last year</span>
                  <span className="font-medium">{displayLastYearConsumption} kWh</span>
                </div>
                <div className="h-1.5 w-full bg-gray-100 rounded-full overflow-hidden">
                  <div 
                    className={`h-full rounded-full ${isDecrease && typeof percentChange === 'number' ? 'bg-green-500' : 'bg-red-500'}`}
                    style={{ width: `${progressPercentage}%` }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Alerts & Meters */}
        <div className="flex items-center justify-end text-[10px] text-gray-500">
          <div className="flex items-center gap-4">
            {/* Clickable Alerts Section */}
            <div 
              onClick={handleAlertsClick} 
              className="flex items-center gap-1 transition-colors group-hover:text-yellow-600 cursor-pointer"
              role="button"
              tabIndex={0}
              aria-label={`View ${anomaliesCount} active alerts for Tower ${building}`}
              onKeyDown={(e) => e.key === 'Enter' && handleAlertsClick(e as any)} // Basic keyboard accessibility
            >
              <AlertTriangle size={12} className="text-yellow-500 transition-transform group-hover:scale-110 duration-300" />
              <span>{anomaliesCount} alerts</span>
            </div>
            {/* Clickable Meters Offline Section */}
            <div 
              onClick={handleMetersClick} 
              className="flex items-center gap-1 transition-colors group-hover:text-red-600 cursor-pointer"
              role="button"
              tabIndex={0}
              aria-label={`View ${offlineMetersCount} offline meters for Tower ${building}`}
              onKeyDown={(e) => e.key === 'Enter' && handleMetersClick(e as any)} // Basic keyboard accessibility
            >
              <span className="w-2 h-2 rounded-full bg-red-500 animate-[pulse_2s_ease-in-out_infinite] shadow-[0_0_12px_rgba(239,68,68,0.3)]" />
              <span>{offlineMetersCount} meters offline</span>
            </div>
          </div>
          {/* Floor count removed here */}
        </div>
      </div>
    </button>
  );
}