import React, { useRef, useLayoutEffect, useMemo } from 'react';
import * as echarts from 'echarts';

// Define color constants for consistency
const COLOR_PREVIOUS_YEAR = '#55A6F2';
const COLOR_CURRENT_YEAR = '#14B8B4';
const COLOR_SBTI = '#6B7BC9';
const COLOR_AVG_CURRENT_YEAR = '#2DD4BF';
const COLOR_PFM = '#9CA3AF';

// Define opacity levels
const OPACITY_PREVIOUS_YEAR = '4D';
const OPACITY_CURRENT_YEAR = 'FF';

interface EnergyBenchmarkChartProps {
  className?: string;
  height?: number;
  currentYearData?: number[];
  previousYearData?: number[];
  // Optional: Add props for targets if they become dynamic
  sbtiTargetValue?: number;
  pfmTargetValue?: number;
  baselineYear?: number;
}

export function EnergyBenchmarkChart({
  className = '',
  height = 210,
  currentYearData,
  previousYearData,
  sbtiTargetValue,
  pfmTargetValue,
  baselineYear = 2023
}: EnergyBenchmarkChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts>();
  const isInitialized = useRef(false);

  const dynamicYears = useMemo(() => {
    const current = new Date().getFullYear();
    return {
      current: current,
      previous: current - 1,
    };
  }, []);

  const chartData = useMemo(() => {
    const actualCurrentYearData = currentYearData && currentYearData.length === 12 ? currentYearData : new Array(12).fill(0);
    const actualPreviousYearData = previousYearData && previousYearData.length === 12 ? previousYearData : new Array(12).fill(0);
    
    const avgCurrentYearValue = actualCurrentYearData.reduce((sum, val) => sum + val, 0) / (actualCurrentYearData.filter(v => v > 0).length || 12);

    return {
      months: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      dataPreviousYear: actualPreviousYearData,
      dataCurrentYear: actualCurrentYearData,
      sbtiTarget: sbtiTargetValue ?? 0, // Use prop or default to 0
      pfmTarget: pfmTargetValue ?? 0,   // Use prop or default to 0
      avgCurrentYear: Math.round(avgCurrentYearValue)
    };
  }, [currentYearData, previousYearData, sbtiTargetValue, pfmTargetValue]);

  const option = useMemo(() => {
    const legendNamePrevious = dynamicYears.previous.toString();
    const legendNameCurrent = dynamicYears.current.toString();
    const legendNameAvgCurrent = `Avg ${baselineYear}`;

    return {
      color: [
        COLOR_PREVIOUS_YEAR + OPACITY_PREVIOUS_YEAR,
        COLOR_CURRENT_YEAR + OPACITY_CURRENT_YEAR,
      ],
      tooltip: { 
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: (params: any[]) => {
          let tooltipContent = `${params[0].name}<br/>`;
          params.forEach(param => {
            const value = typeof param.value === 'number' ? param.value.toLocaleString() : '-';
            tooltipContent += `${param.marker} ${param.seriesName}: ${value} kWh<br/>`;
          });
          return tooltipContent;
        }
      },
      legend: {
        data: [
          { name: legendNamePrevious, itemStyle: { color: COLOR_PREVIOUS_YEAR + OPACITY_PREVIOUS_YEAR } },
          { name: legendNameCurrent, itemStyle: { color: COLOR_CURRENT_YEAR } },
          { name: 'SBTi Target', itemStyle: { color: COLOR_SBTI } },
          { name: legendNameAvgCurrent, itemStyle: { color: COLOR_AVG_CURRENT_YEAR } },
          { name: 'PFM Target', itemStyle: { color: COLOR_PFM } }
        ],
        right: 10, top: 0,
        textStyle: { color: '#64748B', fontSize: 10 },
        itemWidth: 10, itemHeight: 10, itemGap: 8,
        icon: 'circle',
        selected: { 'PFM Target': false, [legendNamePrevious]: true } // Show previous year by default
      },
      grid: { left: '8%', right: '5%', bottom: '10%', top: '12%', containLabel: false },
      xAxis: {
        type: 'category',
        data: chartData.months,
        axisLine: { lineStyle: { color: '#E2E8F0' } },
        axisLabel: { color: '#64748B', fontSize: 10, margin: 10 },
        axisTick: { show: false }
      },
      yAxis: {
        type: 'value', name: 'kWh',
        nameLocation: 'middle', nameGap: 35,
        nameTextStyle: { color: '#64748B', fontSize: 10 },
        axisLine: { show: false }, axisTick: { show: false },
        axisLabel: {
          color: '#64748B', fontSize: 10, margin: 5, align: 'right',
          formatter: (value: number) => {
            if (value >= 1000000) return (value / 1000000).toFixed(1) + 'M';
            if (value >= 1000) return (value / 1000).toFixed(0) + 'k';
            return value.toLocaleString('en-US');
          }
        },
        splitLine: {
          show: true,
          z: -10, 
          zlevel: -1, 
          lineStyle: { color: '#f1f5f9', type: 'dashed' }
        },
      },
      series: [
        {
          name: legendNamePrevious, type: 'bar', barWidth: 6, barGap: '50%',
          z: 1, zlevel: 1,
          data: chartData.dataPreviousYear,
          itemStyle: { borderRadius: [3, 3, 0, 0], color: COLOR_PREVIOUS_YEAR + OPACITY_PREVIOUS_YEAR }
        },
        {
          name: legendNameCurrent, type: 'bar', barWidth: 6,
          z: 2, zlevel: 1,
          data: chartData.dataCurrentYear,
          itemStyle: { borderRadius: [3, 3, 0, 0], color: COLOR_CURRENT_YEAR }
        },
        {
          name: 'SBTi Target', type: 'line', symbol: 'none',
          z: 4, zlevel: 1,
          lineStyle: { color: COLOR_SBTI, width: 1.5, type: 'dashed' },
          data: Array(12).fill(chartData.sbtiTarget)
        },
        {
          name: legendNameAvgCurrent, type: 'line', symbol: 'none',
          z: 5, zlevel: 1,
          lineStyle: { color: COLOR_AVG_CURRENT_YEAR, width: 1.5, type: 'dashed' },
          data: Array(12).fill(chartData.avgCurrentYear)
        },
        {
          name: 'PFM Target', type: 'line', symbol: 'none',
          z: 6, zlevel: 1,
          lineStyle: { color: COLOR_PFM, width: 1.5, type: 'dashed' },
          data: Array(12).fill(chartData.pfmTarget)
        }
      ]
    };
  }, [chartData, dynamicYears]);

  useLayoutEffect(() => {
    let chart: echarts.ECharts | undefined = undefined;
    if (chartRef.current) {
      chart = chartInstance.current || echarts.init(chartRef.current);
      chart.setOption(option, true);
      chartInstance.current = chart;
      isInitialized.current = true;
    }
    const handleResize = () => chart?.resize();
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current && typeof chartInstance.current.dispose === 'function') {
        try {
          chartInstance.current.dispose();
        } catch (e) {
          console.error("Error disposing chart:", e);
        }
      }
      chartInstance.current = undefined;
      isInitialized.current = false;
    };
  }, [option]); 

  const isLoading = currentYearData === undefined || previousYearData === undefined;

  return (
    <div className={`${className} pt-0 -mt-1`}>
      <div style={{ height }}>
        {isLoading ? (
          <div className="h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" aria-label="Loading chart"></div>
          </div>
        ) : (
          <div ref={chartRef} className="w-full h-full" />
        )}
      </div>
    </div>
  );
}