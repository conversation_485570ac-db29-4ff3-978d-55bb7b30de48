import React from 'react';
import { Building2, <PERSON><PERSON><PERSON><PERSON><PERSON>, WifiOff, Gauge, Info } from 'lucide-react';
import { BUILDINGS } from '../../lib/constants';
import { useNavigate } from 'react-router-dom';
import { formatNumber } from '../../lib/utils/formatters';
import { TEXT_STYLES } from '../../lib/config/ui';
import { FloorAnalyticsModal } from '../dashboard/FloorAnalyticsModal';

// Energy usage levels and colors
const ENERGY_LEVELS = [
  { range: '0', color: '#F8FAFC', gradient: 'from-slate-50 to-white', value: 0 },
  { range: '1k', color: '#EFF6FF', gradient: 'from-blue-50 to-blue-100/50', value: 1000 },
  { range: '2k', color: '#DBEAFE', gradient: 'from-blue-100 to-blue-200/50', value: 2000 },
  { range: '3k', color: '#BFDBFE', gradient: 'from-blue-200 to-blue-300/50', value: 3000 },
  { range: '4k+', color: '#60A5FA', gradient: 'from-blue-300 to-blue-400/50', value: 4000 }
] as const;

interface BuildingVisualization3DProps {
  selectedFloor?: number;
  onFloorSelect?: (floor: number) => void;
  buildingId?: BuildingId;
  floorData?: Array<{
    floor: number;
    consumption: number;
    hasAlert?: boolean;
    isDisconnected?: boolean;
  }>;
}

export function BuildingVisualization3D({ selectedFloor, onFloorSelect, buildingId = 'A', floorData = [] }: BuildingVisualization3DProps) {
  const [hoveredFloor, setHoveredFloor] = React.useState<number | null>(null);
  const [showAnalytics, setShowAnalytics] = React.useState(false);
  const [activeFloor, setActiveFloor] = React.useState<number | null>(null);
  const navigate = useNavigate();

  const handleFloorClick = (floor: number) => {
    navigate(`/meters?building=${buildingId}&floor=${floor}`, {
      state: { expandFloor: true }
    });
  };

  // Generate floors
  const floors = React.useMemo(() => {
    return Object.keys(BUILDINGS[buildingId].floorUsage)
      .map(floor => parseInt(floor))
      .filter(floor => floor !== 13) // Explicitly filter out floor 13
      .sort((a, b) => (typeof b === 'number' && typeof a === 'number' ? b - a : 0));
  }, [buildingId]);

  return (
    <div className="relative w-full h-full flex flex-col overflow-hidden">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-20 flex items-center justify-between bg-gradient-to-r from-white via-white/95 to-white/90 backdrop-blur-sm pb-2">
        <div className="flex items-center gap-2">
          <Building2 size={16} className="text-primary-blue" />
          <h3 className="text-sm font-semibold text-transparent bg-clip-text bg-gradient-to-r from-primary-blue to-blue-600">
            Tower {buildingId}
          </h3>
        </div>
        
        {/* Energy Usage Legend */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Gauge size={14} className="text-gray-400" />
            <span className="text-xs font-medium text-gray-500">Energy Usage</span>
            <span className="text-[10px] text-gray-400">(kWh)</span>
          </div>
          <div className="flex items-center gap-1">
            {ENERGY_LEVELS.map((level) => (
              <div
                key={level.range}
                className="transition-transform duration-200 relative group"
              >
                <div
                  className={`w-6 h-3 rounded-sm cursor-help bg-gradient-to-br ${level.gradient} border border-gray-100/50 shadow-[0_1px_2px_rgba(14,126,228,0.05)]`}
                >
                  <span className="absolute -bottom-4 left-1/2 -translate-x-1/2 text-[9px] text-gray-500 font-medium">
                    {level.range}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Floor Analytics Modal */}
      {showAnalytics && activeFloor && (
        <FloorAnalyticsModal
          floorNumber={activeFloor}
          onClose={() => {
            setShowAnalytics(false);
            setActiveFloor(null);
          }}
        />
      )}

      {/* Main Content Grid */}
      <div className="absolute inset-0 pt-12 grid gap-[4px] content-center overflow-hidden" style={{ gridTemplateRows: `repeat(${floors.length}, 22px)` }}>
        {floors.map((floor, index) => {
         const data = floorData?.find(d => d.floor === floor);
         const isSelected = floor === selectedFloor;
         
         const getColor = () => {
           if (!data) return '#E2E8F0';
           const consumption = data.consumption;
           if (consumption === 0) return '#F1F5F9';
           if (consumption < 1000) return '#EFF6FF';
           if (consumption < 2000) return '#DBEAFE';
           if (consumption < 3000) return '#BFDBFE';
           if (consumption < 4000) return '#93C5FD';
           return '#60A5FA';
         };

         const getGradient = () => {
           if (!data) return 'rgba(226, 232, 240, 0.1)';
           const consumption = data.consumption;
           if (consumption === 0) return 'rgba(226, 232, 240, 0.1)';
           if (consumption < 1000) return 'rgba(59, 130, 246, 0.05)';
           if (consumption < 2000) return 'rgba(59, 130, 246, 0.1)';
           if (consumption < 3000) return 'rgba(59, 130, 246, 0.15)';
           if (consumption < 4000) return 'rgba(59, 130, 246, 0.2)';
           return 'rgba(59, 130, 246, 0.25)';
         };

          return (
            <div key={floor} className="relative flex items-center" style={{ gridRow: index + 1 }}>
              {/* Floor Label */}
              <div className="absolute left-6 flex items-center z-10">
                <button
                  onClick={() => handleFloorClick(floor)}
                  onMouseEnter={() => setHoveredFloor(floor)}
                  onMouseLeave={() => setHoveredFloor(null)}
                  className={`flex items-center gap-2 px-2.5 py-1 rounded-lg transition-all duration-300 ${
                    isSelected || hoveredFloor === floor
                      ? 'bg-gradient-to-br from-blue-50/80 via-blue-50/60 to-white text-primary-blue shadow-[0_2px_4px_rgba(14,126,228,0.15)] font-medium border border-blue-200/80'
                      : 'text-gray-400 hover:bg-gradient-to-br hover:from-blue-50/40 hover:to-white hover:text-primary-blue hover:shadow-[0_1px_2px_rgba(14,126,228,0.1)]'
                  }`}
                >
                  <span className="text-[10px] w-6 tabular-nums font-medium">FL.{floor}</span>
                  {data?.hasAlert && (
                    <div className="relative flex h-3 w-3">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                      <AlertTriangle size={12} className="text-red-500 relative animate-pulse" />
                    </div>
                  )}
                  {data?.isDisconnected && (
                    <WifiOff size={12} className="text-gray-400 hover:text-gray-600 transition-colors" />
                  )}
                  {isSelected && (
                    <div className="absolute -right-2 top-1/2 w-2 h-px bg-primary-blue transform -translate-y-1/2 animate-fadeIn" />
                  )}
                </button>
              </div>

              {/* Floor Visualization */}
              <div
                onClick={() => handleFloorClick(floor)}
                onMouseEnter={() => setHoveredFloor(floor)}
                onMouseLeave={() => setHoveredFloor(null)}
                className={`absolute left-1/2 -translate-x-[80px] w-[160px] group transition-all duration-300 cursor-pointer ${
                  hoveredFloor === floor ? 'scale-[1.02] z-10 shadow-[0_4px_12px_rgba(14,126,228,0.15)]' : ''
                }`}
                style={{
                  background: `linear-gradient(165deg, ${getColor()}CC, ${getColor()}FF)`,
                  border: isSelected ? '2px solid rgba(59, 130, 246, 0.8)' : '1px solid rgba(59, 130, 246, 0.2)',
                  boxShadow: isSelected 
                    ? `0 4px 12px ${getGradient()}, inset 0 1px 2px rgba(255,255,255,0.3)`
                    : `inset 0 1px 2px ${getGradient()}, 0 2px 4px rgba(14,126,228,0.1)`,
                  height: '18px',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/30 to-white/0 opacity-0 group-hover:opacity-100 transition-all duration-300" />
                
                {/* Usage Type */}
                <div className="absolute -right-28 top-1/2 -translate-y-1/2 text-[10px] font-medium text-gray-400 group-hover:text-gray-600 whitespace-nowrap transition-all duration-300 group-hover:translate-x-1">
                  {BUILDINGS[buildingId].floorUsage[floor] || ''}
                </div>
              </div>
            </div>
          );
        })}
      
        {/* Building Base Shadow */}
        <div
          className="absolute left-1/2 -translate-x-[80px] w-[160px] -bottom-2 h-12"
          style={{
            background: 'radial-gradient(ellipse at center, rgba(59, 130, 246, 0.12) 0%, rgba(59, 130, 246, 0) 80%)',
            borderRadius: '50%',
          }}
        />
      </div>
    </div>
  );
}