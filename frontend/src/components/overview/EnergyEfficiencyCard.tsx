import React from 'react';
import { Info } from 'lucide-react';
import { createPortal } from 'react-dom';
import { SCALE_CATEGORIES, BUILDING_CONFIG } from '../../lib/config/building';

interface EnergyEfficiencyProps {
  value: number;
  gfa: number;
  hideValue?: boolean;
}

// Helper function to determine category based on value
function getCategory(value: number) {
  // Classify by checking if value is within or below category threshold
  for (let i = SCALE_CATEGORIES.length - 1; i >= 0; i--) {
    if (value <= SCALE_CATEGORIES[i].value) {
      return SCALE_CATEGORIES[i];
    }
  }
  // If above all thresholds, default to highest category
  return SCALE_CATEGORIES[0];
}

// Memoized scale bar component for better performance
function ScaleBar({ value }: { value: number }) {
  const [tooltipData, setTooltipData] = React.useState<{
    x: number;
    y: number;
    label: string;
    value: number;
    description: string;
  } | null>(null);

  const handleMouseEnter = (event: React.MouseEvent, cat: typeof SCALE_CATEGORIES[number]) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setTooltipData({
      x: rect.left + rect.width / 2,
      y: rect.top - 10,
      label: cat.label,
      value: cat.value,
      description: BUILDING_CONFIG.energyIntensity.descriptions[cat.id.toLowerCase() as keyof typeof BUILDING_CONFIG.energyIntensity.descriptions]
    });
  };

  const handleMouseLeave = () => {
    setTooltipData(null);
  };

  // Calculate marker position based on the scale range
  // The scale goes from 0 to BEC (171)
  const maxValue = BUILDING_CONFIG.energyIntensity.target.bec;
  
  // Get current category
  const currentCategory = getCategory(value);

  let finalPositionPercent = 0;
  // --- New Segment-Based Position Calculation --- 
  // Use exact SCALE_CATEGORIES values for boundaries
  const segments = [
    { id: 'BEC', range: [SCALE_CATEGORIES[1].value, SCALE_CATEGORIES[0].value], startPercent: 0 },  // 141-171
    { id: 'HEPS', range: [SCALE_CATEGORIES[2].value, SCALE_CATEGORIES[1].value], startPercent: 25 }, // 82-141
    { id: 'ECON', range: [SCALE_CATEGORIES[3].value, SCALE_CATEGORIES[2].value], startPercent: 50 }, // 57-82
    { id: 'ZEB', range: [SCALE_CATEGORIES[4].value, SCALE_CATEGORIES[3].value], startPercent: 75 },  // 0-57
  ];
  const currentSegment = segments.find(seg => seg.id === currentCategory.id);

  if (currentSegment) {
    const [lowerBound, upperBound] = currentSegment.range;
    const rangeWidth = upperBound - lowerBound;
    // Calculate position within segment (0 to 1), reversed for visual bar
    const positionWithinSegment = (rangeWidth === 0) ? 0 : Math.max(0, Math.min(1, (upperBound - value) / rangeWidth));
    // Map to final percentage: segment start + position within segment * segment width (25%)
    finalPositionPercent = currentSegment.startPercent + (positionWithinSegment * 25);
  } else {
    // Fallback to simple numerical percentage if category not found (shouldn't happen)
    finalPositionPercent = ((maxValue - value) / maxValue) * 100;
  }
  const clampedPositionPercent = Math.min(100, Math.max(0, finalPositionPercent));
  // --- End New Calculation ---

  return (
    <div className="space-y-1 relative mt-4">
      {/* Categories */}
      <div className="flex h-5 relative">
        {SCALE_CATEGORIES.slice(0, -1).map((cat) => (
          <div
            key={cat.id}
            className={`flex-1 h-5 first:rounded-l-lg last:rounded-r-lg flex items-center justify-center transition-colors duration-300 hover:z-20 ${
              cat.id === currentCategory.id ? 'ring-2 ring-white ring-opacity-70 z-10' : ''
            }`}
            onMouseEnter={(e) => handleMouseEnter(e, cat)}
            onMouseLeave={handleMouseLeave}
            style={{ 
              background: `linear-gradient(165deg, ${cat.color}CC, ${cat.color}FF)`,
              boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.1), 0 1px 3px rgba(0,0,0,0.05)',
              cursor: 'help'
            }}
          >
            <span className="text-[11px] font-medium text-white tracking-wider">
              {cat.label}
            </span>
          </div>
        ))}
      </div>
      
      {/* Triangle indicator below scale */}
      <div className="relative h-2">
        <div 
          className="absolute w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-b-[6px] border-b-blue-600 drop-shadow-sm"
          style={{ 
            left: `${clampedPositionPercent}%`,
            transform: 'translateX(-50%)',
            top: '-2px'
          }}
        />
      </div>

      {/* Portal Tooltip */}
      {tooltipData && createPortal(
        <div
          className="fixed pointer-events-none animate-fadeIn z-[9999]"
          style={{
            left: tooltipData.x,
            top: tooltipData.y,
            transform: 'translate(-50%, -100%)'
          }}
        >
          <div className="bg-gray-900/90 backdrop-blur-sm text-white text-[10px] rounded-lg py-1.5 px-3 whitespace-nowrap shadow-lg">
            <div className="font-medium">{tooltipData.description}</div>
            <div className="text-gray-400 text-[9px] mt-0.5">
              {tooltipData.value} kWh/(m²·yr)
            </div>
          </div>
          <div className="w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-gray-900/90 mx-auto" />
        </div>,
        document.body
      )}

      {/* Scale Values */}
      <div className="flex justify-between">
        {SCALE_CATEGORIES.map((cat) => (
          <div
            key={cat.id}
            className="text-[7px] font-medium text-gray-400 first:text-left last:text-right relative z-10"
            style={{ width: cat.id === 'ZERO' ? '20px' : 'auto' }}
          >
            {cat.value}
          </div>
        ))}
      </div>
    </div>
  );
}

export function EnergyEfficiencyCard({ value, gfa, hideValue = false }: EnergyEfficiencyProps) {
  const category = getCategory(value);

  return (
    <div className="w-full max-w-[400px] mx-auto">
      {/* Render the scale bar, passing the current value */}
      <ScaleBar value={value} />

      {!hideValue && (
        <div className="relative w-28 p-2 bg-gradient-to-br from-white to-[#DBE4FF] shadow-[4px_8px_24px_rgba(14,125,228,0.15)] rounded-lg border-[2px] backdrop-blur-sm hover:scale-105 hover:shadow-[4px_12px_32px_rgba(14,125,228,0.2)] transition-all duration-300"
             style={{ borderColor: category.color }}>
          <div className="text-center">
            <div className="text-base font-bold text-transparent bg-clip-text bg-gradient-to-br from-primary-blue to-blue-600">
              {value.toFixed(2)}
            </div>
            <div className="text-[10px] text-gray-500 leading-tight mt-0.5">
              kWh/m²·yr
            </div>
          </div>
          <div className="absolute -bottom-8 left-1/2 -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
            <div className="text-[10px] text-gray-500 flex items-center gap-1">
              <Info size={12} className="text-gray-400" />
              <span>Based on {gfa?.toLocaleString() || '0'} m² GFA</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}