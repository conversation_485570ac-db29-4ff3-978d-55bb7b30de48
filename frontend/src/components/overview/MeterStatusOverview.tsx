import React from 'react';
import { Alert<PERSON>riangle, <PERSON>R<PERSON>, Building2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { TEXT_STYLES, GRADIENT_STYLES, SHADOW_STYLES, BORDER_STYLES, ANIMATION_STYLES } from '../../lib/config/ui';
import { METER_STATUS_CONFIG } from '../../lib/config/meter-status';

interface AlertSection {
  title: string;
  icon: typeof AlertTriangle;
  description: string;
  alerts: {
    urgent: number;
    high: number;
    low: number;
    total: number;
    location?: string;
  };
  details?: {
    category: string;
    status: 'critical' | 'warning' | 'normal';
    value: string;
    threshold: string;
  }[];
  floorAlerts?: Array<{
    floor: number;
    type: 'anomaly' | 'disconnected';
  }>;
}

interface MeterStatusOverviewProps {
  sections: AlertSection[];
  onViewAll?: () => void;
}

export function MeterStatusOverview({ sections, onViewAll }: MeterStatusOverviewProps) {
  const stats = React.useMemo(() => ({
    total: 500,
    active: 470,
    disconnected: 20,
    warning: 10
  }), []);
  const navigate = useNavigate();
  
  const handleViewAll = () => {
    navigate('/meters');
  };

  return (
    <div className={`w-full p-2.5 ${GRADIENT_STYLES.card} ${BORDER_STYLES.card} ${SHADOW_STYLES.card} backdrop-blur-sm ${SHADOW_STYLES.hover.primary} ${ANIMATION_STYLES.transition} overflow-hidden`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className={`p-1 rounded-lg ${GRADIENT_STYLES.icon.container}`}>
            <Building2 size={14} className="text-primary-blue" />
          </div>
          <h3 className="text-[11px] font-medium text-gray-500">Building Meters</h3>
        </div>
        {(
          <button
            onClick={handleViewAll}
            className="flex items-center gap-1 text-[9px] text-gray-400 hover:text-gray-600 transition-colors group whitespace-nowrap"
          >
            View all meters
            <ArrowRight size={14} className="transition-transform group-hover:translate-x-1" />
          </button>
        )}
      </div>

      <div className="space-y-1.5">
        {sections.map((section) => (
          <React.Fragment key={section.title}>
            <div className="grid grid-cols-2 gap-2">
              {/* Online Meters */}
              <div className={`p-1.5 ${GRADIENT_STYLES.meter.online} ${BORDER_STYLES.card} ${ANIMATION_STYLES.hover.container.secondary} ${ANIMATION_STYLES.hover.scale.sm} ${ANIMATION_STYLES.transition}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-primary-blue animate-pulse shadow-[0_0_12px_rgba(14,126,228,0.3)]" />
                    <span className="text-[10px] font-medium text-gray-500">Online</span>
                  </div>
                  <span className="text-[11px] font-medium text-primary-blue">{stats.active}</span>
                </div>
              </div>

              {/* Offline Meters */}
              <div className={`p-1.5 ${GRADIENT_STYLES.meter.offline} ${BORDER_STYLES.card} ${ANIMATION_STYLES.hover.container.danger} ${ANIMATION_STYLES.hover.scale.sm} ${ANIMATION_STYLES.transition}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-red-500 animate-pulse shadow-[0_0_12px_rgba(239,68,68,0.3)]" />
                    <span className="text-[10px] font-medium text-gray-500">Offline</span>
                  </div>
                  <span className="text-[11px] font-medium text-primary-blue">{stats.disconnected}</span>
                </div>
              </div>
            </div>
            <div className="text-[9px] text-gray-400 text-right">
              {stats.total} total meters across all towers
            </div>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}