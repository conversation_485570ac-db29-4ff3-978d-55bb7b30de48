/* Print-specific styles for training slides */
@media print {
  /* Force color printing */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }

  /* Ensure backgrounds are printed */
  body {
    background-color: #f3f4f6 !important;
  }

  /* Hide navigation elements */
  .no-print {
    display: none !important;
  }

  /* Preserve slide backgrounds */
  .slide-container {
    background-color: white !important;
    box-shadow: none !important;
    page-break-inside: avoid !important;
    margin-bottom: 20px !important;
  }

  /* Preserve gradient backgrounds */
  .bg-gradient-to-br {
    background-image: var(--gradient-fallback) !important;
  }

  /* Specific color preservation */
  .bg-blue-50 {
    background-color: #eff6ff !important;
  }
  
  .bg-blue-100 {
    background-color: #dbeafe !important;
  }
  
  .bg-blue-600 {
    background-color: #2563eb !important;
  }
  
  .bg-green-50 {
    background-color: #f0fdf4 !important;
  }
  
  .bg-green-100 {
    background-color: #dcfce7 !important;
  }
  
  .bg-purple-50 {
    background-color: #faf5ff !important;
  }
  
  .bg-purple-100 {
    background-color: #f3e8ff !important;
  }

  /* Text colors */
  .text-white {
    color: #ffffff !important;
  }
  
  .text-blue-600 {
    color: #2563eb !important;
  }
  
  .text-green-600 {
    color: #16a34a !important;
  }
  
  .text-purple-600 {
    color: #9333ea !important;
  }

  /* Page setup */
  @page {
    size: A4 landscape;
    margin: 10mm;
  }

  /* Ensure slide content fits on page */
  [class*="aspect-"] {
    aspect-ratio: auto !important;
    height: auto !important;
    max-height: 180mm !important;
  }
}