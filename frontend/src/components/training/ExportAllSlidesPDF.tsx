import React from 'react';
import { Document, Page, Text, View, StyleSheet, PDFDownloadLink, Font } from '@react-pdf/renderer';
import { FileDown } from 'lucide-react';

// Import all slide modules
import { quickStartSlides } from './slides/QuickStartSlides';
import { dashboardSlides } from './slides/DashboardSlides';
import { analyticsSlides } from './slides/AnalyticsSlides';
import { metersSlides } from './slides/MetersSlides';
import { alarmsSlides } from './slides/AlarmsSlides';
import { comparisonSlides } from './slides/ComparisonSlides';
import { diagramSlides } from './slides/DiagramSlides';
import { settingsSlides } from './slides/SettingsSlides';

// PDF Styles
const styles = StyleSheet.create({
  page: {
    padding: 40,
    backgroundColor: '#ffffff',
    fontFamily: 'Helvetica',
  },
  coverPage: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1e40af',
  },
  coverTitle: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 20,
    textAlign: 'center',
  },
  coverSubtitle: {
    fontSize: 24,
    color: '#dbeafe',
    marginBottom: 40,
    textAlign: 'center',
  },
  coverDate: {
    fontSize: 16,
    color: '#93bbfc',
    position: 'absolute',
    bottom: 60,
  },
  tocPage: {
    padding: 40,
  },
  tocTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 40,
    color: '#1e40af',
    textAlign: 'center',
  },
  tocItem: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'center',
  },
  tocNumber: {
    width: 40,
    fontSize: 16,
    color: '#4b5563',
  },
  tocText: {
    flex: 1,
    fontSize: 16,
    color: '#1f2937',
  },
  tocSlideCount: {
    fontSize: 14,
    color: '#6b7280',
    marginLeft: 10,
  },
  moduleHeader: {
    backgroundColor: '#1e40af',
    margin: -40,
    marginBottom: 30,
    padding: 40,
    paddingBottom: 20,
  },
  moduleTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 10,
  },
  moduleSubtitle: {
    fontSize: 18,
    color: '#dbeafe',
  },
  slideContainer: {
    marginBottom: 40,
    borderBottom: '1 solid #e5e7eb',
    paddingBottom: 30,
  },
  slideHeader: {
    backgroundColor: '#f3f4f6',
    margin: -20,
    marginBottom: 20,
    padding: 20,
    borderRadius: 8,
  },
  slideNumber: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 5,
  },
  slideTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#374151',
    marginTop: 20,
    marginBottom: 10,
  },
  content: {
    fontSize: 12,
    lineHeight: 1.8,
    color: '#374151',
    marginBottom: 10,
  },
  bulletContainer: {
    marginLeft: 20,
    marginBottom: 15,
  },
  bulletItem: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  bullet: {
    width: 20,
    fontSize: 12,
    color: '#4b5563',
  },
  bulletText: {
    flex: 1,
    fontSize: 12,
    lineHeight: 1.6,
    color: '#374151',
  },
  pageNumber: {
    position: 'absolute',
    bottom: 30,
    right: 40,
    fontSize: 10,
    color: '#9ca3af',
  },
  infoBox: {
    backgroundColor: '#eff6ff',
    border: '2 solid #3b82f6',
    borderRadius: 8,
    padding: 15,
    marginVertical: 15,
  },
  warningBox: {
    backgroundColor: '#fef3c7',
    border: '2 solid #f59e0b',
    borderRadius: 8,
    padding: 15,
    marginVertical: 15,
  },
  tipBox: {
    backgroundColor: '#d1fae5',
    border: '2 solid #10b981',
    borderRadius: 8,
    padding: 15,
    marginVertical: 15,
  },
  keyFeature: {
    backgroundColor: '#f9fafb',
    border: '1 solid #e5e7eb',
    borderRadius: 8,
    padding: 15,
    marginVertical: 10,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 40,
    right: 40,
    borderTop: '1 solid #e5e7eb',
    paddingTop: 10,
    fontSize: 10,
    color: '#6b7280',
    textAlign: 'center',
  },
});

// Training modules with full content
const trainingModules = [
  {
    id: 'quick-start',
    title: 'Quick Start Guide',
    slides: quickStartSlides,
    content: [
      {
        title: 'Welcome to Energy Management System',
        sections: [
          {
            type: 'overview',
            content: 'The Energy Management System (EMS) provides comprehensive monitoring and analysis of electricity consumption across your facilities. This guide will help you get started quickly.'
          },
          {
            type: 'bullets',
            title: 'What You\'ll Learn',
            items: [
              'Navigate the main dashboard',
              'View real-time power consumption',
              'Access key features and reports',
              'Understand the system layout',
              'Get help when needed'
            ]
          }
        ]
      },
      {
        title: 'System Navigation',
        sections: [
          {
            type: 'bullets',
            title: 'Main Menu Items',
            items: [
              'Dashboard - Real-time overview of all systems',
              'Analytics - Detailed consumption analysis and trends',
              'Meters - Individual meter monitoring and management',
              'Meter Diagram - Visual power distribution hierarchy',
              'Compare - Side-by-side meter performance analysis',
              'Alarms - Alert configuration and history',
              'Reports - Generate and download reports',
              'Settings - System configuration and user management'
            ]
          }
        ]
      },
      {
        title: 'First Steps',
        sections: [
          {
            type: 'numbered',
            title: 'Getting Started Checklist',
            items: [
              'Log in with your credentials',
              'Review the dashboard for current status',
              'Check for any active alarms',
              'Explore the meter hierarchy in Meter Diagram',
              'Generate your first report'
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'dashboard',
    title: 'Dashboard Overview',
    slides: dashboardSlides,
    content: [
      {
        title: 'Dashboard Features',
        sections: [
          {
            type: 'overview',
            content: 'The dashboard provides a comprehensive real-time view of your facility\'s energy consumption, allowing you to monitor all critical metrics at a glance.'
          },
          {
            type: 'bullets',
            title: 'Key Metrics Displayed',
            items: [
              'Current Power Demand (kW)',
              'Today\'s Total Energy Consumption (kWh)',
              'Active Alarms and Notifications',
              'System Status by Building/Tower',
              'Monthly Consumption Trends',
              'Peak Demand Indicators'
            ]
          }
        ]
      },
      {
        title: 'Building Overview',
        sections: [
          {
            type: 'bullets',
            title: 'Building Cards Show',
            items: [
              'Real-time power consumption per building',
              'Number of active meters',
              'Current alarm status',
              'Quick access to building details',
              'Percentage of total consumption'
            ]
          }
        ]
      },
      {
        title: 'Interactive Features',
        sections: [
          {
            type: 'bullets',
            title: 'Dashboard Interactions',
            items: [
              'Click on building cards for detailed view',
              'Hover over charts for specific values',
              'Use time range selector for historical data',
              'Export dashboard snapshot as PDF',
              'Configure auto-refresh intervals'
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'analytics',
    title: 'Analytics & Reporting',
    slides: analyticsSlides,
    content: [
      {
        title: 'Analytics Overview',
        sections: [
          {
            type: 'overview',
            content: 'The Analytics module provides deep insights into your energy consumption patterns, helping you identify opportunities for optimization and cost savings.'
          },
          {
            type: 'bullets',
            title: 'Analysis Types',
            items: [
              'Consumption Analysis - Track usage over time',
              'Comparison Analysis - Compare multiple meters',
              'Performance Metrics - Efficiency indicators',
              'Cost Analysis - Energy cost breakdown',
              'Trend Analysis - Identify patterns and anomalies'
            ]
          }
        ]
      },
      {
        title: 'Time-based Analysis',
        sections: [
          {
            type: 'bullets',
            title: 'Available Time Ranges',
            items: [
              'Real-time (Last 5 minutes)',
              'Daily (24-hour view)',
              'Weekly (7-day comparison)',
              'Monthly (30-day trends)',
              'Yearly (12-month overview)',
              'Custom date ranges'
            ]
          }
        ]
      },
      {
        title: 'Report Generation',
        sections: [
          {
            type: 'bullets',
            title: 'Export Options',
            items: [
              'PDF Reports - Formatted for printing',
              'CSV Export - Raw data for analysis',
              'Email Delivery - Automated distribution',
              'Custom Templates - Branded reports'
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'meters',
    title: 'Meter Management',
    slides: metersSlides,
    content: [
      {
        title: 'Meter Overview',
        sections: [
          {
            type: 'overview',
            content: 'The Meters module allows you to monitor and manage individual meters throughout your facility, providing detailed insights into each measurement point.'
          },
          {
            type: 'bullets',
            title: 'Meter Information',
            items: [
              'Real-time power readings',
              'Current, voltage, and power factor',
              'Historical consumption data',
              'Alarm thresholds and status',
              'Meter location and hierarchy',
              'Calibration and maintenance records'
            ]
          }
        ]
      },
      {
        title: 'Meter Types',
        sections: [
          {
            type: 'bullets',
            title: 'Supported Meter Categories',
            items: [
              'Main Meters - Primary facility feeds',
              'Sub-meters - Building or floor level',
              'Tenant Meters - Individual tenant monitoring',
              'Equipment Meters - Specific equipment monitoring',
              'Virtual Meters - Calculated from multiple sources'
            ]
          }
        ]
      },
      {
        title: 'Live Monitoring',
        sections: [
          {
            type: 'bullets',
            title: 'Real-time Features',
            items: [
              'Live power demand graphs',
              'Instantaneous readings update',
              'Alarm status indicators',
              'Power quality metrics',
              'Communication status'
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'alarms',
    title: 'Alarms & Notifications',
    slides: alarmsSlides,
    content: [
      {
        title: 'Alarm System Overview',
        sections: [
          {
            type: 'overview',
            content: 'The alarm system helps you proactively manage your facility by alerting you to abnormal conditions, equipment issues, and consumption anomalies.'
          },
          {
            type: 'bullets',
            title: 'Alarm Types',
            items: [
              'High Consumption - Exceeds threshold',
              'Low Power Factor - Below optimal levels',
              'Communication Loss - Meter offline',
              'Voltage Anomalies - Outside normal range',
              'Demand Peaks - Approaching limits',
              'Equipment Faults - Hardware issues'
            ]
          }
        ]
      },
      {
        title: 'Alarm Configuration',
        sections: [
          {
            type: 'bullets',
            title: 'Configurable Parameters',
            items: [
              'Threshold values for each metric',
              'Severity levels (Critical, Warning, Info)',
              'Notification delays to prevent false alarms',
              'Escalation rules for unacknowledged alarms',
              'Email recipients by alarm type',
              'Active hours for notifications'
            ]
          }
        ]
      },
      {
        title: 'Notification Management',
        sections: [
          {
            type: 'bullets',
            title: 'Notification Options',
            items: [
              'Email alerts with detailed information',
              'In-app notifications',
              'SMS for critical alarms (optional)',
              'Daily summary emails',
              'Alarm acknowledgment tracking'
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'comparison',
    title: 'Meter Comparison',
    slides: comparisonSlides,
    content: [
      {
        title: 'Comparison Features',
        sections: [
          {
            type: 'overview',
            content: 'The Comparison module enables side-by-side analysis of multiple meters, helping identify performance differences and optimization opportunities.'
          },
          {
            type: 'bullets',
            title: 'Comparison Capabilities',
            items: [
              'Compare up to 5 meters simultaneously',
              'Multiple metrics comparison',
              'Time-synchronized data',
              'Percentage difference calculations',
              'Trend comparison over time'
            ]
          }
        ]
      },
      {
        title: 'Analysis Tools',
        sections: [
          {
            type: 'bullets',
            title: 'Available Metrics',
            items: [
              'Power consumption (kW)',
              'Energy usage (kWh)',
              'Power factor comparison',
              'Peak demand timing',
              'Cost per kWh'
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'diagram',
    title: 'Electricity Meter Diagram',
    slides: diagramSlides,
    content: [
      {
        title: 'Power Distribution Visualization',
        sections: [
          {
            type: 'overview',
            content: 'The Meter Diagram provides a visual representation of your facility\'s power distribution hierarchy, making it easy to understand power flow and relationships.'
          },
          {
            type: 'bullets',
            title: 'Diagram Features',
            items: [
              'Hierarchical meter relationships',
              'Real-time power flow indicators',
              'Color-coded status indicators',
              'Interactive meter selection',
              'Zoom and pan capabilities',
              'Export diagram as image'
            ]
          }
        ]
      },
      {
        title: 'Interactive Elements',
        sections: [
          {
            type: 'bullets',
            title: 'User Interactions',
            items: [
              'Click meters for detailed information',
              'Hover for quick stats',
              'Drag to rearrange layout',
              'Double-click to focus on branch',
              'Right-click for context menu'
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'settings',
    title: 'System Settings',
    slides: settingsSlides,
    content: [
      {
        title: 'Settings Overview',
        sections: [
          {
            type: 'overview',
            content: 'The Settings module allows administrators to configure system parameters, manage users, and customize the platform to meet specific needs.'
          },
          {
            type: 'bullets',
            title: 'Configuration Areas',
            items: [
              'User Management - Add/edit users and roles',
              'Meter Configuration - Set thresholds and parameters',
              'Email Settings - Configure notification delivery',
              'System Preferences - Customize display options',
              'Data Retention - Set data storage policies',
              'API Access - Manage integration tokens'
            ]
          }
        ]
      },
      {
        title: 'User Management',
        sections: [
          {
            type: 'bullets',
            title: 'User Roles',
            items: [
              'Administrator - Full system access',
              'Facility Manager - Operational access',
              'Energy Analyst - Read and report access',
              'Technician - Meter management access',
              'Viewer - Read-only access'
            ]
          }
        ]
      },
      {
        title: 'Best Practices',
        sections: [
          {
            type: 'bullets',
            title: 'Security Recommendations',
            items: [
              'Use strong passwords for all accounts',
              'Review user access quarterly',
              'Enable email notifications for critical alarms',
              'Regular backup of configuration',
              'Document custom settings'
            ]
          }
        ]
      }
    ]
  }
];

// PDF Document Component
const TrainingManualPDF = () => {
  let pageCounter = 1;

  return (
    <Document>
      {/* Cover Page */}
      <Page size="A4" orientation="landscape" style={styles.page}>
        <View style={styles.coverPage}>
          <Text style={styles.coverTitle}>Energy Management System</Text>
          <Text style={styles.coverSubtitle}>Complete Training Manual</Text>
          <Text style={styles.coverSubtitle}>
            {trainingModules.reduce((sum, m) => sum + m.slides.length, 0)} Training Slides
          </Text>
          <Text style={styles.coverDate}>Generated on {new Date().toLocaleDateString()}</Text>
        </View>
      </Page>

      {/* Table of Contents */}
      <Page size="A4" orientation="landscape" style={styles.tocPage}>
        <Text style={styles.tocTitle}>Table of Contents</Text>
        <View>
          {trainingModules.map((module, index) => (
            <View key={module.id} style={styles.tocItem}>
              <Text style={styles.tocNumber}>{index + 1}.</Text>
              <Text style={styles.tocText}>{module.title}</Text>
              <Text style={styles.tocSlideCount}>({module.slides.length} slides)</Text>
            </View>
          ))}
        </View>
        <Text style={styles.pageNumber}>Page {pageCounter++}</Text>
      </Page>

      {/* Training Modules */}
      {trainingModules.map((module) => (
        <React.Fragment key={module.id}>
          {/* Module Title Page */}
          <Page size="A4" orientation="landscape" style={styles.page} wrap>
            <View style={styles.moduleHeader}>
              <Text style={styles.moduleTitle}>{module.title}</Text>
              <Text style={styles.moduleSubtitle}>
                This module contains {module.slides.length} slides
              </Text>
            </View>

            {/* Module Content */}
            {module.content.map((slideContent, slideIndex) => (
              <View key={slideIndex} style={styles.slideContainer}>
                <View style={styles.slideHeader}>
                  <Text style={styles.slideNumber}>
                    Slide {slideIndex + 1} of {module.slides.length}
                  </Text>
                  <Text style={styles.slideTitle}>{slideContent.title}</Text>
                </View>

                {slideContent.sections.map((section, sectionIndex) => (
                  <View key={sectionIndex}>
                    {section.type === 'overview' && (
                      <Text style={styles.content}>{section.content}</Text>
                    )}
                    
                    {section.type === 'bullets' && (
                      <View>
                        {section.title && (
                          <Text style={styles.sectionTitle}>{section.title}</Text>
                        )}
                        <View style={styles.bulletContainer}>
                          {section.items?.map((item, itemIndex) => (
                            <View key={itemIndex} style={styles.bulletItem}>
                              <Text style={styles.bullet}>•</Text>
                              <Text style={styles.bulletText}>{item}</Text>
                            </View>
                          ))}
                        </View>
                      </View>
                    )}

                    {section.type === 'numbered' && (
                      <View>
                        {section.title && (
                          <Text style={styles.sectionTitle}>{section.title}</Text>
                        )}
                        <View style={styles.bulletContainer}>
                          {section.items?.map((item, itemIndex) => (
                            <View key={itemIndex} style={styles.bulletItem}>
                              <Text style={styles.bullet}>{itemIndex + 1}.</Text>
                              <Text style={styles.bulletText}>{item}</Text>
                            </View>
                          ))}
                        </View>
                      </View>
                    )}
                  </View>
                ))}
              </View>
            ))}

            <Text style={styles.pageNumber}>Page {pageCounter++}</Text>
          </Page>
        </React.Fragment>
      ))}

      {/* Support Information Page */}
      <Page size="A4" orientation="landscape" style={styles.page}>
        <Text style={styles.moduleTitle}>Support Information</Text>
        
        <View style={styles.infoBox}>
          <Text style={styles.slideTitle}>Technical Support</Text>
          <View style={styles.bulletContainer}>
            <View style={styles.bulletItem}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>Phone: ************</Text>
            </View>
            <View style={styles.bulletItem}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>Support Hours: Monday-Friday, 9:00 AM - 6:00 PM</Text>
            </View>
          </View>
        </View>

        <View style={styles.tipBox}>
          <Text style={styles.slideTitle}>Quick Tips</Text>
          <View style={styles.bulletContainer}>
            <View style={styles.bulletItem}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>Use keyboard shortcuts: Press ? for help</Text>
            </View>
            <View style={styles.bulletItem}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>Export data regularly for backup</Text>
            </View>
            <View style={styles.bulletItem}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>Set up email notifications for critical alarms</Text>
            </View>
            <View style={styles.bulletItem}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>Review meter thresholds monthly</Text>
            </View>
          </View>
        </View>

        <View style={styles.warningBox}>
          <Text style={styles.slideTitle}>Important Notes</Text>
          <View style={styles.bulletContainer}>
            <View style={styles.bulletItem}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>Always verify critical data before making decisions</Text>
            </View>
            <View style={styles.bulletItem}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>Keep your login credentials secure</Text>
            </View>
            <View style={styles.bulletItem}>
              <Text style={styles.bullet}>•</Text>
              <Text style={styles.bulletText}>Report any system anomalies to support</Text>
            </View>
          </View>
        </View>

        <Text style={styles.footer}>
          © 2024 Energy Management System v1.0
        </Text>
      </Page>
    </Document>
  );
};

// Export Button Component
export const ExportAllSlidesPDFButton = () => {
  const [isHovered, setIsHovered] = React.useState(false);
  
  return (
    <PDFDownloadLink
      document={<TrainingManualPDF />}
      fileName={`EMS_Training_Manual_${new Date().toISOString().split('T')[0]}.pdf`}
      className="inline-flex items-center gap-2 px-4 py-2 rounded-lg transition-all"
      style={{ 
        backgroundColor: isHovered ? '#1d4ed8' : '#2563eb', 
        color: '#ffffff',
        cursor: 'pointer'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {({ blob, url, loading, error }) => (
        <>
          <FileDown size={20} />
          {loading ? 'Generating PDF...' : 'Export Training Manual PDF'}
        </>
      )}
    </PDFDownloadLink>
  );
};