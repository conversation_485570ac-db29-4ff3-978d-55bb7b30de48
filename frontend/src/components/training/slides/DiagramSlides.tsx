import React from 'react';
import { Zap, Building, Activity, Network, Info, MousePointer, Calculator } from 'lucide-react';
import { SlidePageIndicator } from '../SlidePageIndicator';

const Slide1 = () => (
  <div className=" relative h-full bg-gradient-to-br from-yellow-50 to-white p-16 flex flex-col justify-center">
    <div className="text-center">
      <h1 className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-600 to-orange-600 mb-8">
        Electricity Meter Diagram
      </h1>
      <p className="text-2xl text-gray-600 mb-12">Visualize power distribution across your building</p>
      <div className="flex justify-center gap-8">
        <div className="text-center">
          <div className="bg-yellow-100 rounded-full p-6 inline-flex mb-4">
            <Zap size={48} className="text-yellow-600" />
          </div>
          <p className="text-lg font-medium">Power Flow</p>
        </div>
        <div className="text-center">
          <div className="bg-orange-100 rounded-full p-6 inline-flex mb-4">
            <Network size={48} className="text-orange-600" />
          </div>
          <p className="text-lg font-medium">Interactive Diagram</p>
        </div>
        <div className="text-center">
          <div className="bg-red-100 rounded-full p-6 inline-flex mb-4">
            <Activity size={48} className="text-red-600" />
          </div>
          <p className="text-lg font-medium">Real-time Status</p>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meter Diagram Page" feature="Introduction" />
  </div>
);

const Slide2 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Understanding the Power Flow</h2>
    <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8">
      <div className="flex items-center justify-center">
        <div className="relative">
          {/* Main Meter */}
          <div className="rounded-lg p-6 text-center shadow-lg" style={{ backgroundColor: '#1e40af', color: '#ffffff' }}>
            <Zap size={32} className="mx-auto mb-2" />
            <h3 className="text-xl font-bold" style={{ color: '#ffffff' }}>Main Meter</h3>
            <p className="text-3xl font-bold mt-2" style={{ color: '#ffffff' }}>2,847 kW</p>
          </div>
          
          {/* Distribution Lines */}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-1 h-16 bg-blue-400"></div>
          
          {/* Building Distribution */}
          <div className="flex gap-8 mt-24">
            <div className="rounded-lg p-4 text-center" style={{ backgroundColor: '#059669', color: '#ffffff' }}>
              <Building size={24} className="mx-auto mb-1" />
              <p className="font-medium">Tower A</p>
              <p className="text-xl font-bold">847 kW</p>
            </div>
            <div className="rounded-lg p-4 text-center" style={{ backgroundColor: '#059669', color: '#ffffff' }}>
              <Building size={24} className="mx-auto mb-1" />
              <p className="font-medium">Tower B</p>
              <p className="text-xl font-bold">925 kW</p>
            </div>
            <div className="rounded-lg p-4 text-center" style={{ backgroundColor: '#059669', color: '#ffffff' }}>
              <Building size={24} className="mx-auto mb-1" />
              <p className="font-medium">Tower C</p>
              <p className="text-xl font-bold">756 kW</p>
            </div>
            <div className="rounded-lg p-4 text-center" style={{ backgroundColor: '#7c3aed', color: '#ffffff' }}>
              <Zap size={24} className="mx-auto mb-1" />
              <p className="font-medium">Chiller</p>
              <p className="text-xl font-bold">850 kW</p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-8 text-center">
        <p className="text-sm text-gray-600">Power flows from the main meter to individual building sections</p>
      </div>
    </div>
    <SlidePageIndicator page="Meter Diagram Page" feature="Diagram Overview" />
  </div>
);

const Slide3 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Interactive Features</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <MousePointer className="text-blue-600" />
          Click to Navigate
        </h3>
        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Tower Nodes</h4>
            <p className="text-sm text-gray-600">Click any tower to view its meter details</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Equipment Nodes</h4>
            <p className="text-sm text-gray-600">Click chillers or equipment for specifications</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Connection Lines</h4>
            <p className="text-sm text-gray-600">Animated lines show active power flow</p>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <Info className="text-green-600" />
          Status Indicators
        </h3>
        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4 flex items-center gap-4">
            <div className="w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
            <div>
              <p className="font-medium">Green - Active</p>
              <p className="text-sm text-gray-600">Normal operation, data flowing</p>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 flex items-center gap-4">
            <div className="w-4 h-4 bg-yellow-500 rounded-full animate-pulse"></div>
            <div>
              <p className="font-medium">Yellow - Warning</p>
              <p className="text-sm text-gray-600">Approaching threshold limits</p>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4 flex items-center gap-4">
            <div className="w-4 h-4 bg-red-500 rounded-full"></div>
            <div>
              <p className="font-medium">Red - Offline</p>
              <p className="text-sm text-gray-600">No data or communication lost</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meter Diagram Page" feature="Navigation" />
  </div>
);

const Slide4 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">System Overview Panel</h2>
    <div className="grid grid-cols-3 gap-8">
      <div className="col-span-2">
        <div className="bg-gray-50 rounded-xl p-8">
          <h3 className="text-2xl font-semibold mb-6">Power Flow Diagram</h3>
          <div className="bg-white rounded-lg p-6 h-96 flex items-center justify-center">
            <div className="text-center">
              <Network size={80} className="text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">Interactive diagram displays here</p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6">
          <h3 className="text-lg font-semibold mb-4">Power Summary</h3>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-600">Total System Load</p>
              <p className="text-2xl font-bold">2,847 kW</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Meters</p>
              <p className="text-2xl font-bold">500</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-6">
          <h3 className="text-lg font-semibold mb-4">System Status</h3>
          <div className="grid grid-cols-3 gap-2 text-center">
            <div className="bg-green-50 rounded-lg p-3">
              <p className="text-2xl font-bold text-green-700">85%</p>
              <p className="text-xs text-green-600">Active</p>
            </div>
            <div className="bg-yellow-50 rounded-lg p-3">
              <p className="text-2xl font-bold text-yellow-700">10%</p>
              <p className="text-xs text-yellow-600">Warning</p>
            </div>
            <div className="bg-red-50 rounded-lg p-3">
              <p className="text-2xl font-bold text-red-700">5%</p>
              <p className="text-xs text-red-600">Offline</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meter Diagram Page" feature="Real-time Updates" />
  </div>
);

const Slide5 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Virtual Meters in Diagram</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <Calculator className="text-purple-600" />
          Data Center & Others
        </h3>
        <div className="bg-white rounded-lg p-6">
          <div className="mb-4">
            <p className="text-sm text-gray-600 mb-2">Calculation Formula:</p>
            <div className="bg-gray-50 rounded-lg p-3 text-sm">
              Total - (Tower A + Tower B + Tower C + Chiller + Tenant)
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Power</span>
              <span className="font-medium">2,847 kW</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Sum of Meters</span>
              <span className="font-medium">2,619 kW</span>
            </div>
            <div className="border-t pt-3">
              <div className="flex justify-between">
                <span className="font-medium">Data Center</span>
                <span className="font-bold text-purple-600">228 kW</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6">Why Virtual Meters?</h3>
        <div className="space-y-3">
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Cost Allocation</h4>
            <p className="text-sm text-gray-600">Calculate consumption for areas without physical meters</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Complete Picture</h4>
            <p className="text-sm text-gray-600">Account for all energy usage in the building</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Validation</h4>
            <p className="text-sm text-gray-600">Verify that all power is accounted for</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meter Diagram Page" feature="Meter Details" />
  </div>
);

const Slide6 = () => (
  <div className=" relative h-full bg-gradient-to-br from-blue-50 to-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Best Practices</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="space-y-6">
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">👁️</span>
            Regular Monitoring
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Check diagram daily for status changes</li>
            <li>• Investigate any red (offline) meters</li>
            <li>• Monitor yellow warnings proactively</li>
            <li>• Verify virtual meter calculations</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">📊</span>
            Analysis Tips
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Compare tower consumptions</li>
            <li>• Track power distribution patterns</li>
            <li>• Identify unusual consumption</li>
            <li>• Use for capacity planning</li>
          </ul>
        </div>
      </div>
      
      <div className="space-y-6">
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">🔧</span>
            Troubleshooting
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Red meters need immediate attention</li>
            <li>• Check communication paths</li>
            <li>• Verify meter configurations</li>
            <li>• Contact support if issues persist</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">💡</span>
            Quick Actions
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Click nodes to see details</li>
            <li>• Use side panel for metrics</li>
            <li>• Export diagram for reports</li>
            <li>• Share insights with team</li>
          </ul>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meter Diagram Page" feature="Power Flow" />
  </div>
);

const Slide7 = () => (
  <div 
    className="h-full p-16 flex flex-col justify-center"
    style={{ 
      background: 'linear-gradient(to bottom right, #d97706, #ea580c)',
      color: '#ffffff'
    }}
  >
    <div className="text-center">
      <div 
        className="rounded-full w-32 h-32 mx-auto mb-8 flex items-center justify-center"
        style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', backdropFilter: 'blur(12px)' }}
      >
        <Zap size={64} style={{ color: '#ffffff' }} />
      </div>
      <h1 className="text-5xl font-bold mb-6" style={{ color: '#ffffff' }}>Meter Diagram Complete!</h1>
      <p className="text-2xl mb-12" style={{ color: '#fef3c7' }}>You can now visualize your building's power distribution</p>
      
      <div 
        className="rounded-xl p-8 max-w-3xl mx-auto"
        style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', backdropFilter: 'blur(12px)' }}
      >
        <h3 className="text-2xl font-semibold mb-6" style={{ color: '#ffffff' }}>Key Takeaways</h3>
        <div className="grid grid-cols-2 gap-6 text-left">
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">1</span>
            </div>
            <p style={{ color: '#ffffff' }}>Understand power flow from main to individual meters</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">2</span>
            </div>
            <p style={{ color: '#ffffff' }}>Click nodes to navigate to detailed views</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">3</span>
            </div>
            <p style={{ color: '#ffffff' }}>Monitor status indicators for system health</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">4</span>
            </div>
            <p style={{ color: '#ffffff' }}>Use virtual meters to track unmetered areas</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meter Diagram Page" feature="Alert Indicators" />
  </div>
);

export const diagramSlides = [
  Slide1, Slide2, Slide3, Slide4, Slide5, Slide6, Slide7
];