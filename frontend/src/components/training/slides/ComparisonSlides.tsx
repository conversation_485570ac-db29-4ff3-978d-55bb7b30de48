import React from 'react';
import { Git<PERSON>ompare, BarChart3, TrendingUp, Calendar, Filter, Download } from 'lucide-react';
import { SlidePageIndicator } from '../SlidePageIndicator';

const Slide1 = () => (
  <div className=" relative h-full bg-gradient-to-br from-purple-50 to-white p-16 flex flex-col justify-center">
    <div className="text-center">
      <h1 className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-pink-600 mb-8">
        Meter Comparison
      </h1>
      <p className="text-2xl text-gray-600 mb-12">Compare any of 520 meters across Tower A/B/C, Podium, and Car Park</p>
      <div className="flex justify-center gap-8">
        <div className="text-center">
          <div className="bg-purple-100 rounded-full p-6 inline-flex mb-4">
            <GitCompare size={48} className="text-purple-600" />
          </div>
          <p className="text-lg font-medium">Side-by-side Analysis</p>
        </div>
        <div className="text-center">
          <div className="bg-pink-100 rounded-full p-6 inline-flex mb-4">
            <BarChart3 size={48} className="text-pink-600" />
          </div>
          <p className="text-lg font-medium">Visual Comparisons</p>
        </div>
        <div className="text-center">
          <div className="bg-indigo-100 rounded-full p-6 inline-flex mb-4">
            <TrendingUp size={48} className="text-indigo-600" />
          </div>
          <p className="text-lg font-medium">Performance Insights</p>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Compare Page" feature="Introduction" />
  </div>
);

const Slide2 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Selecting Meters to Compare</h2>
    <div className="bg-gray-50 rounded-xl p-8">
      <div className="grid grid-cols-2 gap-8">
        <div>
          <h3 className="text-2xl font-semibold mb-6">Meter Selection</h3>
          <div className="bg-white rounded-lg p-6">
            <div className="space-y-4">
              <div className="p-3 bg-purple-50 rounded-lg flex items-center justify-between">
                <label className="flex items-center gap-3">
                  <input type="checkbox" checked disabled className="w-4 h-4" />
                  <div>
                    <p className="font-medium">Tower A - Main</p>
                    <p className="text-sm text-gray-600">Sub Meter • 847.5 kW</p>
                  </div>
                </label>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg flex items-center justify-between">
                <label className="flex items-center gap-3">
                  <input type="checkbox" checked disabled className="w-4 h-4" />
                  <div>
                    <p className="font-medium">Tower B - Main</p>
                    <p className="text-sm text-gray-600">Sub Meter • 925.3 kW</p>
                  </div>
                </label>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg flex items-center justify-between opacity-50">
                <label className="flex items-center gap-3">
                  <input type="checkbox" disabled className="w-4 h-4" />
                  <div>
                    <p className="font-medium">Tower C - Main</p>
                    <p className="text-sm text-gray-600">Sub Meter • 756.8 kW</p>
                  </div>
                </label>
              </div>
            </div>
            <p className="text-sm text-gray-500 mt-4">Select 2-5 meters to compare</p>
          </div>
        </div>
        
        <div>
          <h3 className="text-2xl font-semibold mb-6">Comparison Tips</h3>
          <div className="space-y-3">
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-medium text-blue-700 mb-2">Similar Types</h4>
              <p className="text-sm text-gray-600">Compare meters of the same type for meaningful insights</p>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <h4 className="font-medium text-green-700 mb-2">Same Building</h4>
              <p className="text-sm text-gray-600">Compare floors within the same tower</p>
            </div>
            <div className="bg-yellow-50 rounded-lg p-4">
              <h4 className="font-medium text-yellow-700 mb-2">Equipment Groups</h4>
              <p className="text-sm text-gray-600">Compare similar equipment like chillers</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Compare Page" feature="Comparison Types" />
  </div>
);

const Slide3 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Time Period Selection</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <Calendar className="text-indigo-600" />
          Date Range Options
        </h3>
        <div className="space-y-3">
          <button className="w-full p-4 bg-white rounded-lg text-left hover:bg-indigo-50 transition-colors">
            <p className="font-medium">Today</p>
            <p className="text-sm text-gray-600">Compare hourly patterns</p>
          </button>
          <button className="w-full p-4 bg-indigo-600 text-white rounded-lg text-left">
            <p className="font-medium">Last 7 Days</p>
            <p className="text-sm text-indigo-100">Daily comparison view</p>
          </button>
          <button className="w-full p-4 bg-white rounded-lg text-left hover:bg-indigo-50 transition-colors">
            <p className="font-medium">Last Month</p>
            <p className="text-sm text-gray-600">Weekly patterns</p>
          </button>
          <button className="w-full p-4 bg-white rounded-lg text-left hover:bg-indigo-50 transition-colors">
            <p className="font-medium">Custom Range</p>
            <p className="text-sm text-gray-600">Select specific dates</p>
          </button>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6">Comparison Views</h3>
        <div className="bg-white rounded-lg p-6 space-y-4">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <p className="font-medium">Power Consumption</p>
              <p className="text-sm text-gray-600">kW over time</p>
            </div>
            <span className="text-purple-600">Default</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <p className="font-medium">Energy Usage</p>
              <p className="text-sm text-gray-600">kWh totals</p>
            </div>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <p className="font-medium">Peak Demand</p>
              <p className="text-sm text-gray-600">Maximum values</p>
            </div>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <p className="font-medium">Power Factor</p>
              <p className="text-sm text-gray-600">Efficiency metrics</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Compare Page" feature="Period Selection" />
  </div>
);

const Slide4 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Comparison Chart View</h2>
    <div className="bg-gray-50 rounded-xl p-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <span className="font-medium">Comparing:</span>
          <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-lg">Tower A</span>
          <span className="text-gray-400">vs</span>
          <span className="px-3 py-1 bg-pink-100 text-pink-700 rounded-lg">Tower B</span>
        </div>
        <div className="flex gap-2">
          <button className="px-4 py-2 bg-white border rounded-lg">Line Chart</button>
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg">Bar Chart</button>
        </div>
      </div>
      
      <div className="bg-white rounded-lg p-6">
        <div className="h-64 bg-gradient-to-r from-purple-50 via-pink-50 to-indigo-50 rounded-lg flex items-center justify-center relative">
          <div className="absolute inset-0 p-4">
            <div className="h-full flex items-end justify-around gap-2">
              <div className="w-full bg-purple-400 rounded-t" style={{height: '75%'}}></div>
              <div className="w-full bg-pink-400 rounded-t" style={{height: '85%'}}></div>
              <div className="w-full bg-purple-400 rounded-t" style={{height: '70%'}}></div>
              <div className="w-full bg-pink-400 rounded-t" style={{height: '78%'}}></div>
              <div className="w-full bg-purple-400 rounded-t" style={{height: '82%'}}></div>
              <div className="w-full bg-pink-400 rounded-t" style={{height: '88%'}}></div>
              <div className="w-full bg-purple-400 rounded-t" style={{height: '80%'}}></div>
              <div className="w-full bg-pink-400 rounded-t" style={{height: '83%'}}></div>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mt-6">
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <p className="text-sm text-gray-600">Tower A Average</p>
            <p className="text-2xl font-bold text-purple-700">847.5 kW</p>
          </div>
          <div className="text-center p-3 bg-pink-50 rounded-lg">
            <p className="text-sm text-gray-600">Tower B Average</p>
            <p className="text-2xl font-bold text-pink-700">925.3 kW</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Compare Page" feature="Meter Selection" />
  </div>
);

const Slide5 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Analysis & Insights</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <TrendingUp className="text-green-600" />
          Key Findings
        </h3>
        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium text-green-700 mb-2">Tower B Uses 9.2% More Power</h4>
            <p className="text-sm text-gray-600">Higher occupancy and older equipment contribute to increased consumption</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium text-orange-700 mb-2">Peak Times Differ</h4>
            <p className="text-sm text-gray-600">Tower A peaks at 2 PM, Tower B at 3 PM</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium text-blue-700 mb-2">Weekend Patterns</h4>
            <p className="text-sm text-gray-600">Tower A reduces 45% on weekends vs Tower B's 38%</p>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6">Recommendations</h3>
        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4 border-l-4 border-yellow-500">
            <p className="font-medium mb-1">Investigate Tower B Equipment</p>
            <p className="text-sm text-gray-600">Schedule maintenance check for HVAC systems</p>
          </div>
          <div className="bg-white rounded-lg p-4 border-l-4 border-yellow-500">
            <p className="font-medium mb-1">Stagger Peak Loads</p>
            <p className="text-sm text-gray-600">Adjust schedules to balance power demand</p>
          </div>
          <div className="bg-white rounded-lg p-4 border-l-4 border-yellow-500">
            <p className="font-medium mb-1">Weekend Optimization</p>
            <p className="text-sm text-gray-600">Implement Tower A's efficiency practices in Tower B</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Compare Page" feature="Comparison View" />
  </div>
);

const Slide6 = () => (
  <div 
    className="h-full p-16 flex flex-col justify-center"
    style={{ 
      background: 'linear-gradient(to bottom right, #7c3aed, #db2777)',
      color: '#ffffff'
    }}
  >
    <div className="text-center">
      <div 
        className="rounded-full w-32 h-32 mx-auto mb-8 flex items-center justify-center"
        style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', backdropFilter: 'blur(12px)' }}
      >
        <GitCompare size={64} style={{ color: '#ffffff' }} />
      </div>
      <h1 className="text-5xl font-bold mb-6" style={{ color: '#ffffff' }}>Meter Comparison Complete!</h1>
      <p className="text-2xl mb-12" style={{ color: '#e9d5ff' }}>You can now compare and analyze meter performance</p>
      
      <div 
        className="rounded-xl p-8 max-w-3xl mx-auto"
        style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', backdropFilter: 'blur(12px)' }}
      >
        <h3 className="text-2xl font-semibold mb-6" style={{ color: '#ffffff' }}>Key Takeaways</h3>
        <div className="grid grid-cols-2 gap-6 text-left">
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">1</span>
            </div>
            <p style={{ color: '#ffffff' }}>Select similar meters for meaningful comparisons</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">2</span>
            </div>
            <p style={{ color: '#ffffff' }}>Choose appropriate time periods for analysis</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">3</span>
            </div>
            <p style={{ color: '#ffffff' }}>Identify patterns and anomalies</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">4</span>
            </div>
            <p style={{ color: '#ffffff' }}>Take action based on insights</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Compare Page" feature="Data Analysis" />
  </div>
);

export const comparisonSlides = [
  Slide1, Slide2, Slide3, Slide4, Slide5, Slide6
];