import React from 'react';
import { BarChart3, TrendingUp, Calendar, FileDown, Filter, Zap } from 'lucide-react';
import { SlidePageIndicator } from '../SlidePageIndicator';

const Slide1 = () => (
  <div className=" relative h-full bg-gradient-to-br from-purple-50 to-white p-16 flex flex-col justify-center relative">
    <div>
      <h1 className="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 mb-4 text-center">
        Analytics & Reporting
      </h1>
      <h2 className="text-3xl text-gray-700 mb-12 text-center">Master Your Energy Data Analysis</h2>
      
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h3 className="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
            <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold mr-3">
              <span className="text-sm">i</span>
            </div>
            Training Agenda
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-start">
              <span className="text-purple-600 mr-2 mt-1">1.</span>
              <p className="text-gray-700">Analytics module overview</p>
            </div>
            <div className="flex items-start">
              <span className="text-purple-600 mr-2 mt-1">2.</span>
              <p className="text-gray-700">Consumption analysis tools</p>
            </div>
            <div className="flex items-start">
              <span className="text-purple-600 mr-2 mt-1">3.</span>
              <p className="text-gray-700">Date range selection</p>
            </div>
            <div className="flex items-start">
              <span className="text-purple-600 mr-2 mt-1">4.</span>
              <p className="text-gray-700">System breakdown charts</p>
            </div>
            <div className="flex items-start">
              <span className="text-purple-600 mr-2 mt-1">5.</span>
              <p className="text-gray-700">Comparison features</p>
            </div>
            <div className="flex items-start">
              <span className="text-purple-600 mr-2 mt-1">6.</span>
              <p className="text-gray-700">Performance metrics</p>
            </div>
            <div className="flex items-start">
              <span className="text-purple-600 mr-2 mt-1">7.</span>
              <p className="text-gray-700">Export and reporting</p>
            </div>
            <div className="flex items-start">
              <span className="text-purple-600 mr-2 mt-1">8.</span>
              <p className="text-gray-700">Advanced analysis tips</p>
            </div>
          </div>
        </div>
        
        <div className="flex justify-center gap-6">
          <div className="w-20 h-20 bg-purple-100 rounded-xl flex items-center justify-center animate-pulse">
            <BarChart3 size={40} className="text-purple-600" />
          </div>
          <div className="w-20 h-20 bg-blue-100 rounded-xl flex items-center justify-center animate-pulse delay-75">
            <TrendingUp size={40} className="text-blue-600" />
          </div>
          <div className="w-20 h-20 bg-green-100 rounded-xl flex items-center justify-center animate-pulse delay-150">
            <Calendar size={40} className="text-green-600" />
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Introduction" />
    <SlidePageIndicator page="Analytics Page" feature="Introduction" />
  </div>
);

const Slide2 = () => (
  <div className=" relative h-full bg-white p-12 relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Analytics Module Overview</h2>
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-2 gap-8">
        <div className="space-y-6">
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-6">
            <h3 className="text-2xl font-semibold mb-4">Consumption Analysis</h3>
            <ul className="space-y-3 text-lg">
              <li className="flex items-start">
                <span className="text-purple-600 mr-3">•</span>
                Track energy usage patterns over time
              </li>
              <li className="flex items-start">
                <span className="text-purple-600 mr-3">•</span>
                Identify peak consumption periods
              </li>
              <li className="flex items-start">
                <span className="text-purple-600 mr-3">•</span>
                Monitor building-specific metrics
              </li>
              <li className="flex items-start">
                <span className="text-purple-600 mr-3">•</span>
                Analyze system breakdown
              </li>
            </ul>
          </div>
          
          <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-xl p-6">
            <h3 className="text-2xl font-semibold mb-4">Comparison Features</h3>
            <ul className="space-y-3 text-lg">
              <li className="flex items-start">
                <span className="text-blue-600 mr-3">•</span>
                Compare different time periods
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-3">•</span>
                Building vs building analysis
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-3">•</span>
                Simplified comparison view
              </li>
            </ul>
          </div>
        </div>
        
        <div className="bg-gray-100 rounded-xl p-8 flex items-center justify-center">
          <div className="bg-white rounded-lg shadow-lg p-6 w-full">
            <div className="h-48 bg-gradient-to-r from-blue-400 to-purple-400 rounded-lg mb-4"></div>
            <p className="text-center text-gray-600">Analytics Dashboard Interface</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Module Overview" />
  </div>
);

const Slide3 = () => (
  <div className=" relative h-full bg-gradient-to-br from-blue-50 to-white p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">View Selection Options</h2>
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-5 gap-4">
        {[
          { view: 'Daily', desc: '24-hour breakdown', icon: '📅', color: 'bg-blue-100 border-blue-300' },
          { view: 'Weekly', desc: '7-day trends', icon: '📊', color: 'bg-green-100 border-green-300' },
          { view: 'Monthly', desc: '30-day overview', icon: '📈', color: 'bg-purple-100 border-purple-300' },
          { view: 'Yearly', desc: '12-month analysis', icon: '📉', color: 'bg-orange-100 border-orange-300' },
          { view: 'Custom', desc: 'Date picker selection', icon: '🎯', color: 'bg-pink-100 border-pink-300' }
        ].map((item, idx) => (
          <div key={idx} className={`${item.color} border-2 rounded-xl p-6 text-center transform hover:scale-105 transition-transform cursor-pointer`}>
            <div className="text-4xl mb-4">{item.icon}</div>
            <h3 className="text-xl font-bold mb-2">{item.view}</h3>
            <p className="text-sm text-gray-600">{item.desc}</p>
          </div>
        ))}
      </div>
      
      <div className="mt-12 bg-white rounded-xl shadow-lg p-8">
        <h3 className="text-2xl font-semibold mb-6">Understanding Time Granularity</h3>
        <div className="grid grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-5xl font-bold text-blue-600 mb-2">Hourly</div>
            <p className="text-gray-600">For daily view - see consumption hour by hour</p>
          </div>
          <div className="text-center">
            <div className="text-5xl font-bold text-green-600 mb-2">Daily</div>
            <p className="text-gray-600">For weekly/monthly views - daily totals</p>
          </div>
          <div className="text-center">
            <div className="text-5xl font-bold text-purple-600 mb-2">Monthly</div>
            <p className="text-gray-600">For yearly view - monthly summaries</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Consumption Tab" />
  </div>
);

const Slide4 = () => (
  <div className=" relative h-full bg-white p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Building & System Selection</h2>
    <div className="max-w-6xl mx-auto">
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-8">
        <div className="grid grid-cols-2 gap-8">
          <div>
            <h3 className="text-2xl font-semibold mb-6">Building Selection</h3>
            <div className="space-y-3">
              <div className="bg-white rounded-lg p-4 border-2 border-blue-500 shadow">
                <label className="flex items-center">
                  <input type="radio" name="building" checked readOnly className="mr-3" />
                  <span className="font-medium">All Buildings</span>
                  <span className="ml-auto text-gray-600">Combined view</span>
                </label>
              </div>
              <div className="bg-white rounded-lg p-4 hover:border-blue-300 border-2 border-gray-200">
                <label className="flex items-center">
                  <input type="radio" name="building" className="mr-3" />
                  <span className="font-medium">Tower A</span>
                  <span className="ml-auto text-gray-600">520 kW avg</span>
                </label>
              </div>
              <div className="bg-white rounded-lg p-4 hover:border-blue-300 border-2 border-gray-200">
                <label className="flex items-center">
                  <input type="radio" name="building" className="mr-3" />
                  <span className="font-medium">Tower B</span>
                  <span className="ml-auto text-gray-600">680 kW avg</span>
                </label>
              </div>
              <div className="bg-white rounded-lg p-4 hover:border-blue-300 border-2 border-gray-200">
                <label className="flex items-center">
                  <input type="radio" name="building" className="mr-3" />
                  <span className="font-medium">Tower C</span>
                  <span className="ml-auto text-gray-600">450 kW avg</span>
                </label>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-2xl font-semibold mb-6">System Filter</h3>
            <div className="space-y-3">
              <div className="bg-white rounded-lg p-4 border-2 border-purple-500 shadow">
                <label className="flex items-center">
                  <input type="checkbox" checked readOnly className="mr-3" />
                  <div className="w-4 h-4 bg-blue-500 rounded mr-3"></div>
                  <span className="font-medium">Chiller Plant</span>
                  <span className="ml-auto text-gray-600">45%</span>
                </label>
              </div>
              <div className="bg-white rounded-lg p-4 border-2 border-purple-500 shadow">
                <label className="flex items-center">
                  <input type="checkbox" checked readOnly className="mr-3" />
                  <div className="w-4 h-4 bg-green-500 rounded mr-3"></div>
                  <span className="font-medium">Air Side</span>
                  <span className="ml-auto text-gray-600">25%</span>
                </label>
              </div>
              <div className="bg-white rounded-lg p-4 hover:border-purple-300 border-2 border-gray-200">
                <label className="flex items-center">
                  <input type="checkbox" className="mr-3" />
                  <div className="w-4 h-4 bg-yellow-500 rounded mr-3"></div>
                  <span className="font-medium">Light & Power</span>
                  <span className="ml-auto text-gray-600">18%</span>
                </label>
              </div>
              <div className="bg-white rounded-lg p-4 hover:border-purple-300 border-2 border-gray-200">
                <label className="flex items-center">
                  <input type="checkbox" className="mr-3" />
                  <div className="w-4 h-4 bg-purple-500 rounded mr-3"></div>
                  <span className="font-medium">Others</span>
                  <span className="ml-auto text-gray-600">12%</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Date Selection" />
  </div>
);

const Slide5 = () => (
  <div className=" relative h-full bg-gradient-to-br from-green-50 to-white p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Understanding Consumption Charts</h2>
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-2xl font-semibold">Daily Energy Consumption</h3>
            <div className="flex gap-4">
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg">Bar Chart</button>
              <button className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg">Line Chart</button>
            </div>
          </div>
          
          <div className="h-64 bg-gray-50 rounded-lg p-4">
            <div className="h-full flex items-end justify-around">
              {[40, 35, 30, 28, 32, 45, 65, 85, 92, 95, 93, 90, 88, 85, 83, 86, 90, 88, 75, 60, 50, 45, 38, 35].map((value, i) => (
                <div key={i} className="flex-1 flex flex-col items-center">
                  <div 
                    className="w-full mx-0.5 bg-gradient-to-t from-blue-600 to-blue-400 rounded-t hover:from-blue-700 hover:to-blue-500 transition-all cursor-pointer"
                    style={{height: `${value}%`}}
                  ></div>
                  {i % 3 === 0 && (
                    <span className="text-xs text-gray-600 mt-1">{i}:00</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-4 text-center">
            <p className="text-sm text-gray-600">Total Consumption</p>
            <p className="text-2xl font-bold text-blue-600">2,845 kWh</p>
          </div>
          <div className="bg-green-50 rounded-lg p-4 text-center">
            <p className="text-sm text-gray-600">Peak Demand</p>
            <p className="text-2xl font-bold text-green-600">385 kW</p>
          </div>
          <div className="bg-purple-50 rounded-lg p-4 text-center">
            <p className="text-sm text-gray-600">Average Load</p>
            <p className="text-2xl font-bold text-purple-600">118.5 kW</p>
          </div>
          <div className="bg-orange-50 rounded-lg p-4 text-center">
            <p className="text-sm text-gray-600">Load Factor</p>
            <p className="text-2xl font-bold text-orange-600">30.8%</p>
          </div>
        </div>
      </div>
      
      <div className="mt-6 bg-yellow-50 rounded-xl p-6 border-2 border-yellow-200">
        <p className="text-center text-lg">
          <strong className="text-yellow-700">Tip:</strong> Click on any bar to see detailed hourly breakdown
        </p>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Building Selection" />
  </div>
);

const Slide6 = () => (
  <div className=" relative h-full bg-white p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Comparison Analysis Features</h2>
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-2 gap-8">
        <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl p-8">
          <h3 className="text-2xl font-semibold mb-6">Time Period Analysis</h3>
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4 shadow">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">This Week</span>
                <span className="text-2xl font-bold text-blue-600">15,234 kWh</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div className="bg-blue-600 h-3 rounded-full" style={{width: '75%'}}></div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg p-4 shadow">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Last Week</span>
                <span className="text-2xl font-bold text-gray-600">14,892 kWh</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div className="bg-gray-600 h-3 rounded-full" style={{width: '73%'}}></div>
              </div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <p className="text-lg font-medium text-green-700">
                &uarr; 2.3% increase compared to last week
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-xl p-8">
          <h3 className="text-2xl font-semibold mb-6">Building Comparison</h3>
          <div className="space-y-3">
            {[
              { name: 'Tower Building', value: 5234, percent: 35 },
              { name: 'Podium Building', value: 6892, percent: 45 },
              { name: 'Car Park Building', value: 3108, percent: 20 }
            ].map((building, idx) => (
              <div key={idx} className="bg-white rounded-lg p-4 shadow">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">{building.name}</span>
                  <span className="font-bold">{building.value} kWh</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${idx === 0 ? 'bg-blue-500' : idx === 1 ? 'bg-green-500' : 'bg-purple-500'}`}
                    style={{width: `${building.percent}%`}}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Chart Types" />
  </div>
);

const Slide7 = () => (
  <div className=" relative h-full bg-gradient-to-br from-purple-50 to-white p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">System Breakdown Analysis</h2>
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="grid grid-cols-2 gap-8">
          <div className="flex items-center justify-center">
            <div className="relative w-80 h-80">
              <svg viewBox="0 0 42 42" className="w-full h-full">
                <circle cx="21" cy="21" r="15.915" fill="transparent" stroke="#e5e7eb" strokeWidth="3" />
                <circle cx="21" cy="21" r="15.915" fill="transparent" stroke="#3b82f6" strokeWidth="3"
                  strokeDasharray="45 55" strokeDashoffset="25" />
                <circle cx="21" cy="21" r="15.915" fill="transparent" stroke="#10b981" strokeWidth="3"
                  strokeDasharray="25 75" strokeDashoffset="-20" />
                <circle cx="21" cy="21" r="15.915" fill="transparent" stroke="#f59e0b" strokeWidth="3"
                  strokeDasharray="18 82" strokeDashoffset="-45" />
                <circle cx="21" cy="21" r="15.915" fill="transparent" stroke="#8b5cf6" strokeWidth="3"
                  strokeDasharray="12 88" strokeDashoffset="-63" />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <p className="text-3xl font-bold">100%</p>
                  <p className="text-gray-600">Total</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="space-y-4">
            <h3 className="text-2xl font-semibold mb-4">Energy Distribution</h3>
            {[
              { name: 'Chiller Plant', value: '1,283 kW', percent: 45, color: 'bg-blue-500' },
              { name: 'Air Side', value: '713 kW', percent: 25, color: 'bg-green-500' },
              { name: 'Light & Power', value: '513 kW', percent: 18, color: 'bg-yellow-500' },
              { name: 'Data Center & Others', value: '342 kW', percent: 12, color: 'bg-purple-500' }
            ].map((system, idx) => (
              <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center flex-1">
                  <div className={`w-4 h-4 ${system.color} rounded mr-3`}></div>
                  <span className="font-medium">{system.name}</span>
                </div>
                <div className="text-right">
                  <span className="font-bold">{system.value}</span>
                  <span className="text-gray-600 ml-2">({system.percent}%)</span>
                </div>
              </div>
            ))}
            
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-700">
                <strong>Insight:</strong> Real-time power demand chart with date selection for detailed analysis.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="System Breakdown" />
  </div>
);

const Slide8 = () => (
  <div className=" relative h-full bg-white p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Export & Reporting Options</h2>
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-2 gap-8 max-w-4xl mx-auto">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8 text-center">
          <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <FileDown size={40} className="text-blue-600" />
          </div>
          <h3 className="text-xl font-semibold mb-4">PDF Reports</h3>
          <ul className="text-left space-y-2 text-gray-700">
            <li>• Professional formatting</li>
            <li>• Include charts & graphs</li>
            <li>• Executive summary</li>
            <li>• Print-ready layout</li>
          </ul>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8 text-center">
          <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-green-600">
              <path d="M3 3h18v18H3zM3 9h18M9 3v18"></path>
            </svg>
          </div>
          <h3 className="text-xl font-semibold mb-4">CSV Export</h3>
          <ul className="text-left space-y-2 text-gray-700">
            <li>• Raw data access</li>
            <li>• Compatible with all spreadsheet software</li>
            <li>• Easy data import/export</li>
          </ul>
        </div>
      </div>
      
      <div className="mt-8 bg-gray-100 rounded-xl p-6">
        <h3 className="text-xl font-semibold mb-4 text-center">Export Settings</h3>
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-3">
            <label className="flex items-center">
              <input type="checkbox" checked readOnly className="mr-3" />
              <span>Include raw data tables</span>
            </label>
            <label className="flex items-center">
              <input type="checkbox" className="mr-3" />
              <span>Include cost analysis</span>
            </label>
          </div>
          <div className="space-y-3">
            <label className="flex items-center">
              <input type="checkbox" checked readOnly className="mr-3" />
              <span>Generate summary page</span>
            </label>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Comparison Tab" />
  </div>
);


const Slide10 = () => (
  <div className=" relative h-full bg-white p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Key Performance Indicators</h2>
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-2 gap-8">
        <div className="space-y-6">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center">
              <Zap className="mr-2 text-blue-600" />
              Energy Efficiency Metrics
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span>Energy Use Intensity (EUI)</span>
                <span className="font-bold text-xl">45.2 kWh/m²/year</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full" style={{width: '68%'}}></div>
              </div>
              <p className="text-sm text-gray-600">Target: 40 kWh/m²/year</p>
            </div>
          </div>
          
          <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-6">
            <h3 className="text-xl font-semibold mb-4">Load Factor</h3>
            <div className="text-center">
              <div className="text-5xl font-bold text-green-600 mb-2">78.5%</div>
              <p className="text-gray-600">Average Load / Peak Demand</p>
              <p className="text-sm text-green-600 mt-2">&uarr; 3.2% improvement from last month</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-8">
          <h3 className="text-xl font-semibold mb-6">Monthly Trends</h3>
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4 shadow">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Total Consumption</span>
                <span className="text-red-600 font-bold">&uarr; 5.2%</span>
              </div>
              <div className="text-2xl font-bold">684,520 kWh</div>
            </div>
            
            <div className="bg-white rounded-lg p-4 shadow">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Peak Demand</span>
                <span className="text-green-600 font-bold">&darr; 2.1%</span>
              </div>
              <div className="text-2xl font-bold">2,850 kW</div>
            </div>
            
            <div className="bg-white rounded-lg p-4 shadow">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Energy Cost</span>
                <span className="text-red-600 font-bold">&uarr; 3.8%</span>
              </div>
              <div className="text-2xl font-bold">฿2,874,984</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Building Comparison" />
  </div>
);

const Slide11 = () => (
  <div className=" relative h-full bg-gradient-to-br from-blue-50 to-white p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Customizing Your Analytics View</h2>
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="grid grid-cols-3 gap-6 mb-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <BarChart3 size={32} className="text-blue-600" />
            </div>
            <h3 className="font-semibold mb-2">Chart Types</h3>
            <p className="text-sm text-gray-600">Switch between bar, line, and area charts</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Calendar size={32} className="text-green-600" />
            </div>
            <h3 className="font-semibold mb-2">Date Presets</h3>
            <p className="text-sm text-gray-600">Quick selection for common date ranges</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
              <TrendingUp size={32} className="text-purple-600" />
            </div>
            <h3 className="font-semibold mb-2">Comparison Mode</h3>
            <p className="text-sm text-gray-600">Side-by-side period comparisons</p>
          </div>
        </div>
        
        <div className="border-t pt-6">
          <h3 className="text-xl font-semibold mb-4">Quick Actions Bar</h3>
          <div className="bg-gray-50 rounded-lg p-4 flex items-center justify-between">
            <div className="flex gap-3">
              <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                Today
              </button>
              <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                This Week
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                This Month
              </button>
              <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                This Year
              </button>
            </div>
            <div className="flex gap-3">
              <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-2">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7 10 12 15 17 10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                Export
              </button>
              <button className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="23 4 23 10 17 10"></polyline>
                  <polyline points="1 20 1 14 7 14"></polyline>
                  <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Performance Metrics" />
  </div>
);

const Slide12 = () => (
  <div className=" relative h-full bg-gradient-to-br from-green-50 to-white p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Analytics Best Practices</h2>
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-2 gap-8">
        <div className="space-y-6">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center">
              <span className="text-2xl mr-3">📊</span>
              Daily Analysis Routine
            </h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-green-600 mr-2">✓</span>
                <span>Review yesterday's consumption vs. average</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-600 mr-2">✓</span>
                <span>Check peak demand times and values</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-600 mr-2">✓</span>
                <span>Identify any unusual patterns</span>
              </li>
              <li className="flex items-start">
                <span className="text-green-600 mr-2">✓</span>
                <span>Compare with same day last week</span>
              </li>
            </ul>
          </div>
          
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold mb-4 flex items-center">
              <span className="text-2xl mr-3">📈</span>
              Monthly Reporting
            </h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">✓</span>
                <span>Generate monthly summary reports</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">✓</span>
                <span>Analyze trends and patterns</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">✓</span>
                <span>Calculate KPIs and metrics</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">✓</span>
                <span>Plan optimization strategies</span>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-8">
          <h3 className="text-2xl font-semibold mb-6 text-center">Common Insights to Look For</h3>
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4 shadow">
              <h4 className="font-semibold text-orange-700 mb-2">🔍 Overnight Consumption</h4>
              <p className="text-sm">Base load should be minimal during non-operational hours</p>
            </div>
            <div className="bg-white rounded-lg p-4 shadow">
              <h4 className="font-semibold text-orange-700 mb-2">⚡ Peak Demand Patterns</h4>
              <p className="text-sm">Identify if peaks are consistent or have anomalies</p>
            </div>
            <div className="bg-white rounded-lg p-4 shadow">
              <h4 className="font-semibold text-orange-700 mb-2">📅 Weekend vs Weekday</h4>
              <p className="text-sm">Significant differences indicate optimization opportunities</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="KPI Analysis" />
  </div>
);

const Slide13 = () => (
  <div className=" relative h-full bg-white p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Troubleshooting Data Issues</h2>
    <div className="max-w-5xl mx-auto">
      <div className="space-y-6">
        <div className="bg-red-50 border-2 border-red-200 rounded-xl p-6">
          <h3 className="text-xl font-semibold text-red-800 mb-4">Missing Data Points</h3>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <p className="font-medium mb-2">Symptoms:</p>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Gaps in charts</li>
                <li>• Zero values at certain hours</li>
                <li>• Incomplete daily totals</li>
              </ul>
            </div>
            <div>
              <p className="font-medium mb-2">Solutions:</p>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Check meter connectivity</li>
                <li>• Verify data collection service</li>
                <li>• Contact technical support</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="bg-yellow-50 border-2 border-yellow-200 rounded-xl p-6">
          <h3 className="text-xl font-semibold text-yellow-800 mb-4">Unusual Spikes</h3>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <p className="font-medium mb-2">Symptoms:</p>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Sudden high values</li>
                <li>• Unrealistic consumption</li>
                <li>• Data exceeding capacity</li>
              </ul>
            </div>
            <div>
              <p className="font-medium mb-2">Solutions:</p>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Verify meter calibration</li>
                <li>• Check for equipment issues</li>
                <li>• Review data validation rules</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="bg-blue-50 border-2 border-blue-200 rounded-xl p-6">
          <h3 className="text-xl font-semibold text-blue-800 mb-4">Slow Loading</h3>
          <div className="grid grid-cols-2 gap-6">
            <div>
              <p className="font-medium mb-2">Symptoms:</p>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Charts take long to load</li>
                <li>• Timeouts on large date ranges</li>
                <li>• Browser becomes unresponsive</li>
              </ul>
            </div>
            <div>
              <p className="font-medium mb-2">Solutions:</p>
              <ul className="text-sm space-y-1 text-gray-700">
                <li>• Use smaller date ranges</li>
                <li>• Clear browser cache</li>
                <li>• Use aggregated views for long periods</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Export Features" />
  </div>
);

const Slide14 = () => (
  <div className=" relative h-full bg-gradient-to-br from-purple-50 to-pink-50 p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Advanced Features</h2>
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-2 gap-8">
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h3 className="text-2xl font-semibold mb-6">Predictive Analytics</h3>
          <div className="bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg p-6 mb-4">
            <div className="h-32 flex items-center justify-center">
              <svg width="200" height="80" viewBox="0 0 200 80">
                <path d="M 0 40 Q 50 20 100 40 T 200 40" stroke="#8b5cf6" strokeWidth="3" fill="none" strokeDasharray="5,5" />
                <path d="M 0 40 Q 50 60 100 40 T 200 40" stroke="#3b82f6" strokeWidth="3" fill="none" />
              </svg>
            </div>
          </div>
          <ul className="space-y-2 text-gray-700">
            <li>• Next day consumption forecast</li>
            <li>• Peak demand predictions</li>
            <li>• Anomaly detection alerts</li>
            <li>• Seasonal trend analysis</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h3 className="text-2xl font-semibold mb-6">Benchmarking</h3>
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Your Building</span>
                <span className="font-bold text-blue-600">45.2 kWh/m²</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{width: '65%'}}></div>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Industry Average</span>
                <span className="font-bold text-gray-600">52.8 kWh/m²</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-gray-600 h-2 rounded-full" style={{width: '75%'}}></div>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium">Best in Class</span>
                <span className="font-bold text-green-600">35.0 kWh/m²</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{width: '50%'}}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6">
        <h3 className="text-xl font-semibold mb-4 text-center">Coming Soon</h3>
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="bg-white rounded-lg p-4">
            <div className="text-2xl mb-2">🤖</div>
            <p className="font-medium">AI Recommendations</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-2xl mb-2">📱</div>
            <p className="font-medium">Mobile Analytics</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="text-2xl mb-2">🔗</div>
            <p className="font-medium">API Integration</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Best Practices" />
  </div>
);

const Slide15 = () => (
  <div className=" relative h-full bg-gradient-to-br from-green-50 to-green-100 p-16 flex flex-col justify-center">
    <div className="text-center max-w-4xl mx-auto">
      <h1 className="text-5xl font-bold text-gray-800 mb-8">
        Analytics Training Complete! 
      </h1>
      <div className="text-2xl text-gray-600 space-y-4 mb-12">
        <p>You're now equipped to analyze your energy data effectively</p>
        <p>Use these insights to optimize consumption and reduce costs</p>
      </div>
      
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
        <h3 className="text-2xl font-semibold mb-6">Key Takeaways</h3>
        <div className="grid grid-cols-2 gap-6 text-left">
          <div className="space-y-3">
            <div className="flex items-start">
              <span className="text-green-600 mr-2">✓</span>
              <span>Select appropriate time ranges and views</span>
            </div>
            <div className="flex items-start">
              <span className="text-green-600 mr-2">✓</span>
              <span>Compare periods to identify trends</span>
            </div>
            <div className="flex items-start">
              <span className="text-green-600 mr-2">✓</span>
              <span>Use filters to focus on specific data</span>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-start">
              <span className="text-green-600 mr-2">✓</span>
              <span>Export reports for stakeholders</span>
            </div>
            <div className="flex items-start">
              <span className="text-green-600 mr-2">✓</span>
              <span>Monitor KPIs regularly</span>
            </div>
            <div className="flex items-start">
              <span className="text-green-600 mr-2">✓</span>
              <span>Act on insights to save energy</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-center gap-4">
        <button className="px-8 py-3 bg-purple-600 text-white rounded-lg text-lg font-medium hover:bg-purple-700 transition-colors">
          Start Analyzing
        </button>
        <button className="px-8 py-3 bg-gray-200 text-gray-700 rounded-lg text-lg font-medium hover:bg-gray-300 transition-colors">
          Next Module
        </button>
      </div>
    </div>
    <SlidePageIndicator page="Analytics Page" feature="Summary" />
  </div>
);

export const analyticsSlides = [
  Slide1, Slide2, Slide3, Slide4, Slide5, Slide6, Slide7, Slide8,
  Slide10, Slide11, Slide12, Slide13, Slide14, Slide15
];