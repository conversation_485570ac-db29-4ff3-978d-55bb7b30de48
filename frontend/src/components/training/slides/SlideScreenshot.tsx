import React from 'react';

interface SlideScreenshotProps {
  src: string;
  alt: string;
  caption?: string;
  className?: string;
  fallback?: React.ReactNode;
}

export const SlideScreenshot: React.FC<SlideScreenshotProps> = ({
  src,
  alt,
  caption,
  className = '',
  fallback
}) => {
  const [imageError, setImageError] = React.useState(false);
  const [imageLoading, setImageLoading] = React.useState(true);

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  if (imageError && fallback) {
    return <>{fallback}</>;
  }

  return (
    <div className={`screenshot-container ${className}`}>
      <div className="relative bg-gray-100 rounded-lg overflow-hidden shadow-lg">
        {imageLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading screenshot...</p>
            </div>
          </div>
        )}
        <img
          src={src}
          alt={alt}
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={`w-full h-auto ${imageLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        />
      </div>
      {caption && (
        <p className="text-sm text-gray-500 mt-2 text-center">{caption}</p>
      )}
    </div>
  );
};

// Placeholder component for when screenshot is not available
export const ScreenshotPlaceholder: React.FC<{
  title: string;
  description?: string;
  height?: string;
}> = ({ title, description, height = 'h-96' }) => {
  return (
    <div className={`${height} bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg shadow-lg flex items-center justify-center`}>
      <div className="text-center p-8">
        <div className="w-24 h-24 bg-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-12 h-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-gray-700 mb-2">{title}</h3>
        {description && (
          <p className="text-gray-500">{description}</p>
        )}
      </div>
    </div>
  );
};