import React from 'react';
import { <PERSON><PERSON>, Building, Bar<PERSON>hart3, <PERSON>ert<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Camera } from 'lucide-react';
import { SlidePageIndicator } from '../SlidePageIndicator';
import { SlideScreenshot, ScreenshotPlaceholder } from './SlideScreenshot';

// TODO: Replace all placeholder images with actual screenshots
const ScreenshotNotice = () => (
  <div className="absolute top-4 right-4 bg-red-100 border border-red-300 rounded-lg p-3 flex items-center gap-2 animate-pulse">
    <Camera className="text-red-600" size={20} />
    <span className="text-sm text-red-700 font-medium">Screenshot needed</span>
  </div>
);

const Slide1 = () => (
  <div className="h-full bg-gradient-to-br from-green-50 to-white p-16 flex flex-col justify-center relative">
    <div className="text-center">
      <h1 className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600 mb-8">
        Quick Start Guide
      </h1>
      <h2 className="text-4xl text-gray-700 mb-12">Energy Management System</h2>
      <p className="text-2xl text-gray-600">Get up and running in just 5 minutes!</p>
    </div>
    <SlidePageIndicator page="Quick Start Guide" feature="Welcome" />
  </div>
);

const Slide2 = () => (
  <div className="h-full bg-white p-12 flex flex-col justify-center relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">System Overview</h2>
    <div className="max-w-4xl mx-auto">
      <div className="grid grid-cols-2 gap-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
          <Building size={48} className="text-blue-600 mb-4" />
          <h3 className="text-2xl font-semibold mb-4">5 Buildings Monitored</h3>
          <p className="text-lg text-gray-700">Tower A, B, C, Podium, and Car Park with 296 meters</p>
        </div>
        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8">
          <Zap size={48} className="text-green-600 mb-4" />
          <h3 className="text-2xl font-semibold mb-4">Real-time Monitoring</h3>
          <p className="text-lg text-gray-700">Live power consumption data from all meters</p>
        </div>
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-8">
          <BarChart3 size={48} className="text-purple-600 mb-4" />
          <h3 className="text-2xl font-semibold mb-4">Detailed Analytics</h3>
          <p className="text-lg text-gray-700">Daily, weekly, monthly, and yearly analysis</p>
        </div>
        <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-8">
          <AlertCircle size={48} className="text-orange-600 mb-4" />
          <h3 className="text-2xl font-semibold mb-4">Smart Alerts</h3>
          <p className="text-lg text-gray-700">Email alerts for critical, warning, and info events</p>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Quick Start Guide" feature="System Overview" />
  </div>
);

const Slide3 = () => (
  <div className="h-full bg-gradient-to-r from-blue-50 to-purple-50 p-12 relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Navigation Basics</h2>
    <div className="max-w-5xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="space-y-6">
          <div className="flex items-center p-6 bg-gray-50 rounded-lg hover:bg-blue-50 transition-colors">
            <div className="text-4xl mr-6">🏠</div>
            <div>
              <h3 className="text-2xl font-semibold mb-2">Dashboard</h3>
              <p className="text-lg text-gray-600">Your home screen with key metrics and real-time data</p>
            </div>
          </div>
          <div className="flex items-center p-6 bg-gray-50 rounded-lg hover:bg-blue-50 transition-colors">
            <div className="text-4xl mr-6">📊</div>
            <div>
              <h3 className="text-2xl font-semibold mb-2">Analytics</h3>
              <p className="text-lg text-gray-600">Detailed consumption analysis and reporting tools</p>
            </div>
          </div>
          <div className="flex items-center p-6 bg-gray-50 rounded-lg hover:bg-blue-50 transition-colors">
            <div className="text-4xl mr-6">⚡</div>
            <div>
              <h3 className="text-2xl font-semibold mb-2">Meters</h3>
              <p className="text-lg text-gray-600">View and manage individual meter details</p>
            </div>
          </div>
          <div className="flex items-center p-6 bg-gray-50 rounded-lg hover:bg-blue-50 transition-colors">
            <div className="text-4xl mr-6">🔔</div>
            <div>
              <h3 className="text-2xl font-semibold mb-2">Alarms</h3>
              <p className="text-lg text-gray-600">Configure alerts and view notification history</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Quick Start Guide" feature="Navigation" />
  </div>
);

const Slide4 = () => (
  <div className=" relative h-full bg-white p-12">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Essential Daily Tasks</h2>
    <div className="max-w-4xl mx-auto">
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-8">
          <h3 className="text-2xl font-semibold mb-6 flex items-center">
            <span className="text-3xl mr-4">1️⃣</span>
            Check Dashboard Overview
          </h3>
          <ul className="space-y-3 text-lg text-gray-700 ml-12">
            <li>• Review total consumption vs yesterday</li>
            <li>• Check for any active alerts</li>
            <li>• Monitor peak demand status</li>
          </ul>
        </div>
        
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-8">
          <h3 className="text-2xl font-semibold mb-6 flex items-center">
            <span className="text-3xl mr-4">2️⃣</span>
            Review Building Performance
          </h3>
          <ul className="space-y-3 text-lg text-gray-700 ml-12">
            <li>• Compare tower consumption</li>
            <li>• Identify unusual patterns</li>
            <li>• Check system breakdown percentages</li>
          </ul>
        </div>
        
        <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-8">
          <h3 className="text-2xl font-semibold mb-6 flex items-center">
            <span className="text-3xl mr-4">3️⃣</span>
            Generate Quick Report
          </h3>
          <ul className="space-y-3 text-lg text-gray-700 ml-12">
            <li>• Export daily summary</li>
            <li>• Share with management</li>
            <li>• Plan optimization actions</li>
          </ul>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Quick Start Guide" feature="Dashboard Basics" />
  </div>
);

const Slide5 = () => (
  <div className=" relative h-full bg-gradient-to-br from-green-50 to-green-100 p-16 flex flex-col justify-center">
    <div className="text-center max-w-4xl mx-auto">
      <h1 className="text-5xl font-bold text-gray-800 mb-8">
        You're Ready! 🎉
      </h1>
      <div className="text-2xl text-gray-600 space-y-4 mb-12">
        <p>Start exploring the system with these simple steps</p>
        <p>Remember: The dashboard is your command center</p>
      </div>
      
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
        <h3 className="text-2xl font-semibold mb-6">Quick Actions</h3>
        <div className="flex justify-around">
          <button 
            className="px-6 py-3 rounded-lg text-lg transition-colors"
            style={{ backgroundColor: '#2563eb', color: '#ffffff' }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#1d4ed8'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#2563eb'}
          >
            Go to Dashboard
          </button>
          <button 
            className="px-6 py-3 rounded-lg text-lg transition-colors"
            style={{ backgroundColor: '#7c3aed', color: '#ffffff' }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#6d28d9'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#7c3aed'}
          >
            View Analytics
          </button>
          <button 
            className="px-6 py-3 rounded-lg text-lg transition-colors"
            style={{ backgroundColor: '#059669', color: '#ffffff' }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#047857'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#059669'}
          >
            Check Meters
          </button>
        </div>
      </div>
      
      <p className="text-lg text-gray-600">
        Need more help? Check out the detailed training modules for each feature
      </p>
    </div>
    <SlidePageIndicator page="Quick Start Guide" feature="Key Features" />
  </div>
);

export const quickStartSlides = [Slide1, Slide2, Slide3, Slide4, Slide5];