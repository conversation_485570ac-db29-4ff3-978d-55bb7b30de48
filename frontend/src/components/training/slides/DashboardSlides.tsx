import React from 'react';
import { Building, Zap, TrendingUp, AlertCircle, BarChart3, Users } from 'lucide-react';
import { SlidePageIndicator } from '../SlidePageIndicator';
import { SlideScreenshot, ScreenshotPlaceholder } from './SlideScreenshot';

const Slide1 = () => (
  <div className="h-full bg-gradient-to-br from-blue-50 to-white p-16 flex flex-col justify-center relative">
    <div>
      <h1 className="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-blue-800 mb-4 text-center">
        Energy Management System
      </h1>
      <h2 className="text-3xl text-gray-700 mb-12 text-center">Dashboard Overview Training</h2>
      
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h3 className="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold mr-3">
              <span className="text-sm">i</span>
            </div>
            What You'll Learn
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">1.</span>
              <p className="text-gray-700">Navigate the main dashboard interface</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">2.</span>
              <p className="text-gray-700">Understanding key energy metrics</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">3.</span>
              <p className="text-gray-700">Monitor building performance</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">4.</span>
              <p className="text-gray-700">Identify consumption patterns</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">5.</span>
              <p className="text-gray-700">Access detailed floor analytics</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">6.</span>
              <p className="text-gray-700">Respond to system alerts</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">7.</span>
              <p className="text-gray-700">Export data for reporting</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">8.</span>
              <p className="text-gray-700">Best practices for monitoring</p>
            </div>
          </div>
        </div>
        
        <div className="flex justify-center gap-6">
          <div className="w-20 h-20 bg-blue-100 rounded-xl flex items-center justify-center">
            <Building size={40} className="text-blue-600" />
          </div>
          <div className="w-20 h-20 bg-green-100 rounded-xl flex items-center justify-center">
            <Zap size={40} className="text-green-600" />
          </div>
          <div className="w-20 h-20 bg-purple-100 rounded-xl flex items-center justify-center">
            <BarChart3 size={40} className="text-purple-600" />
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="Introduction" />
  </div>
);

const Slide2 = () => (
  <div className="h-full bg-white p-12 flex relative">
    <div className="w-1/2 pr-8 flex flex-col justify-center">
      <h2 className="text-4xl font-bold text-gray-800 mb-6">Welcome to Your Dashboard</h2>
      <div className="space-y-6 text-xl text-gray-600">
        <p className="flex items-start">
          <span className="text-blue-600 mr-3">•</span>
          Real-time energy monitoring across 520 meters
        </p>
        <p className="flex items-start">
          <span className="text-blue-600 mr-3">•</span>
          Instant alerts for abnormal consumption
        </p>
        <p className="flex items-start">
          <span className="text-blue-600 mr-3">•</span>
          Historical data analysis with mock data mode toggle
        </p>
        <p className="flex items-start">
          <span className="text-blue-600 mr-3">•</span>
          Cost optimization insights
        </p>
      </div>
    </div>
    <div className="w-1/2 bg-gray-100 rounded-xl p-8 flex items-center justify-center">
      <SlideScreenshot
        src="/images/training/dashboard-overview.png"
        alt="Dashboard Overview"
        caption="Main Dashboard Interface"
        fallback={
          <ScreenshotPlaceholder
            title="Dashboard Overview"
            description="Screenshot will be captured using the screenshot tool"
          />
        }
      />
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="Overview" />
  </div>
);

const Slide3 = () => (
  <div className="h-full bg-gradient-to-r from-blue-50 to-white p-12 relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Key Metrics at a Glance</h2>
    <div className="grid grid-cols-2 gap-8 max-w-5xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8 transform hover:scale-105 transition-transform">
        <div className="flex items-center mb-4">
          <div className="w-16 h-16 bg-blue-100 rounded-xl flex items-center justify-center mr-4">
            <Zap size={32} className="text-blue-600" />
          </div>
          <h3 className="text-2xl font-semibold">Total Consumption</h3>
        </div>
        <p className="text-gray-600 text-lg">Monitor your building's total energy usage in real-time with kWh measurements updated every minute.</p>
      </div>
      
      <div className="bg-white rounded-xl shadow-lg p-8 transform hover:scale-105 transition-transform">
        <div className="flex items-center mb-4">
          <div className="w-16 h-16 bg-green-100 rounded-xl flex items-center justify-center mr-4">
            <TrendingUp size={32} className="text-green-600" />
          </div>
          <h3 className="text-2xl font-semibold">Peak Demand</h3>
        </div>
        <p className="text-gray-600 text-lg">Track your highest power demand to optimize usage patterns and reduce peak charges.</p>
      </div>
      
      <div className="bg-white rounded-xl shadow-lg p-8 transform hover:scale-105 transition-transform">
        <div className="flex items-center mb-4">
          <div className="w-16 h-16 bg-yellow-100 rounded-xl flex items-center justify-center mr-4">
            <AlertCircle size={32} className="text-yellow-600" />
          </div>
          <h3 className="text-2xl font-semibold">Active Alerts</h3>
        </div>
        <p className="text-gray-600 text-lg">Stay informed about system issues with real-time alerts for abnormal consumption or equipment problems.</p>
      </div>
      
      <div className="bg-white rounded-xl shadow-lg p-8 transform hover:scale-105 transition-transform">
        <div className="flex items-center mb-4">
          <div className="w-16 h-16 bg-purple-100 rounded-xl flex items-center justify-center mr-4">
            <BarChart3 size={32} className="text-purple-600" />
          </div>
          <h3 className="text-2xl font-semibold">Energy Costs</h3>
        </div>
        <p className="text-gray-600 text-lg">View your energy costs in real-time and track monthly spending against budget targets.</p>
      </div>
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="Key Metrics" />
  </div>
);

const Slide4 = () => (
  <div className="h-full bg-white p-12 relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-8">Understanding the Building Overview</h2>
    <div className="flex gap-8 h-5/6">
      <div className="w-2/3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
        <div className="bg-white rounded-lg p-6 h-full shadow-inner">
          <div className="grid grid-cols-3 gap-4 h-full">
            <div className="rounded-lg p-4 shadow-lg" style={{ backgroundColor: '#1e40af', color: '#ffffff' }}>
              <h3 className="text-xl font-bold mb-2">Tower Building</h3>
              <p className="text-3xl font-bold">520 kW</p>
              <p className="text-sm opacity-90">&uarr; 5% from last week</p>
            </div>
            <div className="rounded-lg p-4 shadow-lg" style={{ backgroundColor: '#1d4ed8', color: '#ffffff' }}>
              <h3 className="text-xl font-bold mb-2">Podium Building</h3>
              <p className="text-3xl font-bold">680 kW</p>
              <p className="text-sm opacity-90">&darr; 2% from last week</p>
            </div>
            <div className="rounded-lg p-4 shadow-lg" style={{ backgroundColor: '#1e3a8a', color: '#ffffff' }}>
              <h3 className="text-xl font-bold mb-2">Car Park Building</h3>
              <p className="text-3xl font-bold">450 kW</p>
              <p className="text-sm opacity-90">&rarr; No change</p>
            </div>
          </div>
        </div>
      </div>
      <div className="w-1/3 space-y-6">
        <div className="bg-yellow-50 rounded-xl p-6 border-2 border-yellow-200">
          <h3 className="text-xl font-semibold mb-2 flex items-center">
            <AlertCircle className="mr-2 text-yellow-600" size={24} />
            Quick Tips
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Click on any tower to see detailed metrics</li>
            <li>• Green arrows indicate improvement</li>
            <li>• Red arrows show increased consumption</li>
            <li>• Percentages compare to same day last week</li>
          </ul>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="Building Overview" />
  </div>
);

const Slide5 = () => (
  <div className="h-full bg-gradient-to-br from-green-50 to-white p-12 relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Real-time Power Flow Monitoring</h2>
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="grid grid-cols-12 gap-4 items-center">
          <div className="col-span-3">
            <div className="bg-gray-100 rounded-lg p-6 text-center">
              <h3 className="text-lg font-semibold mb-2">Power Input</h3>
              <p className="text-3xl font-bold text-blue-600">2,850 kW</p>
              <div className="mt-4 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div className="h-full bg-blue-600 animate-pulse" style={{width: '85%'}}></div>
              </div>
            </div>
          </div>
          
          <div className="col-span-1 flex justify-center">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-gray-400">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </div>
          
          <div className="col-span-3">
            <div className="bg-gray-100 rounded-lg p-6 text-center">
              <h3 className="text-lg font-semibold mb-2">Main Distribution</h3>
              <p className="text-3xl font-bold text-green-600">2,850 kW</p>
            </div>
          </div>
          
          <div className="col-span-1 flex justify-center">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-gray-400">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </div>
          
          <div className="col-span-4">
            <div className="space-y-2">
              <div className="bg-blue-50 rounded-lg p-3 flex justify-between items-center">
                <span className="font-medium">Buildings</span>
                <span className="font-bold text-blue-600">1,650 kW</span>
              </div>
              <div className="bg-green-50 rounded-lg p-3 flex justify-between items-center">
                <span className="font-medium">Chiller Plant</span>
                <span className="font-bold text-green-600">850 kW</span>
              </div>
              <div className="bg-purple-50 rounded-lg p-3 flex justify-between items-center">
                <span className="font-medium">Others</span>
                <span className="font-bold text-purple-600">350 kW</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <p className="text-center text-gray-700">
            <strong>Live Updates:</strong> Data refreshes every 30 seconds with mock data mode available
          </p>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="Power Flow" />
  </div>
);

const Slide6 = () => (
  <div className="h-full bg-white p-12 relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Daily Load Profile Analysis</h2>
    <div className="max-w-6xl mx-auto">
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-8">
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <div className="h-64 flex items-end justify-around">
            {[30, 28, 26, 25, 28, 35, 55, 75, 85, 90, 88, 85, 82, 80, 78, 82, 86, 88, 75, 60, 45, 38, 32, 30].map((value, i) => (
              <div key={i} className="flex flex-col items-center flex-1">
                <div 
                  className={`w-full mx-0.5 rounded-t transition-all duration-300 ${
                    i >= 9 && i <= 17 ? 'bg-red-400' : 'bg-blue-400'
                  }`}
                  style={{height: `${value}%`}}
                ></div>
                {i % 3 === 0 && (
                  <span className="text-xs text-gray-600 mt-2">{i}:00</span>
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-center gap-8 mt-6">
            <div className="flex items-center">
              <div className="w-4 h-4 bg-blue-400 rounded mr-2"></div>
              <span className="text-sm text-gray-600">Off-Peak Hours</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 bg-red-400 rounded mr-2"></div>
              <span className="text-sm text-gray-600">Peak Hours (9:00 - 22:00) Mon-Fri</span>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-4 mt-6">
          <div className="bg-white rounded-lg p-4 text-center">
            <p className="text-gray-600 text-sm">Average Load</p>
            <p className="text-2xl font-bold text-gray-800">1,850 kW</p>
          </div>
          <div className="bg-white rounded-lg p-4 text-center">
            <p className="text-gray-600 text-sm">Peak Demand</p>
            <p className="text-2xl font-bold text-red-600">2,450 kW @ 14:30</p>
          </div>
          <div className="bg-white rounded-lg p-4 text-center">
            <p className="text-gray-600 text-sm">Load Factor</p>
            <p className="text-2xl font-bold text-green-600">75.5%</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="Load Profile" />
  </div>
);

const Slide7 = () => (
  <div className="h-full bg-gradient-to-br from-yellow-50 to-white p-12 relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Alert System Overview</h2>
    <div className="max-w-5xl mx-auto space-y-6">
      <div className="bg-red-50 border-2 border-red-200 rounded-xl p-6 transform hover:scale-105 transition-transform">
        <div className="flex items-start">
          <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
            <AlertCircle className="text-red-600" size={24} />
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-red-800 mb-2">Critical Alerts</h3>
            <p className="text-gray-700">Immediate attention required for system failures or dangerous conditions</p>
            <p className="text-sm text-gray-600 mt-2">Example: Main breaker trip, transformer overload</p>
          </div>
        </div>
      </div>
      
      <div className="bg-yellow-50 border-2 border-yellow-200 rounded-xl p-6 transform hover:scale-105 transition-transform">
        <div className="flex items-start">
          <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
            <AlertCircle className="text-yellow-600" size={24} />
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-yellow-800 mb-2">Warning Alerts</h3>
            <p className="text-gray-700">Conditions that may lead to problems if not addressed</p>
            <p className="text-sm text-gray-600 mt-2">Example: High consumption trend, approaching capacity limit</p>
          </div>
        </div>
      </div>
      
      <div className="bg-blue-50 border-2 border-blue-200 rounded-xl p-6 transform hover:scale-105 transition-transform">
        <div className="flex items-start">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
            <AlertCircle className="text-blue-600" size={24} />
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-blue-800 mb-2">Information Alerts</h3>
            <p className="text-gray-700">General notifications about system status and scheduled events</p>
            <p className="text-sm text-gray-600 mt-2">Example: Scheduled maintenance, data refresh notifications</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="Alert System" />
  </div>
);

const Slide8 = () => (
  <div className="h-full bg-white p-12 relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Navigating Between Views</h2>
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-2 gap-8">
        <div className="space-y-6">
          <h3 className="text-2xl font-semibold text-gray-700 mb-4">Main Navigation Menu</h3>
          {[
            { name: 'Dashboard', desc: 'Overview of all systems', icon: '🏠' },
            { name: 'Analytics', desc: 'Detailed consumption analysis', icon: '📊' },
            { name: 'Meters', desc: 'Individual meter management', icon: '⚡' },
            { name: 'Alarms', desc: 'Alert configuration and history', icon: '🔔' },
            { name: 'Settings', desc: 'System configuration', icon: '⚙️' }
          ].map((item, idx) => (
            <div key={idx} className="bg-gray-50 rounded-lg p-4 flex items-center hover:bg-blue-50 transition-colors cursor-pointer">
              <span className="text-3xl mr-4">{item.icon}</span>
              <div>
                <h4 className="font-semibold text-lg">{item.name}</h4>
                <p className="text-sm text-gray-600">{item.desc}</p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl p-8">
          <h3 className="text-2xl font-semibold text-gray-700 mb-6">Quick Actions</h3>
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4 shadow">
              <h4 className="font-semibold mb-2">Export Data</h4>
              <p className="text-sm text-gray-600">Download reports in PDF or CSV format</p>
            </div>
            <div className="bg-white rounded-lg p-4 shadow">
              <h4 className="font-semibold mb-2">Date Range Selection</h4>
              <p className="text-sm text-gray-600">Compare different time periods easily</p>
            </div>
            <div className="bg-white rounded-lg p-4 shadow">
              <h4 className="font-semibold mb-2">Auto-Refresh Toggle</h4>
              <p className="text-sm text-gray-600">Enable/disable automatic data updates</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="Navigation" />
  </div>
);

const Slide9 = () => (
  <div className="h-full bg-gradient-to-r from-green-50 to-blue-50 p-12 relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Understanding System Breakdown</h2>
    <div className="max-w-5xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="flex items-center justify-center mb-8">
          <div className="relative w-80 h-80">
            <svg viewBox="0 0 100 100" className="transform -rotate-90">
              <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" strokeWidth="20" />
              <circle cx="50" cy="50" r="40" fill="none" stroke="#3b82f6" strokeWidth="20" 
                strokeDasharray={`${2 * Math.PI * 40 * 0.45} ${2 * Math.PI * 40}`} 
                strokeDashoffset="0" />
              <circle cx="50" cy="50" r="40" fill="none" stroke="#10b981" strokeWidth="20" 
                strokeDasharray={`${2 * Math.PI * 40 * 0.25} ${2 * Math.PI * 40}`} 
                strokeDashoffset={`-${2 * Math.PI * 40 * 0.45}`} />
              <circle cx="50" cy="50" r="40" fill="none" stroke="#f59e0b" strokeWidth="20" 
                strokeDasharray={`${2 * Math.PI * 40 * 0.15} ${2 * Math.PI * 40}`} 
                strokeDashoffset={`-${2 * Math.PI * 40 * 0.70}`} />
              <circle cx="50" cy="50" r="40" fill="none" stroke="#8b5cf6" strokeWidth="20" 
                strokeDasharray={`${2 * Math.PI * 40 * 0.15} ${2 * Math.PI * 40}`} 
                strokeDashoffset={`-${2 * Math.PI * 40 * 0.85}`} />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <p className="text-3xl font-bold text-gray-800">2,850</p>
                <p className="text-gray-600">kW Total</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          {[
            { name: 'Chiller Plant', value: '45%', color: 'bg-blue-500' },
            { name: 'Air Side', value: '25%', color: 'bg-green-500' },
            { name: 'Light & Power', value: '15%', color: 'bg-yellow-500' },
            { name: 'Others', value: '15%', color: 'bg-purple-500' }
          ].map((item, idx) => (
            <div key={idx} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className={`w-4 h-4 ${item.color} rounded mr-3`}></div>
                <span className="font-medium">{item.name}</span>
              </div>
              <span className="font-bold text-gray-700">{item.value}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="System Breakdown" />
  </div>
);

const Slide10 = () => (
  <div className="h-full bg-white p-12 relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Best Practices & Tips</h2>
    <div className="max-w-6xl mx-auto">
      <div className="grid grid-cols-2 gap-8">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
          <h3 className="text-2xl font-semibold mb-6 flex items-center">
            <span className="text-3xl mr-3">💡</span>
            Daily Monitoring
          </h3>
          <ul className="space-y-4 text-lg">
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">✓</span>
              Check dashboard first thing in the morning
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">✓</span>
              Review any overnight alerts
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">✓</span>
              Compare today's consumption with yesterday
            </li>
            <li className="flex items-start">
              <span className="text-blue-600 mr-2">✓</span>
              Monitor peak demand trends
            </li>
          </ul>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8">
          <h3 className="text-2xl font-semibold mb-6 flex items-center">
            <span className="text-3xl mr-3">🎯</span>
            Energy Saving Actions
          </h3>
          <ul className="space-y-4 text-lg">
            <li className="flex items-start">
              <span className="text-green-600 mr-2">✓</span>
              Identify unusual consumption patterns
            </li>
            <li className="flex items-start">
              <span className="text-green-600 mr-2">✓</span>
              Set up alerts for threshold breaches
            </li>
            <li className="flex items-start">
              <span className="text-green-600 mr-2">✓</span>
              Schedule equipment during off-peak hours
            </li>
          </ul>
        </div>
      </div>
      
      <div className="mt-8 bg-yellow-50 rounded-xl p-6 border-2 border-yellow-200">
        <p className="text-center text-lg">
          <strong className="text-yellow-700">Pro Tip:</strong> Use the export feature to create weekly reports for management meetings
        </p>
      </div>
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="Best Practices" />
  </div>
);

const Slide11 = () => (
  <div className="h-full bg-gradient-to-br from-purple-50 to-white p-12 relative">
    <h2 className="text-4xl font-bold text-center text-gray-800 mb-12">Customizing Your Dashboard</h2>
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-xl shadow-lg p-8">
        <div className="flex justify-center mb-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
              <AlertCircle size={40} className="text-purple-600" />
            </div>
            <h3 className="text-xl font-semibold mb-2">Alert Settings</h3>
            <p className="text-gray-600">Configure notification preferences</p>
          </div>
        </div>
        
        <div className="border-t pt-6">
          <h4 className="text-lg font-semibold mb-4">Available Customizations:</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
              <input type="checkbox" checked className="mr-3" readOnly />
              <span>Auto-refresh data every 30 seconds</span>
            </div>
            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
              <input type="checkbox" checked className="mr-3" readOnly />
              <span>Show cost in local currency</span>
            </div>
            <div className="flex items-center p-3 bg-gray-50 rounded-lg">
              <input type="checkbox" checked className="mr-3" readOnly />
              <span>Mock data mode for testing</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="Customization" />
  </div>
);

const Slide12 = () => (
  <div className="h-full bg-gradient-to-br from-green-50 to-green-100 p-16 flex flex-col justify-center relative">
    <div className="text-center max-w-4xl mx-auto">
      <h1 className="text-5xl font-bold text-gray-800 mb-8">
        You're Ready to Start!
      </h1>
      <div className="text-2xl text-gray-600 space-y-4 mb-12">
        <p>You now have the knowledge to effectively use the dashboard</p>
        <p>Remember to check it daily for optimal energy management</p>
      </div>
      
      <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
        <h3 className="text-2xl font-semibold mb-6">Next Steps:</h3>
        <div className="grid grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-4xl mb-2">📊</div>
            <p className="font-medium">Explore Analytics</p>
            <p className="text-sm text-gray-600 mt-1">Deep dive into your data</p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-2">🔔</div>
            <p className="font-medium">Set Up Alerts</p>
            <p className="text-sm text-gray-600 mt-1">Stay informed automatically</p>
          </div>
          <div className="text-center">
            <div className="text-4xl mb-2">📈</div>
            <p className="font-medium">Create Reports</p>
            <p className="text-sm text-gray-600 mt-1">Share insights with your team</p>
          </div>
        </div>
      </div>
      
      <div className="flex justify-center gap-4">
        <button className="px-8 py-3 bg-blue-600 text-white rounded-lg text-lg font-medium hover:bg-blue-700 transition-colors">
          Start Using Dashboard
        </button>
        <button className="px-8 py-3 bg-gray-200 text-gray-700 rounded-lg text-lg font-medium hover:bg-gray-300 transition-colors">
          View More Training
        </button>
      </div>
    </div>
    <SlidePageIndicator page="Dashboard Page" feature="Summary" />
  </div>
);

export const dashboardSlides = [
  Slide1, Slide2, Slide3, Slide4, Slide5, Slide6, 
  Slide7, Slide8, Slide9, Slide10, Slide11, Slide12
];