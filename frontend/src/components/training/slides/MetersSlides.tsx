import React from 'react';
import { Gauge, Activity, Zap, AlertCircle, TrendingUp, Settings, Users, BarChart3, Search, Filter } from 'lucide-react';
import { SlidePageIndicator } from '../SlidePageIndicator';

const Slide1 = () => (
  <div className=" relative h-full bg-gradient-to-br from-blue-50 to-white p-16 flex flex-col justify-center">
    <div>
      <h1 className="text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600 mb-4 text-center">
        Meter Management
      </h1>
      <h2 className="text-3xl text-gray-700 mb-12 text-center">Managing 520 meters across Tower A/B/C, Podium, and Car Park</h2>
      
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h3 className="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold mr-3">
              <span className="text-sm">i</span>
            </div>
            Topics Covered
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">1.</span>
              <p className="text-gray-700">Meter hierarchy structure</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">2.</span>
              <p className="text-gray-700">Search and filter meters</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">3.</span>
              <p className="text-gray-700">Real-time monitoring</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">4.</span>
              <p className="text-gray-700">Meter status indicators</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">5.</span>
              <p className="text-gray-700">Detailed meter views</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">6.</span>
              <p className="text-gray-700">Historical data access</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">7.</span>
              <p className="text-gray-700">Virtual meters concept</p>
            </div>
            <div className="flex items-start">
              <span className="text-blue-600 mr-2 mt-1">8.</span>
              <p className="text-gray-700">Best practices</p>
            </div>
          </div>
        </div>
        
        <div className="flex justify-center gap-6">
          <div className="text-center">
            <div className="bg-blue-100 rounded-full p-4 inline-flex mb-2">
              <Gauge size={32} className="text-blue-600" />
            </div>
            <p className="text-sm font-medium">520 Meters</p>
          </div>
          <div className="text-center">
            <div className="bg-green-100 rounded-full p-4 inline-flex mb-2">
              <Activity size={32} className="text-green-600" />
            </div>
            <p className="text-sm font-medium">Real-time Data</p>
          </div>
          <div className="text-center">
            <div className="bg-purple-100 rounded-full p-4 inline-flex mb-2">
              <Zap size={32} className="text-purple-600" />
            </div>
            <p className="text-sm font-medium">Power Monitoring</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meters Page" feature="Introduction" />
  </div>
);

const Slide2 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Meter Hierarchy</h2>
    <div className="flex gap-8">
      <div className="flex-1">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
          <h3 className="text-2xl font-semibold mb-6">Building Structure</h3>
          <div className="space-y-4">
            <div className="flex items-center gap-4 bg-white rounded-lg p-4">
              <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
              <div>
                <p className="font-medium">Main Meter</p>
                <p className="text-sm text-gray-600">Building total consumption</p>
              </div>
            </div>
            <div className="flex items-center gap-4 bg-white rounded-lg p-4 ml-8">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <div>
                <p className="font-medium">Tower Meters</p>
                <p className="text-sm text-gray-600">Tower A, B, C distribution</p>
              </div>
            </div>
            <div className="flex items-center gap-4 bg-white rounded-lg p-4 ml-16">
              <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
              <div>
                <p className="font-medium">Floor Meters</p>
                <p className="text-sm text-gray-600">All floors with detailed monitoring</p>
              </div>
            </div>
            <div className="flex items-center gap-4 bg-white rounded-lg p-4 ml-24">
              <div className="w-4 h-4 bg-purple-500 rounded-full"></div>
              <div>
                <p className="font-medium">Tenant Meters</p>
                <p className="text-sm text-gray-600">Individual tenant usage</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex-1">
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8">
          <h3 className="text-2xl font-semibold mb-6">Equipment Types</h3>
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium mb-2">HVAC Systems</h4>
              <p className="text-sm text-gray-600">Chillers, cooling towers, AHUs</p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium mb-2">Lighting Systems</h4>
              <p className="text-sm text-gray-600">Common area and emergency lighting</p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium mb-2">Data Center</h4>
              <p className="text-sm text-gray-600">Servers, network equipment, UPS</p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-medium mb-2">Other Equipment</h4>
              <p className="text-sm text-gray-600">Elevators, pumps, general power</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meters Page" feature="Meter Management" />
  </div>
);

const Slide3 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Meter Tree View</h2>
    <div className="bg-gray-50 rounded-xl p-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 text-gray-400" size={20} />
            <input 
              type="text" 
              placeholder="Search meters..." 
              className="pl-10 pr-4 py-2 border rounded-lg bg-white"
              disabled
            />
          </div>
          <button className="flex items-center gap-2 px-4 py-2 bg-white border rounded-lg">
            <Filter size={16} />
            <span>Filters</span>
          </button>
        </div>
        <div className="flex gap-2">
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg">Tree View</button>
          <button className="px-4 py-2 bg-white border rounded-lg">Table View</button>
        </div>
      </div>
      
      <div className="bg-white rounded-lg overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50 border-b">
            <tr>
              <th className="text-left p-4">Meter Name</th>
              <th className="text-left p-4">Location</th>
              <th className="text-left p-4">Type</th>
              <th className="text-right p-4">Current Power</th>
              <th className="text-center p-4">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b hover:bg-gray-50">
              <td className="p-4 font-medium">TWR-A-05-01</td>
              <td className="p-4">Tower A - 5th Floor</td>
              <td className="p-4">
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm">Sub Meter</span>
              </td>
              <td className="p-4 text-right font-medium">125.4 kW</td>
              <td className="p-4 text-center">
                <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-sm">Active</span>
              </td>
            </tr>
            <tr className="border-b hover:bg-gray-50">
              <td className="p-4 font-medium">CHL-01</td>
              <td className="p-4">Chiller Plant 1</td>
              <td className="p-4">
                <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded text-sm">Equipment</span>
              </td>
              <td className="p-4 text-right font-medium">850.8 kW</td>
              <td className="p-4 text-center">
                <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-sm">Active</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <SlidePageIndicator page="Meters Page" feature="Meter List View" />
  </div>
);

const Slide4 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Real-time Monitoring</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <Activity className="text-blue-600" />
          Live Data Stream
        </h3>
        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium">Power</span>
              <span className="text-2xl font-bold text-blue-600">2,847 kW</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full" style={{width: '71%'}}></div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium">Voltage</span>
              <span className="text-xl font-semibold">230.5 V</span>
            </div>
            <p className="text-sm text-gray-600">Phase A: 230.5V | Phase B: 231.2V | Phase C: 229.8V</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">Current</span>
              <span className="text-xl font-semibold">452.3 A</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <TrendingUp className="text-green-600" />
          Performance Metrics
        </h3>
        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4">
            <p className="text-sm text-gray-600 mb-1">Power Factor</p>
            <p className="text-2xl font-bold text-green-600">0.95</p>
            <p className="text-xs text-green-600">Excellent</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <p className="text-sm text-gray-600 mb-1">Daily Consumption</p>
            <p className="text-2xl font-bold">15,847 kWh</p>
            <p className="text-xs text-gray-600">-5% vs yesterday</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <p className="text-sm text-gray-600 mb-1">Peak Demand</p>
            <p className="text-2xl font-bold text-orange-600">3,245 kW</p>
            <p className="text-xs text-gray-600">at 14:30</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meters Page" feature="Meter Details" />
  </div>
);

const Slide5 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Meter Status Indicators</h2>
    <div className="grid grid-cols-3 gap-8">
      <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8">
        <div className="flex items-center justify-center mb-6">
          <div className="w-24 h-24 rounded-full flex items-center justify-center animate-pulse" style={{ backgroundColor: '#059669' }}>
            <Activity size={48} style={{ color: '#ffffff' }} />
          </div>
        </div>
        <h3 className="text-2xl font-semibold text-center mb-4">Active</h3>
        <ul className="space-y-2 text-sm">
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Normal operation</span>
          </li>
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Data flowing</span>
          </li>
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>Within thresholds</span>
          </li>
        </ul>
      </div>
      
      <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-8">
        <div className="flex items-center justify-center mb-6">
          <div className="w-24 h-24 rounded-full flex items-center justify-center animate-pulse" style={{ backgroundColor: '#d97706' }}>
            <AlertCircle size={48} style={{ color: '#ffffff' }} />
          </div>
        </div>
        <h3 className="text-2xl font-semibold text-center mb-4">Warning</h3>
        <ul className="space-y-2 text-sm">
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span>Approaching limits</span>
          </li>
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span>Irregular patterns</span>
          </li>
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span>Needs attention</span>
          </li>
        </ul>
      </div>
      
      <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-8">
        <div className="flex items-center justify-center mb-6">
          <div className="w-24 h-24 rounded-full flex items-center justify-center" style={{ backgroundColor: '#dc2626' }}>
            <Zap size={48} style={{ color: '#ffffff' }} />
          </div>
        </div>
        <h3 className="text-2xl font-semibold text-center mb-4">Offline</h3>
        <ul className="space-y-2 text-sm">
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>No data received</span>
          </li>
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>Communication lost</span>
          </li>
          <li className="flex items-center gap-2">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>Immediate action</span>
          </li>
        </ul>
      </div>
    </div>
    <SlidePageIndicator page="Meters Page" feature="Real-time Data" />
  </div>
);

const Slide6 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Meter Details View</h2>
    <div className="bg-gray-50 rounded-xl p-8">
      <div className="grid grid-cols-3 gap-8">
        <div className="col-span-2">
          <div className="bg-white rounded-lg p-6 mb-6">
            <h3 className="text-xl font-semibold mb-4">Meter Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Meter ID</p>
                <p className="font-medium">MTR-TWR-A-05-01</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Location</p>
                <p className="font-medium">Tower A - Floor 5</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Type</p>
                <p className="font-medium">Digital Power Meter</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Model</p>
                <p className="font-medium">Schneider PM5320</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Live Chart</h3>
            <div className="h-48 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg flex items-center justify-center">
              <BarChart3 size={64} className="text-blue-400" />
            </div>
          </div>
        </div>
        
        <div>
          <div className="bg-white rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Current Readings</h3>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Power</p>
                <p className="text-2xl font-bold text-blue-600">125.4 kW</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Energy Today</p>
                <p className="text-xl font-semibold">847.3 kWh</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Peak Demand</p>
                <p className="text-xl font-semibold text-orange-600">152.8 kW</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Power Factor</p>
                <p className="text-xl font-semibold text-green-600">0.94</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meters Page" feature="Meter Hierarchy" />
  </div>
);

const Slide7 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Virtual Meters</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <Settings className="text-purple-600" />
          What are Virtual Meters?
        </h3>
        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Calculated Values</h4>
            <p className="text-sm text-gray-600">
              Virtual meters calculate values based on other physical meters
            </p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Example: Data Center</h4>
            <p className="text-sm text-gray-600">
              Total Power - (Tower A + Tower B + Tower C + Chiller + Tenant)
            </p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Cost Allocation</h4>
            <p className="text-sm text-gray-600">
              Useful for areas without physical meters
            </p>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <Users className="text-blue-600" />
          Creating Virtual Meters
        </h3>
        <div className="bg-white rounded-lg p-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Name</label>
              <input 
                type="text" 
                className="w-full p-2 border rounded-lg" 
                placeholder="e.g., Common Area Lighting"
                disabled
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Formula</label>
              <select className="w-full p-2 border rounded-lg" disabled>
                <option>Sum of selected meters</option>
                <option>Difference calculation</option>
                <option>Percentage of parent</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Selected Meters</label>
              <div className="space-y-2">
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked disabled />
                  <span className="text-sm">Floor 1 Lighting</span>
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked disabled />
                  <span className="text-sm">Floor 2 Lighting</span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meters Page" feature="Virtual Meters" />
  </div>
);

const Slide8 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Meter Groups</h2>
    <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8">
      <div className="grid grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg p-6 border-2 border-blue-200">
          <h3 className="text-lg font-semibold mb-2 text-blue-700">Tower A Group</h3>
          <p className="text-3xl font-bold mb-2">847.5 kW</p>
          <p className="text-sm text-gray-600">All floors with sub-meters</p>
        </div>
        <div className="bg-white rounded-lg p-6 border-2 border-green-200">
          <h3 className="text-lg font-semibold mb-2 text-green-700">HVAC Systems</h3>
          <p className="text-3xl font-bold mb-2">1,250.3 kW</p>
          <p className="text-sm text-gray-600">12 meters • All equipment</p>
        </div>
        <div className="bg-white rounded-lg p-6 border-2 border-purple-200">
          <h3 className="text-lg font-semibold mb-2 text-purple-700">Podium & Car Park</h3>
          <p className="text-3xl font-bold mb-2">325.8 kW</p>
          <p className="text-sm text-gray-600">Retail and parking meters</p>
        </div>
      </div>
      
      <div className="bg-white rounded-lg p-6">
        <h3 className="text-xl font-semibold mb-4">Group Management Benefits</h3>
        <div className="grid grid-cols-2 gap-4">
          <ul className="space-y-2">
            <li className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5"></div>
              <span>Organize meters by location or function</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5"></div>
              <span>Compare group performance</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5"></div>
              <span>Bulk operations and settings</span>
            </li>
          </ul>
          <ul className="space-y-2">
            <li className="flex items-start gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-1.5"></div>
              <span>Aggregated reporting</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-1.5"></div>
              <span>Simplified monitoring</span>
            </li>
            <li className="flex items-start gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-1.5"></div>
              <span>Custom alert rules per group</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meters Page" feature="Meter Configuration" />
  </div>
);

const Slide9 = () => (
  <div className=" relative h-full bg-gradient-to-br from-blue-50 to-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Best Practices</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="space-y-6">
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">📊</span>
            Regular Monitoring
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Check meter status daily</li>
            <li>• Review offline meters immediately</li>
            <li>• Monitor unusual consumption patterns</li>
            <li>• Verify data accuracy weekly</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">🔧</span>
            Maintenance
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Schedule regular calibration</li>
            <li>• Keep meter firmware updated</li>
            <li>• Document all changes</li>
            <li>• Test communication monthly</li>
          </ul>
        </div>
      </div>
      
      <div className="space-y-6">
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">📁</span>
            Organization
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Use consistent naming conventions</li>
            <li>• Group meters logically</li>
            <li>• Maintain accurate location data</li>
            <li>• Update meter metadata promptly</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">⚡</span>
            Performance
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Set appropriate thresholds</li>
            <li>• Use virtual meters wisely</li>
            <li>• Optimize data collection intervals</li>
            <li>• Archive old data regularly</li>
          </ul>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meters Page" feature="Thresholds & Alerts" />
  </div>
);

const Slide10 = () => (
  <div 
    className="h-full p-16 flex flex-col justify-center"
    style={{ 
      background: 'linear-gradient(to bottom right, #1e40af, #0891b2)',
      color: '#ffffff'
    }}
  >
    <div className="text-center">
      <div 
        className="rounded-full w-32 h-32 mx-auto mb-8 flex items-center justify-center"
        style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', backdropFilter: 'blur(12px)' }}
      >
        <Gauge size={64} style={{ color: '#ffffff' }} />
      </div>
      <h1 className="text-5xl font-bold mb-6" style={{ color: '#ffffff' }}>Meter Management Complete!</h1>
      <p className="text-2xl mb-12" style={{ color: '#dbeafe' }}>You're ready to monitor and manage your building's meters</p>
      
      <div 
        className="rounded-xl p-8 max-w-3xl mx-auto"
        style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', backdropFilter: 'blur(12px)' }}
      >
        <h3 className="text-2xl font-semibold mb-6" style={{ color: '#ffffff' }}>Key Takeaways</h3>
        <div className="grid grid-cols-2 gap-6 text-left">
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">1</span>
            </div>
            <p style={{ color: '#ffffff' }}>Understand meter hierarchy and organization structure</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">2</span>
            </div>
            <p style={{ color: '#ffffff' }}>Monitor real-time data and meter status</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">3</span>
            </div>
            <p style={{ color: '#ffffff' }}>Create virtual meters for calculated values</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">4</span>
            </div>
            <p style={{ color: '#ffffff' }}>Follow best practices for maintenance and organization</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Meters Page" feature="Historical Data" />
  </div>
);

export const metersSlides = [
  Slide1, Slide2, Slide3, Slide4, Slide5, 
  Slide6, Slide7, Slide8, Slide9, Slide10
];