import React from 'react';
import { Settings, Users, Shield, Mail, Database, Key, UserCheck, FileText, ToggleRight } from 'lucide-react';
import { SlidePageIndicator } from '../SlidePageIndicator';

const Slide1 = () => (
  <div className=" relative h-full bg-gradient-to-br from-gray-50 to-white p-16 flex flex-col justify-center">
    <div className="text-center">
      <h1 className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-gray-600 to-gray-800 mb-8">
        System Settings
      </h1>
      <p className="text-2xl text-gray-600 mb-12">Configure system preferences and user management</p>
      <div className="flex justify-center gap-8">
        <div className="text-center">
          <div className="bg-gray-100 rounded-full p-6 inline-flex mb-4">
            <Users size={48} className="text-gray-600" />
          </div>
          <p className="text-lg font-medium">User Management</p>
        </div>
        <div className="text-center">
          <div className="bg-blue-100 rounded-full p-6 inline-flex mb-4">
            <Shield size={48} className="text-blue-600" />
          </div>
          <p className="text-lg font-medium">Permissions</p>
        </div>
        <div className="text-center">
          <div className="bg-green-100 rounded-full p-6 inline-flex mb-4">
            <Settings size={48} className="text-green-600" />
          </div>
          <p className="text-lg font-medium">System Config</p>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Settings Page" feature="Introduction" />
  </div>
);

const Slide2 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">User Management Overview</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6">User Roles</h3>
        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium text-blue-700 mb-2">Administrator</h4>
            <p className="text-sm text-gray-600">Full system access, user management, all configurations</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium text-green-700 mb-2">Manager</h4>
            <p className="text-sm text-gray-600">View all data, create reports, manage alerts</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium text-orange-700 mb-2">Operator</h4>
            <p className="text-sm text-gray-600">Monitor systems, acknowledge alarms, view reports</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium text-gray-700 mb-2">Viewer</h4>
            <p className="text-sm text-gray-600">Read-only access to dashboards and reports</p>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6">User List</h3>
        <div className="bg-white rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="text-left p-3 text-sm">Name</th>
                <th className="text-left p-3 text-sm">Role</th>
                <th className="text-center p-3 text-sm">Status</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="p-3">
                  <div className="font-medium">John Admin</div>
                  <div className="text-xs text-gray-600"><EMAIL></div>
                </td>
                <td className="p-3">
                  <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm">Admin</span>
                </td>
                <td className="p-3 text-center">
                  <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-sm">Active</span>
                </td>
              </tr>
              <tr className="border-b">
                <td className="p-3">
                  <div className="font-medium">Sarah Manager</div>
                  <div className="text-xs text-gray-600"><EMAIL></div>
                </td>
                <td className="p-3">
                  <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-sm">Manager</span>
                </td>
                <td className="p-3 text-center">
                  <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-sm">Active</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Settings Page" feature="User Management Tab" />
  </div>
);

const Slide3 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Creating New Users</h2>
    <div className="bg-gray-50 rounded-xl p-8">
      <div className="grid grid-cols-2 gap-8">
        <div>
          <h3 className="text-2xl font-semibold mb-6">User Information</h3>
          <div className="bg-white rounded-lg p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Full Name</label>
              <input type="text" className="w-full p-2 border rounded-lg" placeholder="Enter full name" disabled />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Email Address</label>
              <input type="email" className="w-full p-2 border rounded-lg" placeholder="<EMAIL>" disabled />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Role</label>
              <select className="w-full p-2 border rounded-lg" disabled>
                <option>Select role...</option>
                <option>Administrator</option>
                <option>Manager</option>
                <option>Operator</option>
                <option>Viewer</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Department</label>
              <input type="text" className="w-full p-2 border rounded-lg" placeholder="e.g., Operations" disabled />
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-2xl font-semibold mb-6">Access Settings</h3>
          <div className="space-y-4">
            <div className="bg-white rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Send Welcome Email</span>
                <ToggleRight className="text-blue-600" size={24} />
              </div>
              <p className="text-sm text-gray-600">User receives login credentials via email</p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Require Password Change</span>
                <ToggleRight className="text-blue-600" size={24} />
              </div>
              <p className="text-sm text-gray-600">Force password reset on first login</p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium">Enable 2FA</span>
                <ToggleRight className="text-gray-400" size={24} />
              </div>
              <p className="text-sm text-gray-600">Two-factor authentication for enhanced security</p>
            </div>
          </div>
          <button className="w-full mt-6 px-4 py-2 bg-blue-600 text-white rounded-lg font-medium">
            Create User
          </button>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Settings Page" feature="User Roles" />
  </div>
);

const Slide4 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Permission Matrix</h2>
    <div className="bg-gray-50 rounded-xl p-8">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b">
              <th className="text-left p-4">Feature</th>
              <th className="text-center p-4 text-blue-600">Admin</th>
              <th className="text-center p-4 text-green-600">Manager</th>
              <th className="text-center p-4 text-orange-600">Operator</th>
              <th className="text-center p-4 text-gray-600">Viewer</th>
            </tr>
          </thead>
          <tbody className="bg-white">
            <tr className="border-b">
              <td className="p-4 font-medium">View Dashboard</td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-green-500 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-green-500 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-green-500 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-green-500 rounded-full mx-auto"></div></td>
            </tr>
            <tr className="border-b">
              <td className="p-4 font-medium">Manage Users</td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-green-500 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-gray-300 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-gray-300 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-gray-300 rounded-full mx-auto"></div></td>
            </tr>
            <tr className="border-b">
              <td className="p-4 font-medium">Configure Alerts</td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-green-500 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-green-500 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-gray-300 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-gray-300 rounded-full mx-auto"></div></td>
            </tr>
            <tr className="border-b">
              <td className="p-4 font-medium">Export Reports</td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-green-500 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-green-500 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-green-500 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-gray-300 rounded-full mx-auto"></div></td>
            </tr>
            <tr className="border-b">
              <td className="p-4 font-medium">System Settings</td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-green-500 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-gray-300 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-gray-300 rounded-full mx-auto"></div></td>
              <td className="text-center p-4"><div className="w-5 h-5 bg-gray-300 rounded-full mx-auto"></div></td>
            </tr>
          </tbody>
        </table>
      </div>
      <div className="flex items-center gap-4 mt-6 justify-center">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-green-500 rounded-full"></div>
          <span className="text-sm">Allowed</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
          <span className="text-sm">Not Allowed</span>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Settings Page" feature="Permissions" />
  </div>
);

const Slide5 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Email Settings</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <Mail className="text-blue-600" />
          SMTP Configuration
        </h3>
        <div className="bg-white rounded-lg p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">SMTP Server</label>
            <input type="text" className="w-full p-2 border rounded-lg" value="smtp.company.com" disabled />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Port</label>
            <input type="text" className="w-full p-2 border rounded-lg" value="587" disabled />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Username</label>
            <input type="text" className="w-full p-2 border rounded-lg" value="<EMAIL>" disabled />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Encryption</label>
            <select className="w-full p-2 border rounded-lg" disabled>
              <option>TLS</option>
              <option>SSL</option>
              <option>None</option>
            </select>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6">Email Templates</h3>
        <div className="space-y-3">
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Welcome Email</h4>
            <p className="text-sm text-gray-600">Sent to new users with login credentials</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Alert Notifications</h4>
            <p className="text-sm text-gray-600">Critical alerts and warnings template</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Daily Report</h4>
            <p className="text-sm text-gray-600">Automated daily summary email</p>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-2">Password Reset</h4>
            <p className="text-sm text-gray-600">Security link for password recovery</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Settings Page" feature="Meter Settings Tab" />
  </div>
);

const Slide6 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">System Configuration</h2>
    <div className="grid grid-cols-3 gap-6">
      <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-4">
          <Database className="text-orange-600" size={32} />
          <h3 className="text-xl font-semibold">Data Retention</h3>
        </div>
        <div className="space-y-3">
          <div className="bg-white rounded-lg p-3">
            <p className="text-sm font-medium">Raw Data</p>
            <p className="text-lg font-bold text-orange-600">30 days</p>
          </div>
          <div className="bg-white rounded-lg p-3">
            <p className="text-sm font-medium">Hourly Aggregates</p>
            <p className="text-lg font-bold text-orange-600">1 year</p>
          </div>
          <div className="bg-white rounded-lg p-3">
            <p className="text-sm font-medium">Daily Summary</p>
            <p className="text-lg font-bold text-orange-600">5 years</p>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-4">
          <Shield className="text-purple-600" size={32} />
          <h3 className="text-xl font-semibold">Security Settings</h3>
        </div>
        <div className="space-y-3">
          <div className="bg-white rounded-lg p-3 flex items-center justify-between">
            <span className="text-sm font-medium">Password Complexity</span>
            <span className="text-sm text-green-600">Strong</span>
          </div>
          <div className="bg-white rounded-lg p-3 flex items-center justify-between">
            <span className="text-sm font-medium">Session Timeout</span>
            <span className="text-sm">30 min</span>
          </div>
          <div className="bg-white rounded-lg p-3 flex items-center justify-between">
            <span className="text-sm font-medium">Failed Login Limit</span>
            <span className="text-sm">5 attempts</span>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-4">
          <FileText className="text-green-600" size={32} />
          <h3 className="text-xl font-semibold">Report Settings</h3>
        </div>
        <div className="space-y-3">
          <div className="bg-white rounded-lg p-3 flex items-center justify-between">
            <span className="text-sm font-medium">Default Format</span>
            <span className="text-sm">PDF</span>
          </div>
          <div className="bg-white rounded-lg p-3 flex items-center justify-between">
            <span className="text-sm font-medium">Logo</span>
            <span className="text-sm text-green-600">Uploaded</span>
          </div>
          <div className="bg-white rounded-lg p-3 flex items-center justify-between">
            <span className="text-sm font-medium">Auto-generation</span>
            <span className="text-sm text-green-600">Enabled</span>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Settings Page" feature="Meter Configuration" />
  </div>
);

const Slide7 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Audit Trail</h2>
    <div className="bg-gray-50 rounded-xl p-8">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold">Recent System Changes</h3>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm">Export Log</button>
      </div>
      <div className="bg-white rounded-lg overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50 border-b">
            <tr>
              <th className="text-left p-4 text-sm">Time</th>
              <th className="text-left p-4 text-sm">User</th>
              <th className="text-left p-4 text-sm">Action</th>
              <th className="text-left p-4 text-sm">Details</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b hover:bg-gray-50">
              <td className="p-4 text-sm">10:45 AM</td>
              <td className="p-4 text-sm">
                <div className="font-medium">John Admin</div>
              </td>
              <td className="p-4 text-sm">
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">User Created</span>
              </td>
              <td className="p-4 text-sm text-gray-600">Added new operator: Mike O.</td>
            </tr>
            <tr className="border-b hover:bg-gray-50">
              <td className="p-4 text-sm">09:30 AM</td>
              <td className="p-4 text-sm">
                <div className="font-medium">Sarah Manager</div>
              </td>
              <td className="p-4 text-sm">
                <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">Setting Changed</span>
              </td>
              <td className="p-4 text-sm text-gray-600">Updated data retention: 30 &rarr; 45 days</td>
            </tr>
            <tr className="border-b hover:bg-gray-50">
              <td className="p-4 text-sm">Yesterday</td>
              <td className="p-4 text-sm">
                <div className="font-medium">System</div>
              </td>
              <td className="p-4 text-sm">
                <span className="px-2 py-1 bg-orange-100 text-orange-700 rounded text-xs">Backup</span>
              </td>
              <td className="p-4 text-sm text-gray-600">Automated backup completed</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <SlidePageIndicator page="Settings Page" feature="Email Settings Tab" />
  </div>
);

const Slide8 = () => (
  <div className=" relative h-full bg-gradient-to-br from-blue-50 to-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Best Practices</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="space-y-6">
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">👥</span>
            User Management
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Review user access quarterly</li>
            <li>• Remove inactive accounts promptly</li>
            <li>• Use role-based access control</li>
            <li>• Enable 2FA for admin accounts</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">🔐</span>
            Security
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Enforce strong passwords</li>
            <li>• Regular security audits</li>
            <li>• Monitor failed login attempts</li>
            <li>• Keep audit logs for compliance</li>
          </ul>
        </div>
      </div>
      
      <div className="space-y-6">
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">⚙️</span>
            Configuration
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Document all changes</li>
            <li>• Test email settings regularly</li>
            <li>• Backup before major changes</li>
            <li>• Plan data retention carefully</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">📋</span>
            Compliance
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Maintain audit trails</li>
            <li>• Regular access reviews</li>
            <li>• Data privacy compliance</li>
            <li>• Document procedures</li>
          </ul>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Settings Page" feature="Notification Setup" />
  </div>
);

const Slide9 = () => (
  <div 
    className="h-full p-16 flex flex-col justify-center"
    style={{ 
      background: 'linear-gradient(to bottom right, #374151, #1f2937)',
      color: '#ffffff'
    }}
  >
    <div className="text-center">
      <div 
        className="rounded-full w-32 h-32 mx-auto mb-8 flex items-center justify-center"
        style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', backdropFilter: 'blur(12px)' }}
      >
        <Settings size={64} style={{ color: '#ffffff' }} />
      </div>
      <h1 className="text-5xl font-bold mb-6" style={{ color: '#ffffff' }}>System Settings Complete!</h1>
      <p className="text-2xl mb-12" style={{ color: '#d1d5db' }}>You're ready to manage users and configure the system</p>
      
      <div 
        className="rounded-xl p-8 max-w-3xl mx-auto"
        style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', backdropFilter: 'blur(12px)' }}
      >
        <h3 className="text-2xl font-semibold mb-6" style={{ color: '#ffffff' }}>Key Takeaways</h3>
        <div className="grid grid-cols-2 gap-6 text-left">
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">1</span>
            </div>
            <p style={{ color: '#ffffff' }}>Manage users with appropriate roles and permissions</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">2</span>
            </div>
            <p style={{ color: '#ffffff' }}>Configure email and system settings properly</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">3</span>
            </div>
            <p style={{ color: '#ffffff' }}>Maintain security and audit trails</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">4</span>
            </div>
            <p style={{ color: '#ffffff' }}>Follow best practices for compliance</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Settings Page" feature="System Settings" />
  </div>
);

export const settingsSlides = [
  Slide1, Slide2, Slide3, Slide4, Slide5, Slide6, Slide7, Slide8, Slide9
];