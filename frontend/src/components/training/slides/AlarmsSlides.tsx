import React from 'react';
import { AlertCircle, Bell, Mail, Shield, Clock, History, Settings, CheckCircle } from 'lucide-react';
import { SlidePageIndicator } from '../SlidePageIndicator';

const Slide1 = () => (
  <div className=" relative h-full bg-gradient-to-br from-red-50 to-white p-16 flex flex-col justify-center">
    <div className="text-center">
      <h1 className="text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-orange-600 mb-8">
        Alarms & Notifications
      </h1>
      <p className="text-2xl text-gray-600 mb-12">Configure and manage system alerts effectively</p>
      <div className="flex justify-center gap-8">
        <div className="text-center">
          <div className="bg-red-100 rounded-full p-6 inline-flex mb-4">
            <AlertCircle size={48} className="text-red-600" />
          </div>
          <p className="text-lg font-medium">Real-time Alerts</p>
        </div>
        <div className="text-center">
          <div className="bg-orange-100 rounded-full p-6 inline-flex mb-4">
            <Mail size={48} className="text-orange-600" />
          </div>
          <p className="text-lg font-medium">Email Notifications</p>
        </div>
        <div className="text-center">
          <div className="bg-yellow-100 rounded-full p-6 inline-flex mb-4">
            <Shield size={48} className="text-yellow-600" />
          </div>
          <p className="text-lg font-medium">Threshold Protection</p>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Alarms Page" feature="Introduction" />
  </div>
);

const Slide2 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Understanding Alarm Severity</h2>
    <div className="grid grid-cols-3 gap-8">
      <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-8">
        <div className="flex items-center justify-center mb-6">
          <div className="w-24 h-24 rounded-full flex items-center justify-center animate-pulse" style={{ backgroundColor: '#dc2626' }}>
            <AlertCircle size={48} style={{ color: '#ffffff' }} />
          </div>
        </div>
        <h3 className="text-2xl font-semibold text-center mb-4 text-red-700">Critical</h3>
        <ul className="space-y-2 text-sm">
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-red-500 rounded-full mt-1.5"></div>
            <span>Immediate action required</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-red-500 rounded-full mt-1.5"></div>
            <span>System failure or outage</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-red-500 rounded-full mt-1.5"></div>
            <span>Safety concerns</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-red-500 rounded-full mt-1.5"></div>
            <span>24/7 notifications</span>
          </li>
        </ul>
      </div>
      
      <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-8">
        <div className="flex items-center justify-center mb-6">
          <div className="w-24 h-24 rounded-full flex items-center justify-center" style={{ backgroundColor: '#ea580c' }}>
            <Bell size={48} style={{ color: '#ffffff' }} />
          </div>
        </div>
        <h3 className="text-2xl font-semibold text-center mb-4 text-orange-700">Warning</h3>
        <ul className="space-y-2 text-sm">
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full mt-1.5"></div>
            <span>Approaching thresholds</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full mt-1.5"></div>
            <span>Performance degradation</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full mt-1.5"></div>
            <span>Preventive action needed</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-orange-500 rounded-full mt-1.5"></div>
            <span>Business hours alerts</span>
          </li>
        </ul>
      </div>
      
      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
        <div className="flex items-center justify-center mb-6">
          <div className="w-24 h-24 rounded-full flex items-center justify-center" style={{ backgroundColor: '#2563eb' }}>
            <Shield size={48} style={{ color: '#ffffff' }} />
          </div>
        </div>
        <h3 className="text-2xl font-semibold text-center mb-4 text-blue-700">Info</h3>
        <ul className="space-y-2 text-sm">
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5"></div>
            <span>System notifications</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5"></div>
            <span>Status updates</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5"></div>
            <span>Maintenance reminders</span>
          </li>
          <li className="flex items-start gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5"></div>
            <span>Daily summaries</span>
          </li>
        </ul>
      </div>
    </div>
    <SlidePageIndicator page="Alarms Page" feature="Active Alarms Tab" />
  </div>
);

const Slide3 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Creating Alarm Rules</h2>
    <div className="bg-gray-50 rounded-xl p-8">
      <div className="grid grid-cols-2 gap-8">
        <div>
          <h3 className="text-2xl font-semibold mb-6">Rule Configuration</h3>
          <div className="bg-white rounded-lg p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Rule Name</label>
              <input 
                type="text" 
                className="w-full p-2 border rounded-lg" 
                placeholder="High Power Consumption Alert"
                disabled
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Metric</label>
              <select className="w-full p-2 border rounded-lg" disabled>
                <option>Power (kW)</option>
                <option>Energy (kWh)</option>
                <option>Voltage (V)</option>
                <option>Current (A)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Condition</label>
              <div className="flex gap-2">
                <select className="flex-1 p-2 border rounded-lg" disabled>
                  <option>Greater than</option>
                  <option>Less than</option>
                  <option>Equal to</option>
                </select>
                <input 
                  type="number" 
                  className="flex-1 p-2 border rounded-lg" 
                  placeholder="3000"
                  disabled
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Severity</label>
              <div className="flex gap-2">
                <button className="px-4 py-2 bg-red-600 text-white rounded-lg">Critical</button>
                <button className="px-4 py-2 bg-white border rounded-lg">Warning</button>
                <button className="px-4 py-2 bg-white border rounded-lg">Info</button>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <h3 className="text-2xl font-semibold mb-6">Common Rules</h3>
          <div className="space-y-3">
            <div className="bg-white rounded-lg p-4 border-l-4 border-red-500">
              <h4 className="font-medium text-red-700">Power Overload</h4>
              <p className="text-sm text-gray-600 mt-1">When power &gt; 3500 kW for 5 minutes</p>
            </div>
            <div className="bg-white rounded-lg p-4 border-l-4 border-orange-500">
              <h4 className="font-medium text-orange-700">High Consumption</h4>
              <p className="text-sm text-gray-600 mt-1">Daily energy &gt; 25,000 kWh</p>
            </div>
            <div className="bg-white rounded-lg p-4 border-l-4 border-yellow-500">
              <h4 className="font-medium text-yellow-700">Low Power Factor</h4>
              <p className="text-sm text-gray-600 mt-1">Power factor &lt; 0.85</p>
            </div>
            <div className="bg-white rounded-lg p-4 border-l-4 border-blue-500">
              <h4 className="font-medium text-blue-700">Meter Offline</h4>
              <p className="text-sm text-gray-600 mt-1">No data received for 15 minutes</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Alarms Page" feature="Alarm Severity" />
  </div>
);

const Slide4 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Email Notification Setup</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <Mail className="text-blue-600" />
          Recipients Management
        </h3>
        <div className="bg-white rounded-lg p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="font-medium">Operations Team</p>
                <p className="text-sm text-gray-600"><EMAIL></p>
              </div>
              <div className="flex gap-2">
                <span className="px-2 py-1 bg-red-100 text-red-700 rounded text-xs">Critical</span>
                <span className="px-2 py-1 bg-orange-100 text-orange-700 rounded text-xs">Warning</span>
              </div>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="font-medium">Facility Manager</p>
                <p className="text-sm text-gray-600"><EMAIL></p>
              </div>
              <div className="flex gap-2">
                <span className="px-2 py-1 bg-red-100 text-red-700 rounded text-xs">Critical</span>
              </div>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="font-medium">Maintenance Team</p>
                <p className="text-sm text-gray-600"><EMAIL></p>
              </div>
              <div className="flex gap-2">
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">Info</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <Clock className="text-green-600" />
          Notification Schedule
        </h3>
        <div className="space-y-4">
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-3">Critical Alerts</h4>
            <p className="text-sm text-gray-600 mb-2">Always send immediately</p>
            <div className="flex items-center gap-2 text-sm">
              <CheckCircle size={16} className="text-green-600" />
              <span>24/7 notifications enabled</span>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-3">Warning Alerts</h4>
            <p className="text-sm text-gray-600 mb-2">Business hours only</p>
            <div className="text-sm text-gray-700">
              Mon-Fri: 8:00 AM - 6:00 PM<br/>
              Sat: 9:00 AM - 1:00 PM
            </div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <h4 className="font-medium mb-3">Info Notifications</h4>
            <p className="text-sm text-gray-600 mb-2">Daily digest</p>
            <div className="text-sm text-gray-700">
              Sent at 9:00 AM daily
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Alarms Page" feature="Alarm Rules Tab" />
  </div>
);

const Slide5 = () => (
  <div className=" relative h-full bg-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Active Alarms Dashboard</h2>
    <div className="bg-gray-50 rounded-xl p-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2 px-3 py-1.5 bg-red-100 border border-red-200 rounded-lg">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-red-700">3 Critical</span>
          </div>
          <div className="flex items-center gap-2 px-3 py-1.5 bg-orange-100 border border-orange-200 rounded-lg">
            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
            <span className="text-sm font-medium text-orange-700">5 Warnings</span>
          </div>
          <div className="flex items-center gap-2 px-3 py-1.5 bg-blue-100 border border-blue-200 rounded-lg">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-sm font-medium text-blue-700">12 Info</span>
          </div>
        </div>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-lg">Acknowledge All</button>
      </div>
      
      <div className="bg-white rounded-lg overflow-hidden">
        <table className="w-full">
          <thead className="bg-gray-50 border-b">
            <tr>
              <th className="text-left p-4">Time</th>
              <th className="text-left p-4">Meter</th>
              <th className="text-left p-4">Alert</th>
              <th className="text-center p-4">Severity</th>
              <th className="text-center p-4">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b hover:bg-red-50">
              <td className="p-4">14:35:22</td>
              <td className="p-4 font-medium">Tower A - Main</td>
              <td className="p-4">Power overload: 3,852 kW</td>
              <td className="p-4 text-center">
                <span className="px-2 py-1 bg-red-100 text-red-700 rounded text-sm">Critical</span>
              </td>
              <td className="p-4 text-center">
                <span className="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-sm">Active</span>
              </td>
            </tr>
            <tr className="border-b hover:bg-orange-50">
              <td className="p-4">14:28:15</td>
              <td className="p-4 font-medium">Chiller Plant</td>
              <td className="p-4">High consumption warning</td>
              <td className="p-4 text-center">
                <span className="px-2 py-1 bg-orange-100 text-orange-700 rounded text-sm">Warning</span>
              </td>
              <td className="p-4 text-center">
                <span className="px-2 py-1 bg-green-100 text-green-700 rounded text-sm">Acknowledged</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <SlidePageIndicator page="Alarms Page" feature="Creating Rules" />
  </div>
);

const Slide6 = () => (
  <div className=" relative h-full bg-white p-16 relative">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Alarm History & Export</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6 flex items-center gap-3">
          <History className="text-purple-600" />
          Alarm History
        </h3>
        <div className="bg-white rounded-lg p-6">
          <div className="h-48 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg mb-4 flex items-center justify-center">
            <History size={64} className="text-purple-400" />
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Active Alarms</span>
              <span className="font-medium">12 alarms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Resolved Today</span>
              <span className="font-medium">35 alarms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Export Options</span>
              <span className="font-medium text-blue-600">CSV, PDF</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8">
        <h3 className="text-2xl font-semibold mb-6">Top Alarm Sources</h3>
        <div className="space-y-3">
          <div className="bg-white rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium">Tower A - HVAC</span>
              <span className="text-sm font-medium text-red-600">18 alarms</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-red-500 h-2 rounded-full" style={{width: '72%'}}></div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium">Chiller Plant</span>
              <span className="text-sm font-medium text-orange-600">12 alarms</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-orange-500 h-2 rounded-full" style={{width: '48%'}}></div>
            </div>
          </div>
          <div className="bg-white rounded-lg p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="font-medium">Tower B - Floor 5</span>
              <span className="text-sm font-medium text-yellow-600">8 alarms</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-yellow-500 h-2 rounded-full" style={{width: '32%'}}></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Alarms Page" feature="Rule Configuration" />
  </div>
);

const Slide7 = () => (
  <div className=" relative h-full bg-gradient-to-br from-blue-50 to-white p-16">
    <h2 className="text-4xl font-bold text-gray-800 mb-12">Best Practices</h2>
    <div className="grid grid-cols-2 gap-8">
      <div className="space-y-6">
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">🎯</span>
            Threshold Setting
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Base thresholds on historical data</li>
            <li>• Consider seasonal variations</li>
            <li>• Start conservative, refine over time</li>
            <li>• Different thresholds for different times</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">📧</span>
            Email Management
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Avoid notification fatigue</li>
            <li>• Group similar alerts</li>
            <li>• Clear subject lines</li>
            <li>• Include actionable information</li>
          </ul>
        </div>
      </div>
      
      <div className="space-y-6">
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">⚡</span>
            Response Procedures
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Document response steps</li>
            <li>• Define escalation paths</li>
            <li>• Regular drill exercises</li>
            <li>• Post-incident reviews</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="text-2xl">📊</span>
            Continuous Improvement
          </h3>
          <ul className="space-y-2 text-gray-700">
            <li>• Review alarm effectiveness</li>
            <li>• Eliminate false positives</li>
            <li>• Update rules regularly</li>
            <li>• Track resolution times</li>
          </ul>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Alarms Page" feature="Notification Settings" />
  </div>
);

const Slide8 = () => (
  <div 
    className="h-full p-16 flex flex-col justify-center"
    style={{ 
      background: 'linear-gradient(to bottom right, #dc2626, #ea580c)',
      color: '#ffffff'
    }}
  >
    <div className="text-center">
      <div 
        className="rounded-full w-32 h-32 mx-auto mb-8 flex items-center justify-center"
        style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', backdropFilter: 'blur(12px)' }}
      >
        <AlertCircle size={64} style={{ color: '#ffffff' }} />
      </div>
      <h1 className="text-5xl font-bold mb-6" style={{ color: '#ffffff' }}>Alarms & Notifications Complete!</h1>
      <p className="text-2xl mb-12" style={{ color: '#fee2e2' }}>You're ready to manage system alerts effectively</p>
      
      <div 
        className="rounded-xl p-8 max-w-3xl mx-auto"
        style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)', backdropFilter: 'blur(12px)' }}
      >
        <h3 className="text-2xl font-semibold mb-6" style={{ color: '#ffffff' }}>Key Takeaways</h3>
        <div className="grid grid-cols-2 gap-6 text-left">
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">1</span>
            </div>
            <p style={{ color: '#ffffff' }}>Configure rules based on critical thresholds</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">2</span>
            </div>
            <p style={{ color: '#ffffff' }}>Set up email notifications for the right people</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">3</span>
            </div>
            <p style={{ color: '#ffffff' }}>Monitor and respond to active alarms</p>
          </div>
          <div className="flex items-start gap-3">
            <div 
              className="w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}
            >
              <span className="text-xs">4</span>
            </div>
            <p style={{ color: '#ffffff' }}>Continuously improve alarm effectiveness</p>
          </div>
        </div>
      </div>
    </div>
    <SlidePageIndicator page="Alarms Page" feature="Email Templates" />
  </div>
);

export const alarmsSlides = [
  Slide1, Slide2, Slide3, Slide4, Slide5, Slide6, Slide7, Slide8
];