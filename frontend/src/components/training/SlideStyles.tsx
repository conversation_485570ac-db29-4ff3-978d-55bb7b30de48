/**
 * Consistent styles for training slides to ensure proper contrast
 */

export const slideStyles = {
  // Completion slide backgrounds - always dark with white text
  completionSlide: {
    wrapper: 'h-full p-16 flex flex-col justify-center',
    darkBlueGradient: { 
      background: 'linear-gradient(to bottom right, #1e40af, #0891b2)',
      color: '#ffffff' 
    },
    darkGreenGradient: {
      background: 'linear-gradient(to bottom right, #14532d, #059669)',
      color: '#ffffff'
    },
    darkPurpleGradient: {
      background: 'linear-gradient(to bottom right, #581c87, #c026d3)',
      color: '#ffffff'
    },
    darkRedGradient: {
      background: 'linear-gradient(to bottom right, #7f1d1d, #ea580c)',
      color: '#ffffff'
    }
  },

  // Content boxes
  boxes: {
    // For use on dark backgrounds
    whiteTransparent: 'bg-white/10 backdrop-blur-md text-white',
    whiteTransparentHover: 'bg-white/20 backdrop-blur-md text-white',
    
    // For use on light backgrounds - NEVER white text
    lightBlue: 'bg-blue-100 text-blue-900',
    lightGreen: 'bg-green-100 text-green-900',
    lightRed: 'bg-red-100 text-red-900',
    lightYellow: 'bg-yellow-100 text-yellow-900',
    lightPurple: 'bg-purple-100 text-purple-900',
  },

  // Buttons
  buttons: {
    primaryBlue: {
      className: 'px-6 py-3 rounded-lg text-lg font-medium transition-all shadow-md hover:shadow-lg',
      style: { backgroundColor: '#2563eb', color: '#ffffff' }
    },
    primaryGreen: {
      className: 'px-6 py-3 rounded-lg text-lg font-medium transition-all shadow-md hover:shadow-lg',
      style: { backgroundColor: '#059669', color: '#ffffff' }
    },
    primaryPurple: {
      className: 'px-6 py-3 rounded-lg text-lg font-medium transition-all shadow-md hover:shadow-lg',
      style: { backgroundColor: '#7c3aed', color: '#ffffff' }
    },
    primaryRed: {
      className: 'px-6 py-3 rounded-lg text-lg font-medium transition-all shadow-md hover:shadow-lg',
      style: { backgroundColor: '#dc2626', color: '#ffffff' }
    },
    secondary: {
      className: 'px-6 py-3 rounded-lg text-lg font-medium transition-all shadow-md hover:shadow-lg bg-gray-100 text-gray-700 hover:bg-gray-200',
    }
  },

  // Text on backgrounds
  headings: {
    onDark: 'text-white',
    onLight: 'text-gray-900',
    onLightMuted: 'text-gray-700'
  },

  // Status indicators
  statusCircles: {
    green: { backgroundColor: '#059669', color: '#ffffff' },
    yellow: { backgroundColor: '#d97706', color: '#ffffff' },
    red: { backgroundColor: '#dc2626', color: '#ffffff' },
    blue: { backgroundColor: '#2563eb', color: '#ffffff' },
    purple: { backgroundColor: '#7c3aed', color: '#ffffff' }
  }
};