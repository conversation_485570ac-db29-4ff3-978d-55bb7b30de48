/* Styles for slide capture to ensure proper rendering */
.slide-capture-container {
  /* Reset any global styles that might affect capture */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
}

/* Ensure slide components render at full size */
.slide-capture-container > div {
  width: 100% !important;
  height: 100% !important;
}

/* Override any height restrictions on slide content */
.slide-capture-container .h-full {
  height: 100% !important;
}

/* Ensure backgrounds render properly */
.slide-capture-container [class*="bg-gradient"] {
  background-attachment: initial !important;
}

/* Force text to render with proper colors */
.slide-capture-container * {
  -webkit-print-color-adjust: exact !important;
  print-color-adjust: exact !important;
  color-adjust: exact !important;
}