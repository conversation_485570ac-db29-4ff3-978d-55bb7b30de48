// Training Screenshots - Static images for training slides
// These would be actual screenshots captured from the running application

export const TrainingScreenshots = {
  dashboard: {
    overview: '/assets/screenshots/dashboard-overview.png',
    towers: '/assets/screenshots/dashboard-towers.png',
    loadProfile: '/assets/screenshots/dashboard-load-profile.png',
    monthlyOverview: '/assets/screenshots/dashboard-monthly.png',
    systemBreakdown: '/assets/screenshots/dashboard-breakdown.png',
  },
  analytics: {
    main: '/assets/screenshots/analytics-main.png',
    consumption: '/assets/screenshots/analytics-consumption.png',
    comparison: '/assets/screenshots/analytics-comparison.png',
    controls: '/assets/screenshots/analytics-controls.png',
    export: '/assets/screenshots/analytics-export.png',
  },
  meters: {
    management: '/assets/screenshots/meters-management.png',
    tree: '/assets/screenshots/meters-tree.png',
    details: '/assets/screenshots/meters-details.png',
    filters: '/assets/screenshots/meters-filters.png',
    virtualMeter: '/assets/screenshots/meters-virtual.png',
  },
  alarms: {
    active: '/assets/screenshots/alarms-active.png',
    rules: '/assets/screenshots/alarms-rules.png',
    createRule: '/assets/screenshots/alarms-create-rule.png',
    notifications: '/assets/screenshots/alarms-notifications.png',
    emailSettings: '/assets/screenshots/alarms-email-settings.png',
  },
  compare: {
    main: '/assets/screenshots/compare-main.png',
    selection: '/assets/screenshots/compare-selection.png',
    chart: '/assets/screenshots/compare-chart.png',
    metrics: '/assets/screenshots/compare-metrics.png',
  },
  diagram: {
    main: '/assets/screenshots/diagram-main.png',
    navigation: '/assets/screenshots/diagram-navigation.png',
    details: '/assets/screenshots/diagram-details.png',
    hierarchy: '/assets/screenshots/diagram-hierarchy.png',
  },
  settings: {
    users: '/assets/screenshots/settings-users.png',
    permissions: '/assets/screenshots/settings-permissions.png',
    system: '/assets/screenshots/settings-system.png',
    mockData: '/assets/screenshots/settings-mock-data.png',
  },
};

// Component to display screenshots in training slides
export interface ScreenshotProps {
  src: string;
  alt: string;
  caption?: string;
  className?: string;
}

export const Screenshot: React.FC<ScreenshotProps> = ({ 
  src, 
  alt, 
  caption, 
  className = '' 
}) => {
  return (
    <div className={`screenshot-container ${className}`}>
      <div className="relative rounded-lg overflow-hidden shadow-lg border border-gray-200">
        <img 
          src={src} 
          alt={alt}
          className="w-full h-auto"
          loading="lazy"
        />
      </div>
      {caption && (
        <p className="text-sm text-gray-600 mt-2 text-center">{caption}</p>
      )}
    </div>
  );
};