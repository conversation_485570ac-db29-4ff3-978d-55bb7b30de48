import React, { useState } from 'react';
import { Camera, Download, Trash2, Image, Loader2, CheckCircle } from 'lucide-react';
import { screenshotService, CapturedScreenshot } from '../../services/screenshotService';
import { Button } from '../ui/Button';

interface ScreenshotManagerProps {
  onScreenshotsCaptured?: (screenshots: Map<string, CapturedScreenshot>) => void;
}

export const ScreenshotManager: React.FC<ScreenshotManagerProps> = ({ onScreenshotsCaptured }) => {
  const [isCapturing, setIsCapturing] = useState(false);
  const [screenshots, setScreenshots] = useState<Map<string, CapturedScreenshot>>(new Map());
  const [currentAction, setCurrentAction] = useState<string>('');
  const [captureProgress, setCaptureProgress] = useState(0);

  const screenshotConfigs = [
    { page: 'dashboard', feature: 'overview', name: 'Dashboard Overview', selector: 'body' },
    { page: 'dashboard', feature: 'towers', name: 'Tower Overview', selector: '.grid.grid-cols-3' },
    { page: 'dashboard', feature: 'load-profile', name: 'Building Load Profile', selector: '[title="Building Load Profile"]' },
    { page: 'analytics', feature: 'consumption', name: 'Analytics Consumption', selector: 'body' },
    { page: 'analytics', feature: 'controls', name: 'Analytics Controls', selector: '.analytics-controls' },
    { page: 'meters', feature: 'tree', name: 'Meter Tree', selector: '.meter-tree' },
    { page: 'meters', feature: 'details', name: 'Meter Details', selector: '.meter-details' },
    { page: 'alarms', feature: 'active', name: 'Active Alarms', selector: 'body' },
    { page: 'alarms', feature: 'rules', name: 'Alarm Rules', selector: 'body' },
    { page: 'compare', feature: 'main', name: 'Meter Comparison', selector: 'body' },
    { page: 'meter-diagram', feature: 'diagram', name: 'Meter Diagram', selector: '.react-flow' },
    { page: 'settings', feature: 'users', name: 'User Management', selector: 'body' },
  ];

  const captureCurrentPage = async () => {
    setIsCapturing(true);
    setCurrentAction('Capturing current page...');
    
    try {
      const path = window.location.pathname;
      let page = 'unknown';
      let feature = 'main';

      // Determine page from URL
      if (path.includes('dashboard')) page = 'dashboard';
      else if (path.includes('analytics')) page = 'analytics';
      else if (path.includes('meters')) page = 'meters';
      else if (path.includes('alarms')) page = 'alarms';
      else if (path.includes('compare')) page = 'compare';
      else if (path.includes('meter-diagram')) page = 'meter-diagram';
      else if (path.includes('settings')) page = 'settings';

      const screenshot = await screenshotService.captureScreenshot(page, feature, {
        delay: 1000,
        removeElements: ['.screenshot-manager']
      });

      const newScreenshots = new Map(screenshots);
      newScreenshots.set(`${page}-${feature}`, screenshot);
      setScreenshots(newScreenshots);

      setCurrentAction('Screenshot captured!');
    } catch (error) {
      console.error('Failed to capture screenshot:', error);
      setCurrentAction('Failed to capture screenshot');
    } finally {
      setIsCapturing(false);
      setTimeout(() => setCurrentAction(''), 3000);
    }
  };

  const captureAllPages = async () => {
    setIsCapturing(true);
    setCaptureProgress(0);
    const newScreenshots = new Map<string, CapturedScreenshot>();

    for (let i = 0; i < screenshotConfigs.length; i++) {
      const config = screenshotConfigs[i];
      setCurrentAction(`Capturing ${config.name}...`);
      setCaptureProgress((i / screenshotConfigs.length) * 100);

      try {
        // For automatic capture, we'd need to navigate programmatically
        // For now, we'll just capture if we're on the right page
        const currentPath = window.location.pathname;
        if (currentPath.includes(config.page.replace('-', ''))) {
          const screenshot = await screenshotService.captureScreenshot(
            config.page,
            config.feature,
            {
              selector: config.selector,
              delay: 1500,
              removeElements: ['.screenshot-manager']
            }
          );
          newScreenshots.set(`${config.page}-${config.feature}`, screenshot);
        }
      } catch (error) {
        console.error(`Failed to capture ${config.name}:`, error);
      }
    }

    setScreenshots(newScreenshots);
    setCaptureProgress(100);
    setCurrentAction('All screenshots captured!');
    
    if (onScreenshotsCaptured) {
      onScreenshotsCaptured(newScreenshots);
    }

    setTimeout(() => {
      setIsCapturing(false);
      setCurrentAction('');
      setCaptureProgress(0);
    }, 2000);
  };

  const downloadScreenshot = (key: string, screenshot: CapturedScreenshot) => {
    const link = document.createElement('a');
    link.download = `ems-screenshot-${key}-${Date.now()}.png`;
    link.href = screenshot.dataUrl;
    link.click();
  };

  const downloadAllScreenshots = () => {
    screenshots.forEach((screenshot, key) => {
      downloadScreenshot(key, screenshot);
    });
  };

  const clearScreenshots = () => {
    setScreenshots(new Map());
    screenshotService.clearScreenshots();
  };

  return (
    <div className="screenshot-manager fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-xl p-4 max-w-md">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Camera size={20} />
          Screenshot Manager
        </h3>
        <span className="text-xs text-gray-500">
          {screenshots.size} screenshot{screenshots.size !== 1 ? 's' : ''}
        </span>
      </div>

      {currentAction && (
        <div className="mb-4 p-3 bg-blue-50 rounded-lg text-sm text-blue-700 flex items-center gap-2">
          {isCapturing ? <Loader2 className="animate-spin" size={16} /> : <CheckCircle size={16} />}
          {currentAction}
        </div>
      )}

      {isCapturing && captureProgress > 0 && (
        <div className="mb-4">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${captureProgress}%` }}
            />
          </div>
        </div>
      )}

      <div className="space-y-2 mb-4">
        <Button
          onClick={captureCurrentPage}
          disabled={isCapturing}
          className="w-full"
          variant="primary"
        >
          <Camera size={16} />
          Capture Current Page
        </Button>
        
        <Button
          onClick={captureAllPages}
          disabled={isCapturing}
          className="w-full"
          variant="secondary"
        >
          <Camera size={16} />
          Capture All Training Pages
        </Button>
      </div>

      {screenshots.size > 0 && (
        <>
          <div className="border-t pt-4 mb-4">
            <h4 className="text-sm font-medium mb-2">Captured Screenshots</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {Array.from(screenshots.entries()).map(([key, screenshot]) => (
                <div key={key} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div className="flex items-center gap-2">
                    <Image size={16} className="text-gray-500" />
                    <span className="text-sm">{key}</span>
                  </div>
                  <button
                    onClick={() => downloadScreenshot(key, screenshot)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <Download size={16} />
                  </button>
                </div>
              ))}
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={downloadAllScreenshots}
              variant="primary"
              className="flex-1"
            >
              <Download size={16} />
              Download All
            </Button>
            <Button
              onClick={clearScreenshots}
              variant="secondary"
              className="flex-1"
            >
              <Trash2 size={16} />
              Clear All
            </Button>
          </div>
        </>
      )}

      <div className="mt-4 text-xs text-gray-500">
        <p>Tips:</p>
        <ul className="list-disc list-inside">
          <li>Navigate to each page before capturing</li>
          <li>Wait for data to load completely</li>
          <li>Hide dev tools for cleaner screenshots</li>
        </ul>
      </div>
    </div>
  );
};