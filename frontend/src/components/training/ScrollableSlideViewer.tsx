import React, { useRef, useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ChevronLeft,
  Home,
  Presentation,
  ArrowUp,
  Printer
} from 'lucide-react';
import { ExportAllSlidesPDFButton } from './ExportAllSlidesPDF';
import { PrintInstructions } from './PrintInstructions';
import { dashboardSlides } from './slides/DashboardSlides';
import { analyticsSlides } from './slides/AnalyticsSlides';
import { metersSlides } from './slides/MetersSlides';
import { alarmsSlides } from './slides/AlarmsSlides';
import { comparisonSlides } from './slides/ComparisonSlides';
import { diagramSlides } from './slides/DiagramSlides';
import { settingsSlides } from './slides/SettingsSlides';
import { quickStartSlides } from './slides/QuickStartSlides';
import './PrintStyles.css';

const presentationMap: Record<string, { title: string; slides: React.ComponentType<{ currentSlide: number }>[] }> = {
  'overview': { title: 'Dashboard Overview', slides: dashboardSlides },
  'analytics': { title: 'Analytics & Reporting', slides: analyticsSlides },
  'meters': { title: 'Meter Management', slides: metersSlides },
  'alarms': { title: 'Alarms & Notifications', slides: alarmsSlides },
  'comparison': { title: 'Meter Comparison', slides: comparisonSlides },
  'diagram': { title: 'Electricity Meter Diagram', slides: diagramSlides },
  'settings': { title: 'System Settings', slides: settingsSlides },
  'quick-start': { title: 'Quick Start Guide', slides: quickStartSlides }
};

// Combined presentation order
const allPresentations = [
  { id: 'quick-start', title: 'Quick Start Guide', slides: quickStartSlides },
  { id: 'overview', title: 'Dashboard Overview', slides: dashboardSlides },
  { id: 'analytics', title: 'Analytics & Reporting', slides: analyticsSlides },
  { id: 'meters', title: 'Meter Management', slides: metersSlides },
  { id: 'alarms', title: 'Alarms & Notifications', slides: alarmsSlides },
  { id: 'comparison', title: 'Meter Comparison', slides: comparisonSlides },
  { id: 'diagram', title: 'Electricity Meter Diagram', slides: diagramSlides },
  { id: 'settings', title: 'System Settings', slides: settingsSlides },
];

export default function ScrollableSlideViewer() {
  const { presentationId } = useParams<{ presentationId: string }>();
  const navigate = useNavigate();
  const slideRefs = useRef<(HTMLDivElement | null)[]>([]);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [showPrintInstructions, setShowPrintInstructions] = useState(false);

  // Handle combined presentation mode
  const isCombinedMode = presentationId === 'all';
  let presentations: { title: string; slides: React.ComponentType<{ currentSlide: number }>[] }[] = [];
  
  if (isCombinedMode) {
    presentations = allPresentations;
  } else if (presentationId && presentationMap[presentationId]) {
    presentations = [presentationMap[presentationId]];
  }

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToSlide = (moduleIndex: number, slideIndex: number) => {
    let totalIndex = 0;
    for (let i = 0; i < moduleIndex; i++) {
      totalIndex += presentations[i].slides.length;
    }
    totalIndex += slideIndex;
    
    slideRefs.current[totalIndex]?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  if (!presentations || presentations.length === 0) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <p className="text-gray-500 mb-4">Presentation not found</p>
          <button
            onClick={() => navigate('/training')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Back to Training Center
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Fixed Header */}
      <div className="sticky top-0 z-40 bg-white shadow-md no-print">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/training')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Home size={20} />
              </button>
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {isCombinedMode ? 'Complete Training Course' : presentations[0].title}
                </h1>
                <p className="text-sm text-gray-500">
                  Scroll to view all slides
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={() => navigate(isCombinedMode ? '/training/all' : `/training/${presentationId}`)}
                className="inline-flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <Presentation size={16} />
                Presentation Mode
              </button>
              <button
                onClick={() => setShowPrintInstructions(true)}
                className="inline-flex items-center gap-2 px-4 py-2 rounded-lg transition-colors"
                style={{ backgroundColor: '#10b981', color: '#ffffff' }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#059669'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = '#10b981'}
              >
                <Printer size={16} />
                Print / Save as PDF
              </button>
              <ExportAllSlidesPDFButton />
            </div>
          </div>
        </div>

        {/* Module Navigation (for combined mode) */}
        {isCombinedMode && (
          <div className="px-6 pb-3 overflow-x-auto">
            <div className="flex gap-2">
              {presentations.map((module, moduleIndex) => (
                <button
                  key={moduleIndex}
                  onClick={() => scrollToSlide(moduleIndex, 0)}
                  className="px-3 py-1 text-sm bg-blue-50 text-blue-700 rounded-full hover:bg-blue-100 transition-colors whitespace-nowrap"
                >
                  {module.title}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Slides Content */}
      <div className="px-4 py-8 max-w-7xl mx-auto">
        {presentations.map((presentation, moduleIndex) => (
          <div key={moduleIndex} className="mb-16">
            {isCombinedMode && (
              <div className="mb-8 text-center">
                <h2 className="text-3xl font-bold text-gray-900 mb-2">{presentation.title}</h2>
                <div className="w-24 h-1 bg-blue-600 mx-auto rounded-full"></div>
              </div>
            )}
            
            <div className="space-y-8">
              {presentation.slides.map((SlideComponent, slideIndex) => {
                const globalIndex = presentations.slice(0, moduleIndex).reduce((acc, p) => acc + p.slides.length, 0) + slideIndex;
                return (
                  <div
                    key={slideIndex}
                    ref={el => slideRefs.current[globalIndex] = el}
                    className="slide-container bg-white rounded-xl shadow-lg overflow-hidden transform transition-all hover:shadow-xl"
                    style={{ overflow: 'hidden' }}
                  >
                    {/* Slide Content */}
                    <div className="aspect-[16/9] relative overflow-hidden">
                      <SlideComponent currentSlide={slideIndex} />
                    </div>
                    
                    {/* Slide Number */}
                    <div className="px-6 py-3" style={{ background: 'linear-gradient(to right, #2563eb, #1d4ed8)', color: '#ffffff' }}>
                      <span className="text-sm font-medium" style={{ color: '#ffffff' }}>
                        {isCombinedMode ? (
                          <>Module {moduleIndex + 1} • Slide {slideIndex + 1} of {presentation.slides.length}</>
                        ) : (
                          <>Slide {slideIndex + 1} of {presentation.slides.length}</>
                        )}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Print Instructions Modal */}
      {showPrintInstructions && (
        <PrintInstructions onClose={() => setShowPrintInstructions(false)} />
      )}

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-8 right-8 p-4 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all transform hover:scale-110 no-print"
          aria-label="Scroll to top"
        >
          <ArrowUp size={20} />
        </button>
      )}

      {/* Help Footer */}
      <div className="bg-gray-800 text-white py-8 px-6 no-print">
        <div className="max-w-4xl mx-auto text-center">
          <h3 className="text-lg font-semibold mb-2">Need Help?</h3>
          <p className="text-gray-300 mb-4">Contact our support team for assistance</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <div className="flex items-center gap-2">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
              </svg>
              <span>088-818-1020</span>
            </div>
            <div className="flex items-center gap-2">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
              <span>Monday-Friday, 9:00 AM - 6:00 PM</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}