import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ChevronLeft, 
  ChevronRight, 
  Home,
  Maximize2,
  Minimize2,
  FileDown,
  Layers
} from 'lucide-react';
import { ExportAllSlidesPDFButton } from './ExportAllSlidesPDF';
import { ExportSlidesAsImagesPDF } from './ExportSlidesAsImages';
import { dashboardSlides } from './slides/DashboardSlides';
import { analyticsSlides } from './slides/AnalyticsSlides';
import { metersSlides } from './slides/MetersSlides';
import { alarmsSlides } from './slides/AlarmsSlides';
import { comparisonSlides } from './slides/ComparisonSlides';
import { diagramSlides } from './slides/DiagramSlides';
import { settingsSlides } from './slides/SettingsSlides';
import { quickStartSlides } from './slides/QuickStartSlides';

const presentationMap: Record<string, React.ComponentType<{ currentSlide: number }>[]> = {
  'overview': dashboardSlides,
  'analytics': analyticsSlides,
  'meters': metersSlides,
  'alarms': alarmsSlides,
  'comparison': comparisonSlides,
  'diagram': diagramSlides,
  'settings': settingsSlides,
  'quick-start': quickStartSlides
};

// Combined presentation order
const allPresentations = [
  { id: 'quick-start', title: 'Quick Start Guide', slides: quickStartSlides },
  { id: 'overview', title: 'Dashboard Overview', slides: dashboardSlides },
  { id: 'analytics', title: 'Analytics & Reporting', slides: analyticsSlides },
  { id: 'meters', title: 'Meter Management', slides: metersSlides },
  { id: 'alarms', title: 'Alarms & Notifications', slides: alarmsSlides },
  { id: 'comparison', title: 'Meter Comparison', slides: comparisonSlides },
  { id: 'diagram', title: 'Electricity Meter Diagram', slides: diagramSlides },
  { id: 'settings', title: 'System Settings', slides: settingsSlides },
];

export default function PresentationViewer() {
  const { presentationId } = useParams<{ presentationId: string }>();
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Handle combined presentation mode
  const isCombinedMode = presentationId === 'all';
  let slides: React.ComponentType<{ currentSlide: number }>[] = [];
  let currentModuleInfo = { title: '', index: 0, slideIndex: 0 };

  if (isCombinedMode) {
    // Combine all slides from all modules
    slides = allPresentations.flatMap(module => module.slides);
    
    // Find current module info
    let slideCount = 0;
    for (let i = 0; i < allPresentations.length; i++) {
      const module = allPresentations[i];
      if (currentSlide < slideCount + module.slides.length) {
        currentModuleInfo = {
          title: module.title,
          index: i,
          slideIndex: currentSlide - slideCount
        };
        break;
      }
      slideCount += module.slides.length;
    }
  } else {
    slides = presentationId ? presentationMap[presentationId] : [];
  }

  const totalSlides = slides?.length || 0;

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'ArrowRight' || e.key === ' ') {
        handleNext();
      } else if (e.key === 'ArrowLeft') {
        handlePrevious();
      } else if (e.key === 'Escape') {
        setIsFullscreen(false);
      } else if (e.key === 'f') {
        toggleFullscreen();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [currentSlide]);

  const handleNext = () => {
    if (currentSlide < totalSlides - 1) {
      setCurrentSlide(currentSlide + 1);
    }
  };

  const handlePrevious = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
    }
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
    setIsFullscreen(!isFullscreen);
  };


  if (!slides || slides.length === 0) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <p className="text-gray-500 mb-4">Presentation not found</p>
          <button
            onClick={() => navigate('/training')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Back to Training Center
          </button>
        </div>
      </div>
    );
  }

  const CurrentSlideComponent = slides[currentSlide];

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50' : 'h-screen'} bg-gray-900 flex flex-col`}>
      {/* Header Controls */}
      {!isFullscreen && (
        <div className="bg-gray-800 px-6 py-3 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate('/training')}
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors text-white"
            >
              <Home size={20} />
            </button>
            <div className="text-white">
              {isCombinedMode ? (
                <div>
                  <p className="text-sm text-gray-400">
                    {currentModuleInfo.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    Slide {currentSlide + 1} of {totalSlides} (Module {currentModuleInfo.index + 1} of {allPresentations.length})
                  </p>
                </div>
              ) : (
                <p className="text-sm text-gray-400">
                  Slide {currentSlide + 1} of {totalSlides}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => navigate(isCombinedMode ? '/training/all/scroll' : `/training/${presentationId}/scroll`)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              title="Switch to scrollable view"
            >
              <Layers size={16} />
              Scrollable View
            </button>
            <ExportAllSlidesPDFButton />
            <button
              onClick={toggleFullscreen}
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors text-white"
              title="Fullscreen (F)"
            >
              <Maximize2 size={20} />
            </button>
          </div>
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex-1 relative overflow-hidden">
        {/* Slide Content */}
        <div className="h-full flex items-center justify-center p-8">
          <div className="w-full max-w-[1400px] h-full max-h-[800px] bg-white rounded-lg shadow-2xl overflow-hidden">
            <CurrentSlideComponent currentSlide={currentSlide} />
          </div>
        </div>

        {/* Navigation Buttons */}
        <button
          onClick={handlePrevious}
          disabled={currentSlide === 0}
          className={`absolute left-4 top-1/2 -translate-y-1/2 p-4 bg-black/50 text-white rounded-full hover:bg-black/70 transition-all ${
            currentSlide === 0 ? 'opacity-30 cursor-not-allowed' : ''
          }`}
        >
          <ChevronLeft size={24} />
        </button>
        <button
          onClick={handleNext}
          disabled={currentSlide === totalSlides - 1}
          className={`absolute right-4 top-1/2 -translate-y-1/2 p-4 bg-black/50 text-white rounded-full hover:bg-black/70 transition-all ${
            currentSlide === totalSlides - 1 ? 'opacity-30 cursor-not-allowed' : ''
          }`}
        >
          <ChevronRight size={24} />
        </button>

        {/* Slide Progress */}
        {isCombinedMode ? (
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 w-[80%] max-w-[600px]">
            <div className="flex items-center gap-3">
              <span className="text-xs text-gray-400">Progress</span>
              <div className="flex-1 bg-gray-300 rounded-full h-2 overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-green-500 to-blue-600 transition-all duration-300"
                  style={{ width: `${((currentSlide + 1) / totalSlides) * 100}%` }}
                />
              </div>
              <span className="text-xs text-gray-400">
                {Math.round(((currentSlide + 1) / totalSlides) * 100)}%
              </span>
            </div>
            <div className="flex gap-1 mt-2">
              {allPresentations.map((module, moduleIndex) => {
                let startSlide = 0;
                for (let i = 0; i < moduleIndex; i++) {
                  startSlide += allPresentations[i].slides.length;
                }
                const isCurrentModule = currentModuleInfo.index === moduleIndex;
                return (
                  <button
                    key={module.id}
                    onClick={() => setCurrentSlide(startSlide)}
                    className={`flex-1 h-1 rounded-full transition-all ${
                      isCurrentModule ? 'bg-blue-600' : 'bg-gray-400 hover:bg-gray-300'
                    }`}
                    title={module.title}
                  />
                );
              })}
            </div>
          </div>
        ) : (
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
            {Array.from({ length: totalSlides }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-2 h-2 rounded-full transition-all ${
                  index === currentSlide 
                    ? 'w-8 bg-blue-600' 
                    : 'bg-gray-400 hover:bg-gray-300'
                }`}
              />
            ))}
          </div>
        )}

        {/* Fullscreen Exit Button */}
        {isFullscreen && (
          <button
            onClick={toggleFullscreen}
            className="absolute top-4 right-4 p-2 bg-black/50 text-white rounded-lg hover:bg-black/70"
          >
            <Minimize2 size={20} />
          </button>
        )}
      </div>


      {/* Help Button */}
      <div className="absolute bottom-4 left-4">
        <button
          onClick={() => {
            alert('Contact Support:\n\nPhone: ************\nSupport Hours: Monday-Friday, 9:00 AM - 6:00 PM');
          }}
          className="bg-gray-800 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-colors"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
          Help
        </button>
      </div>
    </div>
  );
}