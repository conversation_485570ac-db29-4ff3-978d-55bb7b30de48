import React from 'react';
import { Printer, Info } from 'lucide-react';

interface PrintInstructionsProps {
  onClose: () => void;
}

export const PrintInstructions: React.FC<PrintInstructionsProps> = ({ onClose }) => {
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 no-print">
      <div className="bg-white rounded-xl p-6 max-w-lg w-full mx-4 shadow-2xl">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Printer className="text-blue-600" size={24} />
          </div>
          <h2 className="text-xl font-semibold">Print Instructions</h2>
        </div>
        
        <div className="space-y-4 text-sm text-gray-700">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex gap-2">
              <Info className="text-blue-600 mt-0.5 flex-shrink-0" size={16} />
              <div>
                <p className="font-medium text-blue-900 mb-1">For best results when saving as PDF:</p>
                <ol className="list-decimal list-inside space-y-1 text-blue-800">
                  <li>Press <kbd className="px-2 py-1 bg-white rounded border">⌘</kbd> + <kbd className="px-2 py-1 bg-white rounded border">P</kbd> to open print dialog</li>
                  <li>In "Destination", select "Save as PDF"</li>
                  <li>Click "More Settings" or "Show Details"</li>
                  <li>Enable "Background graphics" or "Print backgrounds"</li>
                  <li>Set "Layout" to "Landscape"</li>
                  <li>Set "Paper size" to "A4" or "Letter"</li>
                  <li>Click "Save" and choose your location</li>
                </ol>
              </div>
            </div>
          </div>
          
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <p className="text-amber-800">
              <strong>Note:</strong> Some gradients and complex backgrounds may not appear exactly as shown on screen due to browser print limitations. For a perfectly formatted PDF, use the "Export Training Manual PDF" button instead.
            </p>
          </div>
        </div>
        
        <div className="flex justify-end gap-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={() => {
              window.print();
              onClose();
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Print Now
          </button>
        </div>
      </div>
    </div>
  );
};