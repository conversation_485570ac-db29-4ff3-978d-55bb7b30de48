import React, { useState } from 'react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { FileDown } from 'lucide-react';
import './ExportSlidesStyles.css';

// Import all slide modules
import { quickStartSlides } from './slides/QuickStartSlides';
import { dashboardSlides } from './slides/DashboardSlides';
import { analyticsSlides } from './slides/AnalyticsSlides';
import { metersSlides } from './slides/MetersSlides';
import { alarmsSlides } from './slides/AlarmsSlides';
import { comparisonSlides } from './slides/ComparisonSlides';
import { diagramSlides } from './slides/DiagramSlides';
import { settingsSlides } from './slides/SettingsSlides';

// All training modules in order
const allModules = [
  { id: 'quick-start', title: 'Quick Start Guide', slides: quickStartSlides },
  { id: 'dashboard', title: 'Dashboard Overview', slides: dashboardSlides },
  { id: 'analytics', title: 'Analytics & Reporting', slides: analyticsSlides },
  { id: 'meters', title: 'Meter Management', slides: metersSlides },
  { id: 'alarms', title: 'Alarms & Notifications', slides: alarmsSlides },
  { id: 'comparison', title: 'Meter Comparison', slides: comparisonSlides },
  { id: 'diagram', title: 'Electricity Meter Diagram', slides: diagramSlides },
  { id: 'settings', title: 'System Settings', slides: settingsSlides },
];

export const ExportSlidesAsImagesPDF = () => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const captureSlideAsImage = async (SlideComponent: React.ComponentType<{ currentSlide: number }>, slideIndex: number): Promise<string> => {
    // Create a temporary container with proper aspect ratio for landscape A4
    // A4 landscape is 297mm x 210mm (aspect ratio ~1.414)
    const slideWidth = 1920; // Full HD width for better quality
    const slideHeight = 1080; // 16:9 aspect ratio matches most slides
    
    const container = document.createElement('div');
    container.style.position = 'fixed';
    container.style.top = '0';
    container.style.left = '0';
    container.style.width = `${slideWidth}px`;
    container.style.height = `${slideHeight}px`;
    container.style.backgroundColor = 'white';
    container.style.zIndex = '-9999';
    container.style.transform = 'scale(1)';
    container.className = 'slide-capture-container';
    
    document.body.appendChild(container);

    // Create a wrapper div to ensure the slide fills the container
    const wrapper = document.createElement('div');
    wrapper.style.width = '100%';
    wrapper.style.height = '100%';
    wrapper.style.display = 'flex';
    wrapper.style.alignItems = 'center';
    wrapper.style.justifyContent = 'center';
    wrapper.style.backgroundColor = '#ffffff';
    container.appendChild(wrapper);

    // Render the slide component into the wrapper
    const { createRoot } = await import('react-dom/client');
    const root = createRoot(wrapper);
    
    await new Promise<void>((resolve) => {
      root.render(
        <div style={{ 
          width: '100%', 
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#ffffff'
        }}>
          <div style={{
            width: '100%',
            height: '100%',
            position: 'relative'
          }}>
            <SlideComponent currentSlide={slideIndex} />
          </div>
        </div>
      );
      
      // Wait longer for complex slides to render
      setTimeout(resolve, 1000);
    });

    try {
      // Capture the slide as canvas with exact dimensions
      const canvas = await html2canvas(container, {
        scale: 1, // Lower scale to reduce memory usage
        useCORS: true,
        logging: false,
        backgroundColor: '#ffffff',
        width: slideWidth,
        height: slideHeight,
        windowWidth: slideWidth,
        windowHeight: slideHeight,
        onclone: (clonedDoc) => {
          // Ensure styles are applied to cloned document
          const clonedContainer = clonedDoc.querySelector('.slide-capture-container');
          if (clonedContainer) {
            clonedContainer.classList.add('slide-capture-container');
          }
        }
      });

      // Convert to data URL with balanced quality
      const imageData = canvas.toDataURL('image/jpeg', 0.85);
      
      // Cleanup
      root.unmount();
      document.body.removeChild(container);
      
      return imageData;
    } catch (error) {
      console.error('Error capturing slide:', error);
      root.unmount();
      if (container && container.parentNode) {
        document.body.removeChild(container);
      }
      throw error;
    }
  };

  const exportAllSlidesAsPDF = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Create PDF document in landscape A4
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });

      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      
      let totalSlides = allModules.reduce((sum, module) => sum + module.slides.length, 0);
      let processedSlides = 0;

      // Add cover page
      pdf.setFillColor(30, 64, 175); // Blue background
      pdf.rect(0, 0, pageWidth, pageHeight, 'F');
      
      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(48);
      pdf.text('Energy Management System', pageWidth / 2, pageHeight / 2 - 20, { align: 'center' });
      
      pdf.setFontSize(24);
      pdf.text('Complete Training Manual', pageWidth / 2, pageHeight / 2, { align: 'center' });
      
      pdf.setFontSize(20);
      pdf.text(`${totalSlides} Training Slides`, pageWidth / 2, pageHeight / 2 + 20, { align: 'center' });
      
      pdf.setFontSize(16);
      pdf.text(`Generated on ${new Date().toLocaleDateString()}`, pageWidth / 2, pageHeight - 30, { align: 'center' });

      // Process each module
      for (const module of allModules) {
        // Add module title page
        pdf.addPage();
        pdf.setFillColor(30, 64, 175);
        pdf.rect(0, 0, pageWidth, 60, 'F');
        
        pdf.setTextColor(255, 255, 255);
        pdf.setFontSize(36);
        pdf.text(module.title, pageWidth / 2, 35, { align: 'center' });
        
        pdf.setFontSize(18);
        pdf.text(`${module.slides.length} slides`, pageWidth / 2, 50, { align: 'center' });
        
        pdf.setTextColor(0, 0, 0);

        // Capture each slide with a small delay to prevent memory overload
        for (let i = 0; i < module.slides.length; i++) {
          // Add a small delay between captures to allow garbage collection
          await new Promise(resolve => setTimeout(resolve, 100));
          try {
            const SlideComponent = module.slides[i];
            const imageData = await captureSlideAsImage(SlideComponent, i);
            
            pdf.addPage();
            
            // Calculate image dimensions to fit perfectly on the page with small margins
            const margin = 5; // 5mm margin on all sides
            const maxWidth = pageWidth - (2 * margin);
            const maxHeight = pageHeight - (2 * margin) - 10; // Leave space for slide number
            
            // Calculate the best fit while maintaining aspect ratio
            const imgAspectRatio = 1920 / 1080; // 16:9 aspect ratio
            const pageAspectRatio = maxWidth / maxHeight;
            
            let imgWidth, imgHeight, xOffset, yOffset;
            
            if (imgAspectRatio > pageAspectRatio) {
              // Image is wider than page ratio - fit to width
              imgWidth = maxWidth;
              imgHeight = maxWidth / imgAspectRatio;
              xOffset = margin;
              yOffset = margin + (maxHeight - imgHeight) / 2;
            } else {
              // Image is taller than page ratio - fit to height
              imgHeight = maxHeight;
              imgWidth = maxHeight * imgAspectRatio;
              xOffset = margin + (maxWidth - imgWidth) / 2;
              yOffset = margin;
            }
            
            // Add white background for the page
            pdf.setFillColor(255, 255, 255);
            pdf.rect(0, 0, pageWidth, pageHeight, 'F');
            
            // Add slide image with calculated dimensions
            pdf.addImage(imageData, 'JPEG', xOffset, yOffset, imgWidth, imgHeight);
            
            // Add slide number at the bottom
            pdf.setFontSize(10);
            pdf.setTextColor(128, 128, 128);
            pdf.text(`${module.title} - Slide ${i + 1} of ${module.slides.length}`, pageWidth / 2, pageHeight - 5, { align: 'center' });
            
            processedSlides++;
            setExportProgress(Math.round((processedSlides / totalSlides) * 100));
          } catch (error) {
            console.error(`Error capturing slide ${i + 1} of ${module.title}:`, error);
            // Add error page
            pdf.addPage();
            pdf.setTextColor(255, 0, 0);
            pdf.text(`Error capturing slide ${i + 1}`, pageWidth / 2, pageHeight / 2, { align: 'center' });
            pdf.setTextColor(0, 0, 0);
          }
        }
      }

      // Add support page
      pdf.addPage();
      pdf.setFontSize(24);
      pdf.text('Support Information', pageWidth / 2, 30, { align: 'center' });
      
      pdf.setFontSize(14);
      pdf.text('For additional help or questions:', 20, 60);
      pdf.text('• Phone: ************', 30, 80);
      pdf.text('• Support Hours: Monday-Friday, 9:00 AM - 6:00 PM', 30, 90);
      
      pdf.setFontSize(10);
      pdf.text('© 2024 Energy Management System v1.0', pageWidth / 2, pageHeight - 20, { align: 'center' });

      // Save the PDF
      pdf.save(`EMS_Training_Manual_Complete_${new Date().toISOString().split('T')[0]}.pdf`);
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF: ' + (error instanceof Error ? error.message : 'Unknown error') + '\n\nPlease try again with fewer slides or contact support.');
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  return (
    <button
      onClick={exportAllSlidesAsPDF}
      disabled={isExporting}
      className="inline-flex items-center gap-2 px-4 py-2 rounded-lg transition-all"
      style={{ 
        backgroundColor: isExporting ? '#9ca3af' : (isHovered ? '#1d4ed8' : '#2563eb'), 
        color: '#ffffff',
        cursor: isExporting ? 'not-allowed' : 'pointer',
        opacity: isExporting ? 0.7 : 1
      }}
      onMouseEnter={() => !isExporting && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <FileDown size={20} />
      {isExporting ? `Exporting... ${exportProgress}%` : 'Export Complete PDF (with images)'}
    </button>
  );
};