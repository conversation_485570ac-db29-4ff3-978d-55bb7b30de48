import React from 'react';
// import { Navigate } from 'react-router-dom';
// import { useAuth } from './hooks/useAuth';

interface AdminRouteProps {
  children: React.ReactNode;
}

/**
 * AdminRoute component
 * 
 * A wrapper component that checks if the current user has admin privileges.
 * If not, it redirects to the home page.
 */
const AdminRoute: React.FC<AdminRouteProps> = ({ children }) => {
  // Temporarily bypass admin check for UI prototype
  // const { isAdmin } = useAuth();
  // if (!isAdmin) {
  //   return <Navigate to="/" replace />;
  // }
  
  return <>{children}</>;
};

export default AdminRoute;
