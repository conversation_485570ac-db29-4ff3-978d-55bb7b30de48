import { useState, useEffect } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user' | 'guest';
}

/**
 * Custom hook for authentication
 * 
 * This is a simplified version that uses localStorage for persistence.
 * In a real application, this would connect to an authentication API.
 */
export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Load user from localStorage on mount
  useEffect(() => {
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (error) {
        console.error('Error parsing stored user:', error);
      }
    }
    
    // For demo purposes, create a default admin user if none exists
    if (!storedUser) {
      const defaultUser: User = {
        id: 'admin-1',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin'
      };
      localStorage.setItem('currentUser', JSON.stringify(defaultUser));
      setUser(defaultUser);
    }
    
    setIsLoading(false);
  }, []);
  
  // Check if the current user is an admin
  const isAdmin = user?.role === 'admin';
  
  // Login function
  const login = (email: string, password: string) => {
    // In a real app, this would make an API call
    // For demo purposes, we'll just set a mock user
    const mockUser: User = {
      id: 'admin-1',
      name: 'Admin User',
      email,
      role: 'admin'
    };
    
    localStorage.setItem('currentUser', JSON.stringify(mockUser));
    setUser(mockUser);
    return Promise.resolve(mockUser);
  };
  
  // Logout function
  const logout = () => {
    localStorage.removeItem('currentUser');
    setUser(null);
  };
  
  return {
    user,
    isLoading,
    isAdmin,
    login,
    logout
  };
}
