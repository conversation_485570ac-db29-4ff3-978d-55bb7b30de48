import React from 'react';
import { ManualChapter } from './ManualTypes';
import ManualImageComponent from './ManualImage';

interface ManualContentProps {
  manualContent: ManualChapter[];
  activeChapter: string | null;
  activeSection: string | null;
  activeSubsection: string | null;
}

const ManualContent: React.FC<ManualContentProps> = ({
  manualContent,
  activeChapter,
  activeSection,
  activeSubsection
}) => {
  // Find the active chapter
  const chapter = manualContent.find(ch => ch.id === activeChapter);
  if (!chapter) return <div className="text-gray-500">Please select a chapter to begin</div>;
  
  // Find the active section
  const section = chapter.sections.find(sec => sec.id === activeSection);
  if (!section) return <div className="text-gray-500">Please select a section to begin</div>;
  
  // Determine if we're showing a subsection
  const subsection = section.subsections?.find(sub => sub.id === activeSubsection);
  
  // Content to display (subsection or section)
  const contentHtml = subsection ? subsection.content : section.content;
  
  // Images to display (from subsection or section)
  const images = subsection?.images || section.images || [];
  
  // Add print-specific classes for better PDF formatting
  const addPrintClasses = (htmlContent: string): string => {
    return htmlContent
      // Add print-friendly classes to card elements
      .replace(/<div class="bg-white rounded-lg (border border-[^"]+)">/g, 
               '<div class="bg-white rounded-lg $1 avoid-break-inside">')
      // Add print-friendly classes to headings
      .replace(/<h3 class="/g, '<h3 class="avoid-break-after ')
      // Make sure all content is visible in print
      .replace(/hidden/g, 'print-visible')
      // Ensure tables don't break across pages
      .replace(/<table/g, '<table class="avoid-break-inside"');
  };
  
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 print-container">
      <div className="mb-6 print-section-header">
        <h2 className="text-2xl font-medium text-gray-800">{section.title}</h2>
        {subsection && (
          <h3 className="text-xl font-medium text-gray-700 mt-1">{subsection.title}</h3>
        )}
      </div>
      
      {/* Render section/subsection content */}
      <div 
        className="manual-content prose prose-blue max-w-none" 
        dangerouslySetInnerHTML={{ __html: addPrintClasses(contentHtml) }} 
      />
      
      {/* Render images */}
      {images.length > 0 && (
        <div className="mt-6 space-y-6">
          {images.map((image, index) => (
            <ManualImageComponent 
              key={index}
              src={image.src}
              alt={image.alt}
              caption={image.caption}
              width={image.width}
              className="print-friendly-image"
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ManualContent;
