import React from 'react';
import ManualSearch from './ManualSearch';
import ManualPrintButton from './ManualPrintButton';
import { ManualHeaderProps } from './interfaces';

const ManualHeader: React.FC<ManualHeaderProps> = ({
  getCurrentTitle,
  searchQuery,
  setSearchQuery,
  manualContent
}) => {
  return (
    <header className="border-b border-gray-200 bg-white sticky top-0 z-10 print:hidden">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left side: Title */}
        <h2 className="text-lg font-medium text-gray-800">{getCurrentTitle()}</h2>
        
        {/* Right side: Search and Export button */}
        <div className="flex items-center space-x-4">
          {/* Desktop search bar */}
          <div className="hidden md:block w-64">
            <ManualSearch
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              searchResults={[]} 
              handleSearchSelect={() => {}}
              manualContent={manualContent}
            />
          </div>
          
          {/* PDF Export Button */}
          <ManualPrintButton manualContent={manualContent} />
        </div>
      </div>
    </header>
  );
};

export default ManualHeader;
