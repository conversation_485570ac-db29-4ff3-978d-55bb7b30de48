import React from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { ManualChapter } from './ManualTypes';

interface ManualNavigationProps {
  manualContent: ManualChapter[];
  activeChapter: string | null;
  activeSection: string | null;
  activeSubsection: string | null;
  setActiveChapter: (chapterId: string) => void;
  setActiveSection: (sectionId: string) => void;
  setActiveSubsection: (subsectionId: string | null) => void;
}

const ManualNavigation: React.FC<ManualNavigationProps> = ({
  manualContent,
  activeChapter,
  activeSection,
  activeSubsection,
  setActiveChapter,
  setActiveSection,
  setActiveSubsection
}) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-800">Energy Management System Manual</h2>
      </div>
      <nav className="p-2">
        {manualContent.map((chapter) => (
          <div key={chapter.id} className="mb-2">
            <button
              className={`w-full flex items-center justify-between p-2 rounded-md text-left ${
                activeChapter === chapter.id ? 'bg-blue-50 text-blue-600' : 'hover:bg-gray-50'
              }`}
              onClick={() => {
                setActiveChapter(chapter.id);
                if (chapter.sections.length > 0) {
                  setActiveSection(chapter.sections[0].id);
                  setActiveSubsection(null);
                }
              }}
            >
              <div className="flex items-center gap-2">
                {chapter.icon && <span>{chapter.icon}</span>}
                <span className="font-medium">{chapter.title}</span>
              </div>
              {activeChapter === chapter.id ? (
                <ChevronDown size={16} />
              ) : (
                <ChevronRight size={16} />
              )}
            </button>
            {activeChapter === chapter.id && (
              <div className="ml-6 mt-1 space-y-1">
                {chapter.sections.map((section) => (
                  <div key={section.id}>
                    <button
                      className={`w-full flex items-center justify-between p-1.5 rounded-md text-left text-sm ${
                        activeSection === section.id
                          ? 'bg-blue-50 text-blue-600'
                          : 'hover:bg-gray-50'
                      }`}
                      onClick={() => {
                        setActiveSection(section.id);
                        setActiveSubsection(null);
                      }}
                    >
                      <span>{section.title}</span>
                      {section.subsections && section.subsections.length > 0 && (
                        <>
                          {activeSection === section.id ? (
                            <ChevronDown size={14} />
                          ) : (
                            <ChevronRight size={14} />
                          )}
                        </>
                      )}
                    </button>
                    {activeSection === section.id &&
                      section.subsections &&
                      section.subsections.length > 0 && (
                        <div className="ml-4 mt-1 space-y-1">
                          {section.subsections.map((subsection) => (
                            <button
                              key={subsection.id}
                              className={`w-full text-left p-1.5 rounded-md text-sm ${
                                activeSubsection === subsection.id
                                  ? 'bg-blue-50 text-blue-600'
                                  : 'hover:bg-gray-50'
                              }`}
                              onClick={() => setActiveSubsection(subsection.id)}
                            >
                              {subsection.title}
                            </button>
                          ))}
                        </div>
                      )}
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </nav>
    </div>
  );
};

export default ManualNavigation;
