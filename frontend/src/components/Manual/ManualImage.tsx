import React from 'react';

interface ManualImageProps {
  src: string;
  alt: string;
  caption?: string;
  width?: string;
  className?: string;
}

/**
 * ManualImage component for displaying screenshots in the manual with optional captions
 * This component is designed to work both in the regular UI and in the PDF export
 */
const ManualImage: React.FC<ManualImageProps> = ({ 
  src, 
  alt, 
  caption, 
  width = '100%',
  className = ''
}) => {
  return (
    <figure className={`my-4 mx-auto ${className}`} style={{ maxWidth: width }}>
      <div className="border border-gray-200 rounded-lg overflow-hidden shadow-sm">
        <img 
          src={src} 
          alt={alt} 
          className="w-full h-auto" 
        />
      </div>
      {caption && (
        <figcaption className="text-sm text-gray-600 text-center mt-2 italic">
          {caption}
        </figcaption>
      )}
    </figure>
  );
};

export default ManualImage;
