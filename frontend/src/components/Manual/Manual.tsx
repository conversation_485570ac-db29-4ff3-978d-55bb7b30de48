import React, { useState, useEffect } from 'react';
import { Book } from 'lucide-react';
import ManualNavigation from './ManualNavigation';
import ManualSearch from './ManualSearch';
import ManualContent from './ManualContent';
import ManualPrintButton from './ManualPrintButton';
import ManualPrintStyles from './ManualPrintStyles';
import { ManualChapter, SearchResult } from './ManualTypes';
import introductionSections from './data/introduction.tsx';
import dashboardSections from './data/dashboard';
import metersSections from './data/meters';
import analyticsSections from './data/analytics';
import reportsSections from './data/reports';
import settingsSections from './data/settings';
import glossarySections from './data/glossary';
import faqSections from './data/faq';

// Manual content structure
const manualContent: ManualChapter[] = [
  {
    id: 'introduction',
    title: 'Introduction',
    icon: <Book size={18} />,
    sections: introductionSections
  },
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: <span className="flex items-center justify-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-layout-dashboard"><rect width="7" height="9" x="3" y="3" rx="1"/><rect width="7" height="5" x="14" y="3" rx="1"/><rect width="7" height="9" x="14" y="12" rx="1"/><rect width="7" height="5" x="3" y="16" rx="1"/></svg>
    </span>,
    sections: dashboardSections
  },
  {
    id: 'meters',
    title: 'Meters',
    icon: <span className="flex items-center justify-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-zap"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/></svg>
    </span>,
    sections: metersSections
  },
  {
    id: 'analytics',
    title: 'Analytics',
    icon: <span className="flex items-center justify-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-bar-chart"><line x1="12" x2="12" y1="20" y2="10"/><line x1="18" x2="18" y1="20" y2="4"/><line x1="6" x2="6" y1="20" y2="16"/></svg>
    </span>,
    sections: analyticsSections
  },
  {
    id: 'reports',
    title: 'Reports',
    icon: <span className="flex items-center justify-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-file-text"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/><polyline points="14 2 14 8 20 8"/><line x1="16" x2="8" y1="13" y2="13"/><line x1="16" x2="8" y1="17" y2="17"/><line x1="10" x2="8" y1="9" y2="9"/></svg>
    </span>,
    sections: reportsSections
  },
  {
    id: 'settings',
    title: 'Settings',
    icon: <span className="flex items-center justify-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-settings"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
    </span>,
    sections: settingsSections
  },
  {
    id: 'glossary',
    title: 'Glossary',
    icon: <span className="flex items-center justify-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-book-open"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/></svg>
    </span>,
    sections: glossarySections
  },
  {
    id: 'faq',
    title: 'FAQ & Troubleshooting',
    icon: <span className="flex items-center justify-center">
      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-help-circle"><circle cx="12" cy="12" r="10"/><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/><line x1="12" y1="17" x2="12.01" y2="17"/></svg>
    </span>,
    sections: faqSections
  }
];

// Helper function to safely get a text snippet
const getTextSnippet = (node: React.ReactNode | undefined | null, fallback: string, maxLength: number = 150): string => {
  if (typeof node === 'string') {
    const snippet = node.replace(/<[^>]*>/g, ''); // Remove HTML tags for snippet
    return snippet.length > maxLength ? snippet.substring(0, maxLength) + '...' : snippet;
  }
  // If content is not a string (likely JSX), use the fallback (e.g., title)
  return fallback.length > maxLength ? fallback.substring(0, maxLength) + '...' : fallback;
};

const Manual: React.FC = () => {
  const [activeChapter, setActiveChapter] = useState<string | null>('introduction');
  const [activeSection, setActiveSection] = useState<string | null>('system-overview');
  const [activeSubsection, setActiveSubsection] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);

  // Effect for handling search
  useEffect(() => {
    if (searchQuery.trim().length < 3) {
      setSearchResults([]);
      return;
    }

    const query = searchQuery.toLowerCase();
    const results: SearchResult[] = [];

    manualContent.forEach(chapter => {
      chapter.sections.forEach(section => {
        const sectionTitleLower = section.title.toLowerCase();
        const sectionContentString = typeof section.content === 'string' ? section.content.toLowerCase() : '';

        // Search section title
        if (sectionTitleLower.includes(query)) {
          results.push({
            chapterId: chapter.id,
            chapterTitle: chapter.title,
            sectionId: section.id,
            sectionTitle: section.title,
            contentSnippet: getTextSnippet(section.content, section.title),
            match: section.title // Assign title as match
          });
        }
        // Search section content (if it's a string and title didn't match)
        else if (sectionContentString && sectionContentString.includes(query)) {
          results.push({
            chapterId: chapter.id,
            chapterTitle: chapter.title,
            sectionId: section.id,
            sectionTitle: section.title,
            contentSnippet: getTextSnippet(section.content, section.title),
            match: section.title // Assign title as match
          });
        }

        // Search subsections
        section.subsections?.forEach(subsection => {
          const subsectionTitleLower = subsection.title.toLowerCase();
          const subsectionContentString = typeof subsection.content === 'string' ? subsection.content.toLowerCase() : '';

          // Search subsection title
          if (subsectionTitleLower.includes(query)) {
            if (!results.some(r => r.chapterId === chapter.id && r.sectionId === section.id && r.subsectionId === subsection.id)) {
              results.push({
                chapterId: chapter.id,
                chapterTitle: chapter.title,
                sectionId: section.id,
                sectionTitle: section.title,
                subsectionId: subsection.id,
                subsectionTitle: subsection.title,
                contentSnippet: getTextSnippet(subsection.content, subsection.title),
                match: subsection.title // Assign title as match
              });
            }
          }
          // Search subsection content (if it's a string and titles didn't match)
          else if (subsectionContentString && subsectionContentString.includes(query)) {
            if (!results.some(r => r.chapterId === chapter.id && r.sectionId === section.id && r.subsectionId === subsection.id)) {
              results.push({
                chapterId: chapter.id,
                chapterTitle: chapter.title,
                sectionId: section.id,
                sectionTitle: section.title,
                subsectionId: subsection.id,
                subsectionTitle: subsection.title,
                contentSnippet: getTextSnippet(subsection.content, subsection.title),
                match: subsection.title // Assign title as match
              });
            }
          }
        });
      });
    });

    setSearchResults(results);
  }, [searchQuery]);

  // Handle search result selection
  const handleSearchSelect = (result: SearchResult) => {
    setActiveChapter(result.chapterId);
    setActiveSection(result.sectionId);
    setActiveSubsection(result.subsectionId || null);
    setSearchQuery('');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Include print styles */}
      <ManualPrintStyles />
      
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Energy Management System Manual</h1>
          <p className="text-gray-600">
            Comprehensive guide to using and configuring your Energy Management System
          </p>
        </div>
        
        {/* Print button */}
        <div className="print-controls">
          <ManualPrintButton manualContent={manualContent} />
        </div>
      </div>
      
      {/* Search bar */}
      <div className="mb-8">
        <ManualSearch
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          searchResults={searchResults}
          handleSearchSelect={handleSearchSelect}
          manualContent={manualContent}
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Navigation sidebar */}
        <div className="md:col-span-1 manual-navigation">
          <ManualNavigation
            manualContent={manualContent}
            activeChapter={activeChapter}
            activeSection={activeSection}
            activeSubsection={activeSubsection}
            setActiveChapter={setActiveChapter}
            setActiveSection={setActiveSection}
            setActiveSubsection={setActiveSubsection}
          />
        </div>
        
        {/* Main content */}
        <div className="md:col-span-3">
          <ManualContent
            manualContent={manualContent}
            activeChapter={activeChapter}
            activeSection={activeSection}
            activeSubsection={activeSubsection}
          />
        </div>
      </div>
    </div>
  );
};

export default Manual;
