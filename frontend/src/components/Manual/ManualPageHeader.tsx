import React from 'react';
import { Search, X } from 'lucide-react';
import ManualPrintButton from './ManualPrintButton';
import { ManualChapter } from './ManualTypes';

interface ManualPageHeaderProps {
  getCurrentTitle: () => string;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  clearSearch: () => void;
  manualContent: ManualChapter[];
}

const ManualPageHeader: React.FC<ManualPageHeaderProps> = ({
  getCurrentTitle,
  searchQuery,
  handleSearch,
  clearSearch,
  manualContent
}) => {
  return (
    <header className="border-b border-gray-200 bg-white sticky top-0 z-10 print:hidden">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left side: Title */}
        <h2 className="text-lg font-medium text-gray-800">{getCurrentTitle()}</h2>
        
        {/* Right side: Search and Export button */}
        <div className="flex items-center space-x-4">
          {/* Desktop search bar */}
          <div className="hidden md:block relative w-64">
            <div className="flex items-center bg-gray-50 rounded-md border border-gray-200 hover:border-gray-300 focus-within:border-blue-400 focus-within:ring-1 focus-within:ring-blue-200 transition-all duration-200">
              <div className="pl-3">
                <Search size={16} className="text-gray-400 group-focus-within:text-blue-500" />
              </div>
              <input
                type="text"
                placeholder="Search manual..."
                value={searchQuery}
                onChange={handleSearch}
                className="w-full bg-transparent border-none focus:ring-0 text-sm text-gray-700 placeholder-gray-400 px-2 py-2 outline-none"
              />
              {searchQuery ? (
                <button 
                  onClick={clearSearch}
                  className="pr-3 text-gray-400 hover:text-gray-600 transition-colors"
                  title="Clear search"
                >
                  <X size={16} />
                </button>
              ) : null}
            </div>
          </div>
          
          {/* PDF Export Button */}
          <ManualPrintButton manualContent={manualContent} />
        </div>
      </div>
    </header>
  );
};

export default ManualPageHeader;