import { ManualChapter } from './ManualTypes';

// ManualHeader Props Interface
export interface ManualHeaderProps {
  getCurrentTitle: () => string;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  manualContent: ManualChapter[];
}

// ManualNavigation Props Interface
export interface ManualNavigationProps {
  manualContent: ManualChapter[];
  activeChapter: string;
  activeSection: string;
  activeSubsection: string;
  setActiveChapter: (chapterId: string) => void;
  setActiveSection: (sectionId: string) => void;
  setActiveSubsection: (subsectionId: string | null) => void;
}

// ManualContent Props Interface
export interface ManualContentProps {
  manualContent: ManualChapter[];
  activeChapter: string;
  activeSection: string;
  activeSubsection: string;
}

// ManualContainer Props Interface
export interface ManualContainerProps {
  // Any props needed for the container component
}

// ManualSearch Props Interface
export interface ManualSearchProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  searchResults: any[];
  handleSearchSelect: (result: any) => void;
  manualContent: ManualChapter[];
}
