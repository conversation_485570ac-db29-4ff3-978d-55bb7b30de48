import React, { useState, useEffect } from 'react';
import {
  Book,
  LayoutDashboard,
  Zap,
  BarChart,
  FileText,
  Settings,
  HelpCircle,
  FileCode2,
  BarChart3,
  Columns,
  AlertCircle,
  Users,
} from 'lucide-react';
import ManualNavigation from './ManualNavigation';
import ManualSearch from './ManualSearch';
import ManualContent from './ManualContent';
import ManualPrintStyles from './ManualPrintStyles';
import { SearchResult, ManualChapter } from './ManualTypes';
import introductionSections from './data/introduction.tsx';
import dashboardSections from './data/dashboard';
import metersSections from './data/meters';
import metersManagementSections from './data/meters-management';
import analyticsSections from './data/analytics';
import consumptionSections from './data/consumption';
import compareSections from './data/compare';
import reportsSections from './data/reports';
import settingsSections from './data/settings';
import alertsSections from './data/alerts';
import glossarySections from './data/glossary';
import faqSections from './data/faq';
import userManagementScreenshotsSections from './data/user-management-screenshots';
import ManualHeader from './ManualHeader';

// Combine all manual content for global search
const manualContent: ManualChapter[] = [
  {
    id: 'introduction',
    title: 'Introduction',
    icon: <Book size={18} />,
    sections: introductionSections
  },
  {
    id: 'dashboard',
    title: 'Dashboard',
    icon: <LayoutDashboard size={18} />,
    sections: dashboardSections
  },
  {
    id: 'meters',
    title: 'Meters',
    icon: <Zap size={18} />,
    sections: metersSections
  },
  {
    id: 'meters-management',
    title: 'Meters Management',
    icon: <FileCode2 size={18} />,
    sections: metersManagementSections
  },
  {
    id: 'analytics',
    title: 'Analytics',
    icon: <BarChart size={18} />,
    sections: analyticsSections
  },
  {
    id: 'consumption',
    title: 'Consumption',
    icon: <BarChart3 size={18} />,
    sections: consumptionSections
  },
  {
    id: 'compare',
    title: 'Compare',
    icon: <Columns size={18} />,
    sections: compareSections
  },
  {
    id: 'reports',
    title: 'Reports',
    icon: <FileText size={18} />,
    sections: reportsSections
  },
  {
    id: 'settings',
    title: 'Settings',
    icon: <Settings size={18} />,
    sections: settingsSections
  },
  {
    id: 'alerts',
    title: 'Alerts',
    icon: <AlertCircle size={18} />,
    sections: alertsSections
  },
  {
    id: 'glossary',
    title: 'Glossary',
    icon: <Book size={18} />,
    sections: glossarySections
  },
  {
    id: 'faq',
    title: 'FAQ',
    icon: <HelpCircle size={18} />,
    sections: faqSections
  },
  {
    id: 'user-management-screenshots',
    title: 'User Management Screenshots',
    icon: <Users size={18} />,
    sections: userManagementScreenshotsSections
  }
];

// Helper function to safely get a text snippet
const getTextSnippet = (node: React.ReactNode | undefined | null, fallback: string, maxLength: number = 150): string => {
  if (typeof node === 'string') {
    const snippet = node.replace(/<[^>]*>/g, ''); // Remove HTML tags for snippet
    return snippet.length > maxLength ? snippet.substring(0, maxLength) + '...' : snippet;
  }
  // If content is not a string (likely JSX), use the fallback (e.g., title)
  return fallback.length > maxLength ? fallback.substring(0, maxLength) + '...' : fallback;
};

const ManualContainer: React.FC = () => {
  // State for active navigation items
  const [activeChapter, setActiveChapter] = useState('introduction');
  const [activeSection, setActiveSection] = useState('system-overview');
  const [activeSubsection, setActiveSubsection] = useState('');
  
  // State for search functionality
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearchActive, setIsSearchActive] = useState(false);
  
  // Effect for handling search
  useEffect(() => {
    if (searchQuery.trim().length < 3) {
      setSearchResults([]);
      setIsSearchActive(false);
      return;
    }

    setIsSearchActive(true);
    const query = searchQuery.toLowerCase();
    const results: SearchResult[] = [];

    manualContent.forEach(chapter => {
      chapter.sections.forEach(section => {
        const sectionTitleLower = section.title.toLowerCase();
        const sectionContentString = typeof section.content === 'string' ? section.content.toLowerCase() : '';

        // Search section title
        if (sectionTitleLower.includes(query)) {
          results.push({
            chapterId: chapter.id,
            chapterTitle: chapter.title,
            sectionId: section.id,
            sectionTitle: section.title,
            contentSnippet: getTextSnippet(section.content, section.title),
            match: section.title // Assign title as match
          });
        }
        // Search section content (if it's a string and title didn't match)
        else if (sectionContentString && sectionContentString.includes(query)) {
          results.push({
            chapterId: chapter.id,
            chapterTitle: chapter.title,
            sectionId: section.id,
            sectionTitle: section.title,
            contentSnippet: getTextSnippet(section.content, section.title),
            match: section.title // Assign title as match, even if content matched
          });
        }

        // Search subsections
        section.subsections?.forEach(subsection => {
          const subsectionTitleLower = subsection.title.toLowerCase();
          const subsectionContentString = typeof subsection.content === 'string' ? subsection.content.toLowerCase() : '';

          // Search subsection title
          if (subsectionTitleLower.includes(query)) {
            if (!results.some(r => r.chapterId === chapter.id && r.sectionId === section.id && r.subsectionId === subsection.id)) {
                results.push({
                  chapterId: chapter.id,
                  chapterTitle: chapter.title,
                  sectionId: section.id,
                  sectionTitle: section.title,
                  subsectionId: subsection.id,
                  subsectionTitle: subsection.title,
                  contentSnippet: getTextSnippet(subsection.content, subsection.title),
                  match: subsection.title // Assign subsection title as match
                });
            }
          }
          // Search subsection content (if it's a string and titles didn't match)
          else if (subsectionContentString && subsectionContentString.includes(query)) {
             if (!results.some(r => r.chapterId === chapter.id && r.sectionId === section.id && r.subsectionId === subsection.id)) {
                results.push({
                  chapterId: chapter.id,
                  chapterTitle: chapter.title,
                  sectionId: section.id,
                  sectionTitle: section.title,
                  subsectionId: subsection.id,
                  subsectionTitle: subsection.title,
                  contentSnippet: getTextSnippet(subsection.content, subsection.title),
                  match: subsection.title // Assign subsection title as match, even if content matched
                });
             }
          }
        });
      });
    });

    setSearchResults(results);
  }, [searchQuery, manualContent]);

  // Handle selecting a search result
  const handleSearchSelect = (result: SearchResult) => {
    setActiveChapter(result.chapterId);
    setActiveSection(result.sectionId);
    setActiveSubsection(result.subsectionId || '');
    setSearchQuery('');
    setSearchResults([]);
    setIsSearchActive(false);
  };

  // Get the title of the current section being viewed
  const getCurrentTitle = () => {
    const chapter = manualContent.find(c => c.id === activeChapter);
    if (!chapter) return 'Energy Management System Manual';
    
    const section = chapter.sections.find(s => s.id === activeSection);
    if (!section) return chapter.title;
    
    if (activeSubsection && section.subsections) {
      const subsection = section.subsections.find(s => s.id === activeSubsection);
      if (subsection) return `${section.title} - ${subsection.title}`;
    }
    
    return section.title;
  };

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Print styles */}
      <ManualPrintStyles />
      
      {/* Header */}
      <ManualHeader 
        getCurrentTitle={getCurrentTitle}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        manualContent={manualContent}
      />
      
      {/* Main content area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Navigation sidebar */}
        <ManualNavigation
          manualContent={manualContent}
          activeChapter={activeChapter}
          activeSection={activeSection}
          activeSubsection={activeSubsection}
          setActiveChapter={setActiveChapter}
          setActiveSection={setActiveSection}
          setActiveSubsection={(subsectionId: string | null) => 
            setActiveSubsection(subsectionId || '')}
        />
        
        {/* Main content */}
        <div className="flex-1 overflow-auto p-6">
          {/* Search box (visible on mobile only) */}
          <div className="mb-6 md:hidden">
            <ManualSearch
              searchQuery={searchQuery}
              setSearchQuery={setSearchQuery}
              searchResults={searchResults}
              handleSearchSelect={handleSearchSelect}
              manualContent={manualContent}
            />
          </div>
          
          {/* Content display */}
          {isSearchActive && searchResults.length > 0 ? (
            <div className="space-y-6">
              <h2 className="text-xl font-medium text-gray-800">Search Results</h2>
              <div className="divide-y divide-gray-100">
                {searchResults.map((result, index) => (
                  <button
                    key={index}
                    className="w-full text-left py-4 hover:bg-gray-50 transition-colors duration-150"
                    onClick={() => handleSearchSelect(result)}
                  >
                    <div className="flex flex-col gap-1">
                      <span className="font-medium text-blue-600">
                        {result.chapterTitle} &gt; {result.sectionTitle}
                        {result.subsectionTitle && ` > ${result.subsectionTitle}`}
                      </span>
                      <p className="text-sm text-gray-600">{result.contentSnippet}</p>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ) : (
            <ManualContent
              manualContent={manualContent}
              activeChapter={activeChapter}
              activeSection={activeSection}
              activeSubsection={activeSubsection}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default ManualContainer;
