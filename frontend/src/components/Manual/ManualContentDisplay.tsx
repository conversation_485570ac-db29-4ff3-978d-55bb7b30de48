import React from 'react';
import { ManualChapter, SearchResult } from './ManualTypes';

interface ManualContentDisplayProps {
  manualContent: ManualChapter[];
  activeChapter: string;
  activeSection: string;
  activeSubsection: string;
  searchResults: SearchResult[];
  handleSearchSelect: (result: SearchResult) => void;
}

const ManualContentDisplay: React.FC<ManualContentDisplayProps> = ({
  manualContent,
  activeChapter,
  activeSection,
  activeSubsection,
  searchResults,
  handleSearchSelect
}) => {
  // Find the current chapter, section, and subsection
  const chapter = manualContent.find(c => c.id === activeChapter);
  if (!chapter) return <div className="p-6">Please select a chapter to begin.</div>;
  
  const section = chapter.sections.find(s => s.id === activeSection);
  if (!section) return <div className="p-6">Please select a section to continue.</div>;
  
  // Function to handle exporting the Application Pages section to PDF
  const handleExportApplicationPages = () => {
    // Create a new window with just the content we want to print
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;
    
    // Create HTML content with styles for the export
    printWindow.document.write(`
      <html>
        <head>
          <title>Energy Management System - Application Pages</title>
          <style>
            @page {
              size: portrait;
              margin: 1.5cm 1.5cm 2.5cm 1.5cm; /* reduced top margin, increased bottom margin */
            }
            
            /* Import professional font */
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
            
            /* Global styles */
            body {
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
              color: #1f2937;
              line-height: 1.6;
              font-size: 10pt;
              max-width: 800px;
              margin: 0 auto;
              padding-top: 0; /* Remove top padding */
              background-color: #ffffff;
              background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0zNiAxOGMwLTkuOTQtOC4wNi0xOC0xOC0xOHY2QTEyIDEyIDAgMCAxIDMwIDEyaC02eiIgZmlsbD0iI2Y4ZmFmYyIvPjwvZz48L3N2Zz4=');
              background-position: center;
              background-attachment: fixed;
              background-repeat: no-repeat;
            }
            
            /* Content styles */
            h1, h2, h3, h4, h5 {
              color: #1e40af;
              font-weight: 600;
              margin-top: 1.2em;
              margin-bottom: 0.5em;
              page-break-after: avoid;
              position: relative;
            }
            
            h1 {
              font-size: 18pt;
              border-bottom: 1px solid #e2e8f0;
              padding-bottom: 0.3cm;
            }
            
            h1::before {
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              transform: translateY(-50%);
              width: 0.3cm;
              height: 0.8cm;
              background-color: #3b82f6;
              border-radius: 0 2px 2px 0;
              margin-left: -1.8cm;
            }
            
            h2 {
              font-size: 14pt;
              margin-top: 1.5cm;
            }
            
            h3 {
              font-size: 12pt;
              color: #2563eb;
            }
            
            h4 {
              font-size: 11pt;
              font-weight: 500;
            }
            
            h5 {
              font-size: 10pt;
              font-weight: 500;
              color: #475569;
            }
            
            p {
              margin-bottom: 0.5cm;
              text-align: justify;
              hyphens: auto;
            }
            
            img {
              max-width: 100%;
              border: 1px solid #e5e7eb;
              border-radius: 4px;
              margin: 0.5cm 0;
              page-break-inside: avoid;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            }
            
            .page-container {
              page-break-after: always;
              background: white;
              padding: 0.5cm;
              border-radius: 4px;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
              margin-bottom: 1cm;
            }
            
            .page-title {
              text-align: left;
              font-size: 18pt;
              margin-bottom: 1cm;
              color: #1e40af;
              border-bottom: 1px solid #e2e8f0;
              padding-bottom: 0.3cm;
              position: relative;
            }
            
            .page-title::before {
              content: '';
              position: absolute;
              left: 0;
              width: 0.3cm;
              height: 0.8cm;
              background-color: #3b82f6;
              border-radius: 0 2px 2px 0;
              margin-left: -1cm;
            }
            
            /* Card and component styles */
            .card {
              border: 1px solid #e5e7eb;
              border-radius: 4px;
              padding: 0.8cm;
              margin-bottom: 1cm;
              page-break-inside: avoid;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
              background: white;
              transition: transform 0.2s ease, box-shadow 0.2s ease;
              position: relative;
            }
            
            .card::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 4px;
              height: 100%;
              background-color: #3b82f6;
              border-radius: 4px 0 0 4px;
            }
            
            .card-title {
              font-weight: 600;
              color: #2563eb;
              margin-bottom: 0.5cm;
              font-size: 12pt;
              border-bottom: 1px solid #f1f5f9;
              padding-bottom: 0.2cm;
            }
            
            /* Avoid page breaks inside elements */
            ul, ol, dl, tr, img, .card {
              page-break-inside: avoid;
            }
            
            /* Force page break */
            .page-break {
              page-break-after: always;
            }
          </style>
        </head>
        <body>
          <!-- Main Content -->
          ${section.content}
          
          <script>
            // Empty script tag to ensure compatibility
          </script>
        </body>
      </html>
    `);
    
    // Trigger print dialog
    printWindow.document.close();
    printWindow.focus();
    
    // Add a slight delay to ensure content is loaded before printing
    setTimeout(() => {
      printWindow.print();
      // Don't close the window after print to allow for saving as PDF
    }, 1500);
  };
  
  // Determine if we should show search results or content
  if (searchResults.length > 0) {
    return (
      <div className="p-6 space-y-6">
        <h2 className="text-xl font-medium text-gray-800">Search Results</h2>
        <div className="divide-y divide-gray-100">
          {searchResults.map((result, index) => (
            <button
              key={index}
              className="w-full text-left py-4 hover:bg-gray-50 transition-colors duration-150"
              onClick={() => handleSearchSelect(result)}
            >
              <div className="flex flex-col gap-1">
                <span className="font-medium text-blue-600">
                  {result.chapterTitle} &gt; {result.sectionTitle}
                  {result.subsectionTitle && ` > ${result.subsectionTitle}`}
                </span>
                <p className="text-sm text-gray-600">{result.contentSnippet}</p>
              </div>
            </button>
          ))}
        </div>
      </div>
    );
  }
  
  // If a subsection is active, show that content
  if (activeSubsection && section.subsections) {
    const subsection = section.subsections.find(s => s.id === activeSubsection);
    if (subsection) {
      return (
        <div className="pt-4 px-6 pb-6">
          <div className="text-sm text-gray-500 mb-4">
            {chapter.title} &gt; {section.title} &gt; {subsection.title}
          </div>
          <div className="mb-6">
            <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: subsection.content }} />
          </div>
          
          {/* Display images if available */}
          {subsection.images && subsection.images.length > 0 && (
            <div className="mt-6 space-y-6">
              {subsection.images.map((image, index) => (
                <figure key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                  <img 
                    src={image.src} 
                    alt={image.alt} 
                    className="w-full object-contain"
                    style={{ width: image.width || '100%' }}
                  />
                  {image.caption && (
                    <figcaption className="text-sm text-gray-600 p-3 bg-gray-50 border-t border-gray-200">
                      {image.caption}
                    </figcaption>
                  )}
                </figure>
              ))}
            </div>
          )}
        </div>
      );
    }
  }
  
  // Otherwise, show the section content
  return (
    <div className="pt-4 px-6 pb-6">
      <div className="flex justify-between items-center mb-4">
        <div className="text-sm text-gray-500">
          {chapter.title} &gt; {section.title}
        </div>
      </div>
      
      <div className="mb-6">
        <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: section.content }} />
      </div>
      
      {/* Display images if available */}
      {section.images && section.images.length > 0 && (
        <div className="mt-6 space-y-6">
          {section.images.map((image, index) => (
            <figure key={index} className="border border-gray-200 rounded-lg overflow-hidden">
              <img 
                src={image.src} 
                alt={image.alt} 
                className="w-full object-contain"
                style={{ width: image.width || '100%' }}
              />
              {image.caption && (
                <figcaption className="text-sm text-gray-600 p-3 bg-gray-50 border-t border-gray-200">
                  {image.caption}
                </figcaption>
              )}
            </figure>
          ))}
        </div>
      )}
      
      {/* Display subsections if available */}
      {section.subsections && section.subsections.length > 0 && (
        <div className="mt-8 space-y-4">
          <h2 className="text-xl font-medium text-gray-800">In This Section</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {section.subsections.map(subsection => (
              <div 
                key={subsection.id}
                className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-all cursor-pointer"
                onClick={() => {
                  // This would need to be passed as a prop or handled by the parent
                  // For now, we'll just log it
                  console.log(`Navigate to subsection: ${subsection.id}`);
                }}
              >
                <h3 className="font-medium text-blue-600 mb-1">{subsection.title}</h3>
                <p className="text-sm text-gray-600 line-clamp-2">
                  {subsection.content.replace(/<[^>]*>/g, '').substring(0, 120)}...
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Export button - only show for Application Pages section, now at the bottom */}
      {section.id === 'application-pages' && (
        <div className="mt-8 flex justify-center">
          <button
            onClick={handleExportApplicationPages}
            className="inline-flex items-center gap-2 py-2 px-6 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-full transition-colors duration-150 shadow-sm"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            Export Application Pages
          </button>
        </div>
      )}
    </div>
  );
};

export default ManualContentDisplay;