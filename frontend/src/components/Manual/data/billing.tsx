import React from 'react';
import { ManualSection } from '../ManualTypes';

// Define content for each section as JSX constants
const BillingOverviewContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Energy Billing Management</h3>
    <p className="text-gray-700 leading-relaxed">
      Track, analyze, and optimize your energy costs with our comprehensive billing management interface. Monitor billing periods, view cost breakdowns, and identify savings opportunities.
    </p>
  </div>
  
  <div className="mb-8">
    <h3 className="text-lg font-medium text-gray-800 mb-4">Key Features</h3>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Cost Tracking and Analysis</h4>
        <p className="text-sm text-gray-600 mb-3">
          Monitor and analyze energy costs across different periods and facilities.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Cost breakdown by facility and department</li>
          <li>Bar charts for consumption-based costs (kWh)</li>
          <li>Line charts for demand-based costs (kW)</li>
          <li>Trend analysis and forecasting</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Billing Period Management</h4>
        <p className="text-sm text-gray-600 mb-3">
          Track billing cycles and periods for better cost management.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Monthly billing cycle tracking</li>
          <li>Period-over-period cost comparisons</li>
          <li>Cost anomaly detection</li>
          <li>Historical billing record management</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Cost Optimization</h4>
        <p className="text-sm text-gray-600 mb-3">
          Identify and implement cost-saving opportunities.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Peak demand identification and management</li>
          <li>Time-of-use optimization recommendations</li>
          <li>Cost reduction opportunity identification</li>
          <li>ROI analysis for efficiency improvements</li>
        </ul>
      </div>
    </div>
  </div>
</>);

const billingSections: ManualSection[] = [
  {
    id: 'billing-overview',
    title: 'Energy Billing Overview',
    content: BillingOverviewContent,
    images: [
      {
        src: '/images/manual/page-billing.png',
        alt: 'Energy Billing Interface',
        caption: 'Energy billing management interface with cost breakdowns, period tracking, and optimization insights',
        width: '90%'
      }
    ]
  }
];

export default billingSections;
