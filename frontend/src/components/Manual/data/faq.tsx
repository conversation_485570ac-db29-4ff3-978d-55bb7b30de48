import React from 'react';
import { ManualSection } from '../ManualTypes';

// Define content for each section as JSX constants
const FrequentlyAskedQuestionsContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Frequently Asked Questions</h3>
    <p className="text-gray-700 leading-relaxed">
      Quick answers to common questions about the Energy Management System.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-3">General Questions</h4>
      
      <div className="space-y-4">
        <div className="border-b border-gray-100 pb-4">
          <h5 className="font-medium text-gray-800 mb-2">How do I get started with the system?</h5>
          <p className="text-sm text-gray-600">
            Start by exploring the Dashboard to get an overview of your energy data. Then, navigate to the Meters section to ensure all your meters are properly configured. The Analytics section will help you gain deeper insights into your energy consumption patterns.
          </p>
        </div>
        
        <div className="border-b border-gray-100 pb-4">
          <h5 className="font-medium text-gray-800 mb-2">How often is my energy data updated?</h5>
          <p className="text-sm text-gray-600">
            Data update frequency depends on your meter configuration. Most modern meters provide data at 15-minute or hourly intervals. You can check the last update time for each meter in the Meters section. System-wide aggregations are typically updated hourly.
          </p>
        </div>
        
        <div className="border-b border-gray-100 pb-4">
          <h5 className="font-medium text-gray-800 mb-2">Can I access the system on mobile devices?</h5>
          <p className="text-sm text-gray-600">
            Yes, the Energy Management System is fully responsive and works on smartphones and tablets. For the best experience on smaller screens, we recommend using the mobile app available for iOS and Android. You can download it from your device's app store.
          </p>
        </div>
        
        <div>
          <h5 className="font-medium text-gray-800 mb-2">What should I do if I notice abnormal energy consumption?</h5>
          <p className="text-sm text-gray-600">
            If you notice unusual patterns or spikes, first verify the data in the Meters section. Then use the Analytics tools to compare with historical patterns and identify possible causes. You can also set up alerts in the Settings section to notify you automatically of anomalies.
          </p>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-3">Dashboard & Reports</h4>
      
      <div className="space-y-4">
        <div className="border-b border-gray-100 pb-4">
          <h5 className="font-medium text-gray-800 mb-2">How can I customize my dashboard?</h5>
          <p className="text-sm text-gray-600">
            Your dashboard can be customized by clicking the "Edit Layout" button in the top-right corner. You can add, remove, resize, and rearrange widgets to suit your needs. Each widget also has its own settings that can be accessed via the gear icon in the widget header.
          </p>
        </div>
        
        
        <div>
          <h5 className="font-medium text-gray-800 mb-2">How do I export data for use in other applications?</h5>
          <p className="text-sm text-gray-600">
            There are multiple ways to export data: 
            <ol className="list-decimal pl-5 mt-2 space-y-1">
              <li>Use the export button (↓) available on most charts and tables</li>
              <li>Generate a custom report in the Reports section and export it</li>
              <li>Use the Data Export tool in the Settings > Data Management section for bulk exports</li>
              <li>Utilize the API for automated data extraction (requires developer access)</li>
            </ol>
          </p>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-3">Account & Security</h4>
      
      <div className="space-y-4">
        <div className="border-b border-gray-100 pb-4">
          <h5 className="font-medium text-gray-800 mb-2">How do I reset my password?</h5>
          <p className="text-sm text-gray-600">
            To reset your password, click on your profile icon in the top-right corner and select "Account Settings." In the Security tab, you'll find the option to change your password. Alternatively, you can use the "Forgot Password" link on the login page to receive a password reset email.
          </p>
        </div>
        
        <div className="border-b border-gray-100 pb-4">
          <h5 className="font-medium text-gray-800 mb-2">How do I add a new user to the system?</h5>
          <p className="text-sm text-gray-600">
            Administrators can add new users in the Settings > User Management section. Click "Add User," enter their email address and details, assign appropriate roles and permissions, and send an invitation. New users will receive an email with instructions to set up their account.
          </p>
        </div>
        
        <div>
          <h5 className="font-medium text-gray-800 mb-2">Is my data secure?</h5>
          <p className="text-sm text-gray-600">
            Yes, your data is secured with industry-standard encryption both in transit and at rest. We employ multiple security measures including role-based access control, multi-factor authentication, regular security audits, and automated threat detection. For more information, please visit the Security section in Settings.
          </p>
        </div>
      </div>
    </div>
  </div>
</>);

const TroubleshootingContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Troubleshooting</h3>
    <p className="text-gray-700 leading-relaxed">
      Solutions to common issues you might encounter when using the Energy Management System.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-3">Data Issues</h4>
      
      <div className="space-y-4">
        <div className="border-b border-gray-100 pb-4">
          <h5 className="font-medium text-gray-800 mb-2">Missing or incomplete data</h5>
          <div className="text-sm text-gray-600">
            <p className="mb-2"><strong>Symptoms:</strong> Charts show gaps, displays "No Data Available," or analytics tools cannot generate results.</p>
            <p className="mb-2"><strong>Possible causes:</strong></p>
            <ul className="list-disc pl-5 mb-2 space-y-1">
              <li>Meter communication failure</li>
              <li>Data processing pipeline interruption</li>
              <li>Incorrect date range selection</li>
              <li>Meter configuration issues</li>
            </ul>
            <p className="mb-2"><strong>Solutions:</strong></p>
            <ol className="list-decimal pl-5 space-y-1">
              <li>Check meter status in the Meters section</li>
              <li>Verify that the selected date range contains data</li>
              <li>Check for any system notifications about data processing</li>
              <li>If the issue persists, contact support for assistance</li>
            </ol>
          </div>
        </div>
        
        <div>
          <h5 className="font-medium text-gray-800 mb-2">Incorrect or suspicious data values</h5>
          <div className="text-sm text-gray-600">
            <p className="mb-2"><strong>Symptoms:</strong> Energy readings appear unusually high or low, sudden unexplained spikes or drops.</p>
            <p className="mb-2"><strong>Possible causes:</strong></p>
            <ul className="list-disc pl-5 mb-2 space-y-1">
              <li>Meter reading errors</li>
              <li>Data transmission issues</li>
              <li>Actual anomalies in energy consumption</li>
              <li>Incorrect meter multiplier configuration</li>
            </ul>
            <p className="mb-2"><strong>Solutions:</strong></p>
            <ol className="list-decimal pl-5 space-y-1">
              <li>Compare with historical data to confirm abnormality</li>
              <li>Check meter configuration in the Meters section</li>
              <li>Verify if any operational changes could explain the pattern</li>
              <li>Use the Data Validation tool in Settings > Data Management</li>
              <li>If needed, flag data for correction using the Data Editing tools</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-3">System Access & Performance</h4>
      
      <div className="space-y-4">
        <div className="border-b border-gray-100 pb-4">
          <h5 className="font-medium text-gray-800 mb-2">Slow performance or timeouts</h5>
          <div className="text-sm text-gray-600">
            <p className="mb-2"><strong>Symptoms:</strong> Pages load slowly, operations time out, or charts take a long time to appear.</p>
            <p className="mb-2"><strong>Possible causes:</strong></p>
            <ul className="list-disc pl-5 mb-2 space-y-1">
              <li>Large date range selected for analysis</li>
              <li>Complex calculations or reports</li>
              <li>Network connectivity issues</li>
              <li>Browser cache problems</li>
            </ul>
            <p className="mb-2"><strong>Solutions:</strong></p>
            <ol className="list-decimal pl-5 space-y-1">
              <li>Narrow your data selection to a smaller time range</li>
              <li>Try breaking complex reports into smaller ones</li>
              <li>Clear your browser cache and cookies</li>
              <li>Try using a different browser or device</li>
              <li>Check your network connection</li>
            </ol>
          </div>
        </div>
        
        <div>
          <h5 className="font-medium text-gray-800 mb-2">Login or access issues</h5>
          <div className="text-sm text-gray-600">
            <p className="mb-2"><strong>Symptoms:</strong> Unable to log in, access denied messages, or missing features.</p>
            <p className="mb-2"><strong>Possible causes:</strong></p>
            <ul className="list-disc pl-5 mb-2 space-y-1">
              <li>Incorrect login credentials</li>
              <li>Account permissions issues</li>
              <li>Account lockout due to failed attempts</li>
              <li>Session timeout</li>
            </ul>
            <p className="mb-2"><strong>Solutions:</strong></p>
            <ol className="list-decimal pl-5 space-y-1">
              <li>Use the "Forgot Password" option to reset your password</li>
              <li>Ensure you're using the correct email address</li>
              <li>Contact your system administrator to verify your permissions</li>
              <li>Check if your account has been deactivated</li>
              <li>Try clearing cookies or using an incognito/private browsing window</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  </div>
</>);

const faqSections: ManualSection[] = [
  {
    id: 'frequently-asked-questions',
    title: 'Frequently Asked Questions',
    content: FrequentlyAskedQuestionsContent,
  },
  {
    id: 'troubleshooting',
    title: 'Troubleshooting',
    content: TroubleshootingContent,
  }
];

export default faqSections;
