import React from 'react';
import { ManualSection } from '../ManualTypes';

// Define content for each section as JSX constants
const SettingsOverviewContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">System Settings</h3>
    <p className="text-gray-700 leading-relaxed">
      Configure your Energy Management System according to your organization's needs. The Settings section provides access to system-wide configurations, user management, permissions, and integration options.
    </p>
  </div>
  
  <div className="mb-8">
    <h3 className="text-lg font-medium text-gray-800 mb-4">Settings Categories</h3>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">User Management</h4>
        <p className="text-sm text-gray-600 mb-3">
          Manage user accounts, roles, and permissions.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>User account creation and management</li>
          <li>Role assignment and customization</li>
          <li>Permission configuration</li>
          <li>Authentication settings</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Organization Settings</h4>
        <p className="text-sm text-gray-600 mb-3">
          Configure organization-wide settings and preferences.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Company information and branding</li>
          <li>Facility and site management</li>
          <li>Department and cost center setup</li>
          <li>System defaults and preferences</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Data Management</h4>
        <p className="text-sm text-gray-600 mb-3">
          Configure data collection, storage, and processing settings.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Data collection intervals</li>
          <li>Storage and retention policies</li>
          <li>Data validation rules</li>
          <li>Import/export configuration</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">System Integration</h4>
        <p className="text-sm text-gray-600 mb-3">
          Connect your Energy Management System with other platforms and services.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>API configuration</li>
          <li>Third-party service connections</li>
          <li>Authentication integration</li>
          <li>Data exchange settings</li>
        </ul>
      </div>
    </div>
  </div>
  
  <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-6">
    <h4 className="font-medium text-blue-700 mb-2">Important Notes About Settings</h4>
    <ul className="list-disc pl-5 text-sm text-gray-600 space-y-2">
      <li>Setting changes may require system administrator approval depending on your role permissions.</li>
      <li>Some configuration changes might temporarily affect system availability while they are being applied.</li>
      <li>Always test major configuration changes in a test environment before applying to production if possible.</li>
      <li>The system maintains a log of all configuration changes for audit and troubleshooting purposes.</li>
    </ul>
  </div>
</>);

const UserManagementContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">User Management</h3>
    <p className="text-gray-700 leading-relaxed">
      The User Management section allows you to control who has access to your Energy Management System and what permissions they have.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Managing Users</h4>
      <p className="text-sm text-gray-600 mb-3">
        Create, edit, and manage user accounts.
      </p>
      <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mb-3">
        <h5 className="font-medium text-blue-700 mb-1">User Management Process:</h5>
        <ol className="list-decimal pl-5 text-sm text-gray-600 space-y-1">
          <li>Navigate to the Settings section and select "User Management"</li>
          <li>View the list of existing users</li>
          <li>Click "Add User" to create a new account</li>
          <li>Enter user details (name, email, contact information)</li>
          <li>Assign roles and permissions</li>
          <li>Configure access levels for specific facilities or data</li>
          <li>Save the user profile</li>
        </ol>
      </div>
      <p className="text-sm text-gray-600">
        New users will receive an email invitation with instructions to set up their password and complete their profile.
      </p>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Role Management</h4>
      <p className="text-sm text-gray-600 mb-3">
        Define and customize roles with specific permissions and access levels.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-3">
          <h5 className="font-medium text-blue-700 mb-1">Default Roles:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li><strong>System Administrator:</strong> Full system access</li>
            <li><strong>Energy Manager:</strong> Full access to analytics and reports</li>
            <li><strong>Facility Manager:</strong> Access limited to assigned facilities</li>
            <li><strong>Executive:</strong> Dashboard and report access only</li>
            <li><strong>Viewer:</strong> Read-only access to specified data</li>
          </ul>
        </div>
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-3">
          <h5 className="font-medium text-blue-700 mb-1">Custom Roles:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Create custom roles with specific permissions</li>
            <li>Clone and modify existing roles</li>
            <li>Define granular access controls</li>
            <li>Set facility-specific permissions</li>
            <li>Configure feature-level access</li>
          </ul>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-2">Authentication Settings</h4>
      <p className="text-sm text-gray-600 mb-3">
        Configure how users authenticate with the system.
      </p>
      <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mb-3">
        <h5 className="font-medium text-blue-700 mb-1">Authentication Options:</h5>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li><strong>Password Policies:</strong> Set password complexity requirements and expiration</li>
          <li><strong>Multi-Factor Authentication:</strong> Enable extra security with MFA</li>
          <li><strong>Single Sign-On (SSO):</strong> Integrate with corporate identity providers</li>
          <li><strong>LDAP/Active Directory:</strong> Connect to enterprise directory services</li>
          <li><strong>OAuth Integration:</strong> Enable third-party authentication providers</li>
        </ul>
      </div>
      <p className="text-sm text-gray-600">
        Authentication settings can be configured globally or customized for specific user groups.
      </p>
    </div>
  </div>
</>);

const OrganizationSettingsContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Organization Settings</h3>
    <p className="text-gray-700 leading-relaxed">
      Configure organization-wide settings and manage your company's structure within the Energy Management System.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Company Information</h4>
      <p className="text-sm text-gray-600 mb-3">
        Set up your organization's profile and basic information.
      </p>
      <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mb-3">
        <h5 className="font-medium text-blue-700 mb-1">Configuration Options:</h5>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Company name and legal information</li>
          <li>Contact details and addresses</li>
          <li>Company logo and branding assets</li>
          <li>Industry classification and business details</li>
          <li>Timezone and regional settings</li>
        </ul>
      </div>
      <p className="text-sm text-gray-600">
        This information is used throughout the system for reports, notifications, and user interfaces.
      </p>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Facility Management</h4>
      <p className="text-sm text-gray-600 mb-3">
        Add and configure facilities, buildings, and locations.
      </p>
      <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mb-3">
        <h5 className="font-medium text-blue-700 mb-1">Facility Configuration:</h5>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Add facilities with detailed profiles</li>
          <li>Set geographic location and address</li>
          <li>Define facility characteristics (size, type, etc.)</li>
          <li>Configure operational schedules</li>
          <li>Set up hierarchical structure (campus → building → floor)</li>
        </ul>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-2">Organizational Structure</h4>
      <p className="text-sm text-gray-600 mb-3">
        Define departments, cost centers, and other organizational units.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-3">
          <h5 className="font-medium text-blue-700 mb-1">Department Management:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Create department structure</li>
            <li>Assign facilities to departments</li>
            <li>Set department managers</li>
            <li>Configure department-level targets</li>
            <li>Define reporting hierarchy</li>
          </ul>
        </div>
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-3">
          <h5 className="font-medium text-blue-700 mb-1">Cost Center Configuration:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Define cost centers for billing</li>
            <li>Map meters to cost centers</li>
            <li>Configure allocation rules</li>
            <li>Set budget constraints</li>
            <li>Configure approval workflows</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</>);

const SystemIntegrationContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">System Integration</h3>
    <p className="text-gray-700 leading-relaxed">
      Connect your Energy Management System with other business systems and external services.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">API Configuration</h4>
      <p className="text-sm text-gray-600 mb-3">
        Set up and manage API access for integrating with other systems.
      </p>
      <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mb-3">
        <h5 className="font-medium text-blue-700 mb-1">API Management:</h5>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Generate API keys and access credentials</li>
          <li>Configure API permissions and scope</li>
          <li>Set rate limits and usage quotas</li>
          <li>Monitor API usage and performance</li>
          <li>Access API documentation and examples</li>
        </ul>
      </div>
      <p className="text-sm text-gray-600">
        The API allows secure integration with building management systems, SCADA, ERP, and other enterprise applications.
      </p>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Third-Party Integrations</h4>
      <p className="text-sm text-gray-600 mb-3">
        Connect with external services and data providers.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-3">
          <h5 className="font-medium text-blue-700 mb-1">Available Integrations:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li><strong>Weather Services:</strong> NOAA, Weather Underground</li>
            <li><strong>Utility Providers:</strong> Bill import and data access</li>
            <li><strong>Building Systems:</strong> BMS, HVAC controls</li>
            <li><strong>Business Apps:</strong> ERP, accounting systems</li>
            <li><strong>Notification Services:</strong> Email, SMS, messaging</li>
          </ul>
        </div>
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-3">
          <h5 className="font-medium text-blue-700 mb-1">Integration Setup:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Authentication configuration</li>
            <li>Data mapping and transformation</li>
            <li>Integration scheduling</li>
            <li>Error handling and notifications</li>
            <li>Logging and monitoring</li>
          </ul>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-2">Data Exchange Settings</h4>
      <p className="text-sm text-gray-600 mb-3">
        Configure how data is imported and exported from the system.
      </p>
      <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mb-3">
        <h5 className="font-medium text-blue-700 mb-1">Data Exchange Options:</h5>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li><strong>File Formats:</strong> Configure supported import/export formats (CSV, XML, JSON)</li>
          <li><strong>FTP/SFTP:</strong> Set up secure file transfer for automated exchanges</li>
          <li><strong>ETL Processes:</strong> Configure data transformation rules</li>
          <li><strong>Webhooks:</strong> Set up real-time data notifications</li>
          <li><strong>Scheduled Exports:</strong> Configure automated data exports</li>
        </ul>
      </div>
    </div>
  </div>
</>);

const settingsSections: ManualSection[] = [
  {
    id: 'settings-overview',
    title: 'Settings Overview',
    content: SettingsOverviewContent,
    images: [
      {
        src: '/images/manual/page-settings.png',
        alt: 'Settings Interface',
        caption: 'Settings interface with clean organization of system configuration options',
        width: '90%'
      }
    ]
  },
  {
    id: 'user-management',
    title: 'User Management',
    content: UserManagementContent,
  },
  {
    id: 'organization-settings',
    title: 'Organization Settings',
    content: OrganizationSettingsContent,
  },
  {
    id: 'system-integration',
    title: 'System Integration',
    content: SystemIntegrationContent,
  }
];

export default settingsSections;
