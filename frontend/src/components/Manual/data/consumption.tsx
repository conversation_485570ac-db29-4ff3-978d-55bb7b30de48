import React from 'react';
import { ManualSection } from '../ManualTypes';

// Define content for each section as JSX constants
const ConsumptionOverviewContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Electricity Consumption Analytics</h3>
    <p className="text-gray-700 leading-relaxed">
      Track and analyze your facility's electricity consumption with our minimalist, data-focused interface. 
      Monitor trends, compare against targets, and identify optimization opportunities.
    </p>
  </div>
  
  <div className="mb-8">
    <h3 className="text-lg font-medium text-gray-800 mb-4">Key Features</h3>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Real-time Consumption Tracking</h4>
        <p className="text-sm text-gray-600 mb-3">
          Monitor your electricity usage in real-time with intuitive visualizations.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Bar charts for energy consumption (kWh) data</li>
          <li>Line charts for power (kW) data visualization</li>
          <li>Time-based filtering with intuitive pill buttons</li>
          <li>Clear metric indicators with trend information</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Consumption Analysis</h4>
        <p className="text-sm text-gray-600 mb-3">
          Analyze consumption patterns and identify optimization opportunities.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Compare against baseline targets</li>
          <li>Identify consumption anomalies</li>
          <li>View detailed time-based breakdowns</li>
          <li>Track efficiency improvements</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Minimalist Design</h4>
        <p className="text-sm text-gray-600 mb-3">
          Tesla-inspired interface focusing on data clarity and usability.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Clean, distraction-free layouts</li>
          <li>Information-rich metrics cards</li>
          <li>Subtle gradients and shadows for visual depth</li>
          <li>Optimized for both desktop and mobile viewing</li>
        </ul>
      </div>
    </div>
  </div>
</>);

const consumptionSections: ManualSection[] = [
  {
    id: 'consumption-overview',
    title: 'Consumption Overview',
    content: ConsumptionOverviewContent,
    images: [
      {
        src: '/images/manual/page-consumption-analytics.png',
        alt: 'Consumption Analytics',
        caption: 'Electricity consumption analytics interface with Tesla-inspired minimalist design featuring intuitive pill buttons and clear metric cards',
        width: '90%'
      }
    ]
  }
];

export default consumptionSections;
