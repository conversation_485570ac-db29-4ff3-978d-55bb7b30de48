import React from 'react';
import { ManualSection } from '../ManualTypes';

// Define content for each section as JSX constants
const MetersOverviewContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Electricity Meters Management</h3>
    <p className="text-gray-700 leading-relaxed">
      The Electricity Meters section allows you to monitor, configure, and manage all connected electricity meters across your facilities.
    </p>
  </div>
  
  <div className="mb-8">
    <h3 className="text-lg font-medium text-gray-800 mb-4">Key Features</h3>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Meter Inventory</h4>
        <p className="text-sm text-gray-600 mb-3">
          View and manage all electricity meters connected to your Energy Management System.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Comprehensive meter listing</li>
          <li>Filter and search capabilities</li>
          <li>Detailed meter information</li>
          <li>Status monitoring and alerts</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Meter Configuration</h4>
        <p className="text-sm text-gray-600 mb-3">
          Set up and configure meters with appropriate parameters.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Communication settings</li>
          <li>Data collection intervals</li>
          <li>Threshold configuration</li>
          <li>Virtual meter setup</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Data Validation</h4>
        <p className="text-sm text-gray-600 mb-3">
          Ensure data quality and accuracy from your meters.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Data quality checks</li>
          <li>Gap detection and correction</li>
          <li>Manual reading entry</li>
          <li>Validation reporting</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Meter Hierarchies</h4>
        <p className="text-sm text-gray-600 mb-3">
          Organize meters in logical hierarchies for better management.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Parent-child relationships</li>
          <li>Building/zone assignments</li>
          <li>Submetering configuration</li>
          <li>Custom grouping options</li>
        </ul>
      </div>
    </div>
  </div>
</>);

const MeterDiagramContent = (<>
  <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
    <h3 className="font-medium text-gray-800 mb-3">Performance-Optimized Meter Diagram</h3>
    <p className="text-sm text-gray-600 mb-3">
      The Meter Diagram has been optimized with several performance enhancements:
    </p>
    <ul className="list-disc pl-5 text-sm text-gray-600 space-y-2 mb-4">
      <li><strong>Image preloading</strong> - Diagrams load instantly with preloading techniques</li>
      <li><strong>Component optimization</strong> - Extracted components like MetricsPanel and StatusLegend for better code splitting</li>
      <li><strong>Terminology updates</strong> - Changed from "Meter Clusters" to "Meter Groups" with accurate counts</li>
      <li><strong>Status indicators</strong> - Added clear visual indicators for each meter group</li>
      <li><strong>Power value display</strong> - Shows real-time power values for each group</li>
      <li><strong>React.memo application</strong> - Applied to all components to prevent unnecessary re-renders</li>
      <li><strong>Optimal layout</strong> - Status legend positioned strategically on the diagram</li>
    </ul>
  </div>
</>);

const MeterTypesContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Supported Meter Types</h3>
    <p className="text-gray-700 leading-relaxed">
      The system supports various types of electricity meters for monitoring energy consumption.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Electricity Meters</h4>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="md:w-2/3">
          <p className="text-sm text-gray-600 mb-3">
            Meters for monitoring electricity consumption, demand, and power quality.
          </p>
          <h5 className="font-medium text-gray-700 mb-1">Supported Capabilities:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Real-time power monitoring (kW, MW)</li>
            <li>Energy consumption tracking (kWh, MWh)</li>
            <li>Power factor measurement</li>
            <li>Demand tracking and profiling</li>
            <li>Power quality analysis</li>
            <li>Harmonics measurement</li>
          </ul>
        </div>
        <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
          <div className="text-center text-gray-500 text-sm">Electricity Meter Image</div>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-2">Virtual Meters</h4>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="md:w-2/3">
          <p className="text-sm text-gray-600 mb-3">
            Software-based meters that calculate values based on other meter readings or formulas.
          </p>
          <h5 className="font-medium text-gray-700 mb-1">Supported Configurations:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Aggregation meters (sum of multiple physical meters)</li>
            <li>Difference meters (subtraction between meters)</li>
          </ul>
          <div className="mt-4 bg-blue-50 p-3 rounded-lg border border-blue-100">
            <h5 className="font-medium text-blue-700 mb-1">How to Create a Virtual Meter:</h5>
            <ol className="list-decimal pl-5 text-sm text-gray-600 space-y-1">
              <li>Navigate to the Settings page</li>
              <li>Select "Meters Management" from the sidebar</li>
              <li>Click on "Create Virtual Meter" button</li>
              <li>Choose the Virtual Meter type (Aggregation or Difference)</li>
              <li>Select the source meters to be used in the calculation</li>
              <li>Name your Virtual Meter and add an optional description</li>
              <li>Save the configuration</li>
            </ol>
          </div>
        </div>
        <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
          <div className="text-center text-gray-500 text-sm">Virtual Meter Diagram</div>
        </div>
      </div>
    </div>
  </div>
</>);

const MeterDataContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Meter Data Management</h3>
    <p className="text-gray-700 leading-relaxed">
      Tools and features for collecting, validating, and managing electricity meter data.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Data Collection</h4>
      <p className="text-sm text-gray-600 mb-3">
        Methods for collecting data from connected meters.
      </p>
      <h5 className="font-medium text-gray-700 mb-1">Supported Methods:</h5>
      <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
        <li>Automated polling via Modbus protocols</li>
        <li>Push collection via MQTT</li>
        <li>BACnet integration</li>
        <li>API data ingestion</li>
        <li>Manual data entry</li>
        <li>File import (CSV)</li>
      </ul>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Data Validation and Editing</h4>
      <p className="text-sm text-gray-600 mb-3">
        Tools for ensuring data quality and integrity.
      </p>
      <h5 className="font-medium text-gray-700 mb-1">Validation Features:</h5>
      <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
        <li>Out-of-range detection</li>
        <li>Spike and dip identification</li>
        <li>Missing data detection</li>
        <li>Manual validation workflow</li>
        <li>Automated correction of common issues</li>
        <li>Audit trail of all changes</li>
      </ul>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-2">Data Storage and Retention</h4>
      <p className="text-sm text-gray-600 mb-3">
        How meter data is stored and managed in the system.
      </p>
      <h5 className="font-medium text-gray-700 mb-1">Storage Features:</h5>
      <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
        <li>Time-series database optimization</li>
        <li>Configurable data resolution</li>
        <li>Automatic data aggregation</li>
        <li>Archiving and retrieval</li>
        <li>Data export options</li>
        <li>Backup and recovery</li>
      </ul>
    </div>
  </div>
</>);

const MeterAlarmsContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Alarms and Notifications</h3>
    <p className="text-gray-700 leading-relaxed">
      Configure alarms and notifications to stay informed about meter status and issues.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Alarm Types</h4>
      <p className="text-sm text-gray-600 mb-3">
        Different types of alarms that can be configured for meters.
      </p>
      <div className="overflow-x-auto">
        <table className="w-full text-sm text-left text-gray-600">
          <thead className="text-xs text-gray-700 uppercase bg-gray-100">
            <tr>
              <th className="px-4 py-2">Alarm Type</th>
              <th className="px-4 py-2">Description</th>
              <th className="px-4 py-2">Typical Use Case</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b">
              <td className="px-4 py-2 font-medium">Threshold</td>
              <td className="px-4 py-2">Triggered when a meter value exceeds a defined threshold</td>
              <td className="px-4 py-2">Peak demand monitoring, consumption alerts</td>
            </tr>
            <tr className="border-b">
              <td className="px-4 py-2 font-medium">Rate of Change</td>
              <td className="px-4 py-2">Triggered when values change too quickly</td>
              <td className="px-4 py-2">Detect sudden spikes or drops in consumption</td>
            </tr>
            <tr className="border-b">
              <td className="px-4 py-2 font-medium">Communication</td>
              <td className="px-4 py-2">Triggered when a meter stops communicating</td>
              <td className="px-4 py-2">Meter connectivity issues, network problems</td>
            </tr>
            <tr className="border-b">
              <td className="px-4 py-2 font-medium">Data Quality</td>
              <td className="px-4 py-2">Triggered when data quality issues are detected</td>
              <td className="px-4 py-2">Erratic readings, incomplete data</td>
            </tr>
            <tr>
              <td className="px-4 py-2 font-medium">Schedule</td>
              <td className="px-4 py-2">Triggered based on time schedules</td>
              <td className="px-4 py-2">Off-hours energy consumption alerts</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-2">Notification Methods</h4>
      <p className="text-sm text-gray-600 mb-3">
        How you can receive alerts when alarms are triggered.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h5 className="font-medium text-gray-700 mb-1">In-System Notifications:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Dashboard alerts</li>
            <li>Notification center</li>
            <li>Color-coded status indicators</li>
            <li>Alarm history log</li>
          </ul>
        </div>
        <div>
          <h5 className="font-medium text-gray-700 mb-1">External Notifications:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Email alerts</li>
            <li>SMS text messages</li>
            <li>Mobile push notifications</li>
            <li>Integration with third-party systems</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</>);

const metersSections: ManualSection[] = [
  {
    id: 'meters-overview',
    title: 'Electricity Meters Overview',
    content: MetersOverviewContent,
    images: []
  },
  {
    id: 'meter-diagram',
    title: 'Meter Diagram',
    content: MeterDiagramContent,
    images: [
      {
        src: '/images/manual/page-meter-diagram.png',
        alt: 'Meter Diagram Interface',
        caption: 'Optimized Meter Diagram displaying meter groups with power values and status indicators',
        width: '90%'
      }
    ]
  },
  {
    id: 'meter-types',
    title: 'Meter Types',
    content: MeterTypesContent,
  },
  {
    id: 'meter-data',
    title: 'Meter Data Management',
    content: MeterDataContent,
  },
  {
    id: 'meter-alarms',
    title: 'Meter Alarms & Notifications',
    content: MeterAlarmsContent,
  }
];

export default metersSections;
