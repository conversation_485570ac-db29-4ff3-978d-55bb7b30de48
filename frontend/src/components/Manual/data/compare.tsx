import React from 'react';
import { ManualSection } from '../ManualTypes';

// Define content for each section as JSX constants
const CompareOverviewContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Energy Comparison Analytics</h3>
    <p className="text-gray-700 leading-relaxed">
      Compare energy metrics across different time periods, facilities, or equipment to identify patterns, anomalies, and optimization opportunities.
    </p>
  </div>
  
  <div className="mb-8">
    <h3 className="text-lg font-medium text-gray-800 mb-4">Key Features</h3>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Time Period Comparison</h4>
        <p className="text-sm text-gray-600 mb-3">
          Compare energy metrics across different time periods.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Day-to-day, week-to-week, or month-to-month comparisons</li>
          <li>Same-period-last-year analysis</li>
          <li>Seasonal variation tracking</li>
          <li>Interactive time selection controls</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Facility Comparison</h4>
        <p className="text-sm text-gray-600 mb-3">
          Compare energy usage and efficiency across different facilities.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Multi-facility energy performance benchmarking</li>
          <li>Normalized comparisons based on area or usage</li>
          <li>Identify high-performing and underperforming facilities</li>
          <li>Side-by-side metric comparison</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Visualization Tools</h4>
        <p className="text-sm text-gray-600 mb-3">
          Clear, intuitive visualizations following best practices for data representation.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Bar charts for energy consumption (kWh) comparisons</li>
          <li>Line charts for power (kW) trend comparisons</li>
          <li>Interactive tooltips with detailed information</li>
          <li>Customizable views and metrics</li>
        </ul>
      </div>
    </div>
  </div>
</>);

const compareSections: ManualSection[] = [
  {
    id: 'compare-overview',
    title: 'Energy Comparison Overview',
    content: CompareOverviewContent,
    images: [
      {
        src: '/images/manual/page-energy-compare.png',
        alt: 'Energy Comparison Analytics',
        caption: 'Energy comparison interface with side-by-side metric analysis, using bar charts for consumption (kWh) and line charts for power (kW) data',
        width: '90%'
      }
    ]
  }
];

export default compareSections;
