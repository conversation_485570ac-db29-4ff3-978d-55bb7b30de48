import React from 'react';
import { ManualSection } from '../ManualTypes';

// Define content for each section as JSX constants
const AnalyticsOverviewContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Analytics Tools Overview</h3>
    <p className="text-gray-700 leading-relaxed">
          The Analytics section provides advanced tools to transform your energy data into actionable insights. These tools help you understand consumption patterns and compare performance across facilities and time periods.
        </p>
      </div>
      
  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <div className="h-2 bg-blue-500"></div>
      <div className="p-4">
        <h3 className="font-medium text-blue-700 mb-2">Consumption Analysis</h3>
        <p className="text-sm text-gray-600 mb-3">
              Visualize and analyze energy usage across different time periods, locations, and systems.
            </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>Time-series trend analysis</li>
              <li>Usage breakdowns by category</li>
              <li>Anomaly detection</li>
              <li>Pattern recognition</li>
            </ul>
          </div>
        </div>
        
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      <div className="h-2 bg-blue-500"></div>
      <div className="p-4">
        <h3 className="font-medium text-blue-700 mb-2">Comparative Analytics</h3>
        <p className="text-sm text-gray-600 mb-3">
              Compare performance across time periods, facilities, or against industry benchmarks.
            </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>Period-over-period comparisons</li>
              <li>Facility benchmarking</li>
              <li>Peer group analysis</li>
              <li>Weather normalization</li>
            </ul>
          </div>
        </div>
      </div>
</>);

const ConsumptionAnalysisContent = (<>
  <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
    <h3 className="font-medium text-gray-800 mb-3">About the Electricity Performance Analytics UI</h3>
    <p className="text-sm text-gray-600 mb-3">
      The analytics interface features a minimalist design inspired by Tesla's UI principles:
    </p>
    <ul className="list-disc pl-5 text-sm text-gray-600 space-y-2 mb-4">
      <li><strong>Enhanced visual hierarchy</strong> with clear page title/description and redesigned metric cards</li>
      <li><strong>Intuitive pill buttons</strong> replacing traditional dropdowns for more intuitive interaction</li>
      <li><strong>Directional indicators</strong> showing metric changes with up/down arrows and visual color coding</li>
      <li><strong>Optimized performance</strong> with React.memo for components and consistent styling</li>
      <li><strong>"Today" as primary focus</strong> with visual emphasis to highlight current performance</li>
    </ul>
  </div>
  
  <div className="space-y-6">
    <h3 className="text-lg font-medium text-gray-800 mb-4">Available Visualizations</h3>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Time Series Charts</h4>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="md:w-2/3">
          <p className="text-sm text-gray-600 mb-3">
            Track energy consumption over time to identify trends, patterns, and anomalies.
          </p>
          <h5 className="font-medium text-gray-700 mb-1">Key Features:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Adjustable time periods (hourly, daily, weekly, monthly)</li>
            <li>Multiple data series comparison</li>
            <li>Anomaly highlighting</li>
            <li>Trend line overlay</li>
            <li>Zoom and pan functionality</li>
          </ul>
        </div>
        <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
          <div className="text-center text-gray-500 text-sm">Chart Preview Image</div>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Comparative Bar Charts</h4>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="md:w-2/3">
          <p className="text-sm text-gray-600 mb-3">
            Compare consumption across different time periods, facilities, or systems.
          </p>
          <h5 className="font-medium text-gray-700 mb-1">Key Features:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Side-by-side comparisons</li>
            <li>Percentage change indicators</li>
            <li>Color-coded performance indicators</li>
            <li>Sortable data presentation</li>
            <li>Customizable comparison periods</li>
          </ul>
        </div>
        <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
          <div className="text-center text-gray-500 text-sm">Chart Preview Image</div>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-2">Consumption Breakdown</h4>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="md:w-2/3">
          <p className="text-sm text-gray-600 mb-3">
            See detailed breakdowns of energy usage by category, system, or location.
          </p>
          <h5 className="font-medium text-gray-700 mb-1">Key Features:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Pie and donut charts showing percentage distribution</li>
            <li>Hierarchical treemaps for multi-level analysis</li>
            <li>Drilldown capability to explore detailed categories</li>
            <li>Absolute and percentage views</li>
            <li>Top consumers identification</li>
          </ul>
        </div>
        <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
          <div className="text-center text-gray-500 text-sm">Chart Preview Image</div>
        </div>
      </div>
    </div>
  </div>
</>);

const ComparativeAnalyticsContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Comparative Analytics Features</h3>
    <p className="text-gray-700 leading-relaxed">
      Compare energy performance across different time periods, buildings, or departments to identify trends, outliers, and opportunities for improvement.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Period-over-Period Comparison</h4>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="md:w-2/3">
          <p className="text-sm text-gray-600 mb-3">
            Compare current consumption with previous periods to track progress and identify trends.
          </p>
          <h5 className="font-medium text-gray-700 mb-1">Key Features:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Day-to-day, week-to-week, month-to-month, and year-to-year comparisons</li>
            <li>Percentage change visualization</li>
            <li>Trend analysis</li>
            <li>Outlier identification</li>
            <li>Customizable baseline periods</li>
          </ul>
        </div>
        <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
          <div className="text-center text-gray-500 text-sm">Chart Preview Image</div>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Facility Benchmarking</h4>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="md:w-2/3">
          <p className="text-sm text-gray-600 mb-3">
            Compare performance across different buildings or facilities to identify best practices and improvement areas.
          </p>
          <h5 className="font-medium text-gray-700 mb-1">Key Features:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Side-by-side facility comparison</li>
            <li>Normalized metrics (kWh/sq ft, kWh/person, etc.)</li>
            <li>Performance ranking</li>
            <li>Efficiency scoring</li>
            <li>Best practice identification</li>
          </ul>
        </div>
        <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
          <div className="text-center text-gray-500 text-sm">Chart Preview Image</div>
        </div>
      </div>
    </div>
  </div>
</>);

const analyticsSections: ManualSection[] = [
  {
    id: 'analytics-overview',
    title: 'Analytics Overview',
    content: AnalyticsOverviewContent,
    images: [
      {
        src: '/images/manual/page-analytics.png',
        alt: 'Analytics Dashboard',
        caption: 'Analytics Dashboard with Tesla-inspired minimalist design featuring intuitive pill buttons and clear metric cards',
        width: '90%'
      },
      {
        src: '/images/manual/analytics-metrics-cards.png',
        alt: 'Metric Cards with Trend Indicators',
        caption: 'Redesigned metric cards with directional arrows showing performance changes and subtle shadows for depth',
        width: '80%'
      },
      {
        src: '/images/manual/analytics-pill-buttons.png',
        alt: 'Intuitive Pill Buttons',
        caption: 'Simplified control interface using pill buttons instead of traditional dropdowns for better usability',
        width: '80%'
      },
      {
        src: '/images/manual/power-line-chart.png',
        alt: 'Power Trend Line Chart',
        caption: 'Line chart visualization for power (kW) data following design best practices',
        width: '80%'
      },
      {
        src: '/images/manual/energy-bar-chart.png',
        alt: 'Energy Consumption Bar Chart',
        caption: 'Bar chart visualization for energy consumption (kWh) data following design best practices',
        width: '80%'
      }
    ]
  },
  {
    id: 'consumption-analysis',
    title: 'Consumption Analysis',
    content: ConsumptionAnalysisContent,
    images: []
  },
  {
    id: 'comparative-analytics',
    title: 'Comparative Analytics',
    content: ComparativeAnalyticsContent,
    images: []
  }
];

export default analyticsSections;
