import React from 'react';
import { ManualSection } from '../ManualTypes';

// Define content for each section as JSX constants
const GlossaryTermsContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Energy Management Terminology</h3>
    <p className="text-gray-700 leading-relaxed">
      This glossary provides definitions for common terms used throughout the Energy Management System.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="mb-6">
      <h4 className="font-medium text-blue-700 mb-3 pb-2 border-b border-gray-200">General Energy Terms</h4>
      
      <dl className="space-y-4">
        <div>
          <dt className="font-medium text-gray-800">Demand</dt>
          <dd className="text-sm text-gray-600 pl-4">The rate at which energy is delivered to loads and scheduling points by generation, transmission, or distribution facilities.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Energy Consumption</dt>
          <dd className="text-sm text-gray-600 pl-4">The amount of energy consumed over a period of time, typically measured in kilowatt-hours (kWh) for electricity.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Load Factor</dt>
          <dd className="text-sm text-gray-600 pl-4">The ratio of average load to peak load during a specific period of time, expressed as a percentage. A high load factor indicates relatively constant energy usage.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Peak Demand</dt>
          <dd className="text-sm text-gray-600 pl-4">The highest amount of power drawn within a specified time period, often measured in kilowatts (kW).</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Baseload</dt>
          <dd className="text-sm text-gray-600 pl-4">The minimum level of demand on an electrical supply system over 24 hours.</dd>
        </div>
      </dl>
    </div>
    
    <div className="mb-6">
      <h4 className="font-medium text-blue-700 mb-3 pb-2 border-b border-gray-200">Metering Terms</h4>
      
      <dl className="space-y-4">
        <div>
          <dt className="font-medium text-gray-800">AMI (Advanced Metering Infrastructure)</dt>
          <dd className="text-sm text-gray-600 pl-4">An integrated system of smart meters, communications networks, and data management systems that enables two-way communication between utilities and customers.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Interval Data</dt>
          <dd className="text-sm text-gray-600 pl-4">Energy consumption data collected at regular intervals (e.g., 15-minute, hourly) rather than just monthly readings.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Meter Gateway</dt>
          <dd className="text-sm text-gray-600 pl-4">A device that collects data from multiple meters and transmits it to a central system.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Pulse Output</dt>
          <dd className="text-sm text-gray-600 pl-4">A meter output that generates pulses at a rate proportional to energy consumption.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Submetering</dt>
          <dd className="text-sm text-gray-600 pl-4">The installation of metering devices that monitor the energy usage of specific systems or areas within a facility, after the primary utility meter.</dd>
        </div>
      </dl>
    </div>
    
    <div className="mb-6">
      <h4 className="font-medium text-blue-700 mb-3 pb-2 border-b border-gray-200">Analytics & Reporting Terms</h4>
      
      <dl className="space-y-4">
        <div>
          <dt className="font-medium text-gray-800">Baseline</dt>
          <dd className="text-sm text-gray-600 pl-4">A reference point or condition against which future performance is measured, typically a historical average of energy consumption.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Degree Days</dt>
          <dd className="text-sm text-gray-600 pl-4">A measurement designed to reflect the demand for energy needed to heat or cool a building. Heating Degree Days (HDD) and Cooling Degree Days (CDD) are commonly used.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Energy Intensity</dt>
          <dd className="text-sm text-gray-600 pl-4">Energy consumption per square foot of facility space, often used as a benchmark metric.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Normalization</dt>
          <dd className="text-sm text-gray-600 pl-4">The process of adjusting energy data to account for variables such as weather, occupancy, or production volume, allowing for fair comparisons.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Regression Analysis</dt>
          <dd className="text-sm text-gray-600 pl-4">A statistical method used to analyze the relationship between energy consumption and influencing factors like weather, occupancy, etc.</dd>
        </div>
      </dl>
    </div>
    
    <div className="mb-6">
      <h4 className="font-medium text-blue-700 mb-3 pb-2 border-b border-gray-200">Cost & Billing Terms</h4>
      
      <dl className="space-y-4">
        <div>
          <dt className="font-medium text-gray-800">Demand Charge</dt>
          <dd className="text-sm text-gray-600 pl-4">A fee based on the maximum rate of electrical consumption during a billing period, typically measured in kilowatts (kW).</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Energy Charge</dt>
          <dd className="text-sm text-gray-600 pl-4">A fee based on the total amount of electricity consumed during a billing period, typically measured in kilowatt-hours (kWh).</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Rate Structure</dt>
          <dd className="text-sm text-gray-600 pl-4">The framework for how a utility charges for energy consumption, which may include fixed charges, energy charges, demand charges, and time-of-use pricing.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Time-of-Use (TOU) Rates</dt>
          <dd className="text-sm text-gray-600 pl-4">Electricity rates that vary based on the time of day, day of week, and season, typically with higher rates during peak demand periods.</dd>
        </div>
      </dl>
    </div>
    
    <div className="mb-6">
      <h4 className="font-medium text-blue-700 mb-3 pb-2 border-b border-gray-200">Sustainability Terms</h4>
      
      <dl className="space-y-4">
        <div>
          <dt className="font-medium text-gray-800">Carbon Footprint</dt>
          <dd className="text-sm text-gray-600 pl-4">The total greenhouse gas emissions caused directly and indirectly by an individual, organization, event, or product.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Greenhouse Gas (GHG) Emissions</dt>
          <dd className="text-sm text-gray-600 pl-4">Gases that trap heat in the atmosphere, including carbon dioxide (CO2), methane (CH4), nitrous oxide (N2O), and fluorinated gases.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Renewable Energy</dt>
          <dd className="text-sm text-gray-600 pl-4">Energy collected from resources that are naturally replenished on a human timescale, such as sunlight, wind, rain, tides, waves, and geothermal heat.</dd>
        </div>
        
        <div>
          <dt className="font-medium text-gray-800">Scope 1, 2, and 3 Emissions</dt>
          <dd className="text-sm text-gray-600 pl-4">Categories used in greenhouse gas accounting. Scope 1 covers direct emissions, Scope 2 covers indirect emissions from purchased electricity, and Scope 3 covers all other indirect emissions.</dd>
        </div>
      </dl>
    </div>
  </div>
</>);

const AbbreviationsContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Common Abbreviations</h3>
    <p className="text-gray-700 leading-relaxed">
      These abbreviations are frequently used throughout the Energy Management System and in energy management discussions.
    </p>
  </div>
  
  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-3 pb-2 border-b border-gray-200">Energy & Power</h4>
      
      <dl className="space-y-3">
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">kW</dt>
          <dd className="text-sm text-gray-600">Kilowatt (power)</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">kWh</dt>
          <dd className="text-sm text-gray-600">Kilowatt-hour (energy)</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">MW</dt>
          <dd className="text-sm text-gray-600">Megawatt</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">MWh</dt>
          <dd className="text-sm text-gray-600">Megawatt-hour</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">BTU</dt>
          <dd className="text-sm text-gray-600">British Thermal Unit</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">HVAC</dt>
          <dd className="text-sm text-gray-600">Heating, Ventilation, and Air Conditioning</dd>
        </div>
      </dl>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-3 pb-2 border-b border-gray-200">Metering & Data</h4>
      
      <dl className="space-y-3">
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">AMI</dt>
          <dd className="text-sm text-gray-600">Advanced Metering Infrastructure</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">AMR</dt>
          <dd className="text-sm text-gray-600">Automated Meter Reading</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">BMS</dt>
          <dd className="text-sm text-gray-600">Building Management System</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">EMS</dt>
          <dd className="text-sm text-gray-600">Energy Management System</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">IoT</dt>
          <dd className="text-sm text-gray-600">Internet of Things</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">M&V</dt>
          <dd className="text-sm text-gray-600">Measurement and Verification</dd>
        </div>
      </dl>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-3 pb-2 border-b border-gray-200">Billing & Economics</h4>
      
      <dl className="space-y-3">
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">ROI</dt>
          <dd className="text-sm text-gray-600">Return on Investment</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">TOU</dt>
          <dd className="text-sm text-gray-600">Time of Use</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">CPP</dt>
          <dd className="text-sm text-gray-600">Critical Peak Pricing</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">DR</dt>
          <dd className="text-sm text-gray-600">Demand Response</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">NPV</dt>
          <dd className="text-sm text-gray-600">Net Present Value</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">SPP</dt>
          <dd className="text-sm text-gray-600">Simple Payback Period</dd>
        </div>
      </dl>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-3 pb-2 border-b border-gray-200">Sustainability</h4>
      
      <dl className="space-y-3">
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">GHG</dt>
          <dd className="text-sm text-gray-600">Greenhouse Gas</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">CO2e</dt>
          <dd className="text-sm text-gray-600">Carbon Dioxide Equivalent</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">ESG</dt>
          <dd className="text-sm text-gray-600">Environmental, Social, and Governance</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">REC</dt>
          <dd className="text-sm text-gray-600">Renewable Energy Certificate</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">PPA</dt>
          <dd className="text-sm text-gray-600">Power Purchase Agreement</dd>
        </div>
        
        <div className="flex">
          <dt className="font-medium text-gray-800 w-24">LEED</dt>
          <dd className="text-sm text-gray-600">Leadership in Energy and Environmental Design</dd>
        </div>
      </dl>
    </div>
  </div>
</>);

const glossarySections: ManualSection[] = [
  {
    id: 'glossary-terms',
    title: 'Glossary of Terms',
    content: GlossaryTermsContent,
  },
  {
    id: 'abbreviations',
    title: 'Abbreviations',
    content: AbbreviationsContent,
  }
];

export default glossarySections;
