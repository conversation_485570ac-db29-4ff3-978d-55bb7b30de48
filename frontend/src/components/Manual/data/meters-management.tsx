import React from 'react';
import { ManualSection } from '../ManualTypes';

// Define content for each section as JSX constants
const MetersManagementOverviewContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Smart Meter Management</h3>
    <p className="text-gray-700 leading-relaxed">
      View, monitor, and manage your electricity meters through a comprehensive interface. Track meter status, organize meter groups, and monitor real-time power and energy values.
    </p>
  </div>
  
  <div className="mb-8">
    <h3 className="text-lg font-medium text-gray-800 mb-4">Key Features</h3>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Meter Status Monitoring</h4>
        <p className="text-sm text-gray-600 mb-3">
          Monitor the status and health of all connected electricity meters.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Real-time status indicators</li>
          <li>Communication quality monitoring</li>
          <li>Error and alert notifications</li>
          <li>Detailed meter diagnostics</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Meter Organization</h4>
        <p className="text-sm text-gray-600 mb-3">
          Organize meters into logical groups for easier management.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Meter grouping by location or function</li>
          <li>Group-level metrics and status indicators</li>
          <li>Hierarchical meter organization</li>
          <li>Custom tagging and filtering</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Power and Energy Monitoring</h4>
        <p className="text-sm text-gray-600 mb-3">
          Track real-time and historical power and energy readings.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Real-time power (kW) values</li>
          <li>Cumulative energy (kWh) readings</li>
          <li>Line charts for power data visualization</li>
          <li>Bar charts for energy consumption data</li>
        </ul>
      </div>
    </div>
  </div>
</>);

const metersManagementSections: ManualSection[] = [
  {
    id: 'meters-management-overview',
    title: 'Meters Management Overview',
    content: MetersManagementOverviewContent,
    images: [
      {
        src: '/images/manual/page-meters-management.png',
        alt: 'Meters Management Interface',
        caption: 'Smart meter management interface showing meter groups, status indicators, and real-time power values',
        width: '90%'
      }
    ]
  }
];

export default metersManagementSections;
