import { ManualSection } from '../ManualTypes';

const userManagementScreenshotsSections: ManualSection[] = [
  {
    id: 'user-management-overview',
    title: 'User Management Interface',
    content: `
      <div class="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
        <h3 class="text-xl font-semibold text-primary-blue mb-2">User Management Interface</h3>
        <p class="text-gray-700 leading-relaxed">
          The User Management interface allows administrators to create, edit, and manage user accounts, roles, and permissions for the Energy Management System.
        </p>
      </div>
      
      <div class="mb-8">
        <h3 class="text-lg font-medium text-gray-800 mb-4">Key Features</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div class="bg-white rounded-lg border border-gray-200 p-4">
            <h4 class="font-medium text-blue-700 mb-2">User Account Management</h4>
            <p class="text-sm text-gray-600 mb-3">
              Create and manage user accounts with appropriate access levels.
            </p>
            <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>User creation with email verification</li>
              <li>Account status monitoring</li>
              <li>Password policy enforcement</li>
              <li>Multi-factor authentication options</li>
            </ul>
          </div>
          
          <div class="bg-white rounded-lg border border-gray-200 p-4">
            <h4 class="font-medium text-blue-700 mb-2">Role-Based Access Control</h4>
            <p class="text-sm text-gray-600 mb-3">
              Implement granular permission control through customizable roles.
            </p>
            <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>Predefined role templates</li>
              <li>Custom role creation</li>
              <li>Permission inheritance and overrides</li>
              <li>Role assignment to user groups</li>
            </ul>
          </div>
          
          <div class="bg-white rounded-lg border border-gray-200 p-4">
            <h4 class="font-medium text-blue-700 mb-2">User Activity Monitoring</h4>
            <p class="text-sm text-gray-600 mb-3">
              Track user activity and system access for security and compliance.
            </p>
            <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>Login/logout tracking</li>
              <li>Action audit logs</li>
              <li>Session management</li>
              <li>Security event alerts</li>
            </ul>
          </div>
        </div>
      </div>
    `,
    images: [
      {
        src: '/images/manual/page-user-management.png',
        alt: 'User Management Interface',
        caption: 'User management interface showing account listing, role assignment, and permission controls',
        width: '90%'
      }
    ]
  }
];

export default userManagementScreenshotsSections;
