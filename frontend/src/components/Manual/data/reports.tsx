import React from 'react';
import { ManualSection } from '../ManualTypes';

// Define content for each section as JSX constants
const ReportsOverviewContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Reports & Scheduling</h3>
    <p className="text-gray-700 leading-relaxed">
      Generate, customize, schedule, and share detailed reports about your energy usage, costs, and savings.
    </p>
  </div>
  
  <div className="mb-8">
    <h3 className="text-lg font-medium text-gray-800 mb-4">Key Features</h3>
    
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Standard Reports</h4>
        <p className="text-sm text-gray-600 mb-3">
          Access pre-configured reports for common energy management needs.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Monthly consumption summaries</li>
          <li>Cost breakdown reports</li>
          <li>Demand profile analysis</li>
          <li>Efficiency and savings reports</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Custom Reports</h4>
        <p className="text-sm text-gray-600 mb-3">
          Build tailored reports to meet your specific requirements.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Custom metrics and calculations</li>
          <li>Personalized layouts and branding</li>
          <li>Multi-facility comparisons</li>
          <li>Advanced filtering options</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Report Scheduling</h4>
        <p className="text-sm text-gray-600 mb-3">
          Automate report generation and delivery on your preferred schedule.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>Flexible scheduling options</li>
          <li>Multiple delivery methods</li>
          <li>Recipient management</li>
          <li>Execution status tracking</li>
        </ul>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h4 className="font-medium text-blue-700 mb-2">Export & Sharing</h4>
        <p className="text-sm text-gray-600 mb-3">
          Share your insights with stakeholders in various formats.
        </p>
        <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
          <li>PDF and CSV exports</li>
          <li>Email distribution</li>
          <li>Shared links and permissions</li>
          <li>Interactive sharing options</li>
        </ul>
      </div>
    </div>
  </div>
</>);

const StandardReportsContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Standard Report Library</h3>
    <p className="text-gray-700 leading-relaxed">
      The system includes a comprehensive set of pre-configured reports designed to address common energy management needs.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Consumption Summary Reports</h4>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="md:w-2/3">
          <p className="text-sm text-gray-600 mb-3">
            Overview reports that summarize energy consumption across different time periods and facilities.
          </p>
          <h5 className="font-medium text-gray-700 mb-1">Available Reports:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li><strong>Monthly Consumption Summary:</strong> Month-by-month overview with trend analysis</li>
            <li><strong>Daily Usage Profile:</strong> Hourly consumption patterns for typical days</li>
            <li><strong>Year-over-Year Comparison:</strong> Long-term trend analysis with annual comparisons</li>
            <li><strong>Multi-Facility Consumption:</strong> Comparative view across all facilities</li>
          </ul>
        </div>
        <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
          <div className="text-center text-gray-500 text-sm">Report Preview Image</div>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Cost & Financial Reports</h4>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="md:w-2/3">
          <p className="text-sm text-gray-600 mb-3">
            Reports focused on energy costs, budgeting, and financial analysis.
          </p>
          <h5 className="font-medium text-gray-700 mb-1">Available Reports:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li><strong>Monthly Cost Summary:</strong> Detailed breakdown of energy expenses</li>
            <li><strong>Budget Tracking Report:</strong> Actual vs. budgeted energy costs</li>
            <li><strong>Rate Analysis Report:</strong> Impact of different rate structures on costs</li>
            <li><strong>Cost Projection Report:</strong> Forecasted costs based on historical patterns</li>
          </ul>
        </div>
        <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
          <div className="text-center text-gray-500 text-sm">Report Preview Image</div>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-2">Sustainability Reports</h4>
      <div className="flex flex-col md:flex-row gap-4">
        <div className="md:w-2/3">
          <p className="text-sm text-gray-600 mb-3">
            Reports tracking environmental impact, carbon emissions, and sustainability metrics.
          </p>
          <h5 className="font-medium text-gray-700 mb-1">Available Reports:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li><strong>Carbon Emissions Report:</strong> CO2 and greenhouse gas emissions tracking</li>
            <li><strong>Sustainability KPI Dashboard:</strong> Key performance indicators for sustainability goals</li>
            <li><strong>Energy Efficiency Report:</strong> Efficiency metrics and improvement tracking</li>
            <li><strong>Renewable Energy Integration:</strong> Analysis of renewable energy usage</li>
          </ul>
        </div>
        <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
          <div className="text-center text-gray-500 text-sm">Report Preview Image</div>
        </div>
      </div>
    </div>
  </div>
</>);

const CustomReportsContent = (<>
  <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
    <h3 className="text-xl font-semibold text-primary-blue mb-2">Custom Report Builder</h3>
    <p className="text-gray-700 leading-relaxed">
      Create tailored reports to meet your specific requirements using the report builder tool.
    </p>
  </div>
  
  <div className="space-y-6">
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Creating Custom Reports</h4>
      <p className="text-sm text-gray-600 mb-3">
        The report builder interface lets you construct reports with exactly the data and format you need.
      </p>
      <div className="bg-blue-50 border border-blue-100 rounded-lg p-3 mb-3">
        <h5 className="font-medium text-blue-700 mb-1">Custom Report Creation Process:</h5>
        <ol className="list-decimal pl-5 text-sm text-gray-600 space-y-1">
          <li>Navigate to the Reports section and select "Create Custom Report"</li>
          <li>Select a report template or start from scratch</li>
          <li>Add data sources from available meters and calculated fields</li>
          <li>Configure visualization types for each data component</li>
          <li>Set up filtering and sorting options</li>
          <li>Apply formatting and branding</li>
          <li>Save and name your custom report</li>
        </ol>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <h4 className="font-medium text-blue-700 mb-2">Available Data Components</h4>
      <p className="text-sm text-gray-600 mb-3">
        The custom report builder offers a wide range of data components that can be included in your reports.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-3">
          <h5 className="font-medium text-blue-700 mb-1">Consumption Components:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Time-series consumption data</li>
            <li>Consumption breakdowns</li>
            <li>Peak demand tracking</li>
            <li>Load profile visualizations</li>
            <li>Anomaly detection</li>
          </ul>
        </div>
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-3">
          <h5 className="font-medium text-blue-700 mb-1">Financial Components:</h5>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Cost calculations</li>
            <li>Budget vs. actual comparisons</li>
            <li>Rate impact analysis</li>
            <li>Savings calculations</li>
            <li>ROI metrics</li>
          </ul>
        </div>
      </div>
    </div>
    
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h4 className="font-medium text-blue-700 mb-2">Visualization Options</h4>
      <p className="text-sm text-gray-600 mb-3">
        Choose from a variety of visualization types to present your data effectively.
      </p>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3">
        <div className="bg-gray-50 border border-gray-100 rounded p-2 text-center">
          <p className="text-sm font-medium text-gray-700">Line Charts</p>
          <p className="text-xs text-gray-500">For time-series data</p>
        </div>
        <div className="bg-gray-50 border border-gray-100 rounded p-2 text-center">
          <p className="text-sm font-medium text-gray-700">Bar Charts</p>
          <p className="text-xs text-gray-500">For comparisons</p>
        </div>
        <div className="bg-gray-50 border border-gray-100 rounded p-2 text-center">
          <p className="text-sm font-medium text-gray-700">Pie Charts</p>
          <p className="text-xs text-gray-500">For distributions</p>
        </div>
        <div className="bg-gray-50 border border-gray-100 rounded p-2 text-center">
          <p className="text-sm font-medium text-gray-700">Heat Maps</p>
          <p className="text-xs text-gray-500">For intensity patterns</p>
        </div>
        <div className="bg-gray-50 border border-gray-100 rounded p-2 text-center">
          <p className="text-sm font-medium text-gray-700">Tables</p>
          <p className="text-xs text-gray-500">For detailed data</p>
        </div>
        <div className="bg-gray-50 border border-gray-100 rounded p-2 text-center">
          <p className="text-sm font-medium text-gray-700">Gauges</p>
          <p className="text-xs text-gray-500">For KPIs</p>
        </div>
        <div className="bg-gray-50 border border-gray-100 rounded p-2 text-center">
          <p className="text-sm font-medium text-gray-700">Maps</p>
          <p className="text-xs text-gray-500">For geographic data</p>
        </div>
        <div className="bg-gray-50 border border-gray-100 rounded p-2 text-center">
          <p className="text-sm font-medium text-gray-700">Custom</p>
          <p className="text-xs text-gray-500">For special needs</p>
        </div>
      </div>
    </div>
  </div>
</>);

const reportsSections: ManualSection[] = [
  {
    id: 'reports-overview',
    title: 'Reports Overview',
    content: ReportsOverviewContent,
    images: [
      {
        src: '/images/manual/page-reports.png',
        alt: 'Reports Dashboard',
        caption: 'Reports interface with clean minimalist design for easy navigation and report generation',
        width: '90%'
      }
    ]
  },
  {
    id: 'standard-reports',
    title: 'Standard Reports',
    content: StandardReportsContent,
  },
  {
    id: 'custom-reports',
    title: 'Custom Reports',
    content: CustomReportsContent,
  }
];

export default reportsSections;
