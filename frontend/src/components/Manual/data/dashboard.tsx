import React from 'react';
import { ManualSection } from '../ManualTypes';

const dashboardSections: ManualSection[] = [
  {
    id: 'dashboard-overview',
    title: 'Dashboard Overview',
    content: (<>
      <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
        <h3 className="text-xl font-semibold text-primary-blue mb-2">Dashboard Guide</h3>
        <p className="text-gray-700 leading-relaxed">
          The Dashboard is your central hub for monitoring energy performance at a glance. This page provides real-time insights into your energy consumption, costs, savings, and system status.
        </p>
      </div>
      
      <div className="mb-8">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Key Dashboard Features</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h4 className="font-medium text-blue-700 mb-2">Energy Consumption Overview</h4>
            <p className="text-sm text-gray-600 mb-3">
              Track real-time and historical energy usage across all connected facilities and meters.
            </p>
            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>View consumption trends over time</li>
              <li>Compare usage against baselines</li>
              <li>Identify peak consumption periods</li>
              <li>Monitor usage by building or department</li>
            </ul>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h4 className="font-medium text-blue-700 mb-2">Cost Tracking</h4>
            <p className="text-sm text-gray-600 mb-3">
              Monitor your energy expenditure and identify cost-saving opportunities.
            </p>
            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>Track current and projected costs</li>
              <li>View cost breakdown by facility</li>
              <li>Analyze cost trends over time</li>
              <li>Receive alerts for unusual spending</li>
            </ul>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h4 className="font-medium text-blue-700 mb-2">Sustainability Metrics</h4>
            <p className="text-sm text-gray-600 mb-3">
              Visualize your environmental impact and sustainability performance.
            </p>
            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>Track carbon emissions</li>
              <li>Monitor renewable energy production</li>
              <li>View water consumption metrics</li>
              <li>Measure progress against sustainability goals</li>
            </ul>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h4 className="font-medium text-blue-700 mb-2">System Health</h4>
            <p className="text-sm text-gray-600 mb-3">
              Ensure all components of your energy management system are functioning properly.
            </p>
            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>View meter connection status</li>
              <li>Monitor data quality indicators</li>
              <li>Track system performance</li>
              <li>Receive alerts for any issues</li>
            </ul>
          </div>
        </div>
      </div>
      
      <div className="mb-8">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Dashboard Customization</h3>
        <p className="text-gray-600 mb-4">
          The dashboard is fully customizable to meet your specific monitoring needs. You can add, remove, and rearrange widgets to create a personalized view of your energy data.
        </p>
        
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-4">
          <h4 className="font-medium text-blue-700 mb-2">Widget Configuration</h4>
          <ol className="list-decimal pl-5 text-sm text-gray-600 space-y-2">
            <li>Click the <strong>Customize</strong> button in the top-right corner of the dashboard</li>
            <li>Use the <strong>+ Add Widget</strong> button to select from available widget types</li>
            <li>Drag and drop widgets to rearrange their positions</li>
            <li>Click the gear icon on any widget to configure its settings</li>
            <li>Use the <strong>Save Layout</strong> button to preserve your changes</li>
          </ol>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h4 className="font-medium text-gray-800 mb-2">Available Widget Types</h4>
          <ul className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-blue-500"></span>
              <span>Energy Consumption Chart</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-blue-500"></span>
              <span>Cost Summary</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-blue-500"></span>
              <span>Peak Demand Tracker</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-blue-500"></span>
              <span>Emissions Monitor</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-blue-500"></span>
              <span>Recent Alerts</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-blue-500"></span>
              <span>Weather Data</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-blue-500"></span>
              <span>System Health Status</span>
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 rounded-full bg-blue-500"></span>
              <span>Energy Savings Tracker</span>
            </li>
          </ul>
        </div>
      </div>
      
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-4">Time Period Selection</h3>
        <p className="text-gray-600 mb-4">
          You can adjust the time period displayed in your dashboard to focus on different timeframes.
        </p>
        
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-4">
          <h4 className="font-medium text-blue-700 mb-2">Using the Time Selector</h4>
          <p className="text-sm text-gray-600 mb-3">
            The time selector in the top-right corner of the dashboard allows you to:
          </p>
          <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
            <li>Select predefined periods (Today, Yesterday, This Week, This Month, etc.)</li>
            <li>Choose a custom date range using the calendar picker</li>
            <li>Compare current data with previous periods</li>
            <li>Set a default time period for when you load the dashboard</li>
          </ul>
        </div>
      </div>
    </>),
    images: [
      {
        src: '/images/manual/page-dashboard-overview.png',
        alt: 'Dashboard Overview',
        caption: 'Energy Management System Dashboard with real-time monitoring',
        width: '90%'
      }
    ],
    subsections: [
      {
        id: 'dashboard-customization',
        title: 'Dashboard Customization',
        content: (<>
          <p className="text-gray-600 mb-4">
            The dashboard is fully customizable to meet your specific monitoring needs. You can add, remove, and rearrange widgets to create a personalized view of your energy data.
          </p>
          
          <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-4">
            <h4 className="font-medium text-blue-700 mb-2">Widget Configuration</h4>
            <ol className="list-decimal pl-5 text-sm text-gray-600 space-y-2">
              <li>Click the <strong>Customize</strong> button in the top-right corner of the dashboard</li>
              <li>Use the <strong>+ Add Widget</strong> button to select from available widget types</li>
              <li>Drag and drop widgets to rearrange their positions</li>
              <li>Click the gear icon on any widget to configure its settings</li>
              <li>Use the <strong>Save Layout</strong> button to preserve your changes</li>
            </ol>
          </div>
          
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h4 className="font-medium text-gray-800 mb-2">Available Widget Types</h4>
            <ul className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                <span>Energy Consumption Chart</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                <span>Cost Summary</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                <span>Peak Demand Tracker</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                <span>Emissions Monitor</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                <span>Recent Alerts</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                <span>Weather Data</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                <span>System Health Status</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="w-2 h-2 rounded-full bg-blue-500"></span>
                <span>Energy Savings Tracker</span>
              </li>
            </ul>
          </div>
        </>),
        images: []
      },
      {
        id: 'time-period-selection',
        title: 'Time Period Selection',
        content: (<>
          <p className="text-gray-600 mb-4">
            You can adjust the time period displayed in your dashboard to focus on different timeframes.
          </p>
          
          <div className="bg-blue-50 border border-blue-100 rounded-lg p-4">
            <h4 className="font-medium text-blue-700 mb-2">Using the Time Selector</h4>
            <p className="text-sm text-gray-600 mb-3">
              The time selector in the top-right corner of the dashboard allows you to:
            </p>
            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li>Select predefined periods (Today, Yesterday, This Week, This Month, etc.)</li>
              <li>Choose a custom date range using the calendar picker</li>
              <li>Compare current data with previous periods</li>
              <li>Set a default time period for when you load the dashboard</li>
            </ul>
          </div>
        </>),
        images: []
      }
    ]
  },
  {
    id: 'widgets-usage',
    title: 'Widgets & Usage',
    content: (<>
      <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
        <h3 className="text-xl font-semibold text-primary-blue mb-2">Dashboard Widgets Guide</h3>
        <p className="text-gray-700 leading-relaxed">
          Dashboard widgets provide focused views of specific aspects of your energy management system. This guide explains each widget type and how to use them effectively.
        </p>
      </div>
      
      <div className="space-y-6">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-800 mb-2">Energy Consumption Widget</h3>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="md:w-2/3">
              <p className="text-gray-600 mb-3">
                This widget displays your energy consumption over time, allowing you to track usage patterns and identify trends.
              </p>
              <h4 className="font-medium text-blue-700 mb-1">Key Features:</h4>
              <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1 mb-3">
                <li>Toggle between different visualization types (line, bar, area)</li>
                <li>View consumption breakdown by energy type</li>
                <li>Compare against historical periods</li>
                <li>Identify peak consumption times</li>
              </ul>
              <h4 className="font-medium text-blue-700 mb-1">How to Use:</h4>
              <ol className="list-decimal pl-5 text-sm text-gray-600 space-y-1">
                <li>Use the dropdown to select the energy type (electricity, gas, water)</li>
                <li>Adjust the time period using the time selector</li>
                <li>Hover over data points to see detailed values</li>
                <li>Click on the legend items to show/hide specific data series</li>
              </ol>
            </div>
            <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
              <div className="text-center text-gray-500 text-sm">Widget Preview Image</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-800 mb-2">Cost Summary Widget</h3>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="md:w-2/3">
              <p className="text-gray-600 mb-3">
                Monitor your energy costs at a glance, with projected spending and cost breakdowns.
              </p>
              <h4 className="font-medium text-blue-700 mb-1">Key Features:</h4>
              <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1 mb-3">
                <li>Current period costs with trend indicators</li>
                <li>Cost breakdown by facility or department</li>
                <li>Month-to-date spending with projections</li>
                <li>Year-over-year cost comparison</li>
              </ul>
              <h4 className="font-medium text-blue-700 mb-1">How to Use:</h4>
              <ol className="list-decimal pl-5 text-sm text-gray-600 space-y-1">
                <li>Toggle between total costs and cost per square foot</li>
                <li>Click through tabs to view different cost metrics</li>
                <li>Use the filter menu to focus on specific facilities</li>
                <li>Export cost data using the download button</li>
              </ol>
            </div>
            <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
              <div className="text-center text-gray-500 text-sm">Widget Preview Image</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-800 mb-2">System Health Widget</h3>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="md:w-2/3">
              <p className="text-gray-600 mb-3">
                Monitor the status and health of all components in your energy management system.
              </p>
              <h4 className="font-medium text-blue-700 mb-1">Key Features:</h4>
              <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1 mb-3">
                <li>Real-time connection status for all meters</li>
                <li>Data quality indicators</li>
                <li>System performance metrics</li>
                <li>Active alerts and warnings</li>
              </ul>
              <h4 className="font-medium text-blue-700 mb-1">How to Use:</h4>
              <ol className="list-decimal pl-5 text-sm text-gray-600 space-y-1">
                <li>View overall system health score at the top</li>
                <li>Click on category tabs to see detailed status</li>
                <li>Expand sections to troubleshoot specific issues</li>
                <li>Use the refresh button to get the latest status</li>
              </ol>
            </div>
            <div className="md:w-1/3 bg-gray-100 rounded-lg p-2 flex items-center justify-center">
              <div className="text-center text-gray-500 text-sm">Widget Preview Image</div>
            </div>
          </div>
        </div>
      </div>
    </>),
    images: []
  }
];

export default dashboardSections;
