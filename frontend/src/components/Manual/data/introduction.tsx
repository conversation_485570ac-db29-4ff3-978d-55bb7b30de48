import React from 'react';
import { ManualSection } from '../ManualTypes';
import { LayoutDashboard, Zap, BarChart, FileText, Settings, BookOpen } from 'lucide-react';

// Revert FeatureCard to a React component using JSX
const FeatureCard: React.FC<{ title: string; description: string; tags: string[]; icon: React.ReactNode }> =
  ({ title, description, tags, icon }) => (
  <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden transition-all duration-300 hover:shadow-lg flex flex-col">
    <div className="h-24 bg-gradient-to-r from-blue-500 to-blue-600 relative overflow-hidden flex items-center justify-center">
       <div className="absolute inset-0 bg-black bg-opacity-10"></div>
       <span className="text-white opacity-80 mb-1">
         {icon} {/* Render icon as ReactNode */}
       </span>
    </div>
    <div className="p-4 flex flex-col flex-grow">
      <h4 className="text-base font-semibold text-gray-800 mb-2">{title}</h4>
      <p className="text-sm text-gray-600 mb-3 flex-grow">{description}</p>
      <div className="flex flex-wrap gap-1.5">
        {tags.map(tag => <span key={tag} className="text-xs font-medium px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full">{tag}</span>)}
      </div>
    </div>
  </div>
);

const introductionSections: ManualSection[] = [
  {
    id: 'system-overview',
    title: 'System Overview',
    // Revert content back to JSX using FeatureCard component and Lucide icons
    content: (
      <>
        <div className="bg-gradient-to-br from-blue-50 via-white to-blue-50/30 p-5 rounded-lg border border-blue-100 mb-6 shadow-sm">
          <h3 className="text-xl font-semibold text-primary-blue mb-2">Welcome to Energy Management System</h3>
          <p className="text-gray-700 leading-relaxed">
            Thank you for choosing our Energy Management System. This manual will guide you through all the features and capabilities of the platform, helping you maximize your energy efficiency and cost savings.
          </p>
          <p className="text-gray-700 leading-relaxed mt-3">
            Below you'll find a feature map providing a quick overview of the platform's key components. Use this as your starting point to navigate through the manual.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 mb-8">
           <FeatureCard
             title="Dashboard"
             description="Central hub for real-time energy consumption, costs, and system status at a glance."
             tags={['Real-time', 'Cost Tracking', 'Overview']}
             icon={<LayoutDashboard size={36} strokeWidth={1.5}/>}
           />
           <FeatureCard
             title="Meters"
             description="Comprehensive monitoring and management of all connected meters across your facilities."
             tags={['Multi-site', 'Alerts', 'Status']}
             icon={<Zap size={36} strokeWidth={1.5}/>}
           />
           <FeatureCard
             title="Analytics"
             description="Advanced data analysis tools to identify trends, anomalies, and optimization opportunities."
             tags={['Forecasting', 'Benchmarking', 'Trends']}
             icon={<BarChart size={36} strokeWidth={1.5}/>}
           />
           <FeatureCard
             title="Reports"
             description="Customizable reporting tools for generating insights and sharing with stakeholders."
             tags={['Scheduled', 'Export', 'Custom']}
             icon={<FileText size={36} strokeWidth={1.5}/>}
           />
            <FeatureCard
             title="Settings"
             description="Configure system settings, user access, notifications, integrations, and more."
             tags={['Users', 'Preferences', 'Integrations']}
             icon={<Settings size={36} strokeWidth={1.5}/>}
           />
            <FeatureCard
             title="Glossary"
             description="Comprehensive reference guide to energy management terminology and concepts."
             tags={['Definitions', 'References', 'Terms']}
             icon={<BookOpen size={36} strokeWidth={1.5}/>}
           />
        </div>
      </>
    )
  },
  {
    id: 'application-pages',
    title: 'Application Pages',
    content: (<>
      <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-xl font-semibold text-primary-blue">Application Pages Overview</h3>
          <button 
            onClick={() => {
              // Create a function to export this specific section as PDF
              const printContent = document.getElementById('application-pages-content');
              if (printContent) {
                const printWindow = window.open('', '_blank');
                if (printWindow) {
                  printWindow.document.write(`
                    <html>
                      <head>
                        <title>Application Pages Overview</title>
                        <link rel="stylesheet" href="/styles.css">
                        <style>
                          body { font-family: Arial, sans-serif; padding: 20px; }
                          img { max-width: 100%; height: auto; }
                        </style>
                      </head>
                      <body>
                        <h1>Application Pages Overview</h1>
                        ${printContent.innerHTML}
                        <script>
                          window.onload = function() { window.print(); }
                        </script>
                      </body>
                    </html>
                  `);
                  printWindow.document.close();
                }
              }
            }}
            className="flex items-center gap-1.5 px-3 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M6 9v12h12V9"></path>
              <path d="M6 9H4a2 2 0 0 0-2 2v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a2 2 0 0 0-2-2h-2"></path>
              <path d="M12 19v-6"></path>
              <path d="M8 15l4 4 4-4"></path>
            </svg>
            Export PDF
          </button>
        </div>
        <p className="text-gray-700 leading-relaxed">
          The Energy Management System is organized into several key interfaces, each designed to provide specific functionality while maintaining a consistent, 
          minimalist design language.
        </p>
        <p className="text-gray-700 leading-relaxed mt-2">
          The following screenshots provide an overview of each main section of the application. For detailed information about each section, please refer to their respective chapters in this manual.
        </p>
      </div>

      <div className="mb-8" id="application-pages-content">
        <h4 className="text-lg font-medium text-gray-800 mb-4">Main Application Pages</h4>
        
        <div className="grid grid-cols-1 gap-8">
          {/* Dashboard */}
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-3">Dashboard / Overview</h5>
            <p className="text-sm text-gray-600 mb-4">
              The central command center of the Energy Management System, providing a real-time overview of your energy usage and key performance indicators. Features the Building Load Profile chart that displays power (kW) data as a line chart following design standards.
            </p>
            <div className="mb-3">
              <img src="/images/manual/page-dashboard-overview.png" alt="Dashboard Overview" className="w-full rounded-md border border-gray-200" />
              <p className="text-xs text-gray-500 italic mt-1 text-center">Dashboard with Building Load Profile chart and key metrics</p>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Key Features:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Real-time energy consumption metrics with trend indicators</li>
                <li>Building Load Profile chart using line visualization for power data (kW)</li>
                <li>Quick access to critical alerts and notifications</li>
              </ul>
            </div>
          </div>
          
          {/* Consumption */}
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-3">Consumption Analytics</h5>
            <p className="text-sm text-gray-600 mb-4">
              Track and analyze your electricity consumption with intuitive visualizations and comparison tools. Features minimalist design with pill-button selectors and clear metric cards.
            </p>
            <div className="mb-3">
              <img src="/images/manual/page-consumption-analytics.png" alt="Consumption Analytics" className="w-full rounded-md border border-gray-200" />
              <p className="text-xs text-gray-500 italic mt-1 text-center">Consumption page with intuitive pill-button controls and clear metric cards</p>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Key Features:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Bar charts for energy consumption (kWh) visualization</li>
                <li>Intuitive pill-button selectors for time period filtering</li>
                <li>Directional arrows showing consumption trends</li>
                <li>Target comparison with baseline metrics</li>
              </ul>
            </div>
          </div>
          
          {/* Compare */}
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-3">Energy Comparison</h5>
            <p className="text-sm text-gray-600 mb-4">
              Compare energy metrics across different time periods, buildings, or systems with side-by-side analysis tools. Maintains visualization standards with bar charts for consumption (kWh) and line charts for power (kW).
            </p>
            <div className="mb-3">
              <img src="/images/manual/page-energy-compare.png" alt="Energy Comparison" className="w-full rounded-md border border-gray-200" />
              <p className="text-xs text-gray-500 italic mt-1 text-center">Compare page with side-by-side metric analysis and proper chart visualization types</p>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Key Features:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Side-by-side comparison of energy metrics</li>
                <li>Consistent visualization standards (bar charts for kWh, line charts for kW)</li>
                <li>Percentage difference calculations</li>
                <li>Multiple comparison modes (time periods, buildings, systems)</li>
                <li>Exportable comparison reports</li>
              </ul>
            </div>
          </div>
          
          {/* Analytics */}
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-3">Advanced Analytics</h5>
            <p className="text-sm text-gray-600 mb-4">
              Deep dive into your energy data with advanced analytics tools, trend analysis, and predictive insights.
            </p>
            <div className="mb-3">
              <img src="/images/manual/page-analytics.png" alt="Analytics Page" className="w-full rounded-md border border-gray-200" />
              <p className="text-xs text-gray-500 italic mt-1 text-center">Analytics page with interactive charts and detailed metrics</p>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Key Features:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Trend analysis with customizable date ranges</li>
                <li>Anomaly detection and highlighting</li>
                <li>Interactive drill-down capabilities</li>
                <li>Custom report generation</li>
              </ul>
            </div>
          </div>
          
          {/* Meters Management */}
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-3">Meters Management</h5>
            <p className="text-sm text-gray-600 mb-4">
              Manage your smart meters, monitor their status, and organize them into logical groups for easier monitoring and analysis.
            </p>
            <div className="mb-3">
              <img src="/images/manual/page-meters-management.png" alt="Meters Management" className="w-full rounded-md border border-gray-200" />
              <p className="text-xs text-gray-500 italic mt-1 text-center">Meters management interface with meter groups and status indicators</p>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Key Features:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Comprehensive meter inventory management</li>
                <li>Real-time status monitoring with visual indicators</li>
                <li>Meter grouping and organization</li>
                <li>Detailed meter specifications and history</li>
              </ul>
            </div>
          </div>
          
          {/* Meter Diagram */}
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-3">Meter Diagram</h5>
            <p className="text-sm text-gray-600 mb-4">
              Visualize your meter network with an interactive diagram showing meter groups, power values, and status indicators in a spatial layout.
            </p>
            <div className="mb-3">
              <img src="/images/manual/page-meter-diagram.png" alt="Meter Diagram" className="w-full rounded-md border border-gray-200" />
              <p className="text-xs text-gray-500 italic mt-1 text-center">Interactive meter diagram with optimized performance and visual indicators</p>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Key Features:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Interactive visual representation of meter groups</li>
                <li>Real-time power values for each meter group</li>
                <li>Status indicators with color coding</li>
                <li>Optimized performance with image preloading</li>
                <li>Detailed metrics panel with key information</li>
              </ul>
            </div>
          </div>
          
          {/* Reports */}
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-3">Reports</h5>
            <p className="text-sm text-gray-600 mb-4">
              Generate, schedule, and manage detailed reports on energy consumption, costs, and efficiency metrics.
            </p>
            <div className="mb-3">
              <img src="/images/manual/page-reports.png" alt="Reports Page" className="w-full rounded-md border border-gray-200" />
              <p className="text-xs text-gray-500 italic mt-1 text-center">Reports page with report templates and generation options</p>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Key Features:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Customizable report templates</li>
                <li>Multiple export formats (PDF, CSV)</li>
                <li>Report sharing and distribution</li>
                <li>Historical report archive</li>
              </ul>
            </div>
          </div>
          
          {/* Billing */}
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-3">Billing Management</h5>
            <p className="text-sm text-gray-600 mb-4">
              Manage energy billing, track costs, and analyze billing trends with detailed breakdowns and visualizations.
            </p>
            <div className="mb-3">
              <img src="/images/manual/page-user-management.png" alt="Billing Management" className="w-full rounded-md border border-gray-200" />
              <p className="text-xs text-gray-500 italic mt-1 text-center">Billing management interface with cost tracking and analysis tools</p>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Key Features:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Detailed billing history and records</li>
                <li>Cost breakdown by meter and time period</li>
                <li>Rate plan management and optimization</li>
                <li>Bill verification and validation</li>
                <li>Cost forecasting and budgeting tools</li>
              </ul>
            </div>
          </div>
          
          {/* Alerts */}
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-3">Alerts & Notifications</h5>
            <p className="text-sm text-gray-600 mb-4">
              Configure alert thresholds, manage notification preferences, and monitor system alerts in real-time.
            </p>
            <div className="mb-3">
              <img src="/images/manual/page-alerts-notifications.png" alt="Alerts and Notifications" className="w-full rounded-md border border-gray-200" />
              <p className="text-xs text-gray-500 italic mt-1 text-center">Alerts configuration with minimalist design</p>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Key Features:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Customizable alert thresholds</li>
                <li>Multiple notification channels (email, SMS, in-app)</li>
                <li>Alert prioritization and categorization</li>
                <li>Alert history and resolution tracking</li>
                <li>Scheduled maintenance notifications</li>
              </ul>
            </div>
          </div>
          
          {/* Settings */}
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-3">System Settings</h5>
            <p className="text-sm text-gray-600 mb-4">
              Configure system parameters, user preferences, and global settings for the Energy Management System.
            </p>
            <div className="mb-3">
              <img src="/images/manual/page-settings.png" alt="Settings Page" className="w-full rounded-md border border-gray-200" />
              <p className="text-xs text-gray-500 italic mt-1 text-center">System settings with configuration options and preferences</p>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Key Features:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>User account management</li>
                <li>System preferences configuration</li>
                <li>Integration settings</li>
                <li>Data import/export options</li>
                <li>System maintenance tools</li>
              </ul>
            </div>
          </div>
          
          {/* Manual */}
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-3">User Manual</h5>
            <p className="text-sm text-gray-600 mb-4">
              Access comprehensive documentation with searchable content, navigation, and detailed guides for all system features.
            </p>
            <div className="mb-3">
              <img src="/images/manual/page-manual.png" alt="Manual Page" className="w-full rounded-md border border-gray-200" />
              <p className="text-xs text-gray-500 italic mt-1 text-center">User manual with searchable content and navigation</p>
            </div>
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">Key Features:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Comprehensive feature documentation</li>
                <li>Searchable content index</li>
                <li>Step-by-step guides and tutorials</li>
                <li>Printable documentation</li>
                <li>Contextual help links throughout the application</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>),
  },
  {
    id: 'main-interface-overview',
    title: 'Main Interface Overview',
    content: (<>
      <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
        <h3 className="text-xl font-semibold text-primary-blue mb-2">The Energy Management System Interface</h3>
        <p className="text-gray-700 leading-relaxed">
          The Energy Management System features a minimalist design that prioritizes clarity, 
          visual hierarchy, and intuitive interactions. The consistent interface elements across all pages 
          help you navigate and operate the system efficiently.
        </p>
      </div>

      <div className="mb-8">
        <h4 className="text-lg font-medium text-gray-800 mb-4">Common Interface Elements</h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5 mb-6">
          {/* Navigation Panel */}
          <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-2">Primary Navigation</h5>
            <p className="text-sm text-gray-600 mb-3">
              The left sidebar provides access to all main sections of the application.
            </p>
            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li><strong>Overview:</strong> Main dashboard with Building Load Profile and key metrics</li>
              <li><strong>Analytics:</strong> Detailed energy analytics and performance data</li>
              <li><strong>Consumption:</strong> Energy consumption tracking with target comparisons</li>
              <li><strong>Compare:</strong> Side-by-side comparison of energy metrics</li>
              <li><strong>Meters:</strong> Smart meter management interface</li>
              <li><strong>Diagram:</strong> Interactive meter diagram visualization</li>
              <li><strong>Reports:</strong> Report generation and management</li>
              <li><strong>Billing:</strong> Energy billing management and history</li>
              <li><strong>Alerts:</strong> System alerts and notification settings</li>
              <li><strong>Settings:</strong> System configuration and user management</li>
            </ul>
          </div>
          
          {/* Header Area */}
          <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
            <h5 className="font-medium text-blue-700 mb-2">Header Area</h5>
            <p className="text-sm text-gray-600 mb-3">
              The top header bar provides contextual functions and global controls.
            </p>
            <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
              <li><strong>Page Title:</strong> Current section name and optional description</li>
              <li><strong>Date Range Selector:</strong> Filter data by time period</li>
              <li><strong>Quick Actions:</strong> Context-specific functions for the current page</li>
              <li><strong>User Menu:</strong> Account settings, preferences, and logout</li>
              <li><strong>Notifications:</strong> System alerts and important updates</li>
              <li><strong>Search:</strong> Global search functionality</li>
            </ul>
          </div>
        </div>
        
        <div className="mb-8">
          <h4 className="text-lg font-medium text-gray-800 mb-4">Design Principles</h4>
          
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm mb-5">
            <p className="text-sm text-gray-600 mb-4">
              The Energy Management System adapts seamlessly to different device types and screen sizes:
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h5 className="font-medium text-blue-700 mb-2">Visual Clarity</h5>
                <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                  <li>Minimalist design with clean lines</li>
                  <li>Consistent spacing and balanced layout</li>
                  <li>Optimized information density</li>
                  <li>Focus on critical data visualization</li>
                </ul>
              </div>
              
              <div>
                <h5 className="font-medium text-blue-700 mb-2">Intuitive Controls</h5>
                <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                  <li>Pill-button selectors for filtering</li>
                  <li>Visual indicators for status changes</li>
                  <li>Directional arrows for trend indication</li>
                  <li>Consistent touch targets and controls</li>
                </ul>
              </div>
              
              <div>
                <h5 className="font-medium text-blue-700 mb-2">Data Visualization</h5>
                <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                  <li>Bar charts for energy consumption (kWh)</li>
                  <li>Line charts for power (kW) data</li>
                  <li>Consistent color coding for metrics</li>
                  <li>Clear labels and contextual information</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mb-8">
          <h4 className="text-lg font-medium text-gray-800 mb-4">Common Controls & Components</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
            {/* Metric Cards */}
            <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
              <h5 className="font-medium text-blue-700 mb-2">Metric Cards</h5>
              <p className="text-sm text-gray-600 mb-2">
                Standardized cards displaying key metrics with visual indicators.
              </p>
              <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                <li><strong>Title:</strong> Clear metric name</li>
                <li><strong>Current Value:</strong> Latest measurement with units</li>
                <li><strong>Change Indicator:</strong> Up/down arrows showing trends</li>
                <li><strong>Percentage Change:</strong> Relative change from previous period</li>
                <li><strong>Tooltip:</strong> Additional context available on hover</li>
              </ul>
            </div>
            
            {/* Chart Elements */}
            <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
              <h5 className="font-medium text-blue-700 mb-2">Chart Elements</h5>
              <p className="text-sm text-gray-600 mb-2">
                Consistent visualization standards across all data charts.
              </p>
              <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                <li><strong>Title & Description:</strong> Clear data context</li>
                <li><strong>Time Range Selector:</strong> Filter charts by period</li>
                <li><strong>Legend:</strong> Color-coded data series identification</li>
                <li><strong>Tooltip:</strong> Precise values on hover/touch</li>
                <li><strong>Zoom Controls:</strong> Focus on specific data ranges</li>
                <li><strong>Export:</strong> Save chart data in various formats</li>
              </ul>
            </div>
            
            {/* Filter Controls */}
            <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
              <h5 className="font-medium text-blue-700 mb-2">Filter Controls</h5>
              <p className="text-sm text-gray-600 mb-2">
                Standardized filtering mechanisms across all data displays.
              </p>
              <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                <li><strong>Date Range Filters:</strong> Today, Week, Month, Custom</li>
                <li><strong>Group Selectors:</strong> Filter by meter groups</li>
                <li><strong>Comparison Options:</strong> Previous period, baseline, target</li>
                <li><strong>Granularity Controls:</strong> Hourly, daily, weekly, monthly data</li>
                <li><strong>Clear Filters:</strong> Reset to default view</li>
              </ul>
            </div>
            
            {/* Table Components */}
            <div className="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
              <h5 className="font-medium text-blue-700 mb-2">Table Components</h5>
              <p className="text-sm text-gray-600 mb-2">
                Interactive data tables with consistent functionality.
              </p>
              <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                <li><strong>Sortable Columns:</strong> Click headers to sort data</li>
                <li><strong>Search Field:</strong> Filter table contents</li>
                <li><strong>Pagination:</strong> Navigate through large datasets</li>
                <li><strong>Row Actions:</strong> Contextual operations for each entry</li>
                <li><strong>Selection:</strong> Multi-select for batch operations</li>
                <li><strong>Export:</strong> Download table data in CSV format</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="mb-8">
          <h4 className="text-lg font-medium text-gray-800 mb-4">Responsive Design</h4>
          
          <div className="bg-white rounded-lg border border-gray-200 p-5 shadow-sm">
            <p className="text-sm text-gray-600 mb-4">
              The Energy Management System adapts seamlessly to different device types and screen sizes:
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h5 className="font-medium text-blue-700 mb-2">Desktop</h5>
                <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                  <li>Full navigation sidebar</li>
                  <li>Multi-column dashboard layout</li>
                  <li>Expanded chart visualizations</li>
                  <li>Side-by-side comparative views</li>
                  <li>Advanced filtering options</li>
                </ul>
              </div>
              
              <div>
                <h5 className="font-medium text-blue-700 mb-2">Tablet</h5>
                <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                  <li>Collapsible navigation menu</li>
                  <li>Optimized two-column layouts</li>
                  <li>Touch-friendly control elements</li>
                  <li>Scrollable data sections</li>
                  <li>Simplified filter options</li>
                </ul>
              </div>
              
              <div>
                <h5 className="font-medium text-blue-700 mb-2">Mobile</h5>
                <ul className="list-disc pl-5 text-sm text-gray-600 space-y-1">
                  <li>Hamburger menu navigation</li>
                  <li>Single-column vertical layout</li>
                  <li>Focused data card presentation</li>
                  <li>Swipe gestures for navigation</li>
                  <li>Essential controls prioritized</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>),
    images: [
      {
        src: '/images/manual/page-dashboard-overview.png',
        alt: 'Main Interface Overview',
        caption: 'The Energy Management System dashboard showing the minimalist design with clear visual hierarchy and intuitive controls',
        width: '90%'
      }
    ]
  },
  {
    id: 'getting-started',
    title: 'Getting Started',
    content: (<>
      <div className="bg-gradient-to-br from-blue-50 to-white p-4 rounded-lg border border-blue-100 mb-6">
        <h3 className="text-xl font-semibold text-primary-blue mb-2">Getting Started Guide</h3>
        <p className="text-gray-700 leading-relaxed">
          This section will help you quickly get up and running with your Energy Management System. Follow the steps below to set up your account, configure your first meters, and start monitoring your energy usage.
        </p>
      </div>
      
      <div className="space-y-6">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-800 mb-2">1. Account Setup</h3>
          <p className="text-gray-600 mb-3">Get your account ready for use by completing these initial steps:</p>
          <ul className="list-disc pl-6 text-gray-600 space-y-1">
            <li>Verify your email address by clicking the confirmation link</li>
            <li>Set up your organization profile with relevant details</li>
            <li>Configure user roles and permissions for your team</li>
            <li>Set your preferences for alerts and notifications</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-800 mb-2">2. Site Configuration</h3>
          <p className="text-gray-600 mb-3">Set up your physical locations and facilities:</p>
          <ul className="list-disc pl-6 text-gray-600 space-y-1">
            <li>Add your sites and facilities to the system</li>
            <li>Configure the location details including address and time zone</li>
            <li>Set up the facility hierarchy if you have multiple buildings</li>
            <li>Upload floor plans or site maps (optional)</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-800 mb-2">3. Meter Setup</h3>
          <p className="text-gray-600 mb-3">Connect and configure your energy meters:</p>
          <ul className="list-disc pl-6 text-gray-600 space-y-1">
            <li>Add your physical meters to the system</li>
            <li>Configure meter communication settings</li>
            <li>Set up data collection intervals</li>
            <li>Verify connectivity and data flow</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-800 mb-2">4. Dashboard Customization</h3>
          <p className="text-gray-600 mb-3">Personalize your dashboard for at-a-glance monitoring:</p>
          <ul className="list-disc pl-6 text-gray-600 space-y-1">
            <li>Select key metrics to display on your dashboard</li>
            <li>Configure widgets and charts for important data points</li>
            <li>Set up custom views for different user roles</li>
            <li>Create shortcuts to frequently accessed reports</li>
          </ul>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-medium text-gray-800 mb-2">5. Alert Configuration</h3>
          <p className="text-gray-600 mb-3">Set up proactive notifications for critical events:</p>
          <ul className="list-disc pl-6 text-gray-600 space-y-1">
            <li>Configure thresholds for consumption and demand</li>
            <li>Set up alerts for anomalies and unusual patterns</li>
            <li>Define notification methods (email, SMS, in-app)</li>
            <li>Assign alert recipients based on roles</li>
          </ul>
        </div>
      </div>
    </>)
  }
];

export default introductionSections;
