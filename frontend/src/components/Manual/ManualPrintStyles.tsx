import React from 'react';

/**
 * Stylesheet component for print-friendly manual formatting
 * Contains all the CSS needed for proper PDF printing
 */
const ManualPrintStyles: React.FC = () => (
  <style dangerouslySetInnerHTML={{ __html: `
    @media print {
      /* General print settings */
      body {
        background-color: white !important;
        color: #333 !important;
        font-size: 12pt !important;
        line-height: 1.5 !important;
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
      }

      /* Hide non-essential elements */
      header, .sidebar, nav, button, .no-print, footer, 
      .manual-navigation, .manual-search, .print-button {
        display: none !important;
      }

      /* Main content container */
      .print-container {
        display: block !important;
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: visible !important;
      }

      /* Chapter styling */
      .print-chapter {
        page-break-before: always !important;
        padding-top: 20pt !important;
      }
      
      /* First chapter should not have a page break */
      .print-chapter:first-child {
        page-break-before: avoid !important;
      }

      /* Section styling */
      .print-section {
        page-break-inside: avoid !important;
        margin-bottom: 15pt !important;
      }

      /* Control page breaks */
      .page-break-before {
        page-break-before: always !important;
      }

      .page-break-after {
        page-break-after: always !important;
      }

      .avoid-break-inside {
        page-break-inside: avoid !important;
      }

      /* Typography for print */
      h1 {
        font-size: 24pt !important;
        margin-top: 20pt !important;
        margin-bottom: 10pt !important;
        color: #1a73e8 !important;
      }

      h2 {
        font-size: 20pt !important;
        margin-top: 15pt !important;
        margin-bottom: 8pt !important;
        color: #1a73e8 !important;
      }

      h3 {
        font-size: 16pt !important;
        margin-top: 12pt !important;
        margin-bottom: 6pt !important;
      }

      h4 {
        font-size: 14pt !important;
        margin-top: 10pt !important;
        margin-bottom: 5pt !important;
      }

      p, li {
        orphans: 3 !important;
        widows: 3 !important;
      }

      /* Table of contents */
      .print-toc {
        margin-bottom: 20pt !important;
      }

      .print-toc-item {
        display: flex !important;
        align-items: center !important;
        margin-bottom: 5pt !important;
      }

      .print-toc-leader {
        flex-grow: 1 !important;
        margin: 0 5pt !important;
        border-bottom: 1pt dotted #ccc !important;
        content: " ................................................. " !important;
      }

      /* Images and figures */
      img, figure {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid !important;
        margin: 10pt 0 !important;
      }

      /* Card styling */
      .card, .bg-white, .rounded-lg {
        border: 1pt solid #ddd !important;
        margin-bottom: 10pt !important;
        padding: 8pt !important;
        page-break-inside: avoid !important;
        box-shadow: none !important;
      }

      /* Header and footer for each page */
      @page {
        margin: 2cm !important;
        @top-right {
          content: "Energy Management System Manual" !important;
          font-size: 9pt !important;
          color: #666 !important;
        }
        @bottom-center {
          content: "Page " counter(page) !important;
          font-size: 9pt !important;
          color: #666 !important;
        }
      }

      /* Cover page special styling */
      .print-cover {
        text-align: center !important;
        height: 100vh !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        page-break-after: always !important;
      }

      .print-cover h1 {
        font-size: 32pt !important;
        color: #1a73e8 !important;
        margin-bottom: 16pt !important;
      }

      .print-cover .logo {
        margin-bottom: 32pt !important;
        max-width: 250pt !important;
      }

      .print-cover .date {
        font-size: 14pt !important;
        color: #666 !important;
        margin-top: 32pt !important;
      }
    }
  `}} />
);

export default ManualPrintStyles;
