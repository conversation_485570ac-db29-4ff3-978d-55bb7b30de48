import React from 'react';

export interface ManualImage {
  src: string;
  alt: string;
  caption?: string;
  width?: string;
}

export interface ManualSubsection {
  id: string;
  title: string;
  content: React.ReactNode;
}

export interface ManualSection {
  id: string;
  title: string;
  content: React.ReactNode;
  images?: ManualImage[];
  subsections?: ManualSubsection[];
}

export interface ManualChapter {
  id: string;
  title: string;
  icon: React.ReactNode;
  sections: ManualSection[];
}

export interface SearchResult {
  chapterId: string;
  chapterTitle: string;
  sectionId: string;
  sectionTitle: string;
  subsectionId?: string;
  subsectionTitle?: string;
  contentSnippet: string;
  match: string;
}
