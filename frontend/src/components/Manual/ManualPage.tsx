import React, { useState, useEffect } from 'react';
import { Book, LayoutDashboard, Zap, BarChart, FileText, Settings, AlertTriangle } from 'lucide-react';
import { ManualPrintStyles, ManualPageHeader, ManualSidebar, ManualContentDisplay } from './';
import { ManualChapter, SearchResult } from './ManualTypes';
import ManualSearch from './ManualSearch';
import introductionSections from './data/introduction.tsx';
import dashboardSections from './data/dashboard.tsx';
import metersSections from './data/meters.tsx';
import analyticsSections from './data/analytics.tsx';
import reportsSections from './data/reports.tsx';
import settingsSections from './data/settings.tsx';
import glossarySections from './data/glossary.tsx';
import faqSections from './data/faq.tsx';
import compareSections from './data/compare.tsx';
import consumptionSections from './data/consumption.tsx';
import billingSections from './data/billing.tsx';
import alertsSections from './data/alerts.tsx';
import metersManagementSections from './data/meters-management.tsx';
import userManagementScreenshots from './data/user-management-screenshots.tsx';

/**
 * ManualPage component - Main container for the Tesla-inspired Energy Management System Manual
 * This component handles state management and renders the various manual components
 */
const ManualPage: React.FC = () => {
  // State for active navigation items
  const [activeChapter, setActiveChapter] = useState('introduction');
  const [activeSection, setActiveSection] = useState('system-overview');
  const [activeSubsection, setActiveSubsection] = useState('');
  
  // State for search functionality
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  
  // Comprehensive manual content with Tesla-inspired design
  const manualContent: ManualChapter[] = [
    {
      id: 'introduction',
      title: 'Introduction',
      icon: <Book size={18} />,
      sections: introductionSections
    },
    {
      id: 'consumption',
      title: 'Consumption',
      icon: <BarChart size={18} />,
      sections: consumptionSections
    },
    {
      id: 'dashboard',
      title: 'Dashboard',
      icon: <LayoutDashboard size={18} />,
      sections: dashboardSections
    },
    {
      id: 'meters',
      title: 'Meters',
      icon: <Zap size={18} />,
      sections: metersSections
    },
    {
      id: 'meters-management',
      title: 'Meters Management',
      icon: <Settings size={18}/>,
      sections: metersManagementSections
    },
    {
      id: 'compare',
      title: 'Compare',
      icon: <BarChart size={18} />,
      sections: compareSections
    },
    {
      id: 'analytics',
      title: 'Analytics',
      icon: <BarChart size={18} />,
      sections: analyticsSections
    },
    {
      id: 'reports',
      title: 'Reports',
      icon: <FileText size={18} />,
      sections: reportsSections
    },
    {
      id: 'billing',
      title: 'Billing',
      icon: <FileText size={18}/>,
      sections: billingSections
    },
    {
      id: 'alerts',
      title: 'Alerts',
      icon: <AlertTriangle size={18}/>,
      sections: alertsSections
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: <Settings size={18} />,
      sections: settingsSections
    },
    {
      id: 'glossary',
      title: 'Glossary',
      icon: <Book size={18} />,
      sections: glossarySections
    },
    {
      id: 'faq',
      title: 'FAQ',
      icon: <span className="flex items-center justify-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-help-circle"><circle cx="12" cy="12" r="10"/><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/><path d="M12 17h.01"/></svg>
      </span>,
      sections: faqSections
    }
  ];

  // Helper function to safely get a text snippet
  const getTextSnippet = (node: React.ReactNode | undefined | null, fallback: string, maxLength: number = 150): string => {
    if (typeof node === 'string') {
      const snippet = node.replace(/<[^>]*>/g, ''); // Remove HTML tags for snippet
      return snippet.length > maxLength ? snippet.substring(0, maxLength) + '...' : snippet;
    }
    // If content is not a string (likely JSX), use the fallback (e.g., title)
    return fallback.length > maxLength ? fallback.substring(0, maxLength) + '...' : fallback;
  };

  // Effect for handling search
  useEffect(() => {
    if (searchQuery.trim().length < 3) {
      setSearchResults([]);
      return;
    }

    const query = searchQuery.toLowerCase();
    const results: SearchResult[] = [];

    manualContent.forEach(chapter => {
      chapter.sections.forEach(section => {
        const sectionTitleLower = section.title.toLowerCase();
        const sectionContentString = typeof section.content === 'string' ? section.content.toLowerCase() : '';

        // Search section title
        if (sectionTitleLower.includes(query)) {
          results.push({
            chapterId: chapter.id,
            chapterTitle: chapter.title,
            sectionId: section.id,
            sectionTitle: section.title,
            contentSnippet: getTextSnippet(section.content, section.title),
            match: section.title // Assign title as match
          });
        }
        // Search section content (if it's a string and title didn't match)
        else if (sectionContentString && sectionContentString.includes(query)) {
          results.push({
            chapterId: chapter.id,
            chapterTitle: chapter.title,
            sectionId: section.id,
            sectionTitle: section.title,
            contentSnippet: getTextSnippet(section.content, section.title),
            match: section.title // Assign title as match
          });
        }

        // Search subsections
        section.subsections?.forEach(subsection => {
          const subsectionTitleLower = subsection.title.toLowerCase();
          const subsectionContentString = typeof subsection.content === 'string' ? subsection.content.toLowerCase() : '';

          // Search subsection title
          if (subsectionTitleLower.includes(query)) {
            if (!results.some(r => r.chapterId === chapter.id && r.sectionId === section.id && r.subsectionId === subsection.id)) {
              results.push({
                chapterId: chapter.id,
                chapterTitle: chapter.title,
                sectionId: section.id,
                sectionTitle: section.title,
                subsectionId: subsection.id,
                subsectionTitle: subsection.title,
                contentSnippet: getTextSnippet(subsection.content, subsection.title),
                match: subsection.title // Assign title as match
              });
            }
          }
          // Search subsection content (if it's a string and titles didn't match)
          else if (subsectionContentString && subsectionContentString.includes(query)) {
            if (!results.some(r => r.chapterId === chapter.id && r.sectionId === section.id && r.subsectionId === subsection.id)) {
              results.push({
                chapterId: chapter.id,
                chapterTitle: chapter.title,
                sectionId: section.id,
                sectionTitle: section.title,
                subsectionId: subsection.id,
                subsectionTitle: subsection.title,
                contentSnippet: getTextSnippet(subsection.content, subsection.title),
                match: subsection.title // Assign title as match
              });
            }
          }
        });
      });
    });

    setSearchResults(results);
  }, [searchQuery, manualContent]);

  // Handle selecting a search result
  const handleSearchSelect = (result: SearchResult) => {
    setActiveChapter(result.chapterId);
    setActiveSection(result.sectionId);
    setActiveSubsection(result.subsectionId || '');
    setSearchQuery('');
    setSearchResults([]);
  };

  // Get the title of the current section being viewed
  const getCurrentTitle = () => {
    const chapter = manualContent.find(c => c.id === activeChapter);
    if (!chapter) return 'Energy Management System Manual';
    
    const section = chapter.sections.find(s => s.id === activeSection);
    if (!section) return chapter.title;
    
    if (activeSubsection && section.subsections) {
      const subsection = section.subsections.find(s => s.id === activeSubsection);
      if (subsection) return `${section.title} - ${subsection.title}`;
    }
    
    return section.title;
  };

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Print styles for the Tesla-inspired PDF export */}
      <ManualPrintStyles />
      
      {/* Header with search and export functionality */}
      <ManualPageHeader
        getCurrentTitle={getCurrentTitle}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        handleSearch={(e) => setSearchQuery(e.target.value)}
        clearSearch={() => setSearchQuery('')}
        manualContent={manualContent}
      />
      
      {/* Main content area with sidebar and content display */}
      <div className="flex flex-1 overflow-hidden">
        {/* Navigation sidebar */}
        <ManualSidebar
          manualContent={manualContent}
          activeChapter={activeChapter}
          activeSection={activeSection}
          activeSubsection={activeSubsection}
          setActiveChapter={setActiveChapter}
          setActiveSection={setActiveSection}
          setActiveSubsection={(subsectionId) => 
            setActiveSubsection(subsectionId || '')}
        />
        
        {/* Main content display */}
        <div className="flex-1 overflow-auto">
          {/* Mobile search box (visible on small screens) */}
          <div className="p-4 md:hidden">
            <div className="relative">
              <input
                type="text"
                placeholder="Search manual..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              {searchQuery && (
                <button
                  onClick={() => setSearchQuery('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
                </button>
              )}
            </div>
          </div>
          
          {/* Content display */}
          <ManualContentDisplay
            manualContent={manualContent}
            activeChapter={activeChapter}
            activeSection={activeSection}
            activeSubsection={activeSubsection}
            searchResults={searchResults}
            handleSearchSelect={handleSearchSelect}
          />
        </div>
      </div>
    </div>
  );
};

export default ManualPage;