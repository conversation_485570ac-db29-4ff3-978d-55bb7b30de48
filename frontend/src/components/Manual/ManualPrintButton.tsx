import React from 'react';
import { FileDown } from 'lucide-react';
import { ManualChapter } from './ManualTypes';

interface ManualPrintButtonProps {
  manualContent: ManualChapter[];
}

/**
 * Print button component for generating a PDF of the entire manual
 * Includes print preparation logic and styling
 */
const ManualPrintButton: React.FC<ManualPrintButtonProps> = ({ manualContent }) => {
  const handleExportPDF = () => {
    // Create a temporary iframe for PDF export
    const printFrame = document.createElement('iframe');
    printFrame.style.position = 'fixed';
    printFrame.style.right = '0';
    printFrame.style.bottom = '0';
    printFrame.style.width = '0';
    printFrame.style.height = '0';
    printFrame.style.border = 'none';
    document.body.appendChild(printFrame);
    
    const frameDoc = printFrame.contentWindow?.document;
    if (!frameDoc) return;
    
    // Open the document for writing
    frameDoc.open();
    
    // Current date formatted for the PDF
    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    // Generate PDF content with Tesla-inspired design
    frameDoc.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Alto CERO Energy Management System - User Manual</title>
        <style>
          @page {
            margin: 1.5cm 2cm 2cm 2cm;
            size: A4;
            @bottom-center {
              content: "Page " counter(page) " of " counter(pages);
              font-size: 8pt;
              color: #666;
            }
            @top-right {
              content: "Alto CERO EMS Manual";
              font-size: 8pt;
              color: #666;
            }
          }
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            counter-reset: page;
          }
          /* Tesla-inspired cover page */
          .cover-page {
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            text-align: center;
            page-break-after: always;
            background-color: #f9f9f9;
            background-image: linear-gradient(135deg, #ffffff 0%, #f5f5f5 100%);
            padding: 3cm 2cm;
            box-sizing: border-box;
          }
          .cover-page-logo {
            margin-bottom: 1cm;
          }
          .cover-page-logo svg {
            width: 8cm;
            height: auto;
          }
          .cover-page-title {
            margin-bottom: 2cm;
          }
          .cover-page-title h1 {
            font-size: 32pt;
            color: #1a73e8;
            margin-bottom: 0.5cm;
            font-weight: 300;
            letter-spacing: -0.02em;
          }
          .cover-page-title h2 {
            font-size: 24pt;
            color: #333;
            margin-bottom: 0.5cm;
            font-weight: 400;
          }
          .cover-page-meta {
            margin-top: 3cm;
            font-size: 10pt;
            color: #666;
          }
          .cover-page-meta p {
            margin: 0.2cm 0;
          }
          /* Table of contents */
          .print-toc {
            page-break-after: always;
            padding-top: 1cm;
            counter-reset: chapter;
          }
          .print-toc h2 {
            font-size: 18pt;
            color: #1a73e8;
            margin-bottom: 1cm;
            border-bottom: 1pt solid #1a73e8;
            padding-bottom: 0.3cm;
          }
          .print-toc-chapters {
            margin-left: 0;
            padding-left: 0;
            list-style-type: none;
          }
          .print-toc-sections {
            margin-left: 0.8cm;
            padding-left: 0;
            list-style-type: none;
          }
          .print-toc-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.3cm;
            font-size: 11pt;
          }
          .print-toc-chapter {
            font-weight: 500;
          }
          .print-toc-title {
            flex-grow: 0;
          }
          .print-toc-leader {
            flex-grow: 1;
            margin: 0 0.5cm;
            border-bottom: 1pt dotted #ccc;
            height: 1pt;
          }
          .print-toc-page {
            flex-grow: 0;
          }
          /* Chapter styling */
          .print-chapter {
            page-break-before: always;
            counter-increment: chapter;
          }
          .print-chapter:first-of-type {
            page-break-before: avoid;
          }
          .print-chapter h1 {
            font-size: 22pt;
            color: #1a73e8;
            margin-bottom: 1cm;
            border-bottom: 1pt solid #1a73e8;
            padding-bottom: 0.3cm;
            counter-reset: section;
          }
          .print-chapter h1::before {
            content: counter(chapter) ". ";
          }
          /* Section styling */
          .print-section {
            margin-bottom: 1cm;
            counter-increment: section;
            counter-reset: subsection;
          }
          .print-section h2 {
            font-size: 16pt;
            color: #1a73e8;
            margin-bottom: 0.5cm;
          }
          .print-section h2::before {
            content: counter(chapter) "." counter(section) " ";
          }
          /* Subsection styling */
          .print-subsection {
            margin-bottom: 0.8cm;
            counter-increment: subsection;
          }
          .print-subsection h3 {
            font-size: 13pt;
            color: #333;
            margin-bottom: 0.3cm;
          }
          .print-subsection h3::before {
            content: counter(chapter) "." counter(section) "." counter(subsection) " ";
          }
          /* Content styling */
          img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 0.5cm auto;
            border-radius: 0.2cm;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
          }
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 0.5cm 0;
          }
          table, th, td {
            border: 1pt solid #ddd;
          }
          th, td {
            padding: 0.3cm;
            text-align: left;
          }
          th {
            background-color: #f7f7f7;
            font-weight: 500;
          }
          .card {
            border: 1pt solid #ddd;
            border-radius: 0.2cm;
            padding: 0.5cm;
            margin-bottom: 0.5cm;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
          }
          code {
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            background-color: #f5f5f5;
            padding: 0.1cm 0.2cm;
            border-radius: 0.1cm;
            font-size: 90%;
          }
          /* Content containers */
          .print-section-content {
            margin-bottom: 0.8cm;
          }
          .print-section-images {
            margin-top: 0.8cm;
          }
          .print-image {
            margin-bottom: 0.8cm;
            break-inside: avoid;
          }
          .print-image-caption {
            font-size: 9pt;
            color: #666;
            margin-top: 0.2cm;
            text-align: center;
            font-style: italic;
          }
        </style>
      </head>
      <body>
        <!-- Cover Page -->
        <div class="cover-page">
          <div class="cover-page-logo">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 60" width="200" height="60">
              <g fill="#1a73e8">
                <path d="M20,10 L40,10 L40,40 L20,40 Z" />
                <path d="M45,10 L65,10 L65,25 L45,25 Z" />
                <path d="M45,30 L65,30 L65,40 L45,40 Z" />
                <path d="M70,10 L90,10 L90,40 L70,40 Z" />
                <path d="M95,10 L115,10 L115,40 L95,40 L95,30 L105,30 L105,20 L95,20 Z" />
              </g>
              <text x="120" y="30" fill="#333" font-family="Arial" font-size="12">Energy Management</text>
            </svg>
          </div>
          
          <div class="cover-page-title">
            <h1>Energy Management System</h1>
            <h2>User Manual</h2>
          </div>
          
          <div class="cover-page-meta">
            <p><strong>Version:</strong> 2.5.0</p>
            <p><strong>Last Updated:</strong> ${currentDate}</p>
            <p><strong>  ${new Date().getFullYear()} Alto CERO</strong></p>
            <p>All rights reserved. Confidential and proprietary.</p>
          </div>
        </div>
        
        <!-- Table of Contents -->
        <div class="print-toc">
          <h2>Table of Contents</h2>
          <ul class="print-toc-chapters">
    `);
    
    // Generate table of contents
    manualContent.forEach((chapter, chIdx) => {
      frameDoc.write(`
        <li>
          <div class="print-toc-item print-toc-chapter">
            <div class="print-toc-title">${chIdx + 1}. ${chapter.title}</div>
            <div class="print-toc-leader"></div>
            <div class="print-toc-page">${chIdx + 1}</div>
          </div>
          <ul class="print-toc-sections">
      `);
      
      chapter.sections.forEach((section, secIdx) => {
        frameDoc.write(`
          <li>
            <div class="print-toc-item">
              <div class="print-toc-title">${chIdx + 1}.${secIdx + 1} ${section.title}</div>
              <div class="print-toc-leader"></div>
              <div class="print-toc-page">${chIdx + 1}</div>
            </div>
          </li>
        `);
      });
      
      frameDoc.write(`
          </ul>
        </li>
      `);
    });
    
    frameDoc.write(`
          </ul>
        </div>
    `);
    
    // Function to get the HTML content for a section or subsection
    const getSectionContent = (content: string, images?: any[]) => {
      let html = `
        <div class="print-section-content">
          ${content}
        </div>
      `;
      
      // Add images if they exist
      if (images && images.length > 0) {
        html += `<div class="print-section-images">`;
        images.forEach((image, idx) => {
          html += `
            <figure class="print-image">
              <img src="${image.src}" alt="${image.alt || 'Figure'}" style="max-width: ${image.width || '100%'}; height: auto;" />
              <figcaption class="print-image-caption">
                ${image.caption || `Figure ${idx + 1}: ${image.alt || 'Image'}`}
              </figcaption>
            </figure>
          `;
        });
        html += `</div>`;
      }
      
      return html;
    };
    
    // Write all manual content
    manualContent.forEach(chapter => {
      frameDoc.write(`
        <div class="print-chapter">
          <h1>${chapter.title}</h1>
      `);
      
      chapter.sections.forEach(section => {
        frameDoc.write(`
          <div class="print-section">
            <h2>${section.title}</h2>
            ${getSectionContent(section.content, section.images)}
          `);
        
        // If there are subsections, add them too
        if (section.subsections && section.subsections.length > 0) {
          section.subsections.forEach(subsection => {
            frameDoc.write(`
              <div class="print-subsection">
                <h3>${subsection.title}</h3>
                ${getSectionContent(subsection.content, subsection.images)}
              </div>
            `);
          });
        }
        
        frameDoc.write(`</div>`);
      });
      
      frameDoc.write(`</div>`); // Close chapter
    });
    
    // Add appendix with disclaimers and contact information
    frameDoc.write(`
      <div class="print-chapter">
        <h1>Appendix</h1>
        <div class="print-section">
          <h2>Contact Information</h2>
          <div class="print-section-content">
            <div class="card">
              <p><strong>Technical Support:</strong> <EMAIL></p>
              <p><strong>Phone:</strong> +66 2 123 4567</p>
              <p><strong>Hours:</strong> Monday to Friday, 9:00 AM - 6:00 PM (ICT)</p>
              <p><strong>Website:</strong> <a href="https://www.altocero.com">www.altocero.com</a></p>
            </div>
          </div>
        </div>
        <div class="print-section">
          <h2>Legal Notice</h2>
          <div class="print-section-content">
            <p>This document contains proprietary information protected by copyright. The software described in this manual is furnished under a license agreement and may be used only in accordance with the terms of that agreement.</p>
            <p>  ${new Date().getFullYear()} Alto CERO. All rights reserved.</p>
          </div>
        </div>
      </div>
    `);
    
    frameDoc.write(`
      </body>
      </html>
    `);
    
    frameDoc.close();
    
    // Trigger save as PDF
    printFrame.contentWindow?.focus();
    printFrame.contentWindow?.print();
    
    // Remove the iframe after printing
    setTimeout(() => {
      document.body.removeChild(printFrame);
    }, 1000);
  };
  
  return (
    <button
      onClick={handleExportPDF}
      className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
      title="Export the entire manual as a PDF document"
    >
      <FileDown className="w-5 h-5" />
      <span>Export PDF</span>
    </button>
  );
};

export default ManualPrintButton;
