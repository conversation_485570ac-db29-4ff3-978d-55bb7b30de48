import React from 'react';
import { Search, X } from 'lucide-react';
import { ManualChapter, SearchResult } from './ManualTypes';

interface ManualSearchProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  searchResults: SearchResult[];
  handleSearchSelect: (result: SearchResult) => void;
  manualContent: ManualChapter[];
}

const ManualSearch: React.FC<ManualSearchProps> = ({
  searchQuery,
  setSearchQuery,
  searchResults,
  handleSearchSelect,
  manualContent
}) => {
  // Handle search functionality
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value.toLowerCase();
    setSearchQuery(query);
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
  };

  return (
    <div className="relative">
      <div className="relative flex items-center w-full">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
        <input
          type="text"
          placeholder="Search manual..."
          className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          value={searchQuery}
          onChange={handleSearch}
        />
        {searchQuery && (
          <button
            onClick={clearSearch}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <X size={18} />
          </button>
        )}
      </div>

      {/* Search results */}
      {searchQuery.length >= 2 && searchResults.length > 0 && (
        <div className="absolute z-10 mt-2 w-full bg-white border border-gray-200 rounded-md shadow-lg max-h-96 overflow-y-auto">
          <div className="p-3 border-b border-gray-200">
            <span className="text-sm text-gray-500">
              {searchResults.length} {searchResults.length === 1 ? 'result' : 'results'} found
            </span>
          </div>
          <div className="divide-y divide-gray-100">
            {searchResults.map((result, index) => (
              <button
                key={index}
                className="w-full text-left p-3 hover:bg-gray-50 transition-colors duration-150"
                onClick={() => handleSearchSelect(result)}
              >
                <div className="flex flex-col gap-1">
                  <span className="font-medium text-blue-600">
                    {result.chapterTitle} &gt; {result.sectionTitle}
                    {result.subsectionTitle && ` > ${result.subsectionTitle}`}
                  </span>
                  <p className="text-sm text-gray-600 line-clamp-2">{result.contentSnippet}</p>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {searchQuery.length >= 2 && searchResults.length === 0 && (
        <div className="absolute z-10 mt-2 w-full bg-white border border-gray-200 rounded-md shadow-lg">
          <div className="p-4 text-center text-gray-500">No results found</div>
        </div>
      )}
    </div>
  );
};

export default ManualSearch;
