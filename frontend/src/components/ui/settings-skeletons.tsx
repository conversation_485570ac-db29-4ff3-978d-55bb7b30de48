import React from 'react';
import { ShimmerSkeleton } from './skeleton';

export const SettingsPageSkeleton: React.FC = () => {
  return (
    <div className="p-4 space-y-6">
      {/* Development Settings Card */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <ShimmerSkeleton className="h-6 w-48 mb-2" />
          <ShimmerSkeleton className="h-4 w-80" />
        </div>
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <ShimmerSkeleton className="h-5 w-32 mb-1" />
              <ShimmerSkeleton className="h-4 w-64" />
            </div>
            <ShimmerSkeleton className="h-6 w-12 rounded-full" />
          </div>
        </div>
      </div>

      {/* User Management Card */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <ShimmerSkeleton className="h-6 w-40 mb-2" />
          <ShimmerSkeleton className="h-4 w-72" />
        </div>
        <div className="p-6">
          {/* User table skeleton */}
          <div className="space-y-4">
            {/* Table header */}
            <div className="grid grid-cols-4 gap-4 pb-3 border-b border-gray-200">
              <ShimmerSkeleton className="h-4 w-16" />
              <ShimmerSkeleton className="h-4 w-20" />
              <ShimmerSkeleton className="h-4 w-12" />
              <ShimmerSkeleton className="h-4 w-20" />
            </div>
            
            {/* Table rows */}
            {Array.from({ length: 3 }, (_, i) => (
              <div key={i} className="grid grid-cols-4 gap-4 py-3">
                <ShimmerSkeleton className="h-4 w-24" />
                <ShimmerSkeleton className="h-4 w-32" />
                <ShimmerSkeleton className="h-6 w-16 rounded-full" />
                <div className="flex gap-2">
                  <ShimmerSkeleton className="h-8 w-16 rounded" />
                  <ShimmerSkeleton className="h-8 w-16 rounded" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export const UserManagementSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <ShimmerSkeleton className="h-6 w-40 mb-2" />
        <ShimmerSkeleton className="h-4 w-72" />
      </div>
      <div className="p-6">
        <div className="space-y-4">
          {/* Controls */}
          <div className="flex justify-between items-center">
            <ShimmerSkeleton className="h-10 w-64" />
            <ShimmerSkeleton className="h-10 w-32" />
          </div>
          
          {/* Table header */}
          <div className="grid grid-cols-5 gap-4 pb-3 border-b border-gray-200">
            <ShimmerSkeleton className="h-4 w-16" />
            <ShimmerSkeleton className="h-4 w-20" />
            <ShimmerSkeleton className="h-4 w-12" />
            <ShimmerSkeleton className="h-4 w-16" />
            <ShimmerSkeleton className="h-4 w-20" />
          </div>
          
          {/* Table rows */}
          {Array.from({ length: 5 }, (_, i) => (
            <div key={i} className="grid grid-cols-5 gap-4 py-3">
              <ShimmerSkeleton className="h-4 w-24" />
              <ShimmerSkeleton className="h-4 w-32" />
              <ShimmerSkeleton className="h-6 w-16 rounded-full" />
              <ShimmerSkeleton className="h-4 w-20" />
              <div className="flex gap-2">
                <ShimmerSkeleton className="h-8 w-16 rounded" />
                <ShimmerSkeleton className="h-8 w-16 rounded" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};