import React from 'react';
import { TEXT_STYLES } from '../../lib/config/ui';
import { LineChart } from 'lucide-react';

interface SectionTitleProps {
  title: string;
  icon?: React.ElementType;
  subtitle?: string;
  rightContent?: React.ReactNode;
  className?: string;
}

export function SectionTitle({ 
  title, 
  icon: Icon = LineChart,
  subtitle, 
  rightContent, 
  className = '' 
}: SectionTitleProps) {
  return (
    <div className={`flex items-center justify-between ${className}`}>
      <div className="flex items-center gap-1.5">
        <div className={`p-1 rounded-lg bg-gradient-to-br from-blue-100/80 via-blue-100/50 to-white border border-blue-200/50 shadow-[0_2px_8px_rgba(14,126,228,0.12)] hover:shadow-[0_4px_16px_rgba(14,126,228,0.15)] hover:scale-105 transition-all duration-300`}>
          <Icon 
            size={12} 
            className="text-primary-blue" 
            strokeWidth={2.5}
          />
        </div>
        <h3 className={TEXT_STYLES.title.base}>{title}</h3>
        {subtitle && (
          <span className="text-[11px] text-gray-500">{subtitle}</span>
        )}
      </div>
      {rightContent}
    </div>
  );
}