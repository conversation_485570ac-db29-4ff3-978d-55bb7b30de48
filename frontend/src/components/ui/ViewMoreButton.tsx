import React from 'react';
import { ArrowRight } from 'lucide-react';

interface ViewMoreButtonProps {
  label: string;
  onClick: () => void;
}

export function ViewMoreButton({ label, onClick }: ViewMoreButtonProps) {
  return (
    <button
      onClick={onClick}
      className="text-[10px] text-gray-400 hover:text-gray-600 transition-colors group whitespace-nowrap flex items-center gap-1 hover:scale-105"
    >
      {label}
      <ArrowRight size={16} className="transition-transform group-hover:translate-x-1" />
    </button>
  );
}