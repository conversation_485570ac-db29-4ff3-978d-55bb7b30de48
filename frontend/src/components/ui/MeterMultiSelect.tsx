import React, { useMemo, useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { Input } from './input';
import { Checkbox } from './checkbox';

export interface MeterOption {
  id: number;
  name: string;
}

interface MeterMultiSelectProps {
  options: MeterOption[];
  selectedIds: number[];
  onChange: (ids: number[]) => void;
  placeholder?: string;
}

const MeterMultiSelect: React.FC<MeterMultiSelectProps> = ({ options, selectedIds, onChange, placeholder = 'Select meters' }) => {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const triggerRef = useRef<HTMLButtonElement>(null);
  const popoverRef = useRef<HTMLDivElement>(null);

  const filtered = useMemo(() => {
    const term = search.toLowerCase();
    return options.filter(o => o.name.toLowerCase().includes(term));
  }, [options, search]);

  const toggleId = (id: number) => {
    if (selectedIds.includes(id)) {
      onChange(selectedIds.filter(i => i !== id));
    } else {
      onChange([...selectedIds, id]);
    }
  };

  // Close popover on outside click
  useEffect(() => {
    if (!open) return;
    function handle(e: MouseEvent) {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(e.target as Node) &&
        triggerRef.current &&
        !triggerRef.current.contains(e.target as Node)
      ) {
        setOpen(false);
      }
    }
    document.addEventListener('mousedown', handle);
    return () => document.removeEventListener('mousedown', handle);
  }, [open]);

  return (
    <div className="relative">
      <button
        type="button"
        ref={triggerRef}
        className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm focus:outline-none"
        onClick={() => setOpen((v) => !v)}
      >
        <span className="truncate">
          {selectedIds.length === 0 ? (
            <span className="text-muted-foreground">{placeholder}</span>
          ) : (
            `${selectedIds.length} meter${selectedIds.length > 1 ? 's' : ''} selected`
          )}
        </span>
        <ChevronDown className="h-4 w-4 opacity-50" />
      </button>
      {open && (
        <div
          ref={popoverRef}
          className="absolute left-0 z-50 w-72 rounded-md border bg-white shadow-md p-2 mt-1"
        >
          <Input
            value={search}
            onChange={e => setSearch(e.target.value)}
            placeholder="Search meters..."
            className="mb-2 h-8"
          />
          <div className="max-h-60 overflow-y-auto space-y-1 pr-1">
            {filtered.length === 0 && (
              <p className="text-xs text-center text-muted-foreground">No meters</p>
            )}
            {filtered.map(opt => (
              <label key={opt.id} className="flex items-center gap-2 text-sm cursor-pointer px-1 py-0.5 rounded hover:bg-gray-100">
                <Checkbox checked={selectedIds.includes(opt.id)} onCheckedChange={() => toggleId(opt.id)} />
                <span>{opt.name}</span>
              </label>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MeterMultiSelect;
