import React from 'react';

interface EmptyStateProps {
  title?: string;
  message: string;
}

/**
 * Reusable empty‑state placeholder – shows a friendly message when data is missing.
 */
export function EmptyState({ title, message }: EmptyStateProps) {
  return (
    <div className="flex flex-col items-center justify-center gap-sm p-lg text-gray500">
      {title && <h3 className="text-lg font-semibold text-gray600 mb-sm">{title}</h3>}
      <p className="text-center max-w-xs">{message}</p>
    </div>
  );
} 