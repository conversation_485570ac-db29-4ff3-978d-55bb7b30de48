import React from 'react';
import { ShimmerSkeleton } from './skeleton';

// Tower Overview Skeleton
export function TowerSkeleton() {
  return (
    <div className="w-full p-3 bg-white rounded-xl border border-blue-100">
      <div className="space-y-3">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <ShimmerSkeleton className="w-6 h-6 rounded-lg" />
            <ShimmerSkeleton className="w-16 h-3" />
          </div>
          <ShimmerSkeleton className="w-20 h-3" />
        </div>

        {/* Stats */}
        <div className="p-1.5 rounded-lg bg-white border border-blue-100">
          <div className="flex items-center justify-between mb-1">
            <ShimmerSkeleton className="w-24 h-2.5" />
            <ShimmerSkeleton className="w-12 h-2.5" />
          </div>
          <div className="flex items-baseline gap-1 mb-2">
            <ShimmerSkeleton className="w-16 h-4" />
            <ShimmerSkeleton className="w-8 h-2.5" />
          </div>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <ShimmerSkeleton className="w-20 h-2" />
              <ShimmerSkeleton className="w-12 h-2" />
            </div>
            <ShimmerSkeleton className="w-full h-1.5 rounded-full" />
          </div>
        </div>

        {/* Alerts & Meters */}
        <div className="flex items-center justify-end">
          <div className="flex items-center gap-4">
            <ShimmerSkeleton className="w-12 h-2.5" />
            <ShimmerSkeleton className="w-16 h-2.5" />
          </div>
        </div>
      </div>
    </div>
  );
}

// Chart Skeleton
export function ChartSkeleton({ height = 210 }: { height?: number }) {
  return (
    <div className="w-full" style={{ height }}>
      <div className="space-y-3 h-full">
        {/* Chart area */}
        <ShimmerSkeleton className="w-full flex-1 rounded-lg" style={{ height: height - 40 }} />
        {/* Legend */}
        <div className="flex items-center justify-center gap-4">
          <div className="flex items-center gap-2">
            <ShimmerSkeleton className="w-3 h-3 rounded-full" />
            <ShimmerSkeleton className="w-12 h-2.5" />
          </div>
          <div className="flex items-center gap-2">
            <ShimmerSkeleton className="w-3 h-3 rounded-full" />
            <ShimmerSkeleton className="w-12 h-2.5" />
          </div>
        </div>
      </div>
    </div>
  );
}

// Performance Card Skeleton
export function PerformanceCardSkeleton() {
  return (
    <div className="space-y-3">
      {/* EUI Card */}
      <div className="p-2 bg-gradient-to-br from-blue-50/60 via-blue-50/40 to-white rounded-lg border border-blue-100/80">
        <div className="flex items-center justify-between mb-1">
          <ShimmerSkeleton className="w-24 h-3" />
          <div className="text-center">
            <ShimmerSkeleton className="w-12 h-4" />
          </div>
        </div>
        <ShimmerSkeleton className="w-full h-8 rounded-lg mt-0.5" />
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-3 gap-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-gradient-to-br from-blue-50/80 via-blue-50/50 to-white rounded-lg p-2 border border-blue-100">
            <ShimmerSkeleton className="w-16 h-2.5 mb-0.5" />
            <ShimmerSkeleton className="w-12 h-3" />
          </div>
        ))}
      </div>
    </div>
  );
}

// Monthly Overview Skeleton
export function MonthlyOverviewSkeleton() {
  return (
    <div className="space-y-3">
      {/* Energy metrics */}
      <div className="grid grid-cols-3 gap-2">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="text-center">
            <ShimmerSkeleton className="w-full h-2.5 mb-1" />
            <ShimmerSkeleton className="w-3/4 h-4 mx-auto" />
          </div>
        ))}
      </div>

      {/* Billing info */}
      <div className="bg-blue-50/50 rounded-lg p-2 border border-blue-100">
        <div className="flex items-center justify-between mb-1">
          <ShimmerSkeleton className="w-16 h-2.5" />
          <ShimmerSkeleton className="w-12 h-2.5" />
        </div>
        <ShimmerSkeleton className="w-20 h-3" />
      </div>
    </div>
  );
}

// Pie Chart Skeleton
export function PieChartSkeleton() {
  return (
    <div className="h-72 flex items-center justify-center">
      <div className="relative">
        <ShimmerSkeleton className="w-48 h-48 rounded-full" />
        <div className="absolute inset-0 flex items-center justify-center">
          <ShimmerSkeleton className="w-24 h-24 rounded-full" />
        </div>
      </div>
    </div>
  );
}