import * as React from "react"
import { X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "./Button"

export interface DrawerHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  onClose?: () => void
}

const DrawerHeader = React.forwardRef<HTMLDivElement, DrawerHeaderProps>(
  ({ className, onClose, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "flex items-center justify-between border-b border-gray-200 p-6",
        className
      )}
      {...props}
    >
      <div className="flex items-center gap-3">
        {children}
      </div>
      {onClose && (
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700"
          aria-label="Close drawer"
        >
          <X className="h-5 w-5" />
        </Button>
      )}
    </div>
  )
)
DrawerHeader.displayName = "DrawerHeader"

export { DrawerHeader }
