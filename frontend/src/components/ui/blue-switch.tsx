import * as React from "react"
import * as SwitchPrimitives from "@radix-ui/react-switch"

import { cn } from "@/lib/utils"

const BlueSwitch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      "peer inline-flex h-8 w-16 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-300 focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 shadow-md",
      className
    )}
    {...props}
    ref={ref}
    style={{ 
      backgroundColor: props.checked ? '#2563eb' : '#d1d5db', 
      borderColor: props.checked ? '#1d4ed8' : 'transparent' 
    }}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        "pointer-events-none block h-7 w-7 rounded-full shadow-lg ring-0 transition-transform duration-200 ease-in-out border-2"
      )}
      style={{ 
        backgroundColor: props.checked ? '#dbeafe' : 'white', 
        borderColor: props.checked ? '#2563eb' : 'transparent',
        transform: props.checked ? 'translateX(28px)' : 'translateX(0)'
      }}
    />
  </SwitchPrimitives.Root>
))
BlueSwitch.displayName = SwitchPrimitives.Root.displayName

export { BlueSwitch }
