import React from 'react';
import * as SwitchPrimitives from "@radix-ui/react-switch";
import { Label } from './label';
import { cn } from '@/lib/utils';

interface ToggleSwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
  disabled?: boolean;
  size?: 'sm' | 'md';
  className?: string;
  labelClassName?: string;
}

const ToggleSwitch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  ToggleSwitchProps
>(({ checked, onChange, label, disabled = false, size = 'md', className, labelClassName }, ref) => {
  // Size variations
  const switchSizeClasses = {
    sm: 'h-5 w-9',
    md: 'h-6 w-11'
  };

  const thumbSizeClasses = {
    sm: 'h-4 w-4 data-[state=checked]:translate-x-4',
    md: 'h-5 w-5 data-[state=checked]:translate-x-5'
  };

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <SwitchPrimitives.Root
        ref={ref}
        checked={checked}
        onCheckedChange={onChange}
        disabled={disabled}
        className={cn(
          "peer inline-flex shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors duration-300 ease-in-out",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-300 focus-visible:ring-offset-2 focus-visible:ring-offset-background",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "data-[state=checked]:bg-blue-600 data-[state=unchecked]:bg-gray-300",
          switchSizeClasses[size]
        )}
      >
        <SwitchPrimitives.Thumb
          className={cn(
            "pointer-events-none block rounded-full bg-white shadow-sm ring-0 transition-transform duration-300 ease-in-out",
            "data-[state=unchecked]:translate-x-0",
            thumbSizeClasses[size]
          )}
        />
      </SwitchPrimitives.Root>
      {label && (
        <Label className={cn("text-sm font-medium text-gray-900", labelClassName)}>
          {label}
        </Label>
      )}
    </div>
  );
});

ToggleSwitch.displayName = 'ToggleSwitch';

export { ToggleSwitch };
