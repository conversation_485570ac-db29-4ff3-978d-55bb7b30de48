import * as React from "react"
import * as SwitchPrimitives from "@radix-ui/react-switch"

import { cn } from "@/lib/utils"

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      "peer inline-flex h-8 w-16 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-300 focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-700 data-[state=unchecked]:bg-gray-200 shadow-md",
      className
    )}
    {...props}
    ref={ref}
    style={{ backgroundColor: props.checked ? '#2563eb' : '#d1d5db', borderColor: props.checked ? '#1d4ed8' : 'transparent' }}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        "pointer-events-none block h-7 w-7 rounded-full bg-white shadow-lg ring-0 transition-transform duration-200 ease-in-out data-[state=checked]:translate-x-7 data-[state=unchecked]:translate-x-0 data-[state=checked]:border-blue-600 data-[state=checked]:bg-blue-200 border-2"
      )}
      style={{ backgroundColor: props.checked ? '#dbeafe' : 'white', borderColor: props.checked ? '#2563eb' : 'transparent' }}
    />
  </SwitchPrimitives.Root>
))
Switch.displayName = SwitchPrimitives.Root.displayName

export { Switch }
