import React from 'react';
import { ShimmerSkeleton } from './skeleton';

// Alarms Page Header Skeleton
export function AlarmsHeaderSkeleton() {
  return (
    <div className="flex justify-between items-center mb-3">
      <div className="flex items-center gap-1.5">
        <ShimmerSkeleton className="w-6 h-6 rounded-lg" />
        <ShimmerSkeleton className="h-4 w-32" />
      </div>
      <ShimmerSkeleton className="h-8 w-48 rounded-md" />
    </div>
  );
}

// Tab Bar Skeleton
export function AlarmsTabBarSkeleton() {
  return (
    <div className="border-b border-gray-200 mb-4">
      <div className="flex space-x-6">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-center gap-2 pb-4">
            <ShimmerSkeleton className="w-4 h-4 rounded-sm" />
            <ShimmerSkeleton className="h-4 w-24" />
          </div>
        ))}
      </div>
    </div>
  );
}

// Table Header Skeleton
export function TableHeaderSkeleton({ columns = 5 }: { columns?: number }) {
  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden">
      {/* Table header */}
      <div className="bg-gray-50 border-b border-gray-200 p-3">
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {[...Array(columns)].map((_, i) => (
            <ShimmerSkeleton key={i} className="h-4 w-20" />
          ))}
        </div>
      </div>
    </div>
  );
}

// Table Row Skeleton
export function TableRowSkeleton({ columns = 5 }: { columns?: number }) {
  return (
    <div className="border-l border-r border-b border-gray-200 p-3 hover:bg-gray-50">
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {[...Array(columns)].map((_, i) => (
          <div key={i} className="flex items-center">
            {i === 0 && <ShimmerSkeleton className="w-4 h-4 rounded-full mr-2" />}
            <ShimmerSkeleton className="h-4 flex-1" />
          </div>
        ))}
      </div>
    </div>
  );
}

// Complete Table Skeleton
export function TableSkeleton({ columns = 5, rows = 8 }: { columns?: number; rows?: number }) {
  return (
    <div>
      <TableHeaderSkeleton columns={columns} />
      {[...Array(rows)].map((_, i) => (
        <TableRowSkeleton key={i} columns={columns} />
      ))}
    </div>
  );
}

// Active Alarms Tab Skeleton
export function ActiveAlarmsTabSkeleton() {
  return (
    <div className="space-y-4">
      {/* Controls bar */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <ShimmerSkeleton className="h-8 w-32 rounded-md" />
          <ShimmerSkeleton className="h-8 w-28 rounded-md" />
        </div>
        <div className="flex items-center gap-2">
          <ShimmerSkeleton className="h-8 w-24 rounded-md" />
          <ShimmerSkeleton className="h-8 w-20 rounded-md" />
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-4 gap-4 mb-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center gap-3">
              <ShimmerSkeleton className="w-10 h-10 rounded-lg" />
              <div className="space-y-1">
                <ShimmerSkeleton className="h-4 w-16" />
                <ShimmerSkeleton className="h-6 w-8" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Table */}
      <TableSkeleton columns={6} rows={8} />

      {/* Pagination */}
      <div className="flex justify-between items-center mt-4">
        <ShimmerSkeleton className="h-4 w-32" />
        <div className="flex items-center gap-2">
          <ShimmerSkeleton className="h-8 w-8 rounded-md" />
          <ShimmerSkeleton className="h-8 w-8 rounded-md" />
          <ShimmerSkeleton className="h-8 w-8 rounded-md" />
          <ShimmerSkeleton className="h-8 w-8 rounded-md" />
        </div>
      </div>
    </div>
  );
}

// Alarm History Tab Skeleton
export function AlarmHistoryTabSkeleton() {
  return (
    <div className="space-y-4">
      {/* Search and filters */}
      <div className="flex gap-4 items-center">
        <ShimmerSkeleton className="h-10 flex-1 max-w-md rounded-md" />
        <ShimmerSkeleton className="h-10 w-32 rounded-md" />
        <ShimmerSkeleton className="h-10 w-28 rounded-md" />
        <ShimmerSkeleton className="h-10 w-24 rounded-md" />
      </div>

      {/* Summary stats */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <div className="grid grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="text-center">
              <ShimmerSkeleton className="h-8 w-16 mx-auto mb-2" />
              <ShimmerSkeleton className="h-4 w-20 mx-auto" />
            </div>
          ))}
        </div>
      </div>

      {/* Table */}
      <TableSkeleton columns={7} rows={10} />

      {/* Pagination */}
      <div className="flex justify-between items-center">
        <ShimmerSkeleton className="h-4 w-40" />
        <div className="flex items-center gap-2">
          <ShimmerSkeleton className="h-8 w-20 rounded-md" />
          <ShimmerSkeleton className="h-8 w-8 rounded-md" />
          <ShimmerSkeleton className="h-8 w-8 rounded-md" />
          <ShimmerSkeleton className="h-8 w-20 rounded-md" />
        </div>
      </div>
    </div>
  );
}

// Alarm Rules Tab Skeleton
export function AlarmRulesTabSkeleton() {
  return (
    <div className="space-y-4">
      {/* Header with create button */}
      <div className="flex justify-between items-center">
        <div className="space-y-1">
          <ShimmerSkeleton className="h-6 w-48" />
          <ShimmerSkeleton className="h-4 w-64" />
        </div>
        <ShimmerSkeleton className="h-10 w-32 rounded-md" />
      </div>

      {/* Filters and search */}
      <div className="flex gap-4 items-center">
        <ShimmerSkeleton className="h-10 flex-1 max-w-sm rounded-md" />
        <ShimmerSkeleton className="h-10 w-36 rounded-md" />
        <ShimmerSkeleton className="h-10 w-28 rounded-md" />
      </div>

      {/* Rules table */}
      <TableSkeleton columns={6} rows={6} />

      {/* Footer with pagination */}
      <div className="flex justify-between items-center">
        <ShimmerSkeleton className="h-4 w-36" />
        <div className="flex items-center gap-2">
          <ShimmerSkeleton className="h-8 w-24 rounded-md" />
          <ShimmerSkeleton className="h-8 w-8 rounded-md" />
          <ShimmerSkeleton className="h-8 w-8 rounded-md" />
          <ShimmerSkeleton className="h-8 w-24 rounded-md" />
        </div>
      </div>
    </div>
  );
}

// Full Alarms Page Skeleton
export function AlarmsPageSkeleton() {
  return (
    <main className="h-[calc(100vh-56px)] p-4 relative bg-gradient-to-br from-[#F9FAFF] via-white to-blue-50/30 overflow-hidden">
      <div className="container mx-auto p-6 flex flex-col h-full overflow-auto">
        {/* Header */}
        <AlarmsHeaderSkeleton />
        
        {/* Tab bar */}
        <AlarmsTabBarSkeleton />
        
        {/* Tab content */}
        <ActiveAlarmsTabSkeleton />
      </div>
    </main>
  );
}

// Loading state for individual tab content
export function TabContentSkeleton({ tab }: { tab: 'active' | 'history' | 'rules' }) {
  switch (tab) {
    case 'active':
      return <ActiveAlarmsTabSkeleton />;
    case 'history':
      return <AlarmHistoryTabSkeleton />;
    case 'rules':
      return <AlarmRulesTabSkeleton />;
    default:
      return <ActiveAlarmsTabSkeleton />;
  }
}

// Quick table loading for partial updates
export function QuickTableSkeleton({ rows = 5 }: { rows?: number }) {
  return (
    <div className="space-y-2">
      {[...Array(rows)].map((_, i) => (
        <div key={i} className="flex items-center gap-4 p-3 bg-gray-50 rounded-md">
          <ShimmerSkeleton className="w-4 h-4 rounded-full" />
          <ShimmerSkeleton className="h-4 flex-1" />
          <ShimmerSkeleton className="h-4 w-24" />
          <ShimmerSkeleton className="h-4 w-16" />
          <ShimmerSkeleton className="h-6 w-20 rounded-full" />
        </div>
      ))}
    </div>
  );
}