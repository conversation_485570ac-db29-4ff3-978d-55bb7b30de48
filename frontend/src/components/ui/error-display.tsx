import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>if<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON>t } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ErrorDisplayProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  isRetrying?: boolean;
  retryCount?: number;
  className?: string;
  showRetryButton?: boolean;
  variant?: 'default' | 'compact' | 'inline';
  errorType?: 'network' | 'data' | 'permission' | 'timeout' | 'generic';
}

interface EnhancedErrorDisplayProps {
  error: Error | null;
  onRetry?: () => void;
  isRetrying?: boolean;
  retryCount?: number;
  className?: string;
  context?: string;
  variant?: 'default' | 'compact' | 'inline';
}

export const EnhancedErrorDisplay: React.FC<EnhancedErrorDisplayProps> = ({
  error,
  onRetry,
  isRetrying = false,
  retryCount = 0,
  className = "",
  context = "data",
  variant = 'default'
}) => {
  if (!error) return null;

  // Determine error type from error message/name
  const getErrorType = (): ErrorDisplayProps['errorType'] => {
    const message = (typeof error.message === 'string' ? error.message : String(error.message || '')).toLowerCase();
    const name = (typeof error.name === 'string' ? error.name : String(error.name || '')).toLowerCase();
    
    if (message.includes('network') || message.includes('fetch') || name.includes('networkerror')) {
      return 'network';
    }
    if (message.includes('timeout')) {
      return 'timeout';
    }
    if (message.includes('permission') || message.includes('unauthorized') || message.includes('forbidden')) {
      return 'permission';
    }
    if (message.includes('data')) {
      return 'data';
    }
    return 'generic';
  };

  return (
    <ErrorDisplay
      title={`Failed to load ${context}`}
      message={error.message}
      onRetry={onRetry}
      isRetrying={isRetrying}
      retryCount={retryCount}
      className={className}
      variant={variant}
      errorType={getErrorType()}
    />
  );
};

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  title,
  message,
  onRetry,
  isRetrying = false,
  retryCount = 0,
  className = "",
  showRetryButton = true,
  variant = 'default',
  errorType = 'generic'
}) => {
  
  // Determine icon and default messages based on error type
  const getErrorConfig = () => {
    switch (errorType) {
      case 'network':
        return {
          icon: WifiOff,
          defaultTitle: 'Connection Error',
          defaultMessage: 'Unable to connect to server. Please check your internet connection.',
          iconColor: 'text-orange-600',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          textColor: 'text-orange-700',
          buttonColor: 'bg-orange-600 hover:bg-orange-700'
        };
      case 'timeout':
        return {
          icon: Clock,
          defaultTitle: 'Request Timeout',
          defaultMessage: 'The request took too long to complete. Please try again.',
          iconColor: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-700',
          buttonColor: 'bg-yellow-600 hover:bg-yellow-700'
        };
      case 'permission':
        return {
          icon: ShieldAlert,
          defaultTitle: 'Access Denied',
          defaultMessage: 'You don\'t have permission to access this data.',
          iconColor: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-700',
          buttonColor: 'bg-red-600 hover:bg-red-700'
        };
      case 'data':
        return {
          icon: AlertTriangle,
          defaultTitle: 'Data Error',
          defaultMessage: 'Unable to load data. The information may be temporarily unavailable.',
          iconColor: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-700',
          buttonColor: 'bg-blue-600 hover:bg-blue-700'
        };
      default:
        return {
          icon: AlertTriangle,
          defaultTitle: 'Error',
          defaultMessage: 'Something went wrong. Please try again.',
          iconColor: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-700',
          buttonColor: 'bg-red-600 hover:bg-red-700'
        };
    }
  };

  const config = getErrorConfig();
  const Icon = config.icon;
  const finalTitle = title || config.defaultTitle;
  const finalMessage = message || config.defaultMessage;

  const handleRetry = () => {
    if (onRetry && !isRetrying) {
      onRetry();
    }
  };

  if (variant === 'inline') {
    return (
      <div className={cn(`flex items-center gap-2 text-sm ${config.textColor}`, className)}>
        <Icon size={16} />
        <span>{finalMessage}</span>
        {showRetryButton && onRetry && (
          <button
            onClick={handleRetry}
            disabled={isRetrying}
            className="text-blue-600 hover:text-blue-800 underline disabled:opacity-50 ml-1"
          >
            {isRetrying ? 'Retrying...' : 'Retry'}
          </button>
        )}
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn(`flex flex-col items-center justify-center p-4 text-center`, className)}>
        <Icon size={24} className={`${config.iconColor} mb-2`} />
        <p className={`text-sm ${config.textColor} mb-2`}>{finalMessage}</p>
        {showRetryButton && onRetry && (
          <button
            onClick={handleRetry}
            disabled={isRetrying}
            className={cn(
              `flex items-center gap-2 px-3 py-1 text-sm rounded-md transition-all border disabled:opacity-50`,
              config.bgColor,
              config.textColor,
              config.borderColor,
              'hover:opacity-80'
            )}
          >
            <RefreshCw size={14} className={isRetrying ? 'animate-spin' : ''} />
            {isRetrying ? 'Retrying...' : 'Retry'}
          </button>
        )}
        {retryCount > 0 && (
          <p className="text-xs text-gray-500 mt-1">Attempt: {retryCount}</p>
        )}
      </div>
    );
  }

  // Default variant
  return (
    <div className={cn(
      `flex flex-col items-center justify-center p-6 text-center rounded-lg border`,
      config.bgColor,
      config.borderColor,
      className
    )}>
      <div className={cn(
        `flex items-center justify-center w-12 h-12 rounded-full mb-4 border`,
        config.bgColor,
        config.borderColor
      )}>
        <Icon size={24} className={config.iconColor} />
      </div>
      <h3 className={cn(`text-lg font-semibold mb-2`, config.textColor)}>{finalTitle}</h3>
      <p className={cn(`mb-4 max-w-md`, config.textColor)}>{finalMessage}</p>
      {showRetryButton && onRetry && (
        <button
          onClick={handleRetry}
          disabled={isRetrying}
          className={cn(
            `flex items-center gap-2 px-4 py-2 text-white rounded-md transition-all disabled:opacity-50`,
            config.buttonColor
          )}
        >
          <RefreshCw size={16} className={isRetrying ? 'animate-spin' : ''} />
          {isRetrying ? 'Retrying...' : 'Try Again'}
        </button>
      )}
      {retryCount > 0 && (
        <p className={cn(`text-sm mt-2`, config.textColor)}>Retry attempt: {retryCount}</p>
      )}
    </div>
  );
};
