import React, { useEffect, useState } from 'react';
import { Tabs, TabsList, TabsTrigger, TabsContent } from './tabs';

export interface TabItem {
  value: string;
  label: string | React.ReactNode;
  icon?: React.ReactNode;
  content: React.ReactNode;
}

interface FlatTabBarProps {
  tabs: TabItem[];
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  className?: string;
}

/**
 * A flat tab bar component with underline style for active tabs
 */
export function FlatTabBar({
  tabs,
  defaultValue,
  value,
  onValueChange,
  className = '',
}: FlatTabBarProps) {
  // Track the previous active tab for animation purposes
  const [prevValue, setPrevValue] = useState<string | undefined>(value || defaultValue || tabs[0]?.value);

  // Update prevValue when the active tab changes
  useEffect(() => {
    if (value && value !== prevValue) {
      setPrevValue(value);
    }
  }, [value, prevValue]);

  return (
    <Tabs
      defaultValue={defaultValue || tabs[0]?.value}
      value={value}
      onValueChange={onValueChange}
      className={`flex flex-col flex-grow ${className}`}
    >
      <div className="border-b border-gray-100 mb-0">
        <TabsList className="flex bg-transparent p-0 space-x-5">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              className={`
                group relative flex items-center gap-2 px-3 py-2.5 text-sm font-medium
                whitespace-nowrap
                transition-all duration-300 ease-in-out
                data-[state=active]:text-primary-blue
                data-[state=inactive]:text-gray-500
                hover:text-primary-blue hover:bg-blue-50/50
                focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-200/50 focus-visible:ring-offset-1
              `}
              onClick={() => {
                if (onValueChange && tab.value !== value) {
                  setPrevValue(value);
                  onValueChange(tab.value);
                }
              }}
            >
              <div className="flex items-center gap-2 relative">
                {tab.icon && (
                  <span className={`
                    flex items-center justify-center w-4 h-4
                    transition-all duration-300
                    group-data-[state=active]:text-primary-blue
                    group-data-[state=inactive]:text-gray-400
                    group-hover:text-primary-blue
                  `}>
                    {tab.icon}
                  </span>
                )}
                <span className={`
                  transition-all duration-300
                  group-data-[state=active]:text-primary-blue
                  group-data-[state=inactive]:text-gray-500
                  group-hover:text-primary-blue
                `}>
                  {tab.label}
                </span>
                {/* Active indicator as absolute positioned element */}
                {tab.value === value && (
                  <span className="absolute bottom-[-1px] left-0 right-0 h-0.5 bg-primary-blue" />
                )}
              </div>
            </TabsTrigger>
          ))}
        </TabsList>
      </div>

      {tabs.map((tab) => (
        <TabsContent
          key={tab.value}
          value={tab.value}
          className="flex-grow mt-0 pt-3 ring-offset-background focus-visible:outline-none rounded-lg"
        >
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
}
