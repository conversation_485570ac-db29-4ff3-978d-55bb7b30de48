import React from 'react';
import { InfoIcon } from 'lucide-react';
import { apiClient } from '@/lib/api/enhancedApiClient';

export const MockDataIndicator: React.FC = () => {
  const isUsingMockData = apiClient.isUsingMockData();

  if (!isUsingMockData) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-gray-50 border border-gray-200 rounded-md px-3 py-1.5 flex items-center gap-2 shadow-sm z-50 opacity-60 hover:opacity-100 transition-opacity">
      <InfoIcon className="w-3 h-3 text-gray-500" />
      <span className="text-xs text-gray-600">
        Using Mock Data
      </span>
    </div>
  );
};