import React from 'react';
import { ShimmerSkeleton } from './skeleton';

// Meters Header Skeleton (controls area)
export function MetersHeaderSkeleton() {
  return (
    <div className="sticky top-0 z-20 bg-white rounded-xl border border-gray-200 p-2 shadow-sm">
      <div className="flex flex-row flex-nowrap items-center justify-between gap-3 w-full">
        {/* Left side: Building and System Type Filters */}
        <div className="flex items-center gap-2">
          {/* Building Selection */}
          <div className="bg-white rounded-lg border border-gray-200 p-1 flex-shrink-0">
            <div className="flex items-center gap-2">
              <ShimmerSkeleton className="h-8 w-32 rounded-md" />
              <ShimmerSkeleton className="h-8 w-24 rounded-md" />
            </div>
          </div>

          {/* System Type Filter Dropdown */}
          <ShimmerSkeleton className="h-8 w-40 rounded-lg" />
        </div>

        {/* Right side: Status Filter Buttons */}
        <div className="flex items-center gap-1 bg-white rounded-lg border border-gray-200 p-1">
          <ShimmerSkeleton className="h-8 w-20 rounded-lg" />
          <ShimmerSkeleton className="h-8 w-16 rounded-lg" />
          <ShimmerSkeleton className="h-8 w-16 rounded-lg" />
        </div>
      </div>
    </div>
  );
}

// Tree Node Skeleton
export function TreeNodeSkeleton({ level = 0 }: { level?: number }) {
  const indent = level * 20; // 20px per level
  
  return (
    <div 
      className="flex items-center py-1.5 px-2 hover:bg-gray-50" 
      style={{ paddingLeft: `${8 + indent}px` }}
    >
      <div className="flex items-center gap-2 flex-1">
        <ShimmerSkeleton className="w-4 h-4 rounded-sm" />
        <ShimmerSkeleton className="w-4 h-4 rounded-full" />
        <ShimmerSkeleton className="h-4 flex-1 max-w-32" />
      </div>
      <ShimmerSkeleton className="w-6 h-4 rounded-full" />
    </div>
  );
}

// Meter Tree Skeleton
export function MeterTreeSkeleton() {
  return (
    <div className="space-y-1">
      {/* Main building nodes */}
      <TreeNodeSkeleton level={0} />
      <TreeNodeSkeleton level={1} />
      <TreeNodeSkeleton level={1} />
      <TreeNodeSkeleton level={2} />
      <TreeNodeSkeleton level={2} />
      <TreeNodeSkeleton level={2} />
      <TreeNodeSkeleton level={1} />
      <TreeNodeSkeleton level={2} />
      <TreeNodeSkeleton level={2} />
      <TreeNodeSkeleton level={0} />
      <TreeNodeSkeleton level={1} />
      <TreeNodeSkeleton level={1} />
      <TreeNodeSkeleton level={2} />
      <TreeNodeSkeleton level={2} />
    </div>
  );
}

// Meter Details Skeleton
export function MeterDetailsSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-3">
        <div className="flex items-start gap-3">
          <ShimmerSkeleton className="w-10 h-10 rounded-lg" />
          <div className="flex-1 space-y-2">
            <ShimmerSkeleton className="h-6 w-48" />
            <ShimmerSkeleton className="h-4 w-32" />
          </div>
          <ShimmerSkeleton className="w-16 h-6 rounded-full" />
        </div>
        <ShimmerSkeleton className="h-4 w-full max-w-md" />
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div className="space-y-2">
              <ShimmerSkeleton className="h-4 w-20" />
              <ShimmerSkeleton className="h-6 w-16" />
              <ShimmerSkeleton className="h-3 w-12" />
            </div>
          </div>
        ))}
      </div>

      {/* Chart/Readings Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <ShimmerSkeleton className="h-5 w-32" />
          <div className="flex items-center gap-2">
            <ShimmerSkeleton className="h-8 w-20 rounded-md" />
            <ShimmerSkeleton className="h-8 w-16 rounded-md" />
          </div>
        </div>
        <ShimmerSkeleton className="h-64 w-full rounded-lg" />
      </div>

      {/* Properties/Configuration */}
      <div className="space-y-4">
        <ShimmerSkeleton className="h-5 w-28" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="flex justify-between items-center py-2 border-b border-gray-100">
              <ShimmerSkeleton className="h-4 w-24" />
              <ShimmerSkeleton className="h-4 w-32" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Split Pane Layout Skeleton
export function MetersSplitPaneSkeleton() {
  return (
    <div className="flex-1 flex gap-6 overflow-hidden">
      {/* Left Pane - Tree View */}
      <div className="w-64 bg-white rounded-xl border border-gray-200 overflow-y-auto max-h-[calc(100vh-200px)] p-2">
        <MeterTreeSkeleton />
      </div>

      {/* Right Pane - Details */}
      <div className="flex-1 bg-white rounded-xl border border-gray-200 p-4 overflow-y-auto">
        <MeterDetailsSkeleton />
      </div>
    </div>
  );
}

// Full Meters Page Skeleton
export function MetersPageSkeleton() {
  return (
    <div className="h-[calc(100vh-56px)] flex flex-col gap-6 p-4">
      {/* Header skeleton */}
      <MetersHeaderSkeleton />
      
      {/* Split pane skeleton */}
      <MetersSplitPaneSkeleton />
    </div>
  );
}

// Tree Loading Skeleton (for when tree is loading but page is loaded)
export function TreeLoadingSkeleton() {
  return (
    <div className="flex flex-col items-center justify-center h-full p-8 text-center">
      <div className="space-y-4">
        <ShimmerSkeleton className="w-16 h-16 rounded-lg mx-auto" />
        <div className="space-y-2">
          <ShimmerSkeleton className="h-4 w-32 mx-auto" />
          <ShimmerSkeleton className="h-3 w-24 mx-auto" />
        </div>
      </div>
    </div>
  );
}

// Details Loading Skeleton (for when details are loading)
export function DetailsLoadingSkeleton() {
  return (
    <div className="h-full flex flex-col items-center justify-center p-8 text-center">
      <div className="space-y-4">
        <ShimmerSkeleton className="w-20 h-20 rounded-lg mx-auto" />
        <div className="space-y-2">
          <ShimmerSkeleton className="h-5 w-40 mx-auto" />
          <ShimmerSkeleton className="h-4 w-32 mx-auto" />
        </div>
      </div>
    </div>
  );
}