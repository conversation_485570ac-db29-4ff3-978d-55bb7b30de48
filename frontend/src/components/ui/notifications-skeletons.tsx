import React from 'react';
import { ShimmerSkeleton } from './skeleton';

export const NotificationsPageSkeleton: React.FC = () => {
  return (
    <div className="h-[calc(100vh-56px)] flex flex-col gap-4 p-4">
      {/* Stats skeleton */}
      <div className="grid grid-cols-4 gap-4 mt-4">
        {Array.from({ length: 4 }, (_, i) => (
          <div key={i} className="bg-white p-4 rounded-xl border border-[#EDEFF9]">
            <ShimmerSkeleton className="h-4 w-24 mb-2" />
            <ShimmerSkeleton className="h-8 w-16" />
          </div>
        ))}
      </div>

      {/* Main content skeleton */}
      <div className="flex-1 bg-white rounded-xl border border-[#EDEFF9] overflow-hidden flex flex-col">
        {/* Header skeleton */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <ShimmerSkeleton className="h-4 w-4 rounded" />
                <ShimmerSkeleton className="h-4 w-20" />
              </div>
              <div className="flex items-center gap-2">
                <ShimmerSkeleton className="h-8 w-24 rounded-lg" />
                <ShimmerSkeleton className="h-8 w-16 rounded-lg" />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <ShimmerSkeleton className="h-8 w-24 rounded-lg" />
              <ShimmerSkeleton className="h-8 w-32 rounded-lg" />
            </div>
          </div>
        </div>

        {/* Table skeleton */}
        <div className="flex-1 overflow-hidden">
          {/* Table header */}
          <div className="bg-gray-50 px-3 py-3 border-b border-gray-200">
            <div className="grid grid-cols-6 gap-3">
              <ShimmerSkeleton className="h-4 w-4" />
              <ShimmerSkeleton className="h-4 w-4" />
              <ShimmerSkeleton className="h-4 w-24" />
              <ShimmerSkeleton className="h-4 w-16" />
              <ShimmerSkeleton className="h-4 w-12" />
              <ShimmerSkeleton className="h-4 w-16" />
            </div>
          </div>
          
          {/* Table rows */}
          <div className="divide-y divide-gray-200">
            {Array.from({ length: 8 }, (_, i) => (
              <div key={i} className="px-3 py-4">
                <div className="grid grid-cols-6 gap-3 items-start">
                  <ShimmerSkeleton className="h-4 w-4 mt-1" />
                  <ShimmerSkeleton className="h-5 w-5 mt-1" />
                  <div className="space-y-1">
                    <ShimmerSkeleton className="h-4 w-full" />
                    <ShimmerSkeleton className="h-3 w-3/4" />
                  </div>
                  <div className="space-y-1">
                    <ShimmerSkeleton className="h-4 w-20" />
                    <ShimmerSkeleton className="h-3 w-16" />
                  </div>
                  <ShimmerSkeleton className="h-4 w-16" />
                  <ShimmerSkeleton className="h-6 w-12 rounded-full" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export const NotificationListSkeleton: React.FC = () => {
  return (
    <div className="divide-y divide-gray-200">
      {Array.from({ length: 6 }, (_, i) => (
        <div key={i} className="px-3 py-4">
          <div className="grid grid-cols-6 gap-3 items-start">
            <ShimmerSkeleton className="h-4 w-4 mt-1" />
            <ShimmerSkeleton className="h-5 w-5 mt-1" />
            <div className="space-y-1">
              <ShimmerSkeleton className="h-4 w-full" />
              <ShimmerSkeleton className="h-3 w-4/5" />
            </div>
            <div className="space-y-1">
              <ShimmerSkeleton className="h-4 w-20" />
              <ShimmerSkeleton className="h-3 w-16" />
            </div>
            <ShimmerSkeleton className="h-4 w-16" />
            <ShimmerSkeleton className="h-6 w-12 rounded-full" />
          </div>
        </div>
      ))}
    </div>
  );
};

export const NotificationStatsSkeleton: React.FC = () => {
  return (
    <div className="grid grid-cols-4 gap-4">
      {Array.from({ length: 4 }, (_, i) => (
        <div key={i} className="bg-white p-4 rounded-xl border border-[#EDEFF9]">
          <ShimmerSkeleton className="h-4 w-24 mb-2" />
          <ShimmerSkeleton className="h-8 w-16" />
        </div>
      ))}
    </div>
  );
};