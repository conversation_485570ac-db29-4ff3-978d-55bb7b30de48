import * as React from "react"
import { cn } from "@/lib/utils"

type DrawerContentProps = React.HTMLAttributes<HTMLDivElement>;

const DrawerContent = React.forwardRef<HTMLDivElement, DrawerContentProps>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("flex-1 overflow-y-auto p-6", className)}
      {...props}
    >
      {children}
    </div>
  )
)
DrawerContent.displayName = "DrawerContent"

export { DrawerContent }
