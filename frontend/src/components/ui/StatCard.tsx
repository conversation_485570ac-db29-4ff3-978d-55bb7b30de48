import React from 'react';
import { cn } from '@/lib/utils';

export interface StatCardProps {
  title: string;
  value: string | number;
  unit?: string;
  icon?: React.ReactNode;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'danger' | 'info';
  className?: string;
  onClick?: () => void;
}

/**
 * A reusable card component for displaying statistics
 */
export function StatCard({
  title,
  value,
  unit,
  icon,
  variant = 'default',
  className,
  onClick
}: StatCardProps) {
  // Define variant styles
  const variantStyles = {
    default: 'bg-white border-gray-200',
    primary: 'bg-gradient-to-br from-blue-50/30 via-blue-50/20 to-transparent border-blue-200',
    success: 'bg-gradient-to-br from-green-50 to-green-100/50 border-green-200',
    warning: 'bg-gradient-to-br from-amber-50 to-amber-100/50 border-amber-200',
    danger: 'bg-gradient-to-br from-red-50 to-red-100/50 border-red-200',
    info: 'bg-gradient-to-br from-blue-50 to-blue-100/50 border-blue-200',
  };

  // Define text color styles
  const textColorStyles = {
    default: 'text-gray-800',
    primary: 'text-primary-blue',
    success: 'text-green-600',
    warning: 'text-amber-600',
    danger: 'text-red-600',
    info: 'text-blue-600',
  };

  return (
    <div
      className={cn(
        'p-3 rounded-xl border shadow-sm transition-all duration-200',
        variantStyles[variant],
        onClick && 'cursor-pointer hover:shadow-md',
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-center gap-2 mb-2">
        {icon && (
          <div className={cn(
            'p-1.5 rounded-lg',
            variant === 'default' ? 'bg-gray-100' : 'bg-white/50'
          )}>
            {icon}
          </div>
        )}
        <span className="text-xs text-gray-600 font-medium">{title}</span>
      </div>
      <div className="flex items-baseline gap-1">
        <span className={cn('text-lg font-bold', textColorStyles[variant])}>
          {typeof value === 'number' ? value.toLocaleString() : value}
        </span>
        {unit && <span className="text-xs text-gray-500">{unit}</span>}
      </div>
    </div>
  );
}
