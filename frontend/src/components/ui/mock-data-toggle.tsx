import React, { useState, useEffect } from 'react';
import { Database, Server } from 'lucide-react';
import { Switch } from './switch';
import { apiClient } from '@/lib/api/enhancedApiClient';

interface MockDataToggleProps {
  onToggle?: (useMockData: boolean) => void;
}

export const MockDataToggle: React.FC<MockDataToggleProps> = ({ onToggle }) => {
  // Use the master setting, not the temporary override
  const [useMockData, setUseMockData] = useState(apiClient.isMockDataEnabledInSettings());

  const handleToggle = (checked: boolean) => {
    setUseMockData(checked);
    // Use the master setting method which will clear any temporary override
    apiClient.setUseMockData(checked);
    onToggle?.(checked);
    
    // Reload the page to apply the change
    window.location.reload();
  };

  useEffect(() => {
    // Check master setting on mount
    const masterSetting = apiClient.isMockDataEnabledInSettings();
    setUseMockData(masterSetting);
  }, []);

  return (
    <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
      <Server className={`w-5 h-5 ${!useMockData ? 'text-blue-600' : 'text-gray-400'}`} />
      <Switch
        checked={useMockData}
        onCheckedChange={handleToggle}
        className="data-[state=checked]:bg-amber-600"
      />
      <Database className={`w-5 h-5 ${useMockData ? 'text-amber-600' : 'text-gray-400'}`} />
      <span className="text-sm font-medium text-gray-700">
        {useMockData ? 'Mock Data' : 'Live API'}
      </span>
    </div>
  );
};