import React from 'react';
import { ShimmerSkeleton } from './skeleton';

// Analytics Tab Controls Skeleton
export function AnalyticsControlsSkeleton() {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 space-y-4">
      {/* Tab buttons */}
      <div className="flex space-x-2">
        <ShimmerSkeleton className="h-9 w-24 rounded-md" />
        <ShimmerSkeleton className="h-9 w-24 rounded-md" />
      </div>
      
      {/* Control row */}
      <div className="flex flex-wrap gap-3 items-center">
        <ShimmerSkeleton className="h-9 w-32 rounded-md" />
        <ShimmerSkeleton className="h-9 w-28 rounded-md" />
        <ShimmerSkeleton className="h-9 w-36 rounded-md" />
        <ShimmerSkeleton className="h-9 w-32 rounded-md" />
        <ShimmerSkeleton className="h-9 w-28 rounded-md" />
      </div>
    </div>
  );
}

// Analytics Chart Skeleton
export function AnalyticsChartSkeleton({ height = 400 }: { height?: number }) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="space-y-4">
        {/* Chart header with title and controls */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <ShimmerSkeleton className="h-6 w-48" />
            <ShimmerSkeleton className="h-4 w-32" />
          </div>
          <div className="flex items-center gap-2">
            <ShimmerSkeleton className="h-8 w-20 rounded-md" />
            <ShimmerSkeleton className="h-8 w-16 rounded-md" />
          </div>
        </div>
        
        {/* Chart area */}
        <ShimmerSkeleton 
          className="w-full rounded-lg" 
          style={{ height: height - 120 }} 
        />
        
        {/* Legend */}
        <div className="flex items-center justify-center gap-6">
          <div className="flex items-center gap-2">
            <ShimmerSkeleton className="w-4 h-4 rounded-full" />
            <ShimmerSkeleton className="w-16 h-3" />
          </div>
          <div className="flex items-center gap-2">
            <ShimmerSkeleton className="w-4 h-4 rounded-full" />
            <ShimmerSkeleton className="w-20 h-3" />
          </div>
        </div>
      </div>
    </div>
  );
}

// Analytics Metrics Cards Skeleton
export function AnalyticsMetricsSkeleton() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="space-y-2">
            <ShimmerSkeleton className="h-4 w-24" />
            <ShimmerSkeleton className="h-6 w-20" />
            <ShimmerSkeleton className="h-3 w-16" />
          </div>
        </div>
      ))}
    </div>
  );
}

// Comparison Tab Skeleton
export function ComparisonTabSkeleton({ height = 400 }: { height?: number }) {
  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="space-y-4">
        {/* Comparison header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <ShimmerSkeleton className="h-6 w-56" />
            <ShimmerSkeleton className="h-4 w-40" />
          </div>
          <ShimmerSkeleton className="h-8 w-24 rounded-md" />
        </div>
        
        {/* Comparison metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
            <ShimmerSkeleton className="h-4 w-20 mb-2" />
            <ShimmerSkeleton className="h-6 w-24" />
          </div>
          <div className="bg-green-50 rounded-lg p-3 border border-green-200">
            <ShimmerSkeleton className="h-4 w-24 mb-2" />
            <ShimmerSkeleton className="h-6 w-20" />
          </div>
        </div>
        
        {/* Chart area */}
        <ShimmerSkeleton 
          className="w-full rounded-lg" 
          style={{ height: height - 180 }} 
        />
        
        {/* Comparison legend */}
        <div className="flex items-center justify-center gap-8">
          <div className="flex items-center gap-2">
            <ShimmerSkeleton className="w-4 h-4 rounded-full" />
            <ShimmerSkeleton className="w-20 h-3" />
          </div>
          <div className="flex items-center gap-2">
            <ShimmerSkeleton className="w-4 h-4 rounded-full" />
            <ShimmerSkeleton className="w-24 h-3" />
          </div>
        </div>
      </div>
    </div>
  );
}

// Full Analytics Page Skeleton
export function AnalyticsPageSkeleton() {
  return (
    <div className="p-3 md:p-4 lg:p-5 space-y-4 bg-gray-50 min-h-screen">
      {/* Controls skeleton */}
      <AnalyticsControlsSkeleton />
      
      {/* Metrics skeleton */}
      <AnalyticsMetricsSkeleton />
      
      {/* Chart skeleton */}
      <AnalyticsChartSkeleton height={500} />
    </div>
  );
}

// Loading state for individual chart data
export function ChartDataSkeleton({ height = 300 }: { height?: number }) {
  return (
    <div className="space-y-3">
      <ShimmerSkeleton 
        className="w-full rounded-lg" 
        style={{ height }} 
      />
      <div className="flex items-center justify-center gap-4">
        <ShimmerSkeleton className="w-3 h-3 rounded-full" />
        <ShimmerSkeleton className="w-16 h-3" />
      </div>
    </div>
  );
}