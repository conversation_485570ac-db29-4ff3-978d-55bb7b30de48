import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-blue-600 text-white hover:bg-blue-700",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        info: "border-transparent bg-blue-600 text-white hover:bg-blue-700",
        warning: "border-transparent bg-amber-500 text-white hover:bg-amber-600",
        critical: "border-transparent bg-red-600 text-white hover:bg-red-700",
        inactive: "border-gray-500 bg-gray-500 text-white hover:bg-gray-600",
        active: "border-green-600 bg-green-600 text-white hover:bg-green-700",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  forceBgClass?: string
}

function Badge({ className, variant, forceBgClass, ...props }: BadgeProps) {
  // Combine variant classes, any forced background classes, and additional className
  const classes = cn(
    badgeVariants({ variant }),
    forceBgClass ?? undefined,
    className
  )
  // Inline style fallback for blue background info badges
  const styleOverride = classes.includes('bg-blue-600')
    ? { backgroundColor: '#2563EB', color: '#FFFFFF' }
    : undefined
  return (
    <div className={classes} style={styleOverride} {...props} />
  )
}

export { Badge, badgeVariants }
