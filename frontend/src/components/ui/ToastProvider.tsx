import React, { createContext, useContext, useState, ReactNode } from 'react';
import Toast, { ToastProps } from './SimpleToast';

type ToastContextType = {
  showToast: (props: Omit<ToastProps, 'visible' | 'onClose'>) => void;
  hideToast: () => void;
};

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

type ToastProviderProps = {
  children: ReactNode;
};

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toast, setToast] = useState<ToastProps | null>(null);

  const showToast = (props: Omit<ToastProps, 'visible' | 'onClose'>) => {
    setToast({ ...props, visible: true, onClose: () => setToast(null) });
  };

  const hideToast = () => {
    setToast(null);
  };

  return (
    <ToastContext.Provider value={{ showToast, hideToast }}>
      {children}
      {toast && <Toast {...toast} />}
    </ToastContext.Provider>
  );
};

export default ToastProvider;
