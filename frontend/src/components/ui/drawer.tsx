import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const drawerVariants = cva(
  "fixed inset-y-0 right-0 z-50 flex flex-col bg-white shadow-lg transition-transform duration-300 ease-in-out",
  {
    variants: {
      size: {
        default: "w-[400px] max-w-[90vw]",
        sm: "w-[320px] max-w-[90vw]",
        lg: "w-[640px] max-w-[90vw]",
        xl: "w-[800px] max-w-[90vw]",
        full: "w-full",
      },
    },
    defaultVariants: {
      size: "default",
    },
  }
)

export interface DrawerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof drawerVariants> {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: React.ReactNode
}

const Drawer = React.forwardRef<HTMLDivElement, DrawerProps>(
  ({ className, size, open, onOpenChange, children, ...props }, ref) => {
    // Handle ESC key to close the drawer
    React.useEffect(() => {
      const handleEsc = (event: KeyboardEvent) => {
        if (event.key === 'Escape' && open && onOpenChange) {
          onOpenChange(false)
        }
      }
      window.addEventListener('keydown', handleEsc)
      return () => {
        window.removeEventListener('keydown', handleEsc)
      }
    }, [open, onOpenChange])

    // Handle click outside to close the drawer
    const drawerRef = React.useRef<HTMLDivElement>(null)
    React.useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          drawerRef.current &&
          !drawerRef.current.contains(event.target as Node) &&
          open &&
          onOpenChange
        ) {
          onOpenChange(false)
        }
      }
      document.addEventListener('mousedown', handleClickOutside)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }, [open, onOpenChange])

    // Prevent body scroll when drawer is open
    React.useEffect(() => {
      if (open) {
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = ''
      }
      return () => {
        document.body.style.overflow = ''
      }
    }, [open])

    if (!open) return null

    return (
      <div className="fixed inset-0 z-50 bg-black/50">
        <div
          ref={ref}
          className={cn(drawerVariants({ size, className }))}
          style={{ transform: open ? 'translateX(0)' : 'translateX(100%)' }}
          {...props}
        >
          {children}
        </div>
      </div>
    )
  }
)
Drawer.displayName = "Drawer"

export { Drawer, drawerVariants }
