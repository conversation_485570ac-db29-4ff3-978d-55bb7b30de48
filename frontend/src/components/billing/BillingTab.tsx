import React, { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { FlatTabBar } from '@/components/ui/FlatTabBar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { DownloadIcon, FileTextIcon, FileIcon, Loader2, ArrowUpDown } from 'lucide-react';
import { formatCurrency, formatBillingPeriod, getMockBillingHistory, BillingRecord } from '@/utils/mockBillingData';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import ErrorDisplay from '@/components/common/ErrorDisplay';

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table"

// Billing Overview Tab
const BillingOverview: React.FC = () => {
  const { data: billingData, isLoading, isError } = useQuery({
    queryKey: ['billingHistory'],
    queryFn: () => getMockBillingHistory(),
    staleTime: 60000,
  });

  const [sorting, setSorting] = useState<SortingState>([]);

  const data = useMemo(() => billingData?.results ?? [], [billingData]);

  const columns = useMemo<ColumnDef<BillingRecord>[]>(() => [
    {
      accessorKey: 'billing_period_end',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Billing Period
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => formatBillingPeriod(row.original.billing_period_start, row.original.billing_period_end),
      enableSorting: true,
    },
    {
      accessorKey: 'consumption_kwh',
      header: 'Consumption (kWh)',
      cell: ({ row }) => row.original.consumption_kwh.toLocaleString(),
      enableSorting: false,
    },
    {
      accessorKey: 'rate_per_kwh',
      header: 'Rate (THB/kWh)',
      cell: ({ row }) => row.original.rate_per_kwh.toFixed(2),
      enableSorting: false,
    },
    {
      accessorKey: 'total_amount',
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Total Amount
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => formatCurrency(row.original.total_amount),
      enableSorting: true,
    },
    {
      accessorKey: 'due_date',
      header: 'Due Date',
      cell: ({ row }) => format(new Date(row.original.due_date), 'MMM d, yyyy'),
      enableSorting: false,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: () => (
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <FileIcon className="h-4 w-4" />
          <span className="sr-only">View Bill</span>
        </Button>
      ),
    },
  ], []);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  if (isLoading) return <LoadingSpinner />;
  if (isError) return <ErrorDisplay message="Failed to load billing data" />;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Billing History</CardTitle>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" className="flex items-center gap-1">
              <DownloadIcon className="h-4 w-4" />
              Export CSV
            </Button>
            <Button variant="outline" size="sm" className="flex items-center gap-1">
              <FileTextIcon className="h-4 w-4" />
              Export PDF
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id} style={{ width: header.getSize() !== 150 ? header.getSize() : undefined }}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

// Bill Export Tab
const BillExport: React.FC = () => {
  const { data: billingData, isLoading, isError } = useQuery({
    queryKey: ['billingHistory'],
    queryFn: () => getMockBillingHistory(),
    staleTime: 60000,
  });

  const [selectedBill, setSelectedBill] = useState<number | null>(null);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  if (isLoading) return <LoadingSpinner />;
  if (isError) return <ErrorDisplay message="Failed to load billing data" />;

  const handleGeneratePdf = () => {
    setIsGenerating(true);
    console.log("Generating PDF for bill:", selectedBill);
    setTimeout(() => {
      setIsGenerating(false);
    }, 2000);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Generate Bill PDF</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <label htmlFor="bill-select" className="block text-sm font-medium text-gray-700 mb-1">
                Select Billing Period
              </label>
              <select
                id="bill-select"
                value={selectedBill || ""}
                onChange={(e) => setSelectedBill(Number(e.target.value))}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
              >
                <option value="">Select a billing period</option>
                {billingData?.results.map((bill) => (
                  <option key={bill.id} value={bill.id}>
                    {formatBillingPeriod(bill.billing_period_start, bill.billing_period_end)}
                  </option>
                ))}
              </select>
            </div>

            <div className="pt-4">
              <Button
                disabled={!selectedBill || isGenerating}
                onClick={handleGeneratePdf}
                className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <FileTextIcon className="h-4 w-4" />
                    Generate PDF Bill
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {selectedBill && (
        <Card>
          <CardHeader>
            <CardTitle>Bill Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="border rounded-md p-6 bg-white">
              <div className="flex justify-between mb-6">
                <div>
                  <h3 className="text-lg font-bold">Alto Cero EMS</h3>
                  <p className="text-sm text-gray-500">Electricity Billing Statement</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">Invoice #INV-{selectedBill.toString().padStart(6, '0')}</p>
                  <p className="text-sm text-gray-500">Date: {format(new Date(), 'MMM d, yyyy')}</p>
                </div>
              </div>

              {billingData?.results.filter(bill => bill.id === selectedBill).map((bill) => (
                <div key={bill.id} className="space-y-4">
                  <div className="border-t border-b py-4">
                    <h4 className="font-medium mb-2">Billing Details</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <p className="text-gray-500">Billing Period:</p>
                      <p>{formatBillingPeriod(bill.billing_period_start, bill.billing_period_end)}</p>
                      <p className="text-gray-500">Consumption:</p>
                      <p>{bill.consumption_kwh.toLocaleString()} kWh</p>
                      <p className="text-gray-500">Rate:</p>
                      <p>{bill.rate_per_kwh.toFixed(2)} THB/kWh</p>
                      <p className="text-gray-500">Due Date:</p>
                      <p>{format(new Date(bill.due_date), 'MMM d, yyyy')}</p>
                    </div>
                  </div>

                  <div className="border-b pb-4">
                    <h4 className="font-medium mb-2">Charges</h4>
                    <div className="flex justify-between text-sm">
                      <p>Electricity Charges ({bill.consumption_kwh.toLocaleString()} kWh × {bill.rate_per_kwh.toFixed(2)} THB)</p>
                      <p>{formatCurrency(bill.total_amount)}</p>
                    </div>
                  </div>

                  <div className="flex justify-between font-bold text-lg">
                    <p>Total Amount Due:</p>
                    <p>{formatCurrency(bill.total_amount)}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// Main Billing Tab Component
const BillingTab: React.FC = () => {
  return (
    <div className="p-6">
      <FlatTabBar
        defaultValue="overview"
        tabs={[
          {
            value: 'overview',
            label: 'Billing Overview',
            content: <BillingOverview />
          },
          {
            value: 'bill-export',
            label: 'Bill Export',
            content: <BillExport />
          }
        ]}
      />
    </div>
  );
};

export default BillingTab;
