import React, { useState, useRef, useEffect } from 'react';
import { MoreVertical, Download, ToggleLeft, ToggleRight } from 'lucide-react';
import { ExportButton } from '../common';

interface MoreActionsDropdownProps {
  showCumulativeToggle?: boolean;
  showCumulative?: boolean;
  onCumulativeToggle?: (show: boolean) => void;
  onExportCSV?: () => void;
  onExportPDF?: () => void;
  className?: string;
}

const MoreActionsDropdown: React.FC<MoreActionsDropdownProps> = ({
  showCumulativeToggle = false,
  showCumulative = false,
  onCumulativeToggle,
  onExportCSV,
  onExportPDF,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* More Actions Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:shadow-sm transition-all duration-200"
        aria-label="More actions"
        aria-expanded={isOpen}
      >
        <MoreVertical size={16} />
        <span className="hidden sm:inline">More</span>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 z-50 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 animate-in fade-in-0 zoom-in-95 slide-in-from-top-2 duration-200" style={{ zIndex: 50 }}>
          <div className="py-1">
            {/* Export Options */}
            {onExportCSV && (
              <>
                <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  Export Data
                </div>
                <button
                  onClick={() => {
                    onExportCSV();
                    setIsOpen(false);
                  }}
                  className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  <Download size={16} className="text-gray-400" />
                  Export as CSV
                </button>
              </>
            )}

            {/* Divider */}
            {onExportCSV && showCumulativeToggle && (
              <div className="my-1 border-t border-gray-100" />
            )}

            {/* View Options */}
            {showCumulativeToggle && onCumulativeToggle && (
              <>
                <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                  View Options
                </div>
                <button
                  onClick={() => {
                    onCumulativeToggle(!showCumulative);
                    setIsOpen(false);
                  }}
                  className="w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  {showCumulative ? (
                    <>
                      <ToggleRight size={16} className="text-blue-500" />
                      <span>Standard View</span>
                    </>
                  ) : (
                    <>
                      <ToggleLeft size={16} className="text-gray-400" />
                      <span>Cumulative View</span>
                    </>
                  )}
                </button>
              </>
            )}

          </div>
        </div>
      )}
    </div>
  );
};

export default MoreActionsDropdown;