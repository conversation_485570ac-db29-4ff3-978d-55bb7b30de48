import * as React from 'react';
import { Layers } from 'lucide-react';
import { SYSTEM_TYPES } from '../../lib/config/energy-systems';
import { SYSTEM_ICONS } from '../../lib/constants';
import DropdownSelector, { SelectOption } from '../common/DropdownSelector';

// Define the system type
export type SystemType = keyof typeof SYSTEM_TYPES | 'all';

interface SystemSelectorProps {
  selectedSystem: SystemType;
  onSystemChange: (system: SystemType) => void;
  className?: string;
}

// Helper function to get the correct system icon key
const getSystemIconKey = (systemId: SystemType): keyof typeof SYSTEM_ICONS | null => {
  if (systemId === 'all') return null;
  
  // Map system IDs to their corresponding names in SYSTEM_ICONS
  const mapping: Record<string, keyof typeof SYSTEM_ICONS> = {
    'data_center_others': 'data_center_others',
    'chillerPlant': 'chillerPlant',
    'airSide': 'airSide',
    'light_power': 'light_power',
    'evCharger': 'evCharger',
    'escalator_elevator': 'escalator_elevator'
  };
  
  return mapping[systemId] || null;
};

const SystemSelector: React.FC<SystemSelectorProps> = ({
  selectedSystem,
  onSystemChange,
  className = ''
}) => {
  // Create options for the dropdown
  const options: SelectOption<SystemType>[] = [
    { 
      id: 'all', 
      name: 'All Systems', 
      color: '#6B7280',
      icon: <Layers size={16} color="#6B7280" />
    },
    ...Object.entries(SYSTEM_TYPES).map(([id, system]) => {
      const systemId = id as SystemType;
      const iconKey = getSystemIconKey(systemId);
      const IconComponent = iconKey ? SYSTEM_ICONS[iconKey] : Layers;
      
      return {
        id: systemId,
        name: system.name,
        color: system.color,
        icon: <IconComponent size={16} color={system.color} />
      };
    })
  ];

  return (
    <DropdownSelector
      options={options}
      selectedValue={selectedSystem}
      onChange={onSystemChange}
      className={className}
      width="200px"
      buttonClassName="w-full"
      label="Select System"
    />
  );
};

export default SystemSelector;
