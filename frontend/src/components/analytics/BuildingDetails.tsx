import React from 'react';
import { Sun, Zap, Droplets } from 'lucide-react';
import { BuildingVisualization3D } from '../overview/BuildingVisualization3D';
import { generateMockFloorData } from '../../lib/config/dashboard';
import type { BuildingId } from '../../types';

interface BuildingDetailsProps {
  buildingId: BuildingId;
}

export function BuildingDetails({ buildingId }: BuildingDetailsProps) {
  const [selectedFloor, setSelectedFloor] = React.useState<number | undefined>();
  const floorData = React.useMemo(() => generateMockFloorData(28), []);

  return (
    <div className="mb-4 h-[400px] bg-gradient-to-br from-white to-blue-50/20 rounded-xl border border-[#EDEFF9] shadow-[0_8px_16px_-4px_rgba(14,125,228,0.08)] p-4 relative">
      {/* Building Title */}
      <div className="absolute top-4 left-4 z-10">
        <h2 className="text-lg font-semibold text-transparent bg-clip-text bg-gradient-to-r from-primary-blue to-blue-600">
          Tower {buildingId}
        </h2>
        <p className="text-sm text-gray-500 mt-1">
          {buildingId === 'B' ? 'Podium Building' : buildingId === 'C' ? 'Car Park Building' : 'Office Tower'}
        </p>
      </div>

      <BuildingVisualization3D
        selectedFloor={selectedFloor}
        floorData={floorData}
        buildingId={buildingId}
        onFloorSelect={setSelectedFloor}
      />

      {/* Quick Stats */}
      <div className="absolute top-4 right-4 flex items-center gap-4">
        <div className="p-3 bg-gradient-to-br from-blue-50/60 via-blue-50/40 to-white rounded-lg border border-blue-100/80">
          <div className="flex items-center gap-2">
            <Sun size={16} className="text-amber-500" />
            <div>
              <div className="text-xs text-gray-500">Solar Generation</div>
              <div className="text-sm font-medium text-primary-blue">19,067 kWh (8%)</div>
            </div>
          </div>
        </div>
        <div className="p-3 bg-gradient-to-br from-blue-50/60 via-blue-50/40 to-white rounded-lg border border-blue-100/80">
          <div className="flex items-center gap-2">
            <Zap size={16} className="text-primary-blue" />
            <div>
              <div className="text-xs text-gray-500">Grid Consumption</div>
              <div className="text-sm font-medium text-primary-blue">219,271 kWh (92%)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}