import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend,
  ResponsiveContainer,
  BarChart,
  Bar,
  TooltipProps
} from 'recharts';
import { PerformanceDataItem, ViewType } from '../../types';
import { Info } from 'lucide-react';

interface PerformanceChartProps {
  data: PerformanceDataItem[] | null;
  isLoading: boolean;
  view: ViewType; // Needed for tooltip label
}

// Define colors (could centralize these later)
const COLORS = {
  actual: '#2563EB',
  baseline: '#94A3B8',
  target: '#10B981',
};

// Simple InfoTooltip (copied from MetricsDisplay)
const InfoTooltip = ({ content }: { content: string }) => (
  <div className="group relative inline-block">
    <Info size={12} className="text-gray-400 cursor-help" />
    <div className="opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity duration-200 absolute z-10 bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-52 p-2 bg-white rounded-lg shadow-lg border border-gray-200 text-xs text-gray-700">
      {content}
      <div className="absolute left-1/2 -bottom-2 transform -translate-x-1/2 w-3 h-3 rotate-45 bg-white border-r border-b border-gray-200"></div>
    </div>
  </div>
);

// Custom Legend
const CustomLegend = React.memo((props: any) => {
  const { payload } = props;
  return (
    <div className="flex flex-wrap justify-center gap-6 mt-3 mb-4 pt-3">
      {payload.map((entry: any, index: number) => (
        <div key={`legend-${index}`} className="flex items-center gap-2">
          <div 
            className="w-3 h-3 rounded-sm flex items-center justify-center border border-gray-300"
            style={{ backgroundColor: entry.color }}
          >
          </div>
          <span className="text-xs font-medium text-gray-700">{entry.value}</span>
          <InfoTooltip content={
            entry.value === 'Actual' ? "Actual measured consumption."
            : entry.value === 'Baseline' ? "Historical consumption reference."
            : entry.value === 'Target' ? "Efficiency goal."
            : ""
          } />
        </div>
      ))}
    </div>
  );
});

// Custom Tooltip
const CustomChartTooltip = React.memo(({ active, payload, label, view }: TooltipProps<number, string> & { view: ViewType }) => {
  if (active && payload && payload.length) {
    const baselineValue = payload.find(p => p.dataKey === 'baseline')?.value ?? 0;
    const targetValue = payload.find(p => p.dataKey === 'target')?.value ?? 0;
    const actualValue = payload.find(p => p.dataKey === 'actual')?.value ?? 0;
    
    const vsBaseline = actualValue - baselineValue;
    const vsBaselinePercent = baselineValue !== 0 ? ((vsBaseline / baselineValue) * 100).toFixed(1) : 'N/A';
    const vsTarget = actualValue - targetValue;
    const vsTargetPercent = targetValue !== 0 ? ((vsTarget / targetValue) * 100).toFixed(1) : 'N/A';

    return (
      <div className="p-3 bg-white border border-gray-200 rounded-lg shadow-md text-sm">
        <p className="font-semibold text-gray-800 mb-2 border-b pb-1">
          {view === 'yearly' || view === 'monthly' ? `Period: ${label}` : `Year: ${label}`}
        </p>
        <div className="space-y-1.5">
          {payload.map((pld) => (
            <div key={pld.dataKey} className="flex items-center justify-between gap-3">
              <div className="flex items-center gap-1.5">
                <div className="w-2.5 h-2.5 rounded-sm" style={{ backgroundColor: pld.color }}></div>
                <span className="text-xs font-medium text-gray-600">{pld.name}</span>
              </div>
              <span className="text-xs font-semibold text-gray-800">{(pld.value ?? 0).toLocaleString()} kWh</span>
            </div>
          ))}
        </div>
        {/* Comparison insights */}
        <div className="mt-2 pt-2 border-t border-gray-100 text-xs">
          <div className={`flex items-center gap-1 ${vsBaseline < 0 ? 'text-green-600' : vsBaseline > 0 ? 'text-red-600' : 'text-gray-500'}`}>
            <span>{vsBaseline < 0 ? '↓' : vsBaseline > 0 ? '↑' : '-'}</span>
            <span className="font-medium">{Math.abs(vsBaseline).toLocaleString()} kWh ({vsBaselinePercent}%)</span>
            <span>vs Baseline</span>
          </div>
          <div className={`flex items-center gap-1 ${vsTarget < 0 ? 'text-green-600' : vsTarget > 0 ? 'text-red-600' : 'text-gray-500'}`}>
            <span>{vsTarget < 0 ? '↓' : vsTarget > 0 ? '↑' : '-'}</span>
            <span className="font-medium">{Math.abs(vsTarget).toLocaleString()} kWh ({vsTargetPercent}%)</span>
            <span>vs Target</span>
          </div>
        </div>
      </div>
    );
  }
  return null;
});

// Main Chart Component
export const PerformanceChart: React.FC<PerformanceChartProps> = ({ data, isLoading, view }) => {
  const chartTitle = 
    view === 'yearly' ? 'Monthly Energy Performance' : 
    view === 'multi-year' ? 'Annual Energy Performance' : 
    'Energy Performance';

  if (isLoading) {
    return (
      <div className="h-80 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="h-80 flex items-center justify-center text-gray-500">
        <div>No performance data available for this period.</div>
      </div>
    );
  }

  // Determine Y-axis label based on typical scale
  const yAxisLabel = view === 'multi-year' ? 'GWh' : 'kWh';
  const tickFormatter = (value: number) => 
    yAxisLabel === 'GWh' ? value.toFixed(1) : value.toLocaleString();

  return (
    <>
      <h3 className="text-base font-semibold mb-2 text-gray-800 text-center">{chartTitle}</h3>
      <p className="text-sm text-gray-600 text-center mb-4">
        Comparing Actual vs Baseline vs Target energy consumption.
      </p>
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 10, right: 10, left: 10, bottom: 30 }}
            barGap={5}
            barCategoryGap="15%"
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" vertical={false} />
            <XAxis 
              dataKey="time" 
              axisLine={false}
              tickLine={false}
              tick={{ fill: '#6B7280', fontSize: 11 }}
              dy={10}
            />
            <YAxis 
              axisLine={false}
              tickLine={false}
              tick={{ fill: '#6B7280', fontSize: 11 }}
              width={60}
              tickFormatter={tickFormatter}
              label={{ 
                value: yAxisLabel, 
                angle: -90, 
                position: 'insideLeft',
                fill: '#6B7280',
                fontSize: 12,
                dy: 40 // Adjust label position
              }}
            />
            <Tooltip content={<CustomChartTooltip view={view} />} cursor={{ fill: '#f1f5f980' }} />
            <Legend content={<CustomLegend />} verticalAlign="top" height={40}/>
            
            <Bar dataKey="baseline" name="Baseline" fill={COLORS.baseline} radius={[2, 2, 0, 0]} maxBarSize={25} />
            <Bar dataKey="target" name="Target" fill={COLORS.target} radius={[2, 2, 0, 0]} maxBarSize={25} />
            <Bar dataKey="actual" name="Actual" fill={COLORS.actual} radius={[4, 4, 0, 0]} maxBarSize={25} />

          </BarChart>
        </ResponsiveContainer>
      </div>
    </>
  );
}; 