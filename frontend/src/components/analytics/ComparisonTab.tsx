import React, { useMemo, useState } from 'react';
import {
  <PERSON>Chart,
  Bar,
  LineChart,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  Brush
} from 'recharts';
import { ViewType } from '../../types/analytics';
import { formatAxisTick, formatNumber } from '../../utils/formatting';
import { generateMockChartData } from '../../utils/mockDataGenerator';
import ExportButton from '../common/ExportButton';
import ChartTypeToggle, { ChartDataType } from '../common/ChartTypeToggle';
import { CHART_STYLES, COLORS } from '../../constants/chartStyles';
import { formatDateForTitle, formatWeekRangeForTitle, formatMonthForTitle, formatYearForTitle } from '../../utils/dateFormatters';
import { getXAxisConfig, ensureAllHoursForDayView } from '../../lib/config/chart-axes';

// Define ComparisonPeriod type locally if not in analytics.ts
type ComparisonPeriod = 'none' | 'day' | 'week' | 'month' | 'year' | 'custom';

interface ComparisonTabProps {
  chartData: any;
  selectedView: ViewType;
  comparisonPeriod: ComparisonPeriod;
  customComparisonStartDate?: Date | null;
  customComparisonEndDate?: Date | null;
  onCustomRangeChange?: (startDate: Date | null, endDate: Date | null) => void;
  selectedDate?: Date;
  selectedSystem?: string;
  systemName?: string;
}

const ComparisonTab: React.FC<ComparisonTabProps> & {
  mockDataCache: Record<string, any>;
  transformCache: Record<string, any>;
} = ({
  chartData,
  selectedView,
  comparisonPeriod,
  customComparisonStartDate,
  customComparisonEndDate,
  onCustomRangeChange,
  selectedDate = new Date(),
  selectedSystem = 'all',
  systemName = 'All Systems'
}) => {
  // State for year range in multi-year view
  const [yearRange, setYearRange] = useState<{ start: number, end: number }>({ start: 2021, end: 2025 });

  // Chart type state
  const [chartType, setChartType] = useState<'energy' | 'power'>('energy');

  // Helper function to check chart type safely
  const isEnergyChart = () => chartType === 'energy';
  const isPowerChart = () => chartType === 'power';

  // Function to generate mock data for the multi-year view with caching
  const generateMockMultiYearData = () => {
    const cacheKey = `multi-year-${yearRange.start}-${yearRange.end}-${chartType}`;
    if (ComparisonTab.mockDataCache[cacheKey]) {
      return ComparisonTab.mockDataCache[cacheKey];
    }
    
    // Create custom multi-year data with both energy and power values
    const mockPoints = [];
    for (let year = yearRange.start; year <= yearRange.end; year++) {
      // Base values that increase each year
      const baseEnergy = 120000 + ((year - yearRange.start) * 20000);
      const basePower = 12000 + ((year - yearRange.start) * 2000);
      
      // Add some randomness
      const randomEnergy = Math.floor(Math.random() * 30000);
      const randomPower = Math.floor(Math.random() * 3000);
      
      mockPoints.push({
        time: `${year}`,
        fullDate: `${year}-01-01`,
        consumption: baseEnergy + randomEnergy,
        power: basePower + randomPower,
        // Add the value property that matches the current chart type
        value: isPowerChart() ? (basePower + randomPower) : (baseEnergy + randomEnergy)
      });
    }
    
    // Transform the data to match the format expected by the comparison chart
    const result = { 
      current: mockPoints,
      comparison: [] 
    };
    
    ComparisonTab.mockDataCache[cacheKey] = result;
    return result;
  };

  // Function to generate mock data for the selected view with power or energy variations
  const mockData = useMemo(() => {
    // Create a cache key based on the view and chart type
    const cacheKey = `${selectedView}-${chartType}`;

    // Return cached data if available
    if (ComparisonTab.mockDataCache[cacheKey]) {
      return ComparisonTab.mockDataCache[cacheKey];
    }

    // Generate data with appropriate data type (energy or power)
    const rawData = generateMockChartData(selectedView, { isPower: isPowerChart() });
    
    // Ensure the data has all required properties
    const data = {
      current: rawData.current.map((item: any) => ({
        ...item,
        value: item.consumption || item.value || 0,
        power: isPowerChart() ? (item.consumption || item.value || 0) : 0,
        consumption: !isPowerChart() ? (item.consumption || item.value || 0) : 0,
        fullDate: item.fullDate || item.time
      })),
      comparison: rawData.comparison.map((item: any) => ({
        ...item,
        value: item.consumption || item.value || 0,
        power: isPowerChart() ? (item.consumption || item.value || 0) : 0,
        consumption: !isPowerChart() ? (item.consumption || item.value || 0) : 0,
        fullDate: item.fullDate || item.time
      }))
    };

    // Cache the result
    ComparisonTab.mockDataCache[cacheKey] = data;

    return data;
  }, [selectedView, chartType]);



  // Use real data or generate mock data if needed
  const dataToUse = useMemo(() => {
    // Check if we have actual data (not just empty arrays)
    // For comparison tab, we need BOTH current and comparison data
    const hasValidCurrentData = chartData?.current && chartData.current.length > 0;
    const hasValidComparisonData = chartData?.comparison && chartData.comparison.length > 0;
    
    // Only use real data if we have both current AND comparison data
    if (hasValidCurrentData && hasValidComparisonData) {
      return chartData;
    }

    // Otherwise, use mock data
    if (selectedView === 'multi-year') {
      return generateMockMultiYearData();
    }
    
    // For other views, use the mock data generator
    const generatedMockData = mockData;
    
    // Validate mock data before using it
    if (!generatedMockData || 
        !generatedMockData.current || 
        generatedMockData.current.length === 0 ||
        !generatedMockData.comparison ||
        generatedMockData.comparison.length === 0) {
      // If mock data generation failed, return empty arrays
      return { current: [], comparison: [] };
    }
    
    return generatedMockData;
  }, [chartData, selectedView, yearRange, comparisonPeriod, mockData]); // Keep dependencies for consistency

  // Process data for the chart using dataToUse
  const processData = (
    filteredData: any[],
    filteredComparisonData: any[]
  ): {
    transformedData: any[],
    metrics: {
      currentTotal: number,
      previousTotal: number,
      percentChange: string,
      isIncrease: boolean
    },
    previousLabel: string
  } => {
    if (selectedView === 'multi-year') {
      // For multi-year view with single bars, just use current data directly
      // Transform to chart format directly (no need for complex aggregation)
      const transformed = filteredData.map((item: any) => {
        // Select the appropriate value based on chartType
        const itemValue = isPowerChart() 
          ? (item.power || item.value || 0)
          : (item.consumption || item.value || 0);
          
        return {
          time: item.time,
          displayTime: formatDisplayDate(new Date(item.time), selectedView),
          yearLabel: item.time.split('-')[0],
          value: itemValue,
          // Include these for chart compatibility even though we only show value
          primaryValue: 0,
          comparisonValue: 0
        };
      });

      // Calculate metrics (simplified for single bar)
      const currentTotal = filteredData.reduce((sum: number, point: any) =>
        sum + (isPowerChart() ? (point.power || point.value || 0) : (point.consumption || point.value || 0)), 0);

      const calculatedMetrics = {
        currentTotal,
        previousTotal: 0,
        percentChange: '0.0',
        isIncrease: false
      };

      return {
        transformedData: transformed,
        metrics: calculatedMetrics,
        previousLabel: ''
      };
    }

    // For other views, proceed with normal comparison logic

    // Create paired data points for comparison
    const transformed = filteredData.map((currentPoint: any, index: number) => {
      // Get the corresponding comparison point
      const comparisonPoint = filteredComparisonData[index] || { consumption: 0, value: 0 };

      // For date formatting - tooltips and display values
      let displayDate = currentPoint.time;
      try {
        // Format date
        if (currentPoint.fullDate) {
          // If we have a full date, use it
          const date = new Date(currentPoint.fullDate);
          displayDate = formatDisplayDate(date, selectedView);
        }
      } catch (e) {
        console.error("Error formatting date", e);
      }

      // For multi-year view, extract and use the year as a label
      let yearLabel = '';
      if ((selectedView as ViewType) === 'multi-year') {
        yearLabel = currentPoint.time;
      } else if (typeof currentPoint.time === 'string') {
        if (currentPoint.time.includes('-')) {
          yearLabel = currentPoint.time.split('-')[0];
        } else {
          // Handle time formats like "00:00", "Jan", etc.
          yearLabel = new Date().getFullYear().toString();
        }
      }

      // Calculate percentage change for tooltips
      const currentValue = isPowerChart() ? (currentPoint.power || currentPoint.value || 0) : (currentPoint.consumption || currentPoint.value || 0);
      const comparisonValue = isPowerChart() ? (comparisonPoint.power || comparisonPoint.value || 0) : (comparisonPoint.consumption || comparisonPoint.value || 0);
      const percentChange = comparisonValue ? ((currentValue - comparisonValue) / comparisonValue) * 100 : 0;

      return {
        // Keep the original time value for the X-axis tick formatter
        time: currentPoint.time,
        // Add a formatted display time for tooltips
        displayTime: displayDate,
        // Add year label for multi-year view
        yearLabel,
        // Keep data mapping correct - colors should match the data
        primaryValue: currentValue,        // Current data (should be blue)
        comparisonValue: comparisonValue,  // Previous data (should be gray)
        // Add percentage change for tooltips
        percentChange: percentChange.toFixed(1)
      };
    });

    // Calculate metrics
    const currentTotal = filteredData.reduce((sum: number, point: any) =>
      sum + (isPowerChart() ? (point.power || point.value || 0) : (point.consumption || point.value || 0)), 0);
    const previousTotal = filteredComparisonData.reduce((sum: number, point: any) =>
      sum + (isPowerChart() ? (point.power || point.value || 0) : (point.consumption || point.value || 0)), 0);
    const rawChange = previousTotal ? ((currentTotal - previousTotal) / previousTotal) * 100 : 0;

    const calculatedMetrics = {
      currentTotal,
      previousTotal,
      percentChange: Math.abs(rawChange).toFixed(1),
      isIncrease: rawChange > 0
    };

    // For week view, ensure days are in proper order
    if (selectedView === 'week' && transformed.length > 0) {
      // Hard-coded day order mapping
      const getDayOrder = (dayName: string): number => {
        switch (dayName) {
          case 'Mon': return 0;
          case 'Tue': return 1;
          case 'Wed': return 2;
          case 'Thu': return 3;
          case 'Fri': return 4;
          case 'Sat': return 5;
          case 'Sun': return 6;
          default: return 999; // Unknown days go last
        }
      };

      // Sort by day name
      transformed.sort((a: any, b: any) => {
        return getDayOrder(a.time) - getDayOrder(b.time);
      });
    }
    // For month view, sort by day number
    else if (selectedView === 'month' && transformed.length > 0) {
      transformed.sort((a: any, b: any) => {
        // Try to parse as integers first
        const dayA = parseInt(a.time, 10);
        const dayB = parseInt(b.time, 10);
        
        if (!isNaN(dayA) && !isNaN(dayB)) {
          return dayA - dayB;
        }
        
        // If both are date strings (YYYY-MM-DD), extract the day part
        if (typeof a.time === 'string' && a.time.includes('-') && 
            typeof b.time === 'string' && b.time.includes('-')) {
          try {
            const dateA = new Date(a.time);
            const dateB = new Date(b.time);
            return dateA.getDate() - dateB.getDate();
          } catch (e) {
            // Fall back to string comparison if date parsing fails
            console.error('Error parsing dates for sorting:', e);
          }
        }
        
        // Fallback to string comparison if not numeric or date
        return String(a.time).localeCompare(String(b.time));
      });
    }
    // For year view, sort by month name
    else if (selectedView === 'year' && transformed.length > 0) {
      // Month order mapping for year view
      const getMonthOrder = (monthName: string): number => {
        const months: Record<string, number> = {
          'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
          'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
        };
        return months[monthName] ?? 999;
      };
      
      // Sort by month name
      transformed.sort((a: any, b: any) => {
        return getMonthOrder(a.time) - getMonthOrder(b.time);
      });
    }

    return {
      transformedData: transformed,
      metrics: calculatedMetrics,
      previousLabel: getPreviousPeriodLabel(comparisonPeriod)
    };
  };

  // Helper function to format display dates based on view type
  const formatDisplayDate = (date: Date, view: ViewType): string => {
    try {
      // Options for Bangkok timezone (UTC+7)
      const options: Intl.DateTimeFormatOptions = { timeZone: 'Asia/Bangkok' };

      switch (view) {
        case 'day':
          return date.toLocaleTimeString('th-TH', { ...options, hour: '2-digit', minute: '2-digit' });
        case 'week':
          return date.toLocaleDateString('en-US', { ...options, weekday: 'short', month: 'short', day: 'numeric' });
        case 'month':
          // Just return the day number for month view
          return date.getDate().toString();
        case 'year':
          return date.toLocaleDateString('en-US', { ...options, month: 'short' });
        case 'multi-year':
          // Use en-US locale for year display to show numeric years instead of Thai calendar years
          return date.getFullYear().toString();
        default:
          return date.toLocaleDateString('th-TH', options);
      }
    } catch (e) {
      console.error("Error formatting date", e);
      return '';
    }
  };

  // Helper function to format X-axis ticks
  const formatXAxisTick = (value: string): string => {
    // Timezone options for Bangkok (UTC+7)
    const options: Intl.DateTimeFormatOptions = { timeZone: 'Asia/Bangkok' };

    try {
      // For week view, handle both date strings and direct day names
      if (selectedView === 'week' && typeof value === 'string') {
        // If it's already a day name (Mon, Tue, etc.), return it directly
        if (['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].includes(value)) {
          return value;
        }
      }

      if (typeof value === 'string' && value.includes('-')) {
        const date = new Date(value);

        if (selectedView === 'day') {
          // For day view, show only full hours (XX:00) for better readability
          const hours = date.getHours();
          const minutes = date.getMinutes();

          // Only show labels for full hours (XX:00)
          if (minutes === 0) {
            return date.toLocaleTimeString('en-US', {
              ...options,
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            });
          } else {
            // Return empty string for non-full hours to hide them
            return '';
          }
        } else if (selectedView === 'week') {
          return date.toLocaleDateString('en-US', { ...options, weekday: 'short' });
        } else if (selectedView === 'month') {
          // Just return the day number for month view
          const day = date.getDate();
          // Only show every other day for better readability in month view
          return day % 2 === 0 ? day.toString() : '';
        } else if (selectedView === 'year') {
          return date.toLocaleDateString('en-US', { ...options, month: 'short' });
        } else if (selectedView === 'multi-year') {
          // Use en-US locale for year display to show numeric years instead of Thai calendar years
          return date.getFullYear().toString();
        }
      }
    } catch (e) {
      console.error("Error formatting X-axis tick", e);
    }

    return String(value);
  };

  // Function to get the previous period label based on comparison period
  const getPreviousPeriodLabel = (period: ComparisonPeriod): string => {
    // Since we always use 'week' comparison period, return appropriate label based on view
    switch (selectedView) {
      case 'day':
        return 'Yesterday';
      case 'week':
        return 'Last Week';
      case 'month':
        return 'Last Month';
      case 'year':
        return 'Last Year';
      case 'multi-year':
        return 'Previous Years';
      default:
        return 'Previous Period';
    }
  };

  // Transform the data for comparison charts
  const transformData = (data: any): any[] => {
    if (!data || !data.current || !data.comparison) return [];

    // Create a cache key for the transformed data
    const cacheKey = `${selectedView}-${chartType}-${comparisonPeriod}`;

    // Check if we already have this transformation cached
    if (ComparisonTab.transformCache?.[cacheKey]) {
      return ComparisonTab.transformCache[cacheKey];
    }

    // For month view, aggregate data by week to reduce crowding
    if (selectedView === 'month' && false) { // Temporarily skip week aggregation
      // Group data by week
      const weeklyCurrentData: Record<string, any> = {};
      const weeklyComparisonData: Record<string, any> = {};
      
      // Process current period data
      data.current.forEach((item: any) => {
        let date: Date;
        
        // Try different date parsing strategies
        if (item.fullDate) {
          date = new Date(item.fullDate);
        } else if (typeof item.time === 'string') {
          // Check if it's already a date string (YYYY-MM-DD or similar)
          if (item.time.includes('-') || item.time.includes('/')) {
            date = new Date(item.time);
          } else if (!isNaN(parseInt(item.time, 10))) {
            // If time is a day number string like "1", "2", etc.
            const day = parseInt(item.time, 10);
            if (day >= 1 && day <= 31) {
              // For month view, assume current month/year
              const now = new Date();
              date = new Date(now.getFullYear(), now.getMonth(), day);
            } else {
              return; // Skip invalid day numbers
            }
          } else {
            return; // Skip items we can't parse
          }
        } else {
          return; // Skip items without time data
        }
        
        // Validate the date
        if (isNaN(date.getTime())) {
          return; // Skip invalid dates
        }
        
        // Get week number (1-5) within the month
        const dayOfMonth = date.getDate();
        const weekOfMonth = Math.ceil(dayOfMonth / 7);
        
        // Validate week number
        if (isNaN(weekOfMonth) || weekOfMonth < 1 || weekOfMonth > 5) {
          return;
        }
        
        const weekKey = `Week ${weekOfMonth}`;
        
        if (!weeklyCurrentData[weekKey]) {
          weeklyCurrentData[weekKey] = {
            time: weekKey,
            primaryValue: 0,
            count: 0
          };
        }
        
        weeklyCurrentData[weekKey].primaryValue += isPowerChart() ? (item.power || item.value || 0) : (item.consumption || item.value || 0);
        weeklyCurrentData[weekKey].count += 1;
      });
      
      // Process comparison period data
      data.comparison.forEach((item: any) => {
        let date: Date;
        
        // Try different date parsing strategies
        if (item.fullDate) {
          date = new Date(item.fullDate);
        } else if (typeof item.time === 'string') {
          // Check if it's already a date string (YYYY-MM-DD or similar)
          if (item.time.includes('-') || item.time.includes('/')) {
            date = new Date(item.time);
          } else if (!isNaN(parseInt(item.time, 10))) {
            // If time is a day number string like "1", "2", etc.
            const day = parseInt(item.time, 10);
            if (day >= 1 && day <= 31) {
              // For month view, assume current month/year
              const now = new Date();
              date = new Date(now.getFullYear(), now.getMonth(), day);
            } else {
              return; // Skip invalid day numbers
            }
          } else {
            return; // Skip items we can't parse
          }
        } else {
          return; // Skip items without time data
        }
        
        // Validate the date
        if (isNaN(date.getTime())) {
          return; // Skip invalid dates
        }
        
        // Get week number (1-5) within the month
        const dayOfMonth = date.getDate();
        const weekOfMonth = Math.ceil(dayOfMonth / 7);
        
        // Validate week number
        if (isNaN(weekOfMonth) || weekOfMonth < 1 || weekOfMonth > 5) {
          return;
        }
        
        const weekKey = `Week ${weekOfMonth}`;
        
        if (!weeklyComparisonData[weekKey]) {
          weeklyComparisonData[weekKey] = {
            time: weekKey,
            comparisonValue: 0,
            count: 0
          };
        }
        
        weeklyComparisonData[weekKey].comparisonValue += isPowerChart() ? (item.power || item.value || 0) : (item.consumption || item.value || 0);
        weeklyComparisonData[weekKey].count += 1;
      });
      
      // Combine the data
      const result: any[] = [];
      const allWeeks = new Set([...Object.keys(weeklyCurrentData), ...Object.keys(weeklyComparisonData)]);
      
      allWeeks.forEach(weekKey => {
        const current = weeklyCurrentData[weekKey];
        const comparison = weeklyComparisonData[weekKey];
        
        result.push({
          time: weekKey,
          primaryValue: current ? current.primaryValue : 0,
          comparisonValue: comparison ? comparison.comparisonValue : 0
        });
      });
      
      // Sort by week number
      result.sort((a, b) => {
        const weekA = parseInt(a.time.split(' ')[1]);
        const weekB = parseInt(b.time.split(' ')[1]);
        return weekA - weekB;
      });
      
      // Cache the result
      ComparisonTab.transformCache[cacheKey] = result;
      return result;
    }

    // For other views, use the original transformation logic
    const { transformedData, metrics, previousLabel } = processData(data.current, data.comparison);
    return transformedData;
  };

  const { transformedData, metrics, previousLabel } = useMemo(() => {
    // Create data sources
    let currentData = dataToUse?.current || [];
    let comparisonData = dataToUse?.comparison || [];


    // For day view, ensure all 24 hours are present
    if (selectedView === 'day') {
      currentData = ensureAllHoursForDayView(currentData, selectedView);
      comparisonData = ensureAllHoursForDayView(comparisonData, selectedView);
    }

    // Transform data for the chart
    let filteredData = [...currentData];
    let filteredComparisonData = [...comparisonData];

    const transformed = transformData({ current: filteredData, comparison: filteredComparisonData });

    return {
      transformedData: transformed,
      metrics: processData(filteredData, filteredComparisonData).metrics,
      previousLabel: getPreviousPeriodLabel(comparisonPeriod)
    };
  }, [dataToUse, selectedView, comparisonPeriod, yearRange]);

  // We're now using Recharts' automatic scaling instead of custom grid configuration

  // No more mock data usage
  const isUsingMockData = false;

  // Function to export data as CSV
  const exportDataAsCSV = () => {
    if (!transformedData || transformedData.length === 0) return;

    // Create CSV content
    const currentPeriodLabel = selectedView === 'day' ? 'Today' :
                               selectedView === 'week' ? 'This Week' :
                               selectedView === 'month' ? 'This Month' :
                               selectedView === 'year' ? 'This Year' :
                               selectedView === 'multi-year' ? 'Recent Years' :
                               'Current Period';
    const headers = `Time,${currentPeriodLabel} (${isEnergyChart() ? 'kWh' : 'kW'}),${previousLabel} (${isEnergyChart() ? 'kWh' : 'kW'})`;
    const rows = transformedData.map((point: any) =>
      `"${formatAxisTick(point.time, selectedView)}",${formatNumber(point.primaryValue)},${formatNumber(point.comparisonValue)}`
    );
    const csvContent = [headers, ...rows].join('\n');

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `energy-comparison-${selectedView}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Function to export data as PDF
  const exportDataAsPDF = () => {
    // In a real application, this would use a PDF generation library
    alert('PDF export functionality would be implemented with a library like jsPDF');
  };

  // X-axis labels removed as they're redundant with the chart title

  // Function to generate a more descriptive chart title based on view type
  const getChartTitle = () => {
    // Base energy/power label
    const energyLabel = isEnergyChart() ? 'Energy' : 'Power';
    const unitLabel = isEnergyChart() ? '(kWh)' : '(kW)';

    // System prefix - make it more prominent
    const systemLabel = selectedSystem !== 'all' ? `${systemName}` : 'All Systems';

    // Time period label
    let periodLabel = '';
    switch (selectedView) {
      case 'multi-year':
        periodLabel = `${yearRange.start}-${yearRange.end}`;
        break;
      case 'year':
        periodLabel = formatYearForTitle(selectedDate);
        break;
      case 'month':
        periodLabel = formatMonthForTitle(selectedDate);
        break;
      case 'week':
        periodLabel = formatWeekRangeForTitle(selectedDate);
        break;
      case 'day':
        periodLabel = formatDateForTitle(selectedDate);
        break;
    }

    // Frequency label
    const frequencyLabel = selectedView === 'day' ? 'Hourly' :
                          selectedView === 'week' || selectedView === 'month' ? 'Daily' :
                          selectedView === 'year' ? 'Monthly' :
                          selectedView === 'multi-year' ? 'Annual' : '';

    return `${systemLabel} | ${frequencyLabel} ${energyLabel} ${unitLabel} - ${periodLabel}`;
  };

  return (
    <div className="space-y-4 p-4 overflow-x-auto">
      {/* Custom Date Range Selector (only shown when comparisonPeriod is 'custom') */}
      {comparisonPeriod === 'custom' && onCustomRangeChange && (
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <span className="text-sm font-medium text-gray-700">Custom Comparison Range:</span>
          <div className="flex gap-2 items-center">
            <input
              type="date"
              className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              value={customComparisonStartDate ? customComparisonStartDate.toISOString().split('T')[0] : ''}
              onChange={(e) => {
                const date = e.target.value ? new Date(e.target.value) : null;
                if (onCustomRangeChange) {
                  onCustomRangeChange(date, customComparisonEndDate || null);
                }
              }}
            />
            <span>to</span>
            <input
              type="date"
              className="border border-gray-300 rounded-md px-3 py-1 text-sm"
              value={customComparisonEndDate ? customComparisonEndDate.toISOString().split('T')[0] : ''}
              onChange={(e) => {
                const date = e.target.value ? new Date(e.target.value) : null;
                if (onCustomRangeChange) {
                  onCustomRangeChange(customComparisonStartDate || null, date);
                }
              }}
            />
          </div>
        </div>
      )}

      {/* Metrics Cards - Redesigned for cohesive style */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Current Period Card */}
        <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-100 rounded-lg p-5 flex flex-col justify-between hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
            <h3 className="text-sm font-medium text-gray-700">{
              selectedView === 'day' ? 'Today' :
              selectedView === 'week' ? 'This Week' :
              selectedView === 'month' ? 'This Month' :
              selectedView === 'year' ? 'This Year' :
              selectedView === 'multi-year' ? 'Recent Years' :
              'Current Period'
            }</h3>
          </div>
          <div className="flex items-baseline gap-1">
            <p className="text-2xl font-semibold text-gray-900">
              {formatNumber(metrics.currentTotal)}
            </p>
            <span className="text-sm text-gray-500">{isEnergyChart() ? 'kWh' : 'kW'}</span>
          </div>
        </div>

        {/* Previous Period Card */}
        <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-100 rounded-lg p-5 flex flex-col justify-between hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-2 h-2 rounded-full bg-gray-400"></div>
            <h3 className="text-sm font-medium text-gray-700">{previousLabel || 'Comparison Period'}</h3>
          </div>
          <div className="flex items-baseline gap-1">
            {metrics.previousTotal > 0 ? (
              <>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatNumber(metrics.previousTotal)}
                </p>
                <span className="text-sm text-gray-500">{isEnergyChart() ? 'kWh' : 'kW'}</span>
              </>
            ) : (
              <p className="text-xl text-gray-400">—</p>
            )}
          </div>
        </div>

        {/* Change Card */}
        <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-100 rounded-lg p-5 flex flex-col justify-between hover:shadow-md transition-shadow duration-300">
          <div className="flex items-center gap-2 mb-3">
            <div className={`w-2 h-2 rounded-full ${metrics.previousTotal > 0 && metrics.isIncrease ? 'bg-red-500' : 'bg-green-500'}`}></div>
            <h3 className="text-sm font-medium text-gray-700">Change</h3>
          </div>
          <div className="flex items-baseline gap-1">
            {metrics.previousTotal > 0 ? (
              <>
                <p className={`text-2xl font-semibold ${metrics.isIncrease ? 'text-red-600' : 'text-green-600'}`}>
                  {metrics.isIncrease ? '+' : '-'}{parseFloat(metrics.percentChange) === 0 ? '0.0' : metrics.percentChange}%
                </p>
                <span className="text-xs text-gray-500 ml-1">{previousLabel}</span>
              </>
            ) : (
              <p className="text-xl text-gray-400">—</p>
            )}
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-100 rounded-lg p-6 mt-4 hover:shadow-md transition-shadow duration-300">
        {/* Chart Header */}
        <div className="flex flex-col mb-4">
          <div className="flex justify-between items-center mb-2">
            <div>
              <h3 className="text-base font-semibold text-gray-900">{getChartTitle()}</h3>
              <p className="text-xs text-gray-500 mt-1">
                {selectedView === 'day' ? 'Hourly breakdown' : 
                 selectedView === 'week' ? 'Daily breakdown' :
                 selectedView === 'month' ? 'Daily breakdown' :
                 selectedView === 'year' ? 'Monthly breakdown' :
                 selectedView === 'multi-year' ? 'Yearly breakdown' : ''}
              </p>
            </div>
            <div className="flex items-center gap-3">
              {/* Toggle between Energy (kWh) and Power (kW) */}
              <button
                onClick={() => {
                  const newType = chartType === 'energy' ? 'power' : 'energy';
                  setChartType(newType);
                }}
                className="flex items-center gap-2 px-3 py-1.5 text-xs font-medium bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <svg className="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  {chartType === 'energy' ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                  )}
                </svg>
                {chartType === 'energy' ? 'Show Power' : 'Show Energy'}
              </button>
            </div>
          </div>

          {/* Year Range Selector for Multi-Year View - moved to its own row for better visibility */}
          {selectedView === 'multi-year' && (
            <div className="flex items-center gap-2 mb-2 bg-gray-50 p-2 rounded">
              <label htmlFor="yearStart" className="text-sm font-medium text-gray-700">Year Range:</label>
              <select
                id="yearStart"
                className="border border-gray-300 rounded px-2 py-1 text-sm"
                value={yearRange.start}
                onChange={(e) => setYearRange(prev => ({ ...prev, start: parseInt(e.target.value, 10) }))}
              >
                {Array.from({ length: 5 }, (_, i) => 2021 + i).map(year => (
                  <option key={`start-${year}`} value={year}>{year}</option>
                ))}
              </select>
              <span>-</span>
              <select
                id="yearEnd"
                className="border border-gray-300 rounded px-2 py-1 text-sm"
                value={yearRange.end}
                onChange={(e) => setYearRange(prev => ({ ...prev, end: parseInt(e.target.value, 10) }))}
              >
                {Array.from({ length: 5 }, (_, i) => 2021 + i).map(year => (
                  <option key={`end-${year}`} value={year} disabled={year < yearRange.start}>{year}</option>
                ))}
              </select>

              {/* Add a note about the comparison */}
              {metrics.previousTotal <= 0 && (
                <span className="text-sm text-gray-500 ml-4">No comparison data available</span>
              )}
            </div>
          )}
        </div>
        
        {(!transformedData || transformedData.length === 0) && (
          <div className="text-center py-20 text-gray-500">
            <div className="mb-4">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 className="text-sm font-medium text-gray-900 mb-1">No Data Available</h3>
            <p className="text-sm text-gray-500">
              {comparisonPeriod === 'none' 
                ? 'Please select a comparison period to view data.'
                : `No data is available for ${selectedView === 'month' ? 'this month' : selectedView === 'year' ? 'this year' : 'the selected period'}.`
              }
            </p>
            <p className="text-xs text-gray-400 mt-2">
              Try selecting a different date range or time period.
            </p>
          </div>
        )}

        {/* Render chart only if data exists */}
        {transformedData && transformedData.length > 0 && (
          <ResponsiveContainer width="100%" height={400}>
            {isEnergyChart() ? (
              <BarChart
                data={transformedData}
                margin={{ top: 20, right: 20, left: 0, bottom: 60 }}
                barSize={selectedView === 'multi-year' ? 50 : selectedView === 'day' ? 16 : selectedView === 'week' ? 28 : 30}
                barGap={3}
                barCategoryGap={selectedView === 'day' ? '25%' : selectedView === 'week' ? '30%' : selectedView === 'month' ? '35%' : '20%'}
                reverseStackOrder={false}
              >
                <defs>
                  <linearGradient id="comparisonGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor="#e2e8f0" stopOpacity={1} />
                    <stop offset="100%" stopColor="#cbd5e1" stopOpacity={1} />
                  </linearGradient>
                  <linearGradient id="primaryGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor={COLORS.primary[500]} stopOpacity={1} />
                    <stop offset="100%" stopColor={COLORS.primary[600]} stopOpacity={1} />
                  </linearGradient>
                </defs>
                <CartesianGrid 
                  strokeDasharray="3 3"
                  vertical={false}
                  stroke="#f3f4f6"
                  strokeOpacity={1}
                />
                <XAxis
                  {...getXAxisConfig(selectedView)}
                  axisLine={{ stroke: '#e5e7eb', strokeWidth: 1 }}
                  tickLine={false}
                  tick={{ 
                    fontSize: 11,
                    fill: '#6b7280',
                    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
                  }}
                  padding={{ left: 20, right: 20 }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ 
                    fontSize: 11,
                    fill: '#6b7280',
                    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
                  }}
                  tickFormatter={(value) => {
                    if (value >= 1000000) {
                      return `${(value / 1000000).toFixed(1)}M`;
                    } else if (value >= 1000) {
                      return `${(value / 1000).toFixed(0)}k`;
                    }
                    return value.toString();
                  }}
                  domain={[0, 'auto']}
                  padding={{ top: 20, bottom: 0 }}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.98)',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    padding: '12px',
                    fontSize: '12px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
                  }}
                  cursor={{ fill: 'rgba(59, 130, 246, 0.1)' }}
                  formatter={(value: number, name: string) => {
                    const formattedValue = `${formatNumber(value)} ${isEnergyChart() ? 'kWh' : 'kW'}`;
                    if (name === 'value') {
                      return [formattedValue, selectedView === 'multi-year' ? 'Total' : 'Current'];
                    }
                    if (name.includes('Previous') || name.includes('vs')) {
                      return [formattedValue, 'Previous'];
                    }
                    if (name.includes('Current')) {
                      return [formattedValue, 'Current'];
                    }
                    return [formattedValue, name];
                  }}
                  labelFormatter={(label) => {
                    if (selectedView === 'multi-year') {
                      return `Year ${label}`;
                    }
                    return label;
                  }}
                />
                <Legend
                  verticalAlign="top"
                  align="right"
                  height={40}
                  iconSize={8}
                  iconType="rect"
                  wrapperStyle={{
                    paddingTop: '0px',
                    fontSize: '11px',
                    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
                    color: '#4b5563'
                  }}
                  formatter={(value) => {
                    // Simplify legend labels
                    if (value === 'value' && selectedView === 'multi-year') {
                      return isEnergyChart() ? 'Energy' : 'Power';
                    }
                    if (value.includes('Previous') || value.includes('vs')) {
                      return 'Previous';
                    }
                    if (value.includes('Current')) {
                      return 'Current';
                    }
                    return value;
                  }}
                />
                {selectedView === 'multi-year' ? (
                  <Bar 
                    dataKey="value"
                    fill="url(#primaryGradient)"
                    name={isEnergyChart() ? 'Annual Energy' : 'Annual Power'}
                    radius={[6, 6, 0, 0]}
                    animationDuration={600}
                  />
                ) : (
                  <>
                    <Bar
                      dataKey="comparisonValue"
                      name={previousLabel || 'Previous Period'}
                      fill="url(#comparisonGradient)"
                      radius={[6, 6, 0, 0]}
                      animationDuration={600}
                    />
                    <Bar
                      dataKey="primaryValue"
                      name={
                        selectedView === 'day' ? 'Today' :
                        selectedView === 'week' ? 'This Week' :
                        selectedView === 'month' ? 'This Month' :
                        selectedView === 'year' ? 'This Year' :
                        selectedView === 'multi-year' ? 'Recent Years' :
                        'Current Period'
                      }
                      fill="url(#primaryGradient)"
                      radius={[6, 6, 0, 0]}
                      animationDuration={600}
                    />
                  </>
                )}
              </BarChart>
            ) : (
              <LineChart
                width={400}
                height={400}
                data={transformedData}
                margin={CHART_STYLES.margins.standard}
              >
                <CartesianGrid 
                  strokeDasharray={CHART_STYLES.grid.strokeDasharray}
                  vertical={CHART_STYLES.grid.vertical}
                  stroke={CHART_STYLES.grid.stroke}
                  strokeOpacity={CHART_STYLES.grid.strokeOpacity}
                />
                <XAxis
                  {...getXAxisConfig(selectedView)}
                  padding={{ left: 10, right: 10 }}
                />
                <YAxis
                  tickLine={CHART_STYLES.axis.tickLine}
                  axisLine={CHART_STYLES.axis.axisLine}
                  tick={{ 
                    fontSize: CHART_STYLES.axis.tick.fontSize,
                    fill: CHART_STYLES.axis.tick.fill,
                    fontFamily: CHART_STYLES.axis.tick.fontFamily
                  }}
                  tickFormatter={(value) => {
                    if (value > 1000) {
                      return `${(value / 1000).toFixed(1)}k`;
                    }
                    return formatNumber(value as number);
                  }}
                  domain={[0, 'auto']} // Let Recharts handle the domain automatically
                  padding={{ top: 10, bottom: 0 }} // Add padding
                  label={{ 
                    value: isEnergyChart() ? "Energy (kWh)" : "Power (kW)",
                    position: "insideLeft",
                    angle: CHART_STYLES.axis.yLabel.angle,
                    style: CHART_STYLES.axis.yLabel.style,
                    dx: CHART_STYLES.axis.yLabel.dx,
                    dy: CHART_STYLES.axis.yLabel.dy
                  }}
                />
                <Tooltip
                  formatter={(value: number, name: string) => {
                    if (name === 'value') {
                      return [`${formatNumber(value)} ${isEnergyChart() ? 'kWh' : 'kW'}`, `${isEnergyChart() ? 'Energy' : 'Power'} Consumption`];
                    }
                    if (name === 'percentChange') {
                      return [`${value}%`, 'Change'];
                    }
                    return [formatNumber(value), name];
                  }}
                  labelFormatter={(label) => {
                    // For multi-year view, use customized label
                    if (selectedView === 'multi-year') {
                      const dataPoint = transformedData.find((d: any) => d.time === label);
                      if (dataPoint && dataPoint.yearLabel) {
                        return `Year: ${dataPoint.yearLabel}`;
                      }
                    }
                    return label;
                  }}
                />
                <Legend
                  verticalAlign="top"
                  align="right"
                  height={36}
                  iconSize={12}
                  iconType="square"
                  wrapperStyle={{
                    paddingTop: '5px',
                    paddingRight: '10px',
                    fontSize: '12px',
                    fontWeight: 'medium'
                  }}
                  formatter={(value) => {
                    // Simplify legend labels
                    if (value === 'value' && selectedView === 'multi-year') {
                      return isEnergyChart() ? 'Annual Energy' : 'Annual Power';
                    }
                    // Return the value as-is since we now have clear labels
                    return value;
                  }}
                />
                {selectedView === 'multi-year' ? (
                  <Line
                    dataKey="value"
                    name={isEnergyChart() ? 'Annual Energy' : 'Annual Power'}
                    stroke={COLORS.primary[600]}
                    dot={false}
                    animationDuration={CHART_STYLES.bar.animation.duration}
                  />
                ) : (
                  <>
                    <Line
                      dataKey="comparisonValue"
                      name={previousLabel}
                      stroke="#cbd5e1"
                      strokeOpacity={0.9}
                      dot={false}
                      animationDuration={CHART_STYLES.bar.animation.duration}
                    />
                    <Line
                      dataKey="primaryValue"
                      name={
                        selectedView === 'day' ? 'Today' :
                        selectedView === 'week' ? 'This Week' :
                        selectedView === 'month' ? 'This Month' :
                        selectedView === 'year' ? 'This Year' :
                        selectedView === 'multi-year' ? 'Recent Years' :
                        'Current Period'
                      }
                      stroke={COLORS.primary[600]}
                      dot={false}
                      animationDuration={CHART_STYLES.bar.animation.duration}
                    />
                  </>
                )}
              </LineChart>
            )}
          </ResponsiveContainer>
        )}
      </div>
    </div>
  );
};

// Initialize static caches
ComparisonTab.mockDataCache = {};
ComparisonTab.transformCache = {};

// Force clear the mock data cache on every reload (for development only)
if (typeof window !== 'undefined') {
  (window as any).__clearedComparisonMockCache = (window as any).__clearedComparisonMockCache || (() => {
    if (ComparisonTab && ComparisonTab.mockDataCache) {
      ComparisonTab.mockDataCache = {};
    }
    if (ComparisonTab && ComparisonTab.transformCache) {
      ComparisonTab.transformCache = {};
    }
    return true;
  })();
}

export default ComparisonTab;
