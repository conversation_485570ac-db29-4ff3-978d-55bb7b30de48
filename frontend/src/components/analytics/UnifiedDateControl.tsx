import * as React from 'react';
import { Calendar, ChevronDown, Clock } from 'lucide-react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import '../../styles/datepicker.css';
import { ViewType } from './AnalyticsControls';
import { ToggleSwitch } from '../ui/ToggleSwitch';
import { format } from 'date-fns';
import DatePresetSelector, { DatePreset } from '../common/DatePresetSelector';

interface UnifiedDateControlProps {
  selectedView: ViewType;
  selectedDate: Date;
  onViewChange: (view: ViewType) => void;
  onDateChange: (date: Date) => void;
  showCumulativeToggle?: boolean;
  showCumulative?: boolean;
  onCumulativeToggle?: (show: boolean) => void;
}

const VIEW_LABELS: Record<ViewType, string> = {
  'day': 'Day',
  'week': 'Week',
  'month': 'Month',
  'year': 'Year',
  'multi-year': 'Multi-Year'
};

const UnifiedDateControl: React.FC<UnifiedDateControlProps> = ({
  selectedView,
  selectedDate,
  onViewChange,
  onDateChange,
  showCumulativeToggle = false,
  showCumulative = false,
  onCumulativeToggle
}) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // Format the date display based on the selected view
  const formatDateDisplay = (): string => {
    switch (selectedView) {
      case 'day':
        return format(selectedDate, 'MMMM d, yyyy');
      case 'week': {
        const startOfWeek = new Date(selectedDate);
        startOfWeek.setDate(selectedDate.getDate() - selectedDate.getDay()); // Assuming Sunday start
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return `${format(startOfWeek, 'MMM d')} - ${format(endOfWeek, 'MMM d, yyyy')}`;
      }
      case 'month':
        return format(selectedDate, 'MMMM yyyy');
      case 'year':
        return format(selectedDate, 'yyyy');
      case 'multi-year': {
        const year = selectedDate.getFullYear();
        return `${year - 2} - ${year + 2}`;
      }
      default:
        return format(selectedDate, 'MMMM d, yyyy');
    }
  };

  // Handle date picker change
  const handleDatePickerChange = (date: Date | null) => {
    if (date) {
      onDateChange(date);
      setIsOpen(false);
    }
  };

  // Handle view change
  const handleViewChange = (view: ViewType) => {
    onViewChange(view);
    // Don't close the dropdown to allow for further selection
  };

  // Handle preset selection
  const handlePresetSelect = (preset: DatePreset) => {
    const newDate = preset.getDate();
    onDateChange(newDate);
    onViewChange(preset.view);
    setIsOpen(false);
  };

  // Close dropdowns when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative z-50" ref={dropdownRef} style={{ zIndex: 50 }}>
      {/* Combined Date Controls Row - Optimized for Desktop */}
      <div className="flex items-center space-x-4">
        {/* Date Presets Dropdown */}
        <DatePresetSelector
          selectedDate={selectedDate}
          selectedView={selectedView}
          onPresetSelect={handlePresetSelect}
        />

        {/* Separator */}
        <div className="h-6 w-px bg-gray-300"></div>

        {/* Main Date Control with modern card styling */}
        <div className="flex items-center">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:shadow-md hover:border-blue-200 rounded-lg transition-all duration-300"
            aria-label="Open date selector"
            aria-expanded={isOpen}
          >
            <Calendar size={16} className="text-blue-500" />
            <span className="text-sm font-semibold text-gray-800">
              {VIEW_LABELS[selectedView]} • {formatDateDisplay()}
            </span>
            <ChevronDown size={14} className={`text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Cumulative Toggle - Moved to the end */}
        {showCumulativeToggle && onCumulativeToggle && (
          <>
            <div className="h-6 w-px bg-gray-300"></div>
            <div className="flex items-center space-x-2">
              <span className="text-xs font-medium text-gray-600">Standard</span>
              <ToggleSwitch
                checked={showCumulative}
                onChange={onCumulativeToggle}
                size="sm"
                aria-label="Toggle between standard and cumulative view"
              />
              <span className="text-xs font-medium text-gray-600">Cumulative</span>
            </div>
          </>
        )}
      </div>

      {/* Dropdown Content with modern card styling */}
      {isOpen && (
        <div className="absolute z-[100] mt-2 bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg border border-[#EDEFF9] w-[320px] animate-in fade-in-0 zoom-in-95 slide-in-from-top-2 duration-200" style={{ zIndex: 100 }}>
          {/* View Selection */}
          <div className="p-3 border-b border-gray-100">
            <div className="flex items-center mb-3 px-3 py-2 bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-lg">
              <Clock size={14} className="text-blue-600 mr-2" />
              <span className="text-sm font-semibold text-gray-800">Time Period View</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {Object.entries(VIEW_LABELS).map(([view, label]) => (
                <button
                  key={view}
                  onClick={() => handleViewChange(view as ViewType)}
                  className={`px-3 py-1.5 text-xs rounded-lg transition-all duration-200 ${
                    selectedView === view
                      ? 'bg-blue-50 text-blue-700 font-medium border border-blue-200'
                      : 'text-gray-700 hover:bg-blue-50 border border-gray-200'
                  }`}
                >
                  {label}
                </button>
              ))}
            </div>
          </div>

          {/* Calendar */}
          <div className="p-3">
            <div className="flex items-center mb-3 px-3 py-2 bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-lg">
              <Calendar size={14} className="text-blue-600 mr-2" />
              <span className="text-sm font-semibold text-gray-800">Select Date</span>
            </div>
            <DatePicker
              selected={selectedDate}
              onChange={handleDatePickerChange}
              inline
              showMonthYearPicker={selectedView === 'month'}
              showYearPicker={selectedView === 'year' || selectedView === 'multi-year'}
              calendarClassName="analytics-date-picker"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default UnifiedDateControl;
