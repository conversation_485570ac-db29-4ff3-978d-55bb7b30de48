import React from 'react';
import { PerformanceMetrics } from '../../types';
import { TrendingUp, TrendingDown, Info } from 'lucide-react';

interface PerformanceMetricsDisplayProps {
  metrics: PerformanceMetrics | null;
  isLoading: boolean;
}

// Simple Tooltip Component (can be enhanced)
const InfoTooltip = ({ content }: { content: string }) => (
  <div className="group relative inline-block ml-1">
    <Info size={14} className="text-gray-400 cursor-help" />
    <div className="opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity duration-200 absolute z-10 bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 p-3 bg-white rounded-lg shadow-lg border border-gray-200 text-xs text-gray-700">
      {content}
      <div className="absolute left-1/2 -bottom-2 transform -translate-x-1/2 w-3 h-3 rotate-45 bg-white border-r border-b border-gray-200"></div>
    </div>
  </div>
);

export const PerformanceMetricsDisplay: React.FC<PerformanceMetricsDisplayProps> = ({ metrics, isLoading }) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {[...Array(2)].map((_, index) => (
          <div key={index} className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm animate-pulse">
            <div className="h-4 bg-gray-200 w-24 rounded mb-2"></div>
            <div className="h-6 bg-gray-300 w-16 mb-2 rounded"></div>
            <div className="h-3 bg-gray-200 w-20 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
         <div className="col-span-full p-4 bg-white rounded-lg text-center text-gray-500 border border-gray-200 shadow-sm">
            No performance metrics available
          </div>
      </div>
    );
  }

  const { energySavings, carbonReduction } = metrics;
  const savingsGood = energySavings > 0;
  const carbonGood = carbonReduction > 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      {/* Energy Savings */}
      <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
        <div className="text-sm font-medium text-gray-500 mb-1 flex items-center">
          <span>Energy Savings</span>
          <InfoTooltip content="Energy Savings = Baseline - Actual (kWh)" />
        </div>
        <div className="text-2xl font-bold text-gray-800">
          {energySavings.toLocaleString()} kWh
        </div>
        <div className={`text-xs mt-1 flex items-center ${savingsGood ? 'text-green-600' : 'text-red-600'}`}>
          {savingsGood ? <TrendingUp size={14} /> : <TrendingDown size={14} />}
          <span className="ml-1">vs baseline</span>
        </div>
      </div>
      
      {/* Carbon Reduction */}
      <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
        <div className="text-sm font-medium text-gray-500 mb-1 flex items-center">
          <span>Carbon Reduction</span>
          <InfoTooltip content="Carbon Reduction = Energy Savings × Emission Factor (kg CO₂e)" />
        </div>
        <div className="text-2xl font-bold text-gray-800">
          {carbonReduction.toLocaleString()} kg CO₂e
        </div>
        <div className={`text-xs mt-1 flex items-center ${carbonGood ? 'text-green-600' : 'text-red-600'}`}>
          {carbonGood ? <TrendingUp size={14} /> : <TrendingDown size={14} />}
          <span className="ml-1">CO₂ avoided</span>
        </div>
      </div>
    </div>
  );
}; 