import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, CalendarDays } from 'lucide-react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Button from '../common/Button';

export type ComparisonPeriod = 'none' | 'day' | 'week' | 'month' | 'year' | 'custom';
export type ViewType = 'day' | 'week' | 'month' | 'year' | 'multi-year';

export interface ComparisonSelectProps {
  selectedPeriod: ComparisonPeriod;
  onChange: (period: ComparisonPeriod) => void;
  customRangeStart?: Date;
  customRangeEnd?: Date;
  onCustomRangeChange?: (startDate: Date | null, endDate: Date | null) => void;
  viewType?: ViewType;
}

const ComparisonSelect: React.FC<ComparisonSelectProps> = ({
  selectedPeriod,
  onChange,
  customRangeStart,
  customRangeEnd,
  onCustomRangeChange,
  viewType = 'day'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showCustomDatePicker, setShowCustomDatePicker] = useState(false);
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    customRangeStart || null,
    customRangeEnd || null
  ]);
  const [startDate, endDate] = dateRange;
  const dropdownRef = useRef<HTMLDivElement>(null);
  const customDateRef = useRef<HTMLDivElement>(null);

  const getPeriodLabels = (): Record<ComparisonPeriod, string> => {
    const baseLabels: Record<ComparisonPeriod, string> = {
      none: 'No Comparison',
      day: 'vs. Yesterday',
      week: 'vs. Last Week',
      month: 'vs. Last Month',
      year: 'vs. Last Year',
      custom: 'Custom Range'
    };

    switch (viewType) {
      case 'day':
        return baseLabels;
      case 'week':
        return {
          ...baseLabels,
          day: 'vs. Previous Day',
          week: 'vs. Last Week'
        };
      case 'month':
        return {
          ...baseLabels,
          day: 'vs. Previous Day',
          week: 'vs. Previous Week',
          month: 'vs. Last Month'
        };
      case 'year':
        return {
          ...baseLabels,
          day: 'vs. Previous Day',
          week: 'vs. Previous Week',
          month: 'vs. Previous Month',
          year: 'vs. Last Year'
        };
      case 'multi-year':
        return {
          ...baseLabels,
          day: 'vs. Previous Day',
          week: 'vs. Previous Week',
          month: 'vs. Previous Month',
          year: 'vs. Previous Year'
        };
      default:
        return baseLabels;
    }
  };

  const periodLabels = getPeriodLabels();

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          customDateRef.current && !customDateRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setShowCustomDatePicker(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handlePeriodChange = (period: ComparisonPeriod) => {
    console.log('ComparisonSelect: selected period', period);
    onChange(period);

    if (period === 'custom') {
      setShowCustomDatePicker(true);
    } else {
      setShowCustomDatePicker(false);
      setIsOpen(false);
    }
  };

  const handleDateRangeChange = (update: [Date | null, Date | null]) => {
    setDateRange(update);

    if (update[0] && update[1] && onCustomRangeChange) {
      onCustomRangeChange(update[0], update[1]);
      setShowCustomDatePicker(false);
    }
  };

  useEffect(() => {
    if (selectedPeriod === 'none') {
      console.log('ComparisonSelect: initialized with period', selectedPeriod);
    }
  }, [selectedPeriod]);

  useEffect(() => {
    if (customRangeStart && customRangeEnd) {
      setDateRange([customRangeStart, customRangeEnd]);
    }
  }, [customRangeStart, customRangeEnd]);

  const formatCustomRange = () => {
    if (!startDate || !endDate) return 'Custom Range';

    const formatDate = (date: Date) => {
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    };

    return `${formatDate(startDate)} - ${formatDate(endDate)}`;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between gap-2 px-4 py-2 bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:shadow-md hover:border-blue-200 rounded-lg transition-all duration-300"
        aria-label="Select comparison period"
        aria-expanded={isOpen}
      >
        <span className="text-sm font-semibold text-gray-800">
          {selectedPeriod === 'custom' && startDate && endDate
            ? formatCustomRange()
            : periodLabels[selectedPeriod]}
        </span>
        <ChevronDown size={14} className={`text-gray-500 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg border border-[#EDEFF9] py-1 z-[100] w-48 max-h-64 overflow-auto animate-in fade-in slide-in-from-top-2 duration-200">
          {Object.entries(periodLabels).map(([period, label]) => (
            <button
              key={period}
              onClick={() => handlePeriodChange(period as ComparisonPeriod)}
              className={`w-full text-left px-4 py-2.5 text-sm hover:bg-blue-50 hover:text-blue-700 transition-all duration-200 ${
                selectedPeriod === period ? 'bg-blue-50 text-blue-700 font-semibold' : 'text-gray-700'
              } ${period === 'custom' ? 'flex items-center justify-between' : ''}`}
              aria-selected={selectedPeriod === period}
            >
              {label}
              {period === 'custom' && <CalendarDays size={14} className={selectedPeriod === period ? 'text-blue-700' : 'text-gray-500'} />}
            </button>
          ))}
        </div>
      )}

      {showCustomDatePicker && (
        <div
          ref={customDateRef}
          className="absolute top-full right-0 mt-2 z-50 bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg border border-[#EDEFF9] p-3 animate-in fade-in slide-in-from-top-2 duration-200"
        >
          <DatePicker
            selectsRange={true}
            startDate={startDate}
            endDate={endDate}
            onChange={handleDateRangeChange}
            inline
            calendarClassName="analytics-date-picker"
          />
        </div>
      )}
    </div>
  );
};

export default ComparisonSelect;