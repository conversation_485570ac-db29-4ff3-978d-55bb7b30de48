import React from 'react';
import { BuildingLoadChart } from '../overview/BuildingLoadChart';
import { DEFAULT_SYSTEM_BREAKDOWN } from '../../lib/config/energy-systems';
import type { LoadProfileData } from '../../lib/config/load-profile';

export interface ChartDisplayProps {
  data: LoadProfileData[];
  comparisonData: LoadProfileData[];
  view: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'multi-year';
  showComparison: boolean;
  selectedSystem: string | null;
  loading?: boolean;
  height?: number | string;
  onBarClick?: (time: string, value: number) => void;
}

const ChartDisplay: React.FC<ChartDisplayProps> = ({
  data,
  comparisonData,
  view,
  showComparison,
  selectedSystem,
  loading = false,
  height = 350,
  onBarClick
}) => {
  if (loading) {
    return (
      <div className="h-[350px] flex items-center justify-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full bg-blue-200"></div>
          <div className="mt-4 text-sm text-gray-500">Loading chart data...</div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-6 bg-white rounded-xl border border-[#EDEFF9] shadow-sm hover:shadow-md transition-all duration-300">
      <h2 className="text-base font-semibold mb-4 text-gray-800 flex items-baseline gap-2">
        Building Load
        {selectedSystem && (
          <span className="text-xs font-medium text-primary-blue bg-blue-50 px-2 py-0.5 rounded-full">
            {DEFAULT_SYSTEM_BREAKDOWN.find(s => s.id === selectedSystem)?.name || 'System'}
          </span>
        )}
      </h2>
      
      <div style={{ height: typeof height === 'number' ? `${height}px` : height }}>
        <BuildingLoadChart 
          data={data}
          comparisonData={comparisonData}
          view={view}
          showComparison={showComparison}
          selectedSystem={selectedSystem}
          onBarClick={onBarClick || (() => {})}
        />
      </div>
    </div>
  );
};

export default ChartDisplay; 