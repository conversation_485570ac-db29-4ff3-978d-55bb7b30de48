import React from 'react';
import { ViewType, BuildingType, AnalyticsTab } from '../../types/analytics';
import { addDays, addMonths, addWeeks, addYears, format, subDays, subMonths, subWeeks, subYears } from 'date-fns';
import { ToggleSwitch } from '../ui/ToggleSwitch';

interface ControlPanelProps {
  selectedView: ViewType;
  selectedDate: Date;
  selectedBuilding: BuildingType;
  selectedTab: AnalyticsTab;
  setSelectedView: (view: ViewType) => void;
  setSelectedDate: (date: Date) => void;
  setSelectedBuilding: (building: BuildingType) => void;
  setSelectedTab: (tab: AnalyticsTab) => void;
  showCumulative: boolean;
  setShowCumulative: (show: boolean) => void;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
  selectedView,
  selectedDate,
  selectedBuilding,
  selectedTab,
  setSelectedView,
  setSelectedDate,
  setSelectedBuilding,
  setSelectedTab,
  showCumulative,
  setShowCumulative
}) => {
  // Handlers for date navigation
  const handlePreviousDate = () => {
    switch (selectedView) {
      case 'day':
        setSelectedDate(subDays(selectedDate, 1));
        break;
      case 'week':
        setSelectedDate(subWeeks(selectedDate, 1));
        break;
      case 'month':
        setSelectedDate(subMonths(selectedDate, 1));
        break;
      case 'year':
        setSelectedDate(subYears(selectedDate, 1));
        break;
    }
  };

  const handleNextDate = () => {
    switch (selectedView) {
      case 'day':
        setSelectedDate(addDays(selectedDate, 1));
        break;
      case 'week':
        setSelectedDate(addWeeks(selectedDate, 1));
        break;
      case 'month':
        setSelectedDate(addMonths(selectedDate, 1));
        break;
      case 'year':
        setSelectedDate(addYears(selectedDate, 1));
        break;
    }
  };

  const handleToday = () => {
    setSelectedDate(new Date());
  };

  const formatDisplayDate = (): string => {
    switch (selectedView) {
      case 'day':
        return format(selectedDate, 'MMMM d, yyyy');
      case 'week': {
        const startOfWeek = selectedDate;
        startOfWeek.setDate(selectedDate.getDate() - selectedDate.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        return `${format(startOfWeek, 'MMM d')} - ${format(endOfWeek, 'MMM d, yyyy')}`;
      }
      case 'month':
        return format(selectedDate, 'MMMM yyyy');
      case 'year':
        return format(selectedDate, 'yyyy');
      default:
        return '';
    }
  };

  return (
    <div className="mb-4 flex items-center justify-between px-4 py-3 bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Tab Pills with clearer labels */}
      <div className="flex items-center">
        <span className="text-xs font-medium text-gray-600 mr-2">View:</span>
        <div className="flex bg-gray-100 p-0.5 rounded-md">
          {['consumption', 'comparison', 'performance'].map((tab) => (
            <button
              key={tab}
              title={`${tab.charAt(0).toUpperCase() + tab.slice(1)} View`}
              className={`px-3 py-1 rounded text-xs font-medium transition-all ${
                selectedTab === tab
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'text-gray-700 hover:bg-gray-200'
              }`}
              onClick={() => setSelectedTab(tab as AnalyticsTab)}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Vertical separator */}
      <div className="h-6 w-px bg-gray-200 mx-3 hidden sm:block"></div>

      {/* Time period selector with clearer labels */}
      <div className="flex items-center">
        <span className="text-xs font-medium text-gray-600 mr-2">Period:</span>
        <div className="flex bg-gray-100 p-0.5 rounded-md">
          {['day', 'week', 'month', 'year', 'multi-year'].map((view) => (
            <button
              key={view}
              title={view === 'multi-year' ? 'Multi-Year View' : `${view.charAt(0).toUpperCase() + view.slice(1)} View`}
              className={`px-2 py-1 rounded text-xs font-medium transition-all ${
                selectedView === view
                  ? 'bg-gray-800 text-white shadow-sm'
                  : 'text-gray-700 hover:bg-gray-200'
              }`}
              onClick={() => setSelectedView(view as ViewType)}
            >
              {view === 'multi-year' ? 'Multi-Year' : view.charAt(0).toUpperCase() + view.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Vertical separator */}
      <div className="h-6 w-px bg-gray-200 mx-3 hidden sm:block"></div>

      {/* Date Navigation with clearer controls */}
      {selectedView !== 'multi-year' && (
        <div className="flex items-center">
          <span className="text-xs font-medium text-gray-600 mr-2">Date:</span>
          <div className="flex items-center space-x-1 bg-gray-100 px-2 py-1 rounded-md">
            <button
              className="p-0.5 text-gray-700 hover:text-blue-500 transition-colors"
              onClick={handlePreviousDate}
              title="Previous Period"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <span className="text-xs font-medium px-1">{formatDisplayDate()}</span>
            <button
              className="p-0.5 text-gray-700 hover:text-blue-500 transition-colors"
              onClick={handleNextDate}
              title="Next Period"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
          <button
            className="ml-2 px-2 py-1 text-xs bg-white border border-gray-200 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            onClick={handleToday}
            title="Go to Today"
          >
            Today
          </button>
        </div>
      )}

      {/* Building Selector with clearer labels */}
      <div className="flex items-center ml-4">
        <span className="text-xs font-medium text-gray-600 mr-2">Building:</span>
        <select
          value={selectedBuilding}
          onChange={(e) => setSelectedBuilding(e.target.value as BuildingType)}
          className="h-7 text-xs bg-gray-100 border border-gray-200 px-2 py-1 rounded-md"
          title="Select Building"
        >
          <option value="all">All Buildings</option>
          <option value="A">Building A</option>
          <option value="B">Building B</option>
          <option value="C">Building C</option>
        </select>
      </div>

      {/* Cumulative Toggle with clearer labels */}
      {selectedTab === 'consumption' && selectedView !== 'multi-year' && (
        <div className="flex items-center ml-4">
          <span className="text-xs font-medium text-gray-600 mr-1">Standard</span>
          <ToggleSwitch
            checked={showCumulative}
            onChange={setShowCumulative}
            size="sm"
            aria-label="Toggle between standard and cumulative view"
          />
          <span className="text-xs font-medium text-gray-600 ml-1">Cumulative</span>
        </div>
      )}
    </div>
  );
};

export default React.memo(ControlPanel);
