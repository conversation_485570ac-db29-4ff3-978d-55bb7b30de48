import React from 'react';
import { ComparisonPeriod } from './ComparisonSelect';
import UnifiedDateControl from './UnifiedDateControl';
import MoreActionsDropdown from './MoreActionsDropdown';

// Update types to match the ones defined in PerformanceAnalytics.tsx
export type ViewType = 'day' | 'week' | 'month' | 'year' | 'multi-year';
export type AnalyticsTab = 'consumption' | 'comparison';

interface AnalyticsControlsProps {
  selectedTab: AnalyticsTab;
  selectedView: ViewType;
  selectedDate: Date;
  showCumulative: boolean;
  comparisonPeriod: ComparisonPeriod;
  onTabChange: (tab: AnalyticsTab) => void;
  onViewChange: (view: ViewType) => void;
  onDateChange: (date: Date) => void;
  onCumulativeToggle: (show: boolean) => void;
  onComparisonPeriodChange: (period: ComparisonPeriod) => void;
  onExportCSV?: () => void;
  onExportPDF?: () => void;
  children?: React.ReactNode;
}

const TABS: { id: AnalyticsTab; label: string }[] = [
  { id: 'consumption', label: 'Consumption' },
  { id: 'comparison', label: 'Comparison' },
  // { id: 'performance', label: 'Performance' }, // Removed
  // { id: 'cost', label: 'Cost' }, // Future tabs
];

const AnalyticsControls: React.FC<AnalyticsControlsProps> = ({
  selectedTab,
  selectedView,
  selectedDate,
  showCumulative,
  comparisonPeriod,
  onTabChange,
  onViewChange,
  onDateChange,
  onCumulativeToggle,
  onComparisonPeriodChange,
  onExportCSV,
  onExportPDF,
  children,
}) => {
  return (
    <div className="bg-gradient-to-br from-white to-gray-50 border border-[#EDEFF9] rounded-xl shadow-sm mb-4 hover:shadow-md transition-all duration-300 overflow-visible">
      {/* Desktop Layout - Simplified with Progressive Disclosure */}
      <div className="hidden md:flex p-4 items-center gap-3 overflow-visible">
        {/* Primary Controls Group */}
        <div className="flex items-center gap-3 flex-1 overflow-visible">
          {/* Navigation Tabs */}
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            {TABS.map((tab) => (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={`px-4 py-1.5 text-sm font-medium rounded-md transition-all duration-200 ${
                  selectedTab === tab.id 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
          
          {/* Vertical Separator */}
          <div className="h-6 w-px bg-gray-300"></div>
          
          {/* Date and View Controls - Without Cumulative Toggle */}
          <div className="flex-1 overflow-visible">
            <UnifiedDateControl
              selectedView={selectedView}
              selectedDate={selectedDate}
              onViewChange={onViewChange}
              onDateChange={onDateChange}
              showCumulativeToggle={false} // Hide cumulative toggle here
              showCumulative={showCumulative}
              onCumulativeToggle={onCumulativeToggle}
            />
          </div>

        </div>

        {/* Secondary Controls Group */}
        <div className="flex items-center gap-3">
          {/* Building & System Selectors */}
          {React.Children.map(children, (child, index) => (
            <React.Fragment key={index}>
              {child}
            </React.Fragment>
          ))}
          
          {/* More Actions Dropdown */}
          <MoreActionsDropdown
            showCumulativeToggle={selectedTab === 'consumption'}
            showCumulative={showCumulative}
            onCumulativeToggle={onCumulativeToggle}
            onExportCSV={onExportCSV}
            onExportPDF={onExportPDF}
          />
        </div>
      </div>
      
      {/* Mobile Layout - Simplified with Progressive Disclosure */}
      <div className="md:hidden flex flex-col gap-3 p-4">
        {/* Primary Controls */}
        <div className="flex flex-col gap-3">
          {/* Navigation Tabs */}
          <div className="flex items-center justify-center bg-gray-100 rounded-lg p-1">
            {TABS.map((tab) => (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={`px-4 py-1.5 text-sm font-medium rounded-md transition-all duration-200 ${
                  selectedTab === tab.id 
                    ? 'bg-white text-gray-900 shadow-sm' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
          
          {/* Date Controls */}
          <div className="bg-gradient-to-br from-white to-gray-50 rounded-lg border border-gray-200 shadow-sm p-3">
            <UnifiedDateControl
              selectedView={selectedView}
              selectedDate={selectedDate}
              onViewChange={onViewChange}
              onDateChange={onDateChange}
              showCumulativeToggle={false} // Hide cumulative toggle here
              showCumulative={showCumulative}
              onCumulativeToggle={onCumulativeToggle}
            />
          </div>
          
          {/* Building & System Selectors with More Actions */}
          <div className="flex items-center justify-between gap-2 bg-gradient-to-br from-white to-gray-50 p-3 rounded-lg border border-gray-200 shadow-sm">
            <div className="flex items-center gap-2 flex-1">
              {React.Children.map(children, (child) => child)}
            </div>
            <MoreActionsDropdown
              showCumulativeToggle={selectedTab === 'consumption'}
              showCumulative={showCumulative}
              onCumulativeToggle={onCumulativeToggle}
              onExportCSV={onExportCSV}
              onExportPDF={onExportPDF}
            />
          </div>
          
        </div>
      </div>
    </div>
  );
};

export default AnalyticsControls;
