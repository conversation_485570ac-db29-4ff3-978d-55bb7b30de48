import React from 'react';
import { SystemBreakdownItem } from '../../types/analytics';
import EnergyBreakdownDonutChart from '../charts/EnergyBreakdownDonutChart';
import { EnergySystemData } from '../charts/EnergyBreakdownDonutChart';

interface SystemBreakdownProps {
  loading: boolean;
  systemBreakdown: SystemBreakdownItem[];
}

const SystemBreakdown: React.FC<SystemBreakdownProps> = ({
  loading,
  systemBreakdown
}) => {
  // Transform systemBreakdown data to match EnergySystemData format
  // Directly use the color provided in the systemBreakdown data
  const chartData: EnergySystemData[] = systemBreakdown.map(item => ({
    id: item.id,
    name: item.name,
    value: item.value,
    percentage: item.percentage,
    color: item.color // Use the color directly from the source data
  }));

  return (
    <>
      <h3 className="text-base font-semibold text-gray-800 mb-3">Energy Consumption Breakdown</h3>

      <EnergyBreakdownDonutChart 
        data={chartData}
        isLoading={loading}
        title=""
        subtitle=""
        cardLayout="pill"
        height={280}
        navigateOnClick={false}
        showPercentage={true}
        innerRadius={55}
        outerRadius={75}
        showWrapper={false}
      />
    </>
  );
};

export default React.memo(SystemBreakdown);
