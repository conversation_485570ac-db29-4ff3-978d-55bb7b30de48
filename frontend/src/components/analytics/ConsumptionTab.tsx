import React, { useMemo, useState, useEffect } from 'react';

// Import additional components from 'recharts' for enhanced chart features
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  AreaChart,
  Area,
  LineChart,
  Line,
  Legend,
  Brush,
  Label
} from 'recharts';

import { ViewType } from '../../types/analytics';
import ExportButton from '../common/ExportButton';
import { ChartDataType } from '../common/ChartTypeToggle';
import { formatNumber, formatAxisTick } from '../../utils/formatting';
import { COLORS, CHART_STYLES } from '../../constants/chartStyles';
import { RECHARTS_LINE_CONFIG } from '../../lib/config/chart-styles';
import { formatDateForTitle, formatWeekRangeForTitle, formatMonthForTitle, formatYearForTitle } from '../../utils/dateFormatters';
import { getXAxisConfig, ensureAllHoursForDayView } from '../../lib/config/chart-axes';

interface ConsumptionTabProps {
  chartData: any;
  view: ViewType | 'day' | 'week' | 'month' | 'year'; // Support both enum types
  showCumulative: boolean;
  selectedDate?: Date;
  selectedSystem?: string;
  systemName?: string;
  chartType?: 'energy' | 'power'; // Add chart type prop
  onChartTypeChange?: (type: 'energy' | 'power') => void; // Add chart type change handler
}

// Extended interface for points with accumulated value
interface AccumulatedConsumptionPoint {
  time: string;       // from HourlyConsumptionPoint
  actual: number;     // from HourlyConsumptionPoint
  predicted?: number; // from HourlyConsumptionPoint
  accumulatedValue?: number;  // Add this explicitly
  sequence?: number;  // For sorting week view days
  fullDate?: string;  // Full date for reference
}

const calculateMetrics = (data: any[] | undefined, analyticsData: any) => {
  // Use the direct analytics data properties if available
  if (analyticsData) {
    return {
      totalConsumption: formatNumber(analyticsData.totalConsumption || 0),
      peakDemand: formatNumber(analyticsData.peakDemand?.value || 0),
      peakDemandTime: analyticsData.peakDemand?.time || null,
      avgLoad: formatNumber(analyticsData.averageLoad || 0),
    };
  }

  // Fallback calculation from data points if the direct properties aren't available
  if (!data || data.length === 0) {
    return {
      totalConsumption: '0',
      peakDemand: '0',
      peakDemandTime: null,
      avgLoad: '0'
    };
  }

  const totalConsumption = data.reduce((sum, point) => sum + (point?.actual || 0), 0);
  let peakDemand = 0;
  let peakDemandTime = null;
  
  // Find peak demand and when it occurred
  data.forEach(point => {
    if (point?.actual > peakDemand) {
      peakDemand = point.actual;
      peakDemandTime = point.time;
    }
  });
  
  const avgLoad = data.length > 0 ? totalConsumption / data.length : 0;

  return {
    totalConsumption: formatNumber(totalConsumption),
    peakDemand: formatNumber(peakDemand),
    peakDemandTime,
    avgLoad: formatNumber(avgLoad),
  };
};

interface MetricCardProps {
  title: string;
  value: string | number;
  unit: string;
  subtitle?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, unit, subtitle }) => (
  <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-lg p-4 flex flex-col hover:shadow-md transition-shadow duration-300">
    <div className="flex items-center gap-2 mb-2">
      <div className="w-2 h-2 rounded-full bg-blue-500"></div>
      <h3 className="text-sm font-medium text-gray-700">{title}</h3>
    </div>
    <div className="flex items-baseline gap-1">
      <span className="text-2xl font-semibold text-gray-900">{value || "—"}</span>
      <span className="text-sm text-gray-500">{unit}</span>
    </div>
    {subtitle && (
      <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
    )}
  </div>
);

// Add static cache to avoid regenerating mock data
export const ConsumptionTab: React.FC<ConsumptionTabProps> & {
  mockDataCache: Record<string, any[]>;
  transformCache: Record<string, any[]>;
} = ({
  chartData,
  view,
  showCumulative,
  selectedDate = new Date(),
  selectedSystem = 'all',
  systemName = 'All Systems',
  chartType = 'energy',
  onChartTypeChange
}) => {
  // Simple loading state
  const isLoading = !chartData; // Consider no data as loading initially

  // Chart type state - toggle between energy (kWh) and power (kW)
  const [chartTypeState, setChartTypeState] = useState<ChartDataType>(chartType);
  
  // State for monthly aggregation (daily vs weekly)
  // Default to daily view for month view
  const [showWeeklyAggregation, setShowWeeklyAggregation] = useState<boolean>(
    view !== 'month'
  );

  // Force energy mode when cumulative is enabled
  useEffect(() => {
    if (showCumulative && chartTypeState === 'power') {
      setChartTypeState('energy');
    }
    
    // Clear the cache when the view is multi-year to ensure we get fresh data
    if ((view as ViewType) === 'multi-year') {
      ConsumptionTab.mockDataCache = {};
      ConsumptionTab.transformCache = {};
    }
  }, [showCumulative, chartTypeState, view]);

  // Update aggregation mode when view changes
  useEffect(() => {
    // Default to daily view for month view
    setShowWeeklyAggregation(view !== 'month');
  }, [view]);

  // Function to generate dynamic context-aware chart title
  const getDynamicChartTitle = () => {
    // Base energy/power label with proper units
    const energyLabel = chartTypeState === 'power' ? 'Power' : 'Energy';
    const unitLabel = chartTypeState === 'power' ? '(kW)' : '(kWh)';

    // System label - make it more prominent
    const systemLabel = selectedSystem !== 'all' ? `${systemName}` : 'All Systems';

    // Time period label with improved formatting
    let periodLabel = '';
    switch(view) {
      case 'day':
        periodLabel = formatDateForTitle(selectedDate);
        break;
      case 'week':
        periodLabel = formatWeekRangeForTitle(selectedDate);
        break;
      case 'month':
        periodLabel = formatMonthForTitle(selectedDate);
        break;
      case 'year':
        periodLabel = formatYearForTitle(selectedDate);
        break;
      case 'multi-year':
        const currentYear = selectedDate.getFullYear();
        periodLabel = `${currentYear-4} to ${currentYear}`; // Show 5 years instead of 3
        break;
    }

    // Frequency label with improved clarity
    let frequencyLabel = '';
    if (showCumulative) {
      // In cumulative mode, only energy (kWh) should be available
      frequencyLabel = 'Cumulative';
    } else {
      frequencyLabel = view === 'day' ? 'Hourly' :
                      view === 'week' || view === 'month' ? 'Daily' :
                      view === 'year' ? 'Monthly' :
                      view === 'multi-year' ? 'Annual' : '';
    }

    // Format: "System Name | Frequency Energy/Power (unit) - Period"
    return `${systemLabel} | ${frequencyLabel} ${energyLabel} ${unitLabel} - ${periodLabel}`;
  };

  // Use memoized chart points from real data, return empty array if no data
  const chartPoints = useMemo(() => {
    // Return empty array if no chart data (not loading anymore)
    if (!chartData || !chartData.hourlyConsumption) return [];
    return chartData.hourlyConsumption;
  }, [chartData]);

  // Calculate metrics after the transformedData is computed
  // This will be moved after transformedData definition

  // Transform raw data points to chart format with accumulation if needed
  const transformedData = useMemo(() => {
    if (!chartPoints || chartPoints.length === 0) {
      // For day view, still return 24 hours even with no data
      if (view === 'day') {
        const hours = [];
        for (let hour = 0; hour < 24; hour++) {
          hours.push({
            time: `${hour}:00`,
            actual: 0,
            sequence: hour,
            fullDate: null
          });
        }
        return hours;
      }
      return [];
    }

    // Create a cache key for the transformed data
    const cacheKey = `${chartPoints.length}-${showCumulative}-${chartTypeState}-${view}-${showWeeklyAggregation}`;

    // Check if we already have this transformation cached
    if (ConsumptionTab.transformCache?.[cacheKey]) {
      return ConsumptionTab.transformCache[cacheKey];
    }

    let accumulated = 0;

    // For day view, ensure all 24 hours are present
    if (view === 'day') {
      // Map the data to extract the correct values
      const mappedData = chartPoints.map((point: any) => {
        const value = chartTypeState === 'power' 
          ? (typeof point.power !== 'undefined' ? point.power : point.actual) 
          : (typeof point.consumption !== 'undefined' ? point.consumption : point.actual);
        
        return {
          time: point.time,
          actual: value,
          sequence: point.sequence,
          fullDate: point.fullDate
        };
      });
      
      // Use shared function to ensure all hours are present
      const fullDayData = ensureAllHoursForDayView(mappedData, view as ViewType);
      
      // Handle accumulation if needed
      if (showCumulative) {
        return fullDayData.map(point => {
          accumulated += point.actual;
          return {
            ...point,
            accumulatedValue: accumulated
          } as AccumulatedConsumptionPoint;
        });
      }
      
      return fullDayData;
    }

    // For non-day views, use the data as-is
    const result = chartPoints.map((point: any) => {
      // Select the appropriate value based on chartType (energy or power)
      const value = chartTypeState === 'power' 
        ? (typeof point.power !== 'undefined' ? point.power : point.actual) 
        : (typeof point.consumption !== 'undefined' ? point.consumption : point.actual);

      // Always preserve sequence and fullDate for week view
      if (showCumulative) {
        accumulated += value;
        return {
          time: point.time,
          sequence: point.sequence,
          fullDate: point.fullDate,
          actual: value,
          accumulatedValue: accumulated
        } as AccumulatedConsumptionPoint;
      }

      return {
        time: point.time,
        sequence: point.sequence,
        fullDate: point.fullDate,
        actual: value
      };
    });

    // For month view, aggregate data by week if enabled
    if (view === 'month' && showWeeklyAggregation && result.length > 0) {
      try {
        // Group data by week
        const weeklyData: Record<string, any> = {};
        
        for (const item of result) {
          let date: Date;
          
          // Extract date from various possible formats
          if (typeof item.time === 'string') {
            if (item.time.includes('-')) {
              // ISO date format (YYYY-MM-DD)
              try {
                date = new Date(item.time);
              } catch (e) {
                console.error('Error parsing ISO date:', e);
                continue;
              }
            } else if (!isNaN(parseInt(item.time, 10))) {
              // Already a day number as string
              const day = parseInt(item.time, 10);
              const today = new Date();
              date = new Date(today.getFullYear(), today.getMonth(), day);
            } else {
              // Other string format, try to extract leading numbers
              const match = item.time.match(/^(\d+)/);
              if (match && match[1]) {
                const day = parseInt(match[1], 10);
                const today = new Date();
                date = new Date(today.getFullYear(), today.getMonth(), day);
              } else {
                console.error('Unable to extract day from:', item.time);
                continue;
              }
            }
          } else if (item.time instanceof Date) {
            date = item.time;
          } else if (item.fullDate) {
            // Try to use fullDate if available
            try {
              date = new Date(item.fullDate);
            } catch (e) {
              console.error('Error parsing fullDate:', e);
              continue;
            }
          } else {
            console.error('Unrecognized time format:', item.time);
            continue;
          }
          
          // Get week number (1-5) within the month
          const weekOfMonth = Math.ceil(date.getDate() / 7);
          const weekKey = `Week ${weekOfMonth}`;
          
          if (!weeklyData[weekKey]) {
            weeklyData[weekKey] = {
              time: weekKey,
              actual: 0,
              count: 0,
              // Use the first day of the week for fullDate reference
              fullDate: new Date(date.getFullYear(), date.getMonth(), (weekOfMonth - 1) * 7 + 1).toISOString()
            };
            
            // If we have accumulated values, initialize them
            if (showCumulative) {
              weeklyData[weekKey].accumulatedValue = 0;
            }
          }
          
          weeklyData[weekKey].actual += item.actual || 0;
          
          // Update accumulated value if we're showing cumulative data
          if (showCumulative && item.accumulatedValue) {
            weeklyData[weekKey].accumulatedValue = item.accumulatedValue;
          }
          
          weeklyData[weekKey].count += 1;
        }
        
        // Convert map back to array and sort by week number
        result.length = 0; // Clear the array
        Array.from(Object.entries(weeklyData))
          .sort((a, b) => {
            const weekA = parseInt(a[0].split(' ')[1]);
            const weekB = parseInt(b[0].split(' ')[1]);
            return weekA - weekB;
          })
          .forEach(([_, value]) => {
            result.push(value);
          });
        
        // Debug the aggregated weekly data
        console.log('[DEBUG] Aggregated Weekly Data for Month View:', result);
      } catch (error) {
        console.error('Error aggregating weekly data for month view:', error);
      }
    }
    // Sort the data points for week, month and year views to ensure proper chronological order
    else if ((view === 'week' || view === 'year') && result.length > 0) {
      try {
        if (view === 'week') {
          // Hard-coded day order mapping for week view
          const getDayOrder = (dayName: string): number => {
            switch (dayName) {
              case 'Mon': return 0;
              case 'Tue': return 1;
              case 'Wed': return 2;
              case 'Thu': return 3;
              case 'Fri': return 4;
              case 'Sat': return 5;
              case 'Sun': return 6;
              default: return 999; // Unknown days go last
            }
          };
          
          // Sort by day name
          result.sort((a: any, b: any) => {
            return getDayOrder(a.time) - getDayOrder(b.time);
          });
          
          // Debug the sorted result for week view
          // eslint-disable-next-line no-console
          console.log('[DEBUG] Sorted Week Data:', result.map((day: any) => day.time));
        } else if (view === 'year') {
          // Month order mapping for year view
          const getMonthOrder = (monthName: string): number => {
            const months = {
              'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
              'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
            };
            return (months as Record<string, number>)[monthName] ?? 999;
          };
          
          // Sort by month name
          result.sort((a: any, b: any) => {
            return getMonthOrder(a.time) - getMonthOrder(b.time);
          });
        }
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('[ERROR] Error sorting data:', error);
      }
    }

    // Cache the result
    if (!ConsumptionTab.transformCache) ConsumptionTab.transformCache = {};
    ConsumptionTab.transformCache[cacheKey] = result;

    return result;
  }, [chartPoints, showCumulative, chartTypeState, view, showWeeklyAggregation]);

  // No more mock data usage
  const isUsingMockData = false;
  
  // Calculate metrics based on transformed data to reflect current chart type
  const metrics = useMemo(() => {
    if (!transformedData || transformedData.length === 0) {
      return { 
        totalConsumption: "—", 
        peakDemand: "—", 
        peakDemandTime: null,
        avgLoad: "—" 
      };
    }
    
    // Use the transformed data which already has the correct values based on chartTypeState
    return calculateMetrics(transformedData, chartData);
  }, [transformedData, chartData, chartTypeState]);

  // Skip loading state handling, always render the UI
  const yAxisLabel = showCumulative
    ? "Cumulative Consumption (kWh)"
    : chartTypeState === 'power'
      ? "Power (kW)"
      : "Energy (kWh)";

  const dataKey = showCumulative ? "accumulatedValue" : "actual";
  const chartUnit = chartTypeState === 'power' ? 'kW' : 'kWh';

  // Function to export data as CSV
  const exportDataAsCSV = () => {
    if (!transformedData || transformedData.length === 0) return;
    
    // Get the data in the correct format
    const data = transformedData.map((point: any) => {
      const date = point.fullDate ? new Date(point.fullDate) : new Date();
      return {
        Date: date.toLocaleDateString(),
        Time: date.toLocaleTimeString(),
        Consumption: point.actual.toFixed(2),
        ...(point.predicted ? { Predicted: point.predicted.toFixed(2) } : {}),
        ...(point.accumulatedValue ? { Accumulated: point.accumulatedValue.toFixed(2) } : {})
      };
    });

    // Convert to CSV
    const headers = Object.keys(data[0]).join(',');
    const rows = data.map((row: Record<string, string>) => Object.values(row).join(',')).join('\n');
    const csv = `${headers}\n${rows}`;

    // Create a blob and download
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', `consumption-data-${view}-${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // Function to export data as PDF
  const exportDataAsPDF = () => {
    // This would be implemented with a PDF library
    alert('PDF export functionality would be implemented with a library like jsPDF');
  };

  return (
    <div className="space-y-3">
      {/* Metric cards with proper alignment */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <MetricCard title="Total Consumption" value={metrics.totalConsumption} unit={chartTypeState === 'power' ? 'kW' : 'kWh'} />
        <MetricCard 
          title="Peak Demand" 
          value={metrics.peakDemand} 
          unit="kW"
          subtitle={metrics.peakDemandTime ? `at ${metrics.peakDemandTime}` : undefined}
        />
        <MetricCard title="Average Load" value={metrics.avgLoad} unit="kW" />
      </div>

      <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-300">
        {/* Chart Header */}
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-base font-semibold text-gray-900">
              {getDynamicChartTitle()}
            </h3>
            <p className="text-xs text-gray-500 mt-1">
              {view === 'day' ? 'Hourly breakdown' : 
               view === 'week' ? 'Daily breakdown' :
               view === 'month' ? (showWeeklyAggregation ? 'Weekly breakdown' : 'Daily breakdown') :
               view === 'year' ? 'Monthly breakdown' : ''}
            </p>
          </div>

          <div className="flex items-center gap-3">
            {/* Daily/Weekly toggle for month view */}
            {view === 'month' && (
              <>
                <button
                  onClick={() => setShowWeeklyAggregation(!showWeeklyAggregation)}
                  className="flex items-center gap-2 px-3 py-1.5 text-xs font-medium bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  <svg className="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    {showWeeklyAggregation ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    )}
                  </svg>
                  {showWeeklyAggregation ? 'Show Daily' : 'Show Weekly'}
                </button>
                <div className="h-4 w-px bg-gray-300"></div>
              </>
            )}
            
            {/* Chart type toggle button - disabled when cumulative is enabled */}
            <button
              onClick={() => {
                if (!showCumulative) {
                  const newType = chartTypeState === 'energy' ? 'power' : 'energy';
                  setChartTypeState(newType);
                  if (onChartTypeChange) {
                    onChartTypeChange(newType);
                  }
                }
              }}
              disabled={showCumulative}
              className={`flex items-center gap-2 px-3 py-1.5 text-xs font-medium rounded-lg transition-colors ${
                showCumulative 
                  ? 'bg-gray-50 text-gray-400 cursor-not-allowed' 
                  : 'bg-gray-100 hover:bg-gray-200'
              }`}
            >
              <svg className="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {chartTypeState === 'energy' ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                )}
              </svg>
              {chartTypeState === 'energy' ? 'Show Power' : 'Show Energy'}
            </button>
          </div>
        </div>

        {/* Show message if no data */}
        {(!chartPoints || chartPoints.length === 0) ? (
          <div className="text-center py-10 text-gray-500">
            No real data is available for the selected period.
          </div>
        ) : (
          /* Show chart when data is available */
          <ResponsiveContainer width="100%" height={400}>
            {showCumulative ? (
              <AreaChart data={transformedData} margin={CHART_STYLES.margins.standard}>
                <defs>
                  <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor={CHART_STYLES.area.colors.primary} stopOpacity={0.7} />
                    <stop offset="100%" stopColor={CHART_STYLES.area.colors.primary} stopOpacity={0.1} />
                  </linearGradient>
                </defs>
                <CartesianGrid 
                  strokeDasharray={CHART_STYLES.grid.strokeDasharray}
                  vertical={CHART_STYLES.grid.vertical}
                  stroke={CHART_STYLES.grid.stroke}
                  strokeOpacity={CHART_STYLES.grid.strokeOpacity}
                />
                <XAxis {...getXAxisConfig(view as ViewType)} />
                <YAxis 
                  tick={CHART_STYLES.axis.tick}
                  axisLine={CHART_STYLES.axis.axisLine}
                  tickLine={CHART_STYLES.axis.tickLine}
                >
                  <Label 
                    value={yAxisLabel}
                    angle={CHART_STYLES.axis.yLabel.angle}
                    position={CHART_STYLES.axis.yLabel.position}
                    style={CHART_STYLES.axis.yLabel.style}
                  />
                </YAxis>
                <Tooltip 
                  contentStyle={CHART_STYLES.tooltip.contentStyle}
                  formatter={(value: number) => [`${formatNumber(value)} ${chartUnit}`, 'Cumulative']} 
                />
                <Area 
                  type="monotone" 
                  dataKey={dataKey} 
                  stroke={CHART_STYLES.area.colors.primary}
                  fill="url(#areaGradient)"
                  strokeWidth={CHART_STYLES.area.strokeWidth}
                  isAnimationActive={true}
                  animationDuration={CHART_STYLES.area.animation.duration}
                  name="Cumulative"
                />
              </AreaChart>
            ) : chartTypeState === 'power' ? (
              <LineChart data={transformedData} margin={RECHARTS_LINE_CONFIG.margins}>
                <defs>
                  <linearGradient id="powerAreaGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor={RECHARTS_LINE_CONFIG.line.stroke} stopOpacity={0.25} />
                    <stop offset="100%" stopColor={RECHARTS_LINE_CONFIG.line.stroke} stopOpacity={0.02} />
                  </linearGradient>
                </defs>
                <CartesianGrid 
                  {...RECHARTS_LINE_CONFIG.cartesianGrid}
                />
                <XAxis 
                  {...getXAxisConfig(view as ViewType)}
                  {...RECHARTS_LINE_CONFIG.xAxis}
                />
                <YAxis 
                  {...RECHARTS_LINE_CONFIG.yAxis}
                >
                  <Label 
                    value={yAxisLabel}
                    {...RECHARTS_LINE_CONFIG.yAxis.label}
                  />
                </YAxis>
                <Tooltip 
                  contentStyle={RECHARTS_LINE_CONFIG.tooltip.contentStyle}
                  cursor={RECHARTS_LINE_CONFIG.tooltip.cursor}
                  formatter={(value: number) => [`${formatNumber(value)} ${chartUnit}`, 'Power']} 
                />
                <Line 
                  {...RECHARTS_LINE_CONFIG.line}
                  dataKey={dataKey}
                  name="Power"
                />
                <Area
                  type="monotone"
                  dataKey={dataKey}
                  stroke="none"
                  fill="url(#powerAreaGradient)"
                  {...RECHARTS_LINE_CONFIG.area}
                />
              </LineChart>
            ) : (
              <BarChart 
                data={transformedData} 
                margin={{ top: 20, right: 20, left: 0, bottom: 60 }}
                barSize={view === 'year' ? 50 : view === 'day' ? 16 : view === 'week' ? 28 : 30}
                barGap={3}
                barCategoryGap={view === 'day' ? '25%' : view === 'week' ? '30%' : view === 'month' ? '35%' : '20%'}
              >
                <defs>
                  <linearGradient id="consumptionGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="0%" stopColor={COLORS.primary[500]} stopOpacity={1} />
                    <stop offset="100%" stopColor={COLORS.primary[600]} stopOpacity={1} />
                  </linearGradient>
                </defs>
                <CartesianGrid 
                  strokeDasharray="3 3"
                  vertical={false}
                  stroke="#f3f4f6"
                  strokeOpacity={1}
                />
                <XAxis 
                  {...getXAxisConfig(view as ViewType)}
                  axisLine={{ stroke: '#e5e7eb', strokeWidth: 1 }}
                  tickLine={false}
                  tick={{ 
                    fontSize: 11,
                    fill: '#6b7280',
                    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
                  }}
                  padding={{ left: 20, right: 20 }}
                />
                <YAxis 
                  axisLine={false}
                  tickLine={false}
                  tick={{ 
                    fontSize: 11,
                    fill: '#6b7280',
                    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
                  }}
                  tickFormatter={(value) => {
                    if (value >= 1000000) {
                      return `${(value / 1000000).toFixed(1)}M`;
                    } else if (value >= 1000) {
                      return `${(value / 1000).toFixed(0)}k`;
                    }
                    return value.toString();
                  }}
                  domain={[0, 'auto']}
                  padding={{ top: 20, bottom: 0 }}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'rgba(255, 255, 255, 0.98)',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    padding: '12px',
                    fontSize: '12px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
                  }}
                  cursor={{ fill: 'rgba(59, 130, 246, 0.1)' }}
                  formatter={(value: number) => [`${formatNumber(value)} ${chartUnit}`, 'Consumption']} 
                />
                <Legend
                  verticalAlign="top"
                  align="right"
                  height={40}
                  iconSize={8}
                  iconType="rect"
                  wrapperStyle={{
                    paddingTop: '0px',
                    fontSize: '11px',
                    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
                    color: '#4b5563'
                  }}
                />
                <Bar 
                  dataKey={dataKey} 
                  fill="url(#consumptionGradient)"
                  radius={[6, 6, 0, 0]}
                  animationDuration={600}
                  name="Energy Consumption"
                />
              </BarChart>
            )}
          </ResponsiveContainer>
        )}
      </div>
    </div>
  );
};

// Initialize static caches
ConsumptionTab.mockDataCache = {};
ConsumptionTab.transformCache = {};

export default ConsumptionTab;

// Force clear the mock data cache on every reload (for development only)
if (typeof window !== 'undefined') {
  (window as any).__clearedConsumptionMockCache = (window as any).__clearedConsumptionMockCache || (() => {
    if (ConsumptionTab && ConsumptionTab.mockDataCache) {
      ConsumptionTab.mockDataCache = {};
    }
    return true;
  })();
}