import React, { useMemo } from 'react';
import { ViewType } from '../../types/analytics';

// Local utility function to format numbers
const formatNumber = (value: number, decimalPlaces: number = 1): string => {
  // Ensure we're not dealing with NaN or undefined
  if (isNaN(value) || value === undefined) {
    return '0.0';
  }
  
  return value.toLocaleString('en-US', {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces
  });
};

// --- SVG Icons ---
const BoltIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M13 10V3L4 14h7v7l9-11h-7z" />
  </svg>
);

const PeakIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
  </svg>
);

const GaugeIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8C9.791 8 8 9.791 8 12s1.791 4 4 4 4-1.791 4-4-1.791-4-4-4zm0 6c-1.105 0-2-.895-2-2s.895-2 2-2 2 .895 2 2-.895 2-2 2z" /> {/* Simplified gauge idea */}
    <path strokeLinecap="round" strokeLinejoin="round" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const ArrowUpIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={3}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M5 15l7-7 7 7" />
  </svg>
);

const ArrowDownIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={3}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
  </svg>
);
// --------------- //

interface MetricCardsProps {
  loading: boolean;
  totalConsumption: number;
  peakDemand: number;
  averageLoad: number;
  selectedView: ViewType;
}

// Helper component for individual metric card
interface MetricCardProps {
  title: string;
  value: string;
  unit: string;
  loading: boolean;
  icon: React.ReactNode;
  changePercent: number; // Calculated change
}

const MetricCard: React.FC<MetricCardProps> = React.memo(({ title, value, unit, loading, icon, changePercent }) => {
  const isPositiveChange = changePercent >= 0;
  const changeColor = isPositiveChange ? 'text-green-600' : 'text-red-600';
  const changeBgColor = isPositiveChange ? 'bg-green-100' : 'bg-red-100';
  const ChangeIcon = isPositiveChange ? ArrowUpIcon : ArrowDownIcon;

  return (
    // Updated card styling to match Analytics.tsx
    <div className="bg-white p-6 rounded-lg shadow border border-gray-200">
      {loading ? (
        // Updated Loading Skeleton
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <div className="h-4 w-2/5 bg-gray-200 animate-pulse rounded"></div>
            <div className="h-5 w-5 bg-gray-200 animate-pulse rounded-full"></div>
          </div>
          <div className="h-8 w-3/5 bg-gray-200 animate-pulse rounded"></div>
          <div className="h-5 w-1/4 bg-gray-200 animate-pulse rounded"></div>
        </div>
      ) : (
        // Actual Content
        <>
          <div className="flex items-center justify-between mb-1">
            <p className="text-sm text-gray-500 font-medium">{title}</p>
            <span className="text-gray-400">{icon}</span>
          </div>
          <p className="text-3xl font-semibold text-gray-800 mb-2">
            {value}
            <span className="text-base font-normal text-gray-500 ml-1.5">{unit}</span>
          </p>
          <div className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${changeBgColor} ${changeColor}`}>
            <ChangeIcon />
            <span className="ml-1">{Math.abs(changePercent).toFixed(1)}%</span>
          </div>
        </>
      )}
    </div>
  );
});

const MetricCards: React.FC<MetricCardsProps> = ({
  loading,
  totalConsumption,
  peakDemand,
  averageLoad,
  selectedView
}) => {
  // Helper function to determine the unit based on the view
  const getUnitForView = (view: ViewType): string => {
    switch (view) {
      case 'day':
      case 'week':
        return 'kWh';
      case 'month':
      case 'year':
        return 'MWh';
      case 'multi-year':
        return 'GWh'; // Use Gigawatt-hours for multi-year view
      default:
        return 'kWh';
    }
  };

  // Convert consumption to appropriate unit
  const getFormattedConsumption = (consumption: number, view: ViewType): string => {
    if (consumption === 0) {
      // Safety check for zero values
      return '0.0';
    }
    
    if (view === 'month' || view === 'year') {
      // Convert kWh to MWh
      return formatNumber(consumption / 1000, 2);
    } else if (view === 'multi-year') {
      // Convert kWh to GWh
      return formatNumber(consumption / 1000000, 2);
    }
    
    // For day and week, show with one decimal place
    return formatNumber(consumption, 1);
  };

  // Calculate random changes once using useMemo to prevent recalculation on every render
  const changes = useMemo(() => ({
    consumption: Math.random() * 20 - 10, // Random +/- 10%
    peak: Math.random() * 20 - 10,
    load: Math.random() * 20 - 10,
  }), [loading]); // Recalculate only when loading state changes (i.e., data reloads)

  const consumptionUnit = getUnitForView(selectedView);
  const formattedConsumption = getFormattedConsumption(totalConsumption, selectedView);
  const formattedPeak = formatNumber(peakDemand);
  const formattedLoad = formatNumber(averageLoad);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <MetricCard
        title="Total Energy Consumption"
        value={formattedConsumption}
        unit={consumptionUnit}
        loading={loading}
        icon={<BoltIcon />}
        changePercent={changes.consumption}
      />
      <MetricCard
        title="Peak Demand"
        value={formattedPeak}
        unit="kW"
        loading={loading}
        icon={<PeakIcon />}
        changePercent={changes.peak}
      />
      <MetricCard
        title="Average Load"
        value={formattedLoad}
        unit="kW"
        loading={loading}
        icon={<GaugeIcon />}
        changePercent={changes.load}
      />
    </div>
  );
};

export default React.memo(MetricCards);
