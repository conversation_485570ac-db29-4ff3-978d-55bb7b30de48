import React from 'react';
import { Zap, TrendingUp, Receipt } from 'lucide-react';
import { formatNumber } from '../../lib/utils/formatters';

export interface StatsCardGroupProps {
  totalConsumption: number;
  peakDemand: number;
  costMultiplier?: number;
  comparisonDiff: number;
  comparisonPeriod: string;
  view: 'daily' | 'weekly' | 'monthly' | 'yearly' | 'multi-year';
}

const StatsCardGroup: React.FC<StatsCardGroupProps> = ({
  totalConsumption,
  peakDemand,
  costMultiplier = 4.2,
  comparisonDiff,
  comparisonPeriod,
  view
}) => {
  const unitLabel = view === 'daily' ? 'kW' : 'kWh';
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {/* Total Consumption */}
      <div className="p-4 bg-white rounded-xl border border-gray-100 hover:shadow-sm transition-all duration-200">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-1.5 rounded-lg bg-blue-100/50">
            <Zap size={16} className="text-primary-blue" />
          </div>
          <span className="text-xs text-gray-500 font-medium">Total Consumption</span>
        </div>
        <div className="flex items-baseline gap-1">
          <span className="text-2xl font-bold text-primary-blue">
            {formatNumber(totalConsumption, { decimals: 1 })}
          </span>
          <span className="text-sm text-gray-500">{unitLabel}</span>
        </div>
      </div>

      {/* Peak Demand */}
      <div className="p-4 bg-white rounded-xl border border-gray-100 hover:shadow-sm transition-all duration-200">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-1.5 rounded-lg bg-blue-100/50">
            <TrendingUp size={16} className="text-primary-blue" />
          </div>
          <span className="text-xs text-gray-500 font-medium">Peak Demand</span>
        </div>
        <div className="flex flex-col">
          <div className="flex items-baseline gap-1">
            <span className="text-2xl font-bold text-primary-blue">
              {formatNumber(peakDemand, { decimals: 1 })}
            </span>
            <span className="text-sm text-gray-500">{unitLabel}</span>
          </div>
        </div>
      </div>

      {/* Cost Estimation */}
      <div className="p-4 bg-white rounded-xl border border-gray-100 hover:shadow-sm transition-all duration-200">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-1.5 rounded-lg bg-blue-100/50">
            <Receipt size={16} className="text-primary-blue" />
          </div>
          <span className="text-xs text-gray-500 font-medium">Estimated Cost</span>
        </div>
        <div className="flex items-baseline gap-1">
          <span className="text-2xl font-bold text-primary-blue">
            ฿{formatNumber(totalConsumption * costMultiplier, { decimals: 0 })}
          </span>
        </div>
      </div>

      {/* Comparison */}
      <div className="p-4 bg-white rounded-xl border border-gray-100 hover:shadow-sm transition-all duration-200">
        <div className="flex items-center gap-2 mb-2">
          <div className="p-1.5 rounded-lg bg-blue-100/50">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary-blue">
              <path d="M3 3v18h18"/>
              <path d="m19 9-5 5-4-4-3 3"/>
            </svg>
          </div>
          <span className="text-xs text-gray-500 font-medium">vs {comparisonPeriod}</span>
        </div>
        <div className="flex items-baseline gap-1">
          <span className={`text-2xl font-bold ${comparisonDiff > 0 ? 'text-red-500' : 'text-green-500'}`}>
            {comparisonDiff > 0 ? '+' : ''}{formatNumber(comparisonDiff, { decimals: 1 })}%
          </span>
        </div>
      </div>
    </div>
  );
};

export default StatsCardGroup; 