@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure all Radix UI dropdowns/selects appear above other content */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
}

/* Fix for Radix Select positioning */
[data-radix-select-viewport] {
  max-height: var(--radix-select-content-available-height) !important;
}

/* Ensure select content is always visible */
[role="listbox"] {
  z-index: 9999 !important;
  position: relative !important;
}
@import './styles/animations.css';

/* Force blue colors to render correctly */
.bg-blue-500 {
  background-color: #3b82f6 !important;
}

.bg-blue-600 {
  background-color: #2563eb !important;
}

.bg-blue-700 {
  background-color: #1d4ed8 !important;
}

.bg-blue-800 {
  background-color: #1e40af !important;
}

.hover\:bg-blue-600:hover {
  background-color: #2563eb !important;
}

.hover\:bg-blue-700:hover {
  background-color: #1d4ed8 !important;
}

/* Force gradient colors to render correctly */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important;
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)) !important;
}

.from-blue-500 {
  --tw-gradient-from: #3b82f6 !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgb(59 130 246 / 0)) !important;
}

.from-blue-600 {
  --tw-gradient-from: #2563eb !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgb(37 99 235 / 0)) !important;
}

.to-blue-600 {
  --tw-gradient-to: #2563eb !important;
}

.to-blue-700 {
  --tw-gradient-to: #1d4ed8 !important;
}

.to-blue-800 {
  --tw-gradient-to: #1e40af !important;
}

.hover\:from-blue-600:hover {
  --tw-gradient-from: #2563eb !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgb(37 99 235 / 0)) !important;
}

.hover\:from-blue-700:hover {
  --tw-gradient-from: #1d4ed8 !important;
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgb(29 78 216 / 0)) !important;
}

.hover\:to-blue-700:hover {
  --tw-gradient-to: #1d4ed8 !important;
}

.hover\:to-blue-800:hover {
  --tw-gradient-to: #1e40af !important;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Ensure select dropdowns are properly displayed */
[data-radix-popper-content-wrapper] {
  z-index: 999 !important;
}

/* Fix for select content positioning */
[role="listbox"] {
  max-height: var(--radix-select-content-available-height);
}

/* Animation for login page blobs */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Fix autofill styling for login inputs */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
  -webkit-text-fill-color: #111827 !important;
  box-shadow: 0 0 0 30px white inset !important;
  background-color: white !important;
  color: #111827 !important;
}