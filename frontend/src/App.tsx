import { BrowserRouter as Router, useLocation, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Sidebar } from './components/layout/Sidebar';
import { Header } from './components/layout/Header';
import { AppRoutes } from './routes';
import { useState, useEffect } from 'react';
import { ToastProvider } from './components/ui/ToastProvider';
import { REFRESH_INTERVALS } from './lib/constants/refresh';
import { AutoRefreshProvider, useAutoRefreshContext } from './lib/contexts/AutoRefreshContext';
import { FloatingMockDataToggle } from './components/common/FloatingMockDataToggle';
import { FloatingHelpButton } from './components/common/FloatingHelpButton';
import { apiClient } from './lib/api/enhancedApiClient';

// Create a client
const queryClient = new QueryClient();

// Main app content with access to router context
function AppContent() {
  const location = useLocation();
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);
  const { isAutoRefreshEnabled, toggleAutoRefresh, refreshInterval } = useAutoRefreshContext();
  
  // Check if floating mock toggle should be shown
  // Only show when mock data is enabled in settings (master control)
  const showFloatingMockToggle = apiClient.isMockDataEnabledInSettings();

  // Check if current page is the meter diagram page
  const isMeterDiagramPage = location.pathname === '/meter-diagram';
  
  // Check if current page is the login page
  const isLoginPage = location.pathname === '/login';
  
  // Check if current page is a training page
  const isTrainingPage = location.pathname.startsWith('/training');
  
  // Check authentication with fallback for initial load
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    // Check localStorage on initial mount
    try {
      return localStorage.getItem('isAuthenticated') === 'true';
    } catch {
      return false;
    }
  });
  
  // Re-check authentication on mount and listen for storage changes
  useEffect(() => {
    const checkAuth = () => {
      const authStatus = localStorage.getItem('isAuthenticated') === 'true';
      setIsAuthenticated(authStatus);
    };
    
    // Initial check
    checkAuth();
    
    // Listen for storage changes (from other tabs or manual updates)
    window.addEventListener('storage', checkAuth);
    
    // Also check periodically in case storage event doesn't fire
    const interval = setInterval(checkAuth, 1000);
    
    return () => {
      window.removeEventListener('storage', checkAuth);
      clearInterval(interval);
    };
  }, []);

  // Sync local state with context
  useEffect(() => {
    if (isAutoRefresh !== isAutoRefreshEnabled) {
      setIsAutoRefresh(isAutoRefreshEnabled);
    }
  }, [isAutoRefreshEnabled]);

  // Handle toggle from header
  const handleToggleAutoRefresh = () => {
    toggleAutoRefresh();
    setIsAutoRefresh(!isAutoRefresh);
  };

  // If login page, render without layout
  if (isLoginPage) {
    return <AppRoutes />;
  }
  
  // If not authenticated and not on login page, redirect to login
  if (!isAuthenticated && !isLoginPage) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="min-h-screen bg-[#F9FAFF] flex flex-col lg:flex-row overflow-hidden">
      <Sidebar />
      <div className="flex-1 flex flex-col min-w-0 lg:ml-16 pt-12">
        <Header
          notifications={3}
          isAutoRefresh={isAutoRefreshEnabled}
          onAutoRefreshToggle={handleToggleAutoRefresh}
          refreshInterval={refreshInterval}
        />
        <main className={`flex-1 overflow-auto mt-0 ${isMeterDiagramPage ? 'bg-[#EEEFF9]' : ''}`}>
          <AppRoutes />
        </main>
      </div>
      {!isTrainingPage && showFloatingMockToggle && <FloatingMockDataToggle />}
      <FloatingHelpButton />
      
      {/* AltoTech footer text - bottom right */}
      <div className="fixed bottom-2 right-2 z-40 pointer-events-none">
        <span className="text-[9px] font-light text-gray-300/60">AltoTech</span>
      </div>
    </div>
  );
}

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <ToastProvider>
          <AutoRefreshProvider>
            <AppContent />
          </AutoRefreshProvider>
        </ToastProvider>
      </Router>
    </QueryClientProvider>
  );
}