// Central location for TypeScript interfaces matching backend models

// Based on backend/apps/meters/models.py Meter model
export interface Meter {
    id: number;
    meter_id: string; // Unique identifier (e.g., "METER_001")
    name: string;
    description?: string; // Optional field
    building: number; // Foreign key to Building model (assuming number ID)
    location?: string; // Optional field
    meter_type: 'main' | 'sub_meter'; // Example choices
    latitude?: number; // Optional field
    longitude?: number; // Optional field
    created_at: string; // ISO date string
    updated_at: string; // ISO date string
}

// Based on backend/apps/meters/models.py Threshold model
export interface Threshold {
    id: number;
    meter: number; // ID of the related meter (or could be Meter object if populated)
    parameter: 'power_demand' | 'voltage' | 'current' | 'power_factor' | 'energy_consumption';
    condition: '>' | '<' | '>=' | '<=';
    value: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
    enabled: boolean;
    created_at: string; // ISO date string
    updated_at: string; // ISO date string
}

// Type for creating/updating thresholds (excluding read-only fields)
// Useful for POST/PATCH requests where meter is often derived from URL
export type ThresholdInput = Omit<Threshold, 'id' | 'meter' | 'created_at' | 'updated_at'> & {
    meter?: number; // Can be optionally included if not using nested URL
};

// Add other model interfaces here as needed
