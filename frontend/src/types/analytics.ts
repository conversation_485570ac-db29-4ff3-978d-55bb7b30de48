// Analytics types
export type ViewType = 'day' | 'week' | 'month' | 'year' | 'multi-year';
// Update BuildingType to include 'all'
export type BuildingType = 'A' | 'B' | 'C' | 'all';
export type AnalyticsTab = 'consumption' | 'comparison' | 'performance';

export interface ChartDataPoint {
  time: string;
  demand: number;
  consumption: number;
}

export interface ChartData {
  current: ChartDataPoint[];
  comparison: ChartDataPoint[];
}

export interface PeakDemand {
  value: number;
  time: string;
}

export interface HourlyConsumptionPoint {
  time: string;       // e.g., "00:00", "01:00"
  actual: number;     // kWh
  predicted?: number; // kWh (optional)
  accumulatedValue?: number; // For cumulative views
}

export interface SystemBreakdownItem {
  name: string;
  value: number;
  color: string;
}

export interface AnalyticsData {
  hourlyConsumption: HourlyConsumptionPoint[];
  totalConsumption: number;
  peakDemand: PeakDemand;
  averageLoad: number;
  systemBreakdown?: SystemBreakdownItem[];
}

export interface TransformedDataPoint {
  time: string;
  displayTime: string;
  currentDemand: number;
  previousDemand: number;
  currentConsumption: number;
  previousConsumption: number;
}

export interface AnalyticsState {
  loading: boolean;
  chartData: ChartData;
  systemBreakdown: SystemBreakdownItem[];
  totalConsumption: number;
  peakDemand: number;
  averageLoad: number;
}

export interface AnalyticsProps {
  selectedView: ViewType;
  selectedDate: Date;
  selectedBuilding: BuildingType;
  selectedTab: AnalyticsTab;
  setSelectedView: (view: ViewType) => void;
  setSelectedDate: (date: Date) => void;
  setSelectedBuilding: (building: BuildingType) => void;
  setSelectedTab: (tab: AnalyticsTab) => void;
  analyticsState: AnalyticsState;
  showCumulative: boolean;
  setShowCumulative: (show: boolean) => void;
}
