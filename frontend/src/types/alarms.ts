/**
 * Represents the severity levels for alarms.
 */
export type AlarmSeverity = 'info' | 'warning' | 'critical';

/**
 * Array of possible AlarmSeverity values.
 */
export const SEVERITY_CHOICES: AlarmSeverity[] = ['info', 'warning', 'critical'];

/**
 * Represents the alarm state in AFDD system.
 */
export type AlarmState = 'active' | 'normal';

/**
 * Represents the acknowledgment state in AFDD system.
 */
export type AckState = 'unacknowledged' | 'acknowledged' | 'cleared';

/**
 * Represents the comparison operators for alarm rules.
 */
export type AlarmOperator = '>' | '<' | '>=' | '<=';

/**
 * Represents an Alarm Rule as defined in the backend.
 */
export interface AlarmRule {
  id: number;
  name: string;
  metric: string;
  threshold: number;
  operator: AlarmOperator;
  severity: AlarmSeverity;
  message: string | null;
  is_active: boolean;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  // Additional fields from AFDD API
  category?: string;
  site_name?: string;
  site_id?: string;
  notification_message?: string;
  meters?: any[];
}

/**
 * Represents an AFDD Log entry (replaces ActiveAlarm).
 */
export interface AFDDLog {
  id: number;
  fault: number | { id: number; name: string; category: string; severity: string };
  fault_name: string;
  site: string | { id: string; name: string };
  site_name: string;
  alarm_state: AlarmState;
  ack_state: AckState;
  severity: AlarmSeverity;
  active_at: string; // ISO date string
  normal_at: string | null; // ISO date string
  acknowledged_at: string | null; // ISO date string
  acknowledged_by?: { id: number; username: string; first_name: string; last_name: string } | null;
  duration: number; // Duration in seconds
  is_active: boolean;
  is_acknowledged: boolean;
  is_cleared: boolean;
  message: string;
}

/**
 * Represents an Active Alarm instance (kept for backward compatibility).
 */
export interface ActiveAlarm {
  id: number;
  rule: number; // ID of the AlarmRule
  rule_name: string;
  metric: string;
  severity: AlarmSeverity;
  operator: AlarmOperator;
  threshold: number;
  trigger_value: number;
  trigger_time: string; // ISO date string
  last_checked_time: string; // ISO date string
  message: string | null;
  notification_sent: boolean;
  /** Optional meter identifier for linking */
  meter_name?: string;
}

/**
 * Represents an entry in the Alarm History.
 */
export interface AlarmHistory {
  id: number;
  rule_name: string;
  metric: string;
  severity: AlarmSeverity;
  threshold: number;
  operator: AlarmOperator;
  trigger_time: string; // ISO date string
  trigger_value: number;
  acknowledge_time: string | null; // ISO date string
  acknowledged_by: number | null; // User ID
  acknowledged_by_username: string | null;
  acknowledgment_notes: string | null;
  resolution_time: string | null; // ISO date string
  meter_name?: string; // Optional meter name
  related_meters?: Array<{id: string, name: string}>; // Optional related meters
}

/**
 * Potential fields for creating/updating an AlarmRule.
 * Make non-readonly fields optional for partial updates.
 */
export type AlarmRulePayload = Partial<Omit<AlarmRule, 'id' | 'created_at' | 'updated_at'> & {
  name: string; // Name is required for creation
  metric: string; // Metric is required for creation
  threshold: number; // Threshold is required for creation
  operator: AlarmOperator; // Operator is required for creation
  severity: AlarmSeverity; // Severity is required for creation
}>;

/**
 * Payload for acknowledging an alarm.
 */
export interface AcknowledgePayload {
  notes?: string;
  // Backend will automatically set the user and time
}

/**
 * Paginated response wrapper for AFDD API.
 */
export interface AFDDApiResponse<T> {
  success: boolean;
  status: number;
  message: string;
  metadata: {
    timestamp: string;
    version: string;
    pagination: {
      count: number;
      page: number;
      page_size: number;
      total_pages: number;
      next: string | null;
      previous: string | null;
    };
  };
  data: T[];
}
