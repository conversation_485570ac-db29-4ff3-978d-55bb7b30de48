// Common Types
export type BuildingId = 'A' | 'B' | 'C';
export type FloorNumber = number | 'B';
export type Priority = 'low' | 'medium' | 'high';
export type AlertCategory = 'equipment' | 'threshold' | 'environmental' | 'system';
export type AlertStatus = 'new' | 'pending' | 'resolved';
export type UserRole = 'system_admin' | 'building_manager' | 'facility_operator' | 'tenant_admin' | 'basic_user';
export type MeterType = 'chillerPlant' | 'airSide' | 'lighting' | 'equipment' | 'evCharger' | 'others' | 'light_power' | 'lightPower' | 'data_center_others' | 'data_center_it' | 'escalator_elevator' | 'tenant';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  status: 'active' | 'inactive';
  lastLogin?: string;
  createdAt: string;
}

export interface UserPermission {
  id: string;
  name: string;
  description: string;
  roles: UserRole[];
}
export type Language = 'th' | 'en';

export interface EnvironmentalData {
  dbt: number;  // Dry Bulb Temperature
  wbt: number;  // Wet Bulb Temperature
  humidity: number;
  timestamp: string;
}

export interface EnergyConsumption {
  total: number;
  peak: number;
  co2: number;
  cost: number;
  previousCost: number;
}

export interface FloorData {
  floorNumber: FloorNumber;
  consumption: number;
  status: 'no-data' | 'low' | 'medium' | 'high' | 'critical';
  devices: {
    total: number;
    active: number;
  };
}

export interface Alert {
  id: string;
  priority: Priority;
  category: AlertCategory;
  message: string;
  timestamp: string;
  status: AlertStatus;
}

export interface BillingData {
  tenantId: string;
  floorNumber: FloorNumber;
  building: BuildingId;
  meterId: string;
  currentConsumption: number;
  monthlyConsumption: number;
  monthlyBill: number;
  dueDate: string;
  status: 'new' | 'pending' | 'paid';
}

// Additional types for Analytics
export type ViewType = 'daily' | 'weekly' | 'monthly' | 'yearly' | 'multi-year';
export type BuildingType = 'all' | BuildingId;
export type AnalyticsTab = 'consumption' | 'comparison' | 'performance';

// System breakdown type
export interface SystemBreakdownItem {
  id: string;
  name: string;
  value: number;
  percentage?: number;
  color: string;
}

// Types for Performance Tab / Performance Analytics
export interface PerformanceDataItem {
  time: string;
  actual: number;
  baseline: number;
  target: number;
}

export interface PerformanceMetrics {
  energySavings: number;
  carbonReduction: number;
}

export interface PerformanceData {
  chartData: PerformanceDataItem[];
  metrics: PerformanceMetrics;
}