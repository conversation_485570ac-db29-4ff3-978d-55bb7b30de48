// Define colors directly since we don't have a central colors file
export const COLORS = {
  primary: {
    100: '#e6f0ff', // Lightest blue
    200: '#c9deff',
    300: '#93c5fd', // Light blue
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',  // Primary blue
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a'  // Darkest blue
  },
  secondary: {
    300: '#10b981', // Green
    400: '#059669',
    500: '#047857',
    600: '#065f46'
  },
  accent: {
    300: '#f59e0b', // Amber
    400: '#d97706',
    500: '#b45309',
    600: '#92400e'
  },
  gray: {
    50: '#fafafa',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827'
  }
};

// Common chart styling constants to ensure consistency across all charts
export const CHART_STYLES = {
  // Bar chart styling
  bar: {
    radius: 8, // More pronounced rounding like Linear.app
    barGap: 12,
    barCategoryGap: 20,
    maxBarSize: 60, // Maximum bar size for all charts
    colors: {
      primary: COLORS.primary[500],
      secondary: COLORS.primary[400],
      tertiary: COLORS.secondary[400],
      accent: COLORS.accent[400],
      fillOpacity: 1.0
    },
    // Gradient definitions for bars
    gradient: {
      id: 'barGradient',
      colors: [
        { offset: '0%', color: COLORS.primary[500], opacity: 1.0 },
        { offset: '100%', color: COLORS.primary[600], opacity: 0.95 }
      ]
    },
    // Animation settings
    animation: {
      duration: 600, // Faster animations like Linear
      easing: 'ease-out'
    }
  },

  // Area chart styling
  area: {
    fillOpacity: 0.7,
    strokeWidth: 2,
    colors: {
      primary: COLORS.primary[500],
      secondary: COLORS.secondary[400]
    },
    // Gradient definitions for areas
    gradient: {
      id: 'areaGradient',
      colors: [
        { offset: '0%', color: COLORS.primary[500], opacity: 0.7 },
        { offset: '100%', color: COLORS.primary[100], opacity: 0.1 }
      ]
    },
    // Animation settings
    animation: {
      duration: 800,
      easing: 'ease-in-out'
    }
  },

  // Line chart styling
  line: {
    strokeWidth: 2,
    dot: {
      r: 3, // Smaller dots like Linear
      strokeWidth: 2,
      fill: '#fff'
    },
    colors: {
      primary: COLORS.primary[500],
      secondary: COLORS.secondary[400],
      accent: COLORS.accent[400]
    },
    // Animation settings
    animation: {
      duration: 800,
      easing: 'ease-in-out'
    }
  },

  // Margins for charts (more breathing room)
  margins: {
    standard: { top: 30, right: 30, left: 25, bottom: 50 }
  },

  // Grid styling (more subtle)
  grid: {
    strokeDasharray: "3 6", // More spaced out like Linear
    vertical: false,
    stroke: COLORS.gray[200],
    strokeOpacity: 0.5 // Lighter grid lines like Linear
  },

  // Axis styling (more refined typography)
  axis: {
    tick: {
      fontSize: 11, // Smaller fonts like Linear
      fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
      fill: COLORS.gray[500], // Lighter text for labels like Linear
      dy: 8
    },
    tickLine: {
      stroke: COLORS.gray[200], // Lighter tick lines
      opacity: 0.4 // More subtle
    },
    axisLine: { 
      stroke: COLORS.gray[200], 
      strokeWidth: 1 // Thinner axis lines like Linear
    },
    label: {
      style: {
        textAnchor: 'middle',
        fill: COLORS.gray[600],
        fontSize: 12,
        fontWeight: 500,
        fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
      },
      offset: 40
    },
    xLabel: {
      position: 'insideBottom',
      offset: -10
    },
    yLabel: {
      position: 'insideLeft',
      angle: -90,
      dy: -24,
      dx: -20,
      style: {
        fontSize: 11,
        fill: COLORS.gray[600],
        fontWeight: 500,
        fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
        letterSpacing: '0.5px',
        opacity: 1.0
      }
    }
  },

  // Container styling (more refined shadow and border)
  container: {
    className: "bg-white rounded-xl shadow-sm overflow-x-auto p-5 border border-gray-100"
  },

  // Header styling (better typography)
  header: {
    className: "text-base font-medium text-gray-800 font-sans"
  },

  // Tooltip styling (more elegant, like Linear)
  tooltip: {
    contentStyle: {
      backgroundColor: 'white',
      border: '1px solid #e5e7eb',
      borderRadius: '8px',
      padding: '10px 12px',
      fontSize: '11px',
      boxShadow: '0 4px 8px -2px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"
    }
  },
  
  // Legend styling
  legend: {
    align: 'right',
    verticalAlign: 'top',
    iconSize: 10, // Smaller icon size like Linear
    iconType: 'circle',
    wrapperStyle: {
      paddingBottom: 16,
      fontSize: 11, // Smaller font like Linear
      fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
      color: COLORS.gray[600]
    }
  }
};

export default CHART_STYLES;
