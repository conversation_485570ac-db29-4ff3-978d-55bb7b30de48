import { ViewType } from '../types/analytics';
import { subDays, subWeeks, subMonths, subYears, format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear } from 'date-fns';

// Helper to generate data points with realistic patterns
const generateDataPoints = (
  startDate: Date,
  endDate: Date,
  granularity: 'hour' | 'day' | 'month',
  baseConsumption: number,
  isPower: boolean,
  variationFactor: number
): { time: string, consumption: number }[] => {
  const data = [];
  let currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    // Format date based on granularity
    let formattedDate;
    if (granularity === 'day') {
      // For day granularity, return day number for month view display
      formattedDate = currentDate.getDate().toString();
    } else if (granularity === 'hour') {
      // For hour granularity, return hour
      formattedDate = format(currentDate, 'HH:00');
    } else {
      // For month granularity
      formattedDate = format(currentDate, 'MMM');
    }
    let consumption = baseConsumption;

    // Introduce realistic patterns based on granularity
    if (granularity === 'hour') {
      const hour = currentDate.getHours();

      // Power consumption has peaks in morning and evening
      if (isPower) {
        // Morning peak (7-9 AM)
        if (hour >= 7 && hour <= 9) {
          consumption *= (1.5 + Math.random() * 0.5) * variationFactor;
        }
        // Evening peak (6-10 PM)
        else if (hour >= 18 && hour <= 22) {
          consumption *= (1.8 + Math.random() * 0.7) * variationFactor;
        }
        // Night time (11 PM - 5 AM)
        else if (hour >= 23 || hour <= 5) {
          consumption *= (0.5 + Math.random() * 0.3) * variationFactor;
        }
        // Mid-day (normal usage)
        else {
          consumption *= (0.8 + Math.random() * 0.4) * variationFactor;
        }
      }
      // Energy consumption follows slightly different pattern
      else {
        // Morning usage (6-10 AM)
        if (hour >= 6 && hour <= 10) {
          consumption *= (1.2 + Math.random() * 0.4) * variationFactor;
        }
        // Afternoon usage (11 AM - 5 PM)
        else if (hour >= 11 && hour <= 17) {
          consumption *= (0.9 + Math.random() * 0.3) * variationFactor;
        }
        // Evening usage (6-11 PM)
        else if (hour >= 18 && hour <= 23) {
          consumption *= (1.5 + Math.random() * 0.5) * variationFactor;
        }
        // Night time (midnight - 5 AM)
        else {
          consumption *= (0.4 + Math.random() * 0.2) * variationFactor;
        }
      }
    } else if (granularity === 'day') {
      const day = currentDate.getDay();

      // Weekend vs weekday patterns
      if (day === 0 || day === 6) { // Weekend (Saturday or Sunday)
        consumption *= (1.3 + Math.random() * 0.4) * variationFactor; // Higher on weekends
      } else {
        // Middle of week slightly higher than start/end
        if (day === 2 || day === 3) { // Tuesday or Wednesday
          consumption *= (1.1 + Math.random() * 0.2) * variationFactor;
        } else {
          consumption *= (0.9 + Math.random() * 0.2) * variationFactor;
        }
      }
    } else if (granularity === 'month') {
      const month = currentDate.getMonth();

      // Seasonal variation (higher in extreme temperature months)
      if (month <= 1 || month >= 10) { // Winter months
        consumption *= (1.4 + Math.random() * 0.3) * variationFactor; // Higher in winter
      } else if (month >= 5 && month <= 8) { // Summer months
        consumption *= (1.3 + Math.random() * 0.3) * variationFactor; // Higher in summer
      } else {
        consumption *= (0.8 + Math.random() * 0.2) * variationFactor; // Lower in spring/fall
      }
    }

    // Add some randomness to make it more realistic
    consumption *= (0.9 + Math.random() * 0.2);

    // For power data, ensure more variation throughout the time period
    if (isPower) {
      consumption *= (0.85 + Math.random() * 0.3);
    }

    data.push({
      time: formattedDate,
      fullDate: format(currentDate, 'yyyy-MM-dd'),
      consumption: Number(consumption.toFixed(2))
    });

    // Advance to next interval based on granularity
    if (granularity === 'hour') {
      currentDate.setHours(currentDate.getHours() + 1);
    } else if (granularity === 'day') {
      currentDate.setDate(currentDate.getDate() + 1);
    } else if (granularity === 'month') {
      currentDate.setMonth(currentDate.getMonth() + 1);
    }
  }

  return data;
};

// Generate mock chart data based on the selected view
export const generateMockChartData = (view: ViewType, options?: { isPower?: boolean }): { current: any[], comparison: any[] } => {
  const now = new Date();
  let currentStartDate: Date, currentEndDate: Date;
  let comparisonStartDate: Date, comparisonEndDate: Date;
  let granularity: 'hour' | 'day' | 'month' = 'day'; // Default granularity
  let _baseConsumption = 100; // Default base consumption
  const isPower = options?.isPower || false;

  switch (view) {
    case 'day':
      currentStartDate = startOfDay(now);
      currentEndDate = endOfDay(now);
      comparisonStartDate = startOfDay(subDays(now, 1));
      comparisonEndDate = endOfDay(subDays(now, 1));
      granularity = 'hour';
      _baseConsumption = isPower ? 300 : 300; // Avg kW or kWh per hour
      break;
    case 'week':
      currentStartDate = startOfWeek(now, { weekStartsOn: 1 }); // Monday start
      currentEndDate = endOfWeek(now, { weekStartsOn: 1 });
      comparisonStartDate = startOfWeek(subWeeks(now, 1), { weekStartsOn: 1 });
      comparisonEndDate = endOfWeek(subWeeks(now, 1), { weekStartsOn: 1 });
      granularity = 'day';
      _baseConsumption = isPower ? 400 : 7000; // Avg kW or daily kWh
      break;
    case 'month':
      currentStartDate = startOfMonth(now);
      currentEndDate = endOfMonth(now);
      comparisonStartDate = startOfMonth(subMonths(now, 1));
      comparisonEndDate = endOfMonth(subMonths(now, 1));
      granularity = 'day';
      _baseConsumption = isPower ? 400 : 7000; // Avg kW or daily kWh
      break;
    case 'year':
    default: // Default to year view
      currentStartDate = startOfYear(now);
      currentEndDate = endOfYear(now);
      comparisonStartDate = startOfYear(subYears(now, 1));
      comparisonEndDate = endOfYear(subYears(now, 1));
      granularity = 'month'; // Monthly data points for yearly view
      _baseConsumption = isPower ? 350 : 210000; // Avg kW or monthly kWh
      break;
  }

  // Special handling for week view to ensure proper day order
  if (view === 'week') {
    const weekData = [];
    
    for (let i = 0; i < 7; i++) {
      const dayDate = new Date(currentStartDate);
      dayDate.setDate(currentStartDate.getDate() + i);
      
      // Format the day name (Mon, Tue, etc.)
      const dayName = format(dayDate, 'EEE');
      // Full date format for reference
      const fullDate = format(dayDate, 'yyyy-MM-dd');
      // Numerical sequence for explicit ordering (0-6)
      const sequence = i;

      // Generate consumption value with realistic patterns
      let consumption = _baseConsumption;
      const day = dayDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

      // Weekend vs weekday patterns
      if (day === 0 || day === 6) { // Weekend (Saturday or Sunday)
        consumption *= (1.3 + Math.random() * 0.4) * 1; // Higher on weekends
      } else {
        // Middle of week slightly higher than start/end
        if (day === 2 || day === 3) { // Tuesday or Wednesday
          consumption *= (1.1 + Math.random() * 0.2) * 1;
        } else {
          consumption *= (0.9 + Math.random() * 0.2) * 1;
        }
      }

      // Add some randomness
      consumption *= (0.9 + Math.random() * 0.2);

      // For power data, ensure more variation
      if (isPower) {
        consumption *= (0.85 + Math.random() * 0.3);
      }

      weekData.push({
        time: dayName, // Display name for the x-axis
        fullDate, // Full date for reference
        sequence, // Explicit numerical ordering (important for chart sorting)
        consumption: Number(consumption.toFixed(2))
      });
    }

    // Sort the data by sequence to ensure chronological order
    weekData.sort((a, b) => a.sequence - b.sequence);
    
    return {
      current: weekData,
      comparison: weekData.map((item) => ({ ...item, consumption: Number((item.consumption * 1.05).toFixed(2)) })),
    };
  }

  // For other views, generate data points as before
  const currentData = generateDataPoints(currentStartDate, currentEndDate, granularity, _baseConsumption, isPower, 1);

  // Add slight variation for comparison data (previous period typically has different pattern)
  const comparisonData = generateDataPoints(
    comparisonStartDate,
    comparisonEndDate,
    granularity,
    _baseConsumption * (isPower ? 1.05 : 1.08), // Previous period was slightly higher
    isPower,
    1
  );

  return {
    current: currentData,
    comparison: comparisonData,
  };
};

// Generate mock data specifically for the ConsumptionTab (no comparison)
export const generateMockConsumptionData = (view: ViewType, options?: { isPower?: boolean }): Array<{ time: string, consumption: number }> => {
  const isPower = options?.isPower || false;
  const baseConsumption = isPower ? 4.5 : 25; // Lower base value for power (kW) vs energy (kWh)
  const variationFactor = isPower ? 1.5 : 2.5; // Power has less variation than energy

  const currentDate = new Date();

  let startDate = new Date();
  let endDate = new Date();
  let granularity: 'hour' | 'day' | 'month' = 'hour';

  // Set date range and granularity based on view
  switch (view) {
    case 'day':
      // For day view: set range to current day
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
      granularity = 'hour';
      break;
    case 'week':
      // For week view: set range to current week (Monday to Sunday)
      // Use weekStartsOn: 1 to ensure Monday is the first day
      startDate = startOfWeek(currentDate, { weekStartsOn: 1 }); // Monday start
      endDate = endOfWeek(currentDate, { weekStartsOn: 1 }); // Sunday end
      granularity = 'day';
      break;
    case 'month':
      // For month view: set range to current month
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(startDate.getMonth() + 1);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
      granularity = 'day';
      break;
    case 'year':
      // For year view: set range to current year
      startDate.setMonth(0, 1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(11, 31);
      endDate.setHours(23, 59, 59, 999);
      granularity = 'month';
      break;
    case 'multi-year':
      // For multi-year view: generate 5 years of annual consumption with smooth growth
      const multiYearData: { time: string; fullDate: string; consumption: number }[] = [];
      const currentYear = currentDate.getFullYear();
      const yearsCount = 5;
      const growthRate = 0.03; // 3% annual growth
      const initialConsumption = isPower ? 300 : 150000; // baseline: 300 kW or 150,000 kWh
      for (let idx = 0; idx < yearsCount; idx++) {
        const year = currentYear - (yearsCount - 1 - idx);
        const trend = Math.pow(1 + growthRate, idx);
        const randomFactor = 0.95 + Math.random() * 0.1;
        const consumption = Math.round(initialConsumption * trend * randomFactor);
        multiYearData.push({ time: `${year}`, fullDate: `${year}-01-01`, consumption });
      }
      return multiYearData;
  }

  // Special handling for week view to ensure proper day order
  if (view === 'week') {
    const weekData = [];
    
    for (let i = 0; i < 7; i++) {
      const dayDate = new Date(startDate);
      dayDate.setDate(startDate.getDate() + i);
      
      // Format the day name (Mon, Tue, etc.)
      const dayName = format(dayDate, 'EEE');
      // Full date format for reference
      const fullDate = format(dayDate, 'yyyy-MM-dd');
      // Numerical sequence for explicit ordering (0-6)
      const sequence = i;

      // Generate consumption value with realistic patterns
      let consumption = baseConsumption;
      const day = dayDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

      // Weekend vs weekday patterns
      if (day === 0 || day === 6) { // Weekend (Saturday or Sunday)
        consumption *= (1.3 + Math.random() * 0.4) * variationFactor; // Higher on weekends
      } else {
        // Middle of week slightly higher than start/end
        if (day === 2 || day === 3) { // Tuesday or Wednesday
          consumption *= (1.1 + Math.random() * 0.2) * variationFactor;
        } else {
          consumption *= (0.9 + Math.random() * 0.2) * variationFactor;
        }
      }

      // Add some randomness
      consumption *= (0.9 + Math.random() * 0.2);

      // For power data, ensure more variation
      if (isPower) {
        consumption *= (0.85 + Math.random() * 0.3);
      }

      weekData.push({
        time: dayName, // Display name for the x-axis
        fullDate, // Full date for reference
        sequence, // Explicit numerical ordering (important for chart sorting)
        consumption: Number(consumption.toFixed(2))
      });
    }

    // Sort the data by sequence to ensure chronological order
    weekData.sort((a, b) => a.sequence - b.sequence);
    
    return weekData;
  }

  // For other views, generate data points as before
  return generateDataPoints(
    startDate,
    endDate,
    granularity,
    baseConsumption,
    isPower,
    variationFactor
  );
};
