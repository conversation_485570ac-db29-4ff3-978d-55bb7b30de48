/**
 * Utility functions for formatting dates in various contexts
 */

/**
 * Format a date for display in chart titles
 * @param date The date to format
 * @returns Formatted date string (e.g., "April 13, 2023")
 */
export function formatDateForTitle(date: Date): string {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

/**
 * Format a week range for display in chart titles
 * @param date A date within the week
 * @returns Formatted week range string (e.g., "April 10-16, 2023")
 */
export function formatWeekRangeForTitle(date: Date): string {
  const startOfWeek = new Date(date);
  // Adjust to the start of the week (Sunday)
  startOfWeek.setDate(date.getDate() - date.getDay());
  
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);
  
  // If the start and end dates are in the same month and year
  if (startOfWeek.getMonth() === endOfWeek.getMonth() && 
      startOfWeek.getFullYear() === endOfWeek.getFullYear()) {
    return `${startOfWeek.toLocaleDateString('en-US', { month: 'long' })} ${startOfWeek.getDate()}-${endOfWeek.getDate()}, ${startOfWeek.getFullYear()}`;
  }
  
  // If the dates span different months but same year
  if (startOfWeek.getFullYear() === endOfWeek.getFullYear()) {
    return `${startOfWeek.toLocaleDateString('en-US', { month: 'short' })} ${startOfWeek.getDate()} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short' })} ${endOfWeek.getDate()}, ${startOfWeek.getFullYear()}`;
  }
  
  // If the dates span different years
  return `${startOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })} - ${endOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
}

/**
 * Format a month for display in chart titles
 * @param date A date within the month
 * @returns Formatted month string (e.g., "April 2023")
 */
export function formatMonthForTitle(date: Date): string {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long'
  });
}

/**
 * Format a year for display in chart titles
 * @param date A date within the year
 * @returns Formatted year string (e.g., "2023")
 */
export function formatYearForTitle(date: Date): string {
  return date.toLocaleDateString('en-US', {
    year: 'numeric'
  });
}
