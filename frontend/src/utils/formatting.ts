import { ViewType } from '../types/analytics';

/**
 * Format number with thousand separators
 */
export const formatNumber = (value: number): string => {
  return value.toLocaleString('en-US', { maximumFractionDigits: 1 });
};

/**
 * Format number with comma separators (alias for compatibility)
 */
export const formatNumberWithCommas = (value: number): string => {
  return value.toLocaleString('en-US');
};

/**
 * Format X-axis tick labels based on the view type
 */
export const formatAxisTick = (tick: string | Date, viewType: ViewType): string => {
  if (!tick) return '';

  try {
    // For month view, handle all cases directly first
    if (viewType === 'month') {
      // Handle Week format for aggregated data
      if (typeof tick === 'string' && tick.startsWith('Week ')) {
        return tick; // Return "Week X" as is
      }
      
      // If it's already a number string, validate it's a proper day (1-31)
      if (typeof tick === 'string' && !isNaN(Number(tick))) {
        const day = parseInt(tick, 10);
        if (!isNaN(day) && day >= 1 && day <= 31) {
          return day.toString();
        }
      }
      
      // If it's a Date object or can be parsed as a date
      let date: Date | null = null;
      
      if (tick instanceof Date) {
        date = tick;
      } else if (typeof tick === 'string') {
        try {
          // Try to parse as a date if it looks like a date string
          if (tick.includes('-') || tick.includes('/')) {
            date = new Date(tick);
          }
        } catch (e) {
          console.error('Error parsing date:', e);
        }
      }
      
      // Extract day from date if we have one
      if (date && !isNaN(date.getTime())) {
        const day = date.getDate();
        if (day >= 1 && day <= 31) {
          return day.toString();
        }
      }
      
      // Last resort: try to extract a number from the beginning of the string
      if (typeof tick === 'string') {
        const match = tick.match(/^(\d+)/);
        if (match && match[1]) {
          const day = parseInt(match[1], 10);
          if (!isNaN(day) && day >= 1 && day <= 31) {
            return day.toString();
          }
        }
      }
      
      // If all else fails, return a placeholder
      return '?';
    }
    
    // Handle Date objects directly
    if (tick instanceof Date) {
      switch (viewType) {
        case 'day':
          return tick.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
        case 'week':
          return tick.toLocaleDateString('en-US', { weekday: 'short', day: 'numeric' });
        case 'year':
          return tick.toLocaleDateString('en-US', { month: 'short' });
        case 'multi-year':
          return tick.getFullYear().toString();
        default:
          return tick.toLocaleDateString();
      }
    }

    // For hour-based ticks (likely in day view)
    if (typeof tick === 'string' && tick.includes(':')) {
      return tick; // Already formatted as HH:MM
    }

    // When tick is a date string or can be parsed as a date
    if (typeof tick === 'string' && (tick.includes('-') || !isNaN(Date.parse(tick)))) {
      const date = new Date(tick);

      switch (viewType) {
        case 'day':
          return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
        case 'week':
          return date.toLocaleDateString('en-US', { weekday: 'short', day: 'numeric' });
        case 'year':
          return date.toLocaleDateString('en-US', { month: 'short' });
        case 'multi-year':
          return date.getFullYear().toString();
        default:
          return date.toLocaleDateString();
      }
    }

    // Default fallback - return the tick as is
    return String(tick);
  } catch (e) {
    console.error('Error formatting axis tick:', e);
    return String(tick);
  }
};

/**
 * Format a timestamp in the standard "8 Apr 2025, 6:24 PM" format
 */
export const formatTimestamp = (date: Date): string => {
  const day = date.getDate();
  const month = date.toLocaleDateString('en-US', { month: 'short' });
  const year = date.getFullYear();
  const time = date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  return `${day} ${month} ${year}, ${time}`;
};

/**
 * Get appropriate unit label based on view type
 */
export const getUnitLabelForView = (viewType: ViewType): string => {
  switch (viewType) {
    case 'day':
      return 'kWh (Hourly)';
    case 'week':
      return 'kWh (Daily)';
    case 'month':
      return 'kWh (Daily)';
    case 'year':
      return 'kWh (Monthly)';
    case 'multi-year':
      return 'kWh (Yearly)';
    default:
      return 'kWh';
  }
};

/**
 * Get the appropriate X-axis label based on view type
 */
export const getViewLabel = (viewType: ViewType): string => {
  switch (viewType) {
    case 'day':
      return 'Hour';
    case 'week':
      return 'Day';
    case 'month':
      return 'Day';
    case 'year':
      return 'Month';
    case 'multi-year':
      return 'Year';
    default:
      return 'Time';
  }
};
