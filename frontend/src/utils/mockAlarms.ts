import { AlarmOperator, AlarmRule, ActiveAlarm, AlarmHistory, AlarmSeverity } from '@/types/alarms';
import { PaginatedResponse } from '@/types/api';
import { format, subDays, subHours } from 'date-fns';

// Mock metrics that can trigger alarms
const METRICS = [
  'power_consumption',
  'energy_consumption',
  'voltage',
  'power_factor',
  'current'
];

// Units for each metric
export const METRIC_UNITS: Record<string, string> = {
  'power_consumption': 'kW',
  'energy_consumption': 'kWh',
  'voltage': 'V',
  'power_factor': '',
  'current': 'A'
};

// Array of possible messages for each severity level
const MESSAGES: Record<AlarmSeverity, string[]> = {
  'INFO': [
    'System is operating outside of optimal range',
    'Value approaching warning threshold',
    'Monitoring recommended',
    'Normal operation with slight deviation'
  ],
  'WARNING': [
    'System performance degraded',
    'Immediate attention recommended',
    'Value exceeding normal operation parameters',
    'Potential issue detected'
  ],
  'CRITICAL': [
    'Immediate action required',
    'Critical system threshold exceeded',
    'System at risk of failure',
    'Serious anomaly detected',
    'Emergency response needed'
  ]
};

// Generate a set of mock alarm rules
export const generateMockAlarmRules = (count = 15): AlarmRule[] => {
  const rules: AlarmRule[] = [];

  for (let i = 1; i <= count; i++) {
    const metricIndex = i % METRICS.length;
    const metric = METRICS[metricIndex];

    // Determine severity pattern: more INFOS than WARNINGS and more WARNINGS than CRITICALS
    let severity: AlarmSeverity;
    if (i % 6 === 0) {
      severity = 'CRITICAL';
    } else if (i % 3 === 0) {
      severity = 'WARNING';
    } else {
      severity = 'INFO';
    }

    // Generate thresholds that make sense for the metric
    let threshold = 0;
    switch (metric) {
      case 'power_consumption':
        threshold = Math.round(300 + Math.random() * 700); // 300-1000 kW
        break;
      case 'energy_consumption':
        threshold = Math.round(5000 + Math.random() * 15000); // 5000-20000 kWh
        break;
      case 'voltage':
        threshold = Math.round(220 + Math.random() * 30); // 220-250 V
        break;
      case 'power_factor':
        threshold = Number((0.75 + Math.random() * 0.2).toFixed(2)); // 0.75-0.95
        break;
      case 'current':
        threshold = Math.round(15 + Math.random() * 35); // 15-50 A
        break;
    }

    // Determine operator based on metric and severity
    let operator: AlarmOperator;
    if (['power_consumption', 'energy_consumption', 'current'].includes(metric)) {
      operator = Math.random() > 0.3 ? '>' : '>=';
    } else if (metric === 'power_factor') {
      operator = Math.random() > 0.5 ? '<' : '<=';
    } else {
      operator = Math.random() > 0.5 ?
        (Math.random() > 0.5 ? '>' : '>=') :
        (Math.random() > 0.5 ? '<' : '<=');
    }

    // Get random message for the severity
    const message = MESSAGES[severity][Math.floor(Math.random() * MESSAGES[severity].length)];

    // Create timestamps
    const createdAt = format(subDays(new Date(), Math.floor(Math.random() * 90)), 'yyyy-MM-dd\'T\'HH:mm:ss');
    const updatedAt = format(subDays(new Date(), Math.floor(Math.random() * 30)), 'yyyy-MM-dd\'T\'HH:mm:ss');

    rules.push({
      id: i,
      name: `${severity.charAt(0)}${severity.slice(1).toLowerCase()} ${metric.split('_').join(' ')} threshold`,
      metric,
      threshold,
      operator,
      severity,
      message: `${message} (${threshold}${METRIC_UNITS[metric]})`,
      is_active: Math.random() > 0.2, // 80% active
      created_at: createdAt,
      updated_at: updatedAt
    });
  }

  return rules;
};

// Mock meter names for our alarms
const MOCK_METERS = [
  'Main Incomer',
  'Chiller Plant',
  'AHU-01',
  'AHU-02',
  'Lighting Panel L1',
  'Lighting Panel L2',
  'Power Panel P1',
  'Power Panel P2',
  'Elevator Motor',
  'Escalator',
  'HVAC Controller',
  'Building Management System',
  'Emergency Generator',
  'Solar Inverter'
];

// Generate a set of active alarms based on the rules
export const generateMockActiveAlarms = (rules: AlarmRule[], count = 10): ActiveAlarm[] => {
  const alarms: ActiveAlarm[] = [];

  // We'll use a subset of the rules for active alarms
  const selectedRules = [...rules]
    .filter(rule => rule.is_active)
    .sort(() => Math.random() - 0.5)
    .slice(0, Math.min(count, rules.length));

  selectedRules.forEach((rule, index) => {
    // Value that triggered the alarm should be related to the threshold and operator
    let triggerValue: number;
    if (rule.operator === '>' || rule.operator === '>=') {
      // For greater than, trigger value should be higher
      triggerValue = rule.threshold * (1 + Math.random() * 0.5);
    } else {
      // For less than, trigger value should be lower
      triggerValue = rule.threshold * (0.5 + Math.random() * 0.4);
    }

    // Format values based on metric type
    switch (rule.metric) {
      case 'power_factor':
        // Power factor is between 0 and 1
        triggerValue = Number((Math.min(1, triggerValue)).toFixed(2));
        break;
      case 'voltage':
        // Voltage typically between 100-250V
        triggerValue = Math.round(220 + (Math.random() * 30 - 15));
        break;
      case 'current':
        // Current in Amperes
        triggerValue = Math.round(triggerValue * 10) / 10;
        break;
      case 'power_consumption':
        // Power in kW
        triggerValue = Math.round(triggerValue * 10) / 10;
        break;
      case 'energy_consumption':
        // Energy in kWh - larger numbers
        triggerValue = Math.round(triggerValue * 100);
        break;
      default:
        triggerValue = Math.round(triggerValue * 100) / 100;
    }

    // Create timestamps
    const triggerTime = format(subHours(new Date(), Math.floor(Math.random() * 72)), 'yyyy-MM-dd\'T\'HH:mm:ss');
    const lastCheckedTime = format(subMinutes(new Date(), Math.floor(Math.random() * 60)), 'yyyy-MM-dd\'T\'HH:mm:ss');

    // Assign a meter name based on the metric type
    let meterName: string;
    if (rule.metric === 'power_consumption' || rule.metric === 'energy_consumption') {
      // For power/energy, use any meter
      meterName = MOCK_METERS[Math.floor(Math.random() * MOCK_METERS.length)];
    } else if (rule.metric === 'voltage') {
      // For voltage, use electrical panels or main incomer
      const electricalMeters = ['Main Incomer', 'Power Panel P1', 'Power Panel P2'];
      meterName = electricalMeters[Math.floor(Math.random() * electricalMeters.length)];
    } else if (rule.metric === 'power_factor') {
      // For power factor, use main incomer or large loads
      const pfMeters = ['Main Incomer', 'Chiller Plant'];
      meterName = pfMeters[Math.floor(Math.random() * pfMeters.length)];
    } else {
      // For other metrics, use any meter
      meterName = MOCK_METERS[Math.floor(Math.random() * MOCK_METERS.length)];
    }

    alarms.push({
      id: index + 1,
      rule: rule.id,
      rule_name: rule.name,
      metric: rule.metric,
      severity: rule.severity,
      operator: rule.operator,
      threshold: rule.threshold,
      trigger_value: triggerValue,
      trigger_time: triggerTime,
      last_checked_time: lastCheckedTime,
      message: rule.message,
      notification_sent: Math.random() > 0.3, // 70% have notifications sent
      meter_name: meterName
    });
  });

  return alarms;
};

// Helper function to subtract minutes for timestamps
function subMinutes(date: Date, minutes: number): Date {
  return new Date(date.getTime() - minutes * 60000);
}

// Helper function to add minutes to a date
function addMinutes(date: Date, minutes: number): Date {
  return new Date(date.getTime() + minutes * 60000);
}

// Generate alarm history entries
export const generateMockAlarmHistory = (rules: AlarmRule[], count = 20): AlarmHistory[] => {
  const history: AlarmHistory[] = [];

  for (let i = 1; i <= count; i++) {
    // Pick a random rule
    const rule = rules[Math.floor(Math.random() * rules.length)];

    // Value that triggered the alarm should be related to the threshold and operator
    let triggerValue: number;
    if (rule.operator === '>' || rule.operator === '>=') {
      // For greater than, trigger value should be higher
      triggerValue = rule.threshold * (1 + Math.random() * 0.5);
    } else {
      // For less than, trigger value should be lower
      triggerValue = rule.threshold * (0.5 + Math.random() * 0.4);
    }

    // Round to 2 decimal places for most metrics
    if (rule.metric !== 'power_factor') {
      triggerValue = Math.round(triggerValue * 100) / 100;
    } else {
      triggerValue = Number(triggerValue.toFixed(2));
    }

    // Create timestamps with realistic patterns
    const daysAgo = Math.floor(Math.random() * 60); // Up to 60 days ago
    const triggerTime = format(subDays(new Date(), daysAgo), 'yyyy-MM-dd\'T\'HH:mm:ss');

    // About 70% of historical alarms are acknowledged
    const isAcknowledged = Math.random() > 0.3;

    let acknowledgeTime = null;
    let acknowledgedBy = null;
    let acknowledgedByUsername = null;
    let acknowledgmentNotes = null;
    let resolutionTime = null;

    if (isAcknowledged) {
      // Acknowledged between 5 minutes and 4 hours after trigger
      const ackMinutesLater = Math.floor(5 + Math.random() * 235);
      acknowledgeTime = format(
        addMinutes(new Date(triggerTime), ackMinutesLater),
        'yyyy-MM-dd\'T\'HH:mm:ss'
      );

      acknowledgedBy = Math.floor(Math.random() * 5) + 1; // User IDs 1-5
      acknowledgedByUsername = ['admin', 'operator', 'engineer', 'manager', 'technician'][acknowledgedBy - 1];

      const noteOptions = [
        'Investigating issue',
        'Monitoring situation',
        'Confirmed equipment malfunction',
        'Scheduled maintenance to resolve',
        'Normal operation, false alarm',
        'Equipment restarted',
        'Applied temporary fix',
        'Referred to service team'
      ];
      acknowledgmentNotes = noteOptions[Math.floor(Math.random() * noteOptions.length)];

      // About 80% of acknowledged alarms are resolved
      if (Math.random() > 0.2) {
        // Resolution happens between 15 minutes and 48 hours after acknowledgment
        const resolutionMinutesLater = Math.floor(15 + Math.random() * 2880);
        resolutionTime = format(
          addMinutes(new Date(acknowledgeTime), resolutionMinutesLater),
          'yyyy-MM-dd\'T\'HH:mm:ss'
        );
      }
    }

    // Assign a meter name based on the metric type (same logic as in generateMockActiveAlarms)
    let meterName: string;
    if (rule.metric === 'power_consumption' || rule.metric === 'energy_consumption') {
      // For power/energy, use any meter
      meterName = MOCK_METERS[Math.floor(Math.random() * MOCK_METERS.length)];
    } else if (rule.metric === 'voltage') {
      // For voltage, use electrical panels or main incomer
      const electricalMeters = ['Main Incomer', 'Power Panel P1', 'Power Panel P2'];
      meterName = electricalMeters[Math.floor(Math.random() * electricalMeters.length)];
    } else if (rule.metric === 'power_factor') {
      // For power factor, use main incomer or large loads
      const pfMeters = ['Main Incomer', 'Chiller Plant'];
      meterName = pfMeters[Math.floor(Math.random() * pfMeters.length)];
    } else {
      // For other metrics, use any meter
      meterName = MOCK_METERS[Math.floor(Math.random() * MOCK_METERS.length)];
    }

    history.push({
      id: i,
      rule_name: rule.name,
      metric: rule.metric,
      severity: rule.severity,
      threshold: rule.threshold,
      operator: rule.operator,
      trigger_time: triggerTime,
      trigger_value: triggerValue,
      acknowledge_time: acknowledgeTime,
      acknowledged_by: acknowledgedBy,
      acknowledged_by_username: acknowledgedByUsername,
      acknowledgment_notes: acknowledgmentNotes,
      resolution_time: resolutionTime,
      meter_name: meterName,
      related_meters: [{ id: String(i), name: meterName }]
    });
  }

  // Sort by trigger time descending (most recent first)
  return history.sort((a, b) =>
    new Date(b.trigger_time).getTime() - new Date(a.trigger_time).getTime()
  );
};

// Function to paginate results
export function paginateResults<T>(data: T[], page = 1, limit = 10): PaginatedResponse<T> {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;

  return {
    count: data.length,
    next: endIndex < data.length ? `?page=${page + 1}&limit=${limit}` : null,
    previous: page > 1 ? `?page=${page - 1}&limit=${limit}` : null,
    results: data.slice(startIndex, endIndex)
  };
}

// Filter and sort functions for active alarms
export function getFilteredAndPaginatedActiveAlarms(
  params: Record<string, string> = {}
): PaginatedResponse<ActiveAlarm> {
  // Filter out temperature and humidity metrics first
  let filteredAlarms = mockActiveAlarms.filter(alarm =>
    !['temperature', 'humidity'].includes(alarm.metric)
  );

  // Apply additional filters
  if (params.severity) {
    filteredAlarms = filteredAlarms.filter(alarm => alarm.severity === params.severity);
  }

  if (params.metric) {
    filteredAlarms = filteredAlarms.filter(alarm => alarm.metric === params.metric);
  }

  // Apply sorting
  if (params.sort) {
    const isDesc = params.order === 'desc';
    const sortKey = params.sort as keyof ActiveAlarm;

    filteredAlarms.sort((a, b) => {
      // Handle date strings
      if (typeof a[sortKey] === 'string' && (a[sortKey] as string).includes('T')) {
        const dateA = new Date(a[sortKey] as string).getTime();
        const dateB = new Date(b[sortKey] as string).getTime();
        return isDesc ? dateB - dateA : dateA - dateB;
      }

      // Handle numbers and strings
      const valueA = a[sortKey];
      const valueB = b[sortKey];

      // Handle null/undefined values
      if (valueA === null || valueA === undefined) return isDesc ? -1 : 1;
      if (valueB === null || valueB === undefined) return isDesc ? 1 : -1;

      if (valueA < valueB) return isDesc ? 1 : -1;
      if (valueA > valueB) return isDesc ? -1 : 1;
      return 0;
    });
  } else {
    // Default sort by trigger_time descending
    filteredAlarms.sort((a, b) =>
      new Date(b.trigger_time).getTime() - new Date(a.trigger_time).getTime()
    );
  }

  // Get page and limit
  const page = params.page ? parseInt(params.page) : 1;
  const limit = params.limit ? parseInt(params.limit) : 10;

  return paginateResults(filteredAlarms, page, limit);
}

// Filter and paginate alarm rules
export function getFilteredAndPaginatedAlarmRules(
  params: Record<string, string> = {}
): PaginatedResponse<AlarmRule> {
  console.log('getFilteredAndPaginatedAlarmRules called with params:', params);
  
  // Filter out temperature and humidity metrics first
  let filteredRules = mockAlarmRules.filter(rule =>
    !['temperature', 'humidity'].includes(rule.metric)
  );

  // Apply additional filters
  if (params.severity) {
    filteredRules = filteredRules.filter(rule => rule.severity === params.severity);
  }

  if (params.is_active) {
    const isActive = params.is_active === 'true';
    filteredRules = filteredRules.filter(rule => rule.is_active === isActive);
  }

  // Apply sorting if ordering parameter is provided
  if (params.ordering) {
    console.log('Sorting by:', params.ordering);
    const isDesc = params.ordering.startsWith('-');
    const sortKey = isDesc ? params.ordering.substring(1) : params.ordering;
    
    filteredRules.sort((a, b) => {
      const aValue = a[sortKey as keyof AlarmRule];
      const bValue = b[sortKey as keyof AlarmRule];
      
      // Handle null/undefined values
      if (aValue === null || aValue === undefined) return isDesc ? -1 : 1;
      if (bValue === null || bValue === undefined) return isDesc ? 1 : -1;
      
      // Sort strings and other values
      if (aValue < bValue) return isDesc ? 1 : -1;
      if (aValue > bValue) return isDesc ? -1 : 1;
      return 0;
    });
    
    console.log('Sorted rules:', filteredRules.map(r => ({ name: r.name, [sortKey]: r[sortKey as keyof AlarmRule] })));
  }

  // Get page and limit
  const page = params.page ? parseInt(params.page) : 1;
  const limit = params.limit ? parseInt(params.limit) : 10;

  return paginateResults(filteredRules, page, limit);
}

// Filter and paginate alarm history
export function getFilteredAndPaginatedAlarmHistory(
  params: Record<string, string> = {}
): PaginatedResponse<AlarmHistory> {
  // Filter out temperature and humidity metrics first
  let filteredHistory = mockAlarmHistory.filter(alarm =>
    !['temperature', 'humidity'].includes(alarm.metric)
  );

  // Apply additional filters
  if (params.severity) {
    filteredHistory = filteredHistory.filter(alarm => alarm.severity === params.severity);
  }

  if (params.metric) {
    filteredHistory = filteredHistory.filter(alarm => alarm.metric === params.metric);
  }

  // Filter by date range if provided
  if (params.start_date) {
    const startDate = new Date(params.start_date).getTime();
    filteredHistory = filteredHistory.filter(alarm =>
      new Date(alarm.trigger_time).getTime() >= startDate
    );
  }

  if (params.end_date) {
    const endDate = new Date(params.end_date).getTime();
    filteredHistory = filteredHistory.filter(alarm =>
      new Date(alarm.trigger_time).getTime() <= endDate
    );
  }

  // Apply sorting
  if (params.sort) {
    const isDesc = params.order === 'desc';
    const sortKey = params.sort as keyof AlarmHistory;

    filteredHistory.sort((a, b) => {
      // Handle date strings
      if (typeof a[sortKey] === 'string' && (a[sortKey] as string).includes('T')) {
        const dateA = new Date(a[sortKey] as string).getTime();
        const dateB = new Date(b[sortKey] as string).getTime();
        return isDesc ? dateB - dateA : dateA - dateB;
      }

      // Handle numbers and strings
      const valueA = a[sortKey];
      const valueB = b[sortKey];

      // Handle null/undefined values
      if (valueA === null || valueA === undefined) return isDesc ? -1 : 1;
      if (valueB === null || valueB === undefined) return isDesc ? 1 : -1;

      if (valueA < valueB) return isDesc ? 1 : -1;
      if (valueA > valueB) return isDesc ? -1 : 1;
      return 0;
    });
  } else {
    // Default sort by trigger_time descending
    filteredHistory.sort((a, b) =>
      new Date(b.trigger_time).getTime() - new Date(a.trigger_time).getTime()
    );
  }

  // Get page and limit
  const page = params.page ? parseInt(params.page) : 1;
  const limit = params.limit ? parseInt(params.limit) : 10;

  return paginateResults(filteredHistory, page, limit);
}

// Export individual alarm related functions
export function getMockAlarmRule(id: number): AlarmRule | null {
  const rule = mockAlarmRules.find(rule => rule.id === id);
  return rule || null;
}

// Create the mock data
const mockAlarmRules = generateMockAlarmRules();
const mockActiveAlarms = generateMockActiveAlarms(mockAlarmRules);
const mockAlarmHistory = generateMockAlarmHistory(mockAlarmRules);

// Import the email client
import { emailApi } from '@/lib/api/emailClient';

// Mock user email for testing
const MOCK_USER_EMAIL = '<EMAIL>';

/**
 * Helper function to get the appropriate unit for a metric
 */
function getUnitForMetric(metric: string): string {
  const metricLower = metric.toLowerCase();

  if (metricLower.includes('power') && !metricLower.includes('factor')) {
    return 'kW';
  }
  if (metricLower.includes('energy')) {
    return 'kWh';
  }
  if (metricLower.includes('voltage')) {
    return 'V';
  }
  if (metricLower.includes('current')) {
    return 'A';
  }
  if (metricLower.includes('frequency')) {
    return 'Hz';
  }
  if (metricLower.includes('factor')) {
    return ''; // Power factor is unitless
  }

  return '';
}

// Function to acknowledge and resolve an alarm
export function acknowledgeAndResolveAlarm(alarmId: string | number): boolean {
  // Find the alarm in the active alarms
  const alarmIndex = mockActiveAlarms.findIndex(alarm => alarm.id.toString() === alarmId.toString());

  if (alarmIndex === -1) {
    console.error(`Alarm with ID ${alarmId} not found in active alarms`);
    return false;
  }

  // Get the alarm before removing it
  const alarm = mockActiveAlarms[alarmIndex];

  // If the alarm is CRITICAL, send an email notification
  if (alarm.severity === 'CRITICAL') {
    // Create a formatted timestamp
    const timestamp = new Date(alarm.trigger_time).toLocaleString();

    // Create the email subject
    const subject = `CRITICAL ALARM: ${alarm.rule_name}`;

    // Create the HTML email body with styling
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="background-color: #f44336; color: white; padding: 15px; border-radius: 5px 5px 0 0; margin-bottom: 20px;">
          <h1 style="margin: 0; font-size: 24px;">Critical Alarm Notification</h1>
        </div>

        <div style="padding: 0 15px;">
          <p style="font-size: 16px; color: #333;">A critical alarm has been triggered in your energy management system:</p>

          <div style="background-color: #f9f9f9; border-left: 4px solid #f44336; padding: 15px; margin: 15px 0;">
            <h2 style="margin-top: 0; color: #d32f2f;">${alarm.rule_name}</h2>
            <p style="margin: 5px 0;"><strong>Meter:</strong> ${alarm.meter_name || 'N/A'}</p>
            <p style="margin: 5px 0;"><strong>Metric:</strong> ${alarm.metric.replace(/_/g, ' ')}</p>
            <p style="margin: 5px 0;"><strong>Trigger Value:</strong> ${alarm.trigger_value} ${getUnitForMetric(alarm.metric)}</p>
            <p style="margin: 5px 0;"><strong>Threshold:</strong> ${alarm.threshold} ${getUnitForMetric(alarm.metric)}</p>
            <p style="margin: 5px 0;"><strong>Time:</strong> ${timestamp}</p>
          </div>

          <p style="font-size: 16px; color: #333;">Please take immediate action to address this issue.</p>

          <div style="margin: 25px 0; text-align: center;">
            <a href="${window.location.origin}/alarms" style="background-color: #2196f3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
              View Alarm Details
            </a>
          </div>

          <p style="color: #666; font-size: 14px; margin-top: 30px; border-top: 1px solid #eee; padding-top: 15px;">
            This is an automated message from the Alto CERO Energy Management System.
            Please do not reply to this email.
          </p>
        </div>
      </div>
    `;

    // Send email notification asynchronously
    emailApi.sendEmail(MOCK_USER_EMAIL, subject, html)
      .then((response) => {
        console.log(`Email notification sent for critical alarm: ${alarm.id}`, response);
      })
      .catch((error: unknown) => {
        console.error('Error sending email notification:', error);
      });
  }

  // Create a history entry for this alarm
  const historyEntry: AlarmHistory = {
    id: mockAlarmHistory.length + 1,
    rule_name: alarm.rule_name,
    metric: alarm.metric,
    severity: alarm.severity,
    threshold: alarm.threshold,
    operator: alarm.operator,
    trigger_time: alarm.trigger_time,
    trigger_value: alarm.trigger_value,
    acknowledge_time: new Date().toISOString(),
    acknowledged_by: 1, // Assuming user ID 1
    acknowledged_by_username: 'admin',
    acknowledgment_notes: 'Acknowledged and resolved via UI',
    resolution_time: new Date().toISOString(),
    meter_name: alarm.meter_name,
    related_meters: [{ id: '1', name: alarm.meter_name || 'Unknown Meter' }]
  };

  // Add to history
  mockAlarmHistory.unshift(historyEntry);

  // Remove from active alarms
  mockActiveAlarms.splice(alarmIndex, 1);

  return true;
}

// Export mock data for use in tests or API mocks
export { mockAlarmRules, mockActiveAlarms, mockAlarmHistory };

// TEMP: Debug mock data shape
console.log('[MOCK DATA] First ActiveAlarm:', mockActiveAlarms[0]);
console.log('[MOCK DATA] First AlarmHistory:', mockAlarmHistory[0]);
