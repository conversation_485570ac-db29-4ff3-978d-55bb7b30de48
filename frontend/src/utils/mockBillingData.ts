import { addMonths, format, subMonths } from 'date-fns';
import { PaginatedResponse } from '@/types/api';

// Types for billing data
export interface BillingRecord {
  id: number;
  billing_period_start: string;
  billing_period_end: string;
  consumption_kwh: number;
  rate_per_kwh: number;
  total_amount: number;
  due_date: string;
  created_at: string;
}

export interface RateConfig {
  id: number;
  name: string;
  rate_per_kwh: number;
  is_active: boolean;
  effective_from: string;
  created_at: string;
  updated_at: string;
}

// Mock data generation
const currentDate = new Date();
export const mockRates: RateConfig[] = [
  {
    id: 1,
    name: 'Rate',
    rate_per_kwh: 3.98,
    is_active: true,
    effective_from: new Date('2025-04-01').toISOString(),
    created_at: new Date('2025-04-01').toISOString(),
    updated_at: new Date('2025-04-01').toISOString(),
  },
  {
    id: 2,
    name: 'Rate',
    rate_per_kwh: 4.27,
    is_active: false,
    effective_from: new Date('2024-02-20').toISOString(),
    created_at: new Date('2024-02-20').toISOString(),
    updated_at: new Date('2024-02-20').toISOString(),
  },
  {
    id: 3,
    name: 'Rate',
    rate_per_kwh: 4.15,
    is_active: false,
    effective_from: new Date('2023-10-12').toISOString(),
    created_at: new Date('2023-10-12').toISOString(),
    updated_at: new Date('2023-10-12').toISOString(),
  },
  {
    id: 4,
    name: 'Rate',
    rate_per_kwh: 3.90,
    is_active: false,
    effective_from: new Date('2022-03-01').toISOString(),
    created_at: new Date('2022-03-01').toISOString(),
    updated_at: new Date('2022-03-01').toISOString(),
  },
  {
    id: 5,
    name: 'Rate',
    rate_per_kwh: 3.65,
    is_active: false,
    effective_from: new Date('2021-09-15').toISOString(),
    created_at: new Date('2021-09-15').toISOString(),
    updated_at: new Date('2021-09-15').toISOString(),
  },
  {
    id: 6,
    name: 'Rate',
    rate_per_kwh: 3.50,
    is_active: false,
    effective_from: new Date('2020-01-10').toISOString(),
    created_at: new Date('2020-01-10').toISOString(),
    updated_at: new Date('2020-01-10').toISOString(),
  }
];

// Generate 12 months of billing history
const generateMockBillingHistory = (): BillingRecord[] => {
  const history: BillingRecord[] = [];
  const baseConsumption = 750; // Base kWh for variation
  
  for (let i = 0; i < 12; i++) {
    const periodEnd = subMonths(currentDate, i);
    const periodStart = subMonths(periodEnd, 1);
    
    // Add some randomness to consumption
    const randomFactor = 0.8 + (Math.random() * 0.4); // Between 0.8 and 1.2
    const consumption = Math.round(baseConsumption * randomFactor);
    
    // Use the active rate
    const activeRate = mockRates.find(r => r.is_active) || mockRates[0];
    
    history.push({
      id: i + 1,
      billing_period_start: periodStart.toISOString(),
      billing_period_end: periodEnd.toISOString(),
      consumption_kwh: consumption,
      rate_per_kwh: activeRate.rate_per_kwh,
      total_amount: Math.round(consumption * activeRate.rate_per_kwh * 100) / 100,
      due_date: addMonths(periodEnd, 1).toISOString(),
      created_at: periodEnd.toISOString(),
    });
  }
  
  return history;
};

const mockBillingHistory = generateMockBillingHistory();

// API mock functions
export function getMockBillingHistory(params: Record<string, string> = {}): PaginatedResponse<BillingRecord> {
  let filteredHistory = [...mockBillingHistory];
  
  // Apply pagination
  const page = parseInt(params.page || '1');
  const limit = parseInt(params.limit || '10');
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  
  return {
    count: filteredHistory.length,
    next: endIndex < filteredHistory.length ? `?page=${page + 1}&limit=${limit}` : null,
    previous: page > 1 ? `?page=${page - 1}&limit=${limit}` : null,
    results: filteredHistory.slice(startIndex, endIndex)
  };
}

export function getMockRateConfigs(): RateConfig[] {
  return mockRates;
}

// Utility functions for billing
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
    minimumFractionDigits: 2
  }).format(amount);
}

export function formatBillingPeriod(startDate: string, endDate: string): string {
  const start = new Date(startDate);
  const end = new Date(endDate);
  return `${format(start, 'MMM d, yyyy')} - ${format(end, 'MMM d, yyyy')}`;
}
