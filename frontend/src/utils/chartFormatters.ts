import { isPeakHour } from '../lib/config/load-profile';

export function getChartTooltipFormatter(styles: any) {
  return (params: any[]) => {
    const time = params[0]?.name || '';
    const demandValue = params.find(p => p.seriesName === 'Building Power Demand')?.value;
    const lastWeekValue = params.find(p => p.seriesName === 'Last Week')?.value;
    
    // Ensure values are numbers
    const demand = typeof demandValue === 'number' ? demandValue : parseFloat(demandValue) || 0;
    const lastWeek = typeof lastWeekValue === 'number' ? lastWeekValue : parseFloat(lastWeekValue) || 0;
    
    const hourStr = time.split(':')[0];
    const hour = parseInt(hourStr, 10);
    const isPeak = !isNaN(hour) && isPeakHour(hour);
    
    // Prevent division by zero and handle edge cases
    let diff = '0.0';
    let diffColor = '#94A3B8'; // neutral color
    if (lastWeek > 0) {
      const diffValue = ((demand - lastWeek) / lastWeek * 100);
      diff = isFinite(diffValue) ? diffValue.toFixed(1) : '0.0';
      diffColor = parseFloat(diff) > 0 ? '#EF4444' : '#10B981';
    } else if (demand > 0) {
      diff = '100.0'; // 100% increase from 0
      diffColor = '#EF4444';
    }

    return `
      <div style="font-weight: 500; margin-bottom: 6px;">
        <span style="font-size: 11px; color: #1E293B;">${time}</span>
        ${isPeak ? '<span style="font-size: 10px; color: #94A3B8; margin-left: 4px; opacity: 0.8;">(Peak Hours Mon-Fri)</span>' : ''}
      </div>
      <!-- ... (rest of the tooltip template) ... -->
    `;
  };
}