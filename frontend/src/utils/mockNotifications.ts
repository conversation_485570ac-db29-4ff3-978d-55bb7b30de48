import { 
  NotificationPreference, 
  NotificationChannel, 
  AlarmSubscription, 
  EmailAlertSettings,
  SeverityLevel
} from '../components/EmailAlerts/EmailAlertSettings';

// Mock notification channels
const mockChannels: NotificationChannel[] = [
  {
    id: 1,
    name: 'Email',
    type: 'email',
    description: 'Email notifications'
  }
];

// Mock notification preferences
const mockPreferences: NotificationPreference[] = [
  {
    id: 1,
    channel: mockChannels[0],
    category: 'Alarms',
    category_display: 'Alarm Notifications',
    is_enabled: true,
    enabled_severities: ['critical', 'high', 'medium'] as SeverityLevel[]
  },
  {
    id: 2,
    channel: mockChannels[0],
    category: 'System',
    category_display: 'System Notifications',
    is_enabled: true,
    enabled_severities: null
  },
  {
    id: 3,
    channel: mockChannels[0],
    category: 'Reports',
    category_display: 'Report Deliveries',
    is_enabled: false,
    enabled_severities: null
  }
];

// Mock alarm subscriptions
const mockAlarmSubscriptions: AlarmSubscription[] = [
  {
    id: 1,
    name: 'Operations Team',
    category: 'consumption_spike',
    category_display: 'Consumption Spike',
    severity: 'high',
    severity_display: 'High',
    device: 'meter-001',
    parameter: 'kWh',
    condition: '>',
    threshold_value: 1000
  },
  {
    id: 2,
    name: 'Management',
    category: 'device_offline',
    category_display: 'Device Offline',
    severity: 'critical',
    severity_display: 'Critical',
    device: null,
    parameter: null,
    condition: '=',
    threshold_value: 1
  },
  {
    id: 3,
    name: 'Technical Support',
    category: 'power_quality',
    category_display: 'Power Quality Issue',
    severity: 'medium',
    severity_display: 'Medium',
    device: 'inverter-002',
    parameter: 'voltage',
    condition: '<',
    threshold_value: 200
  }
];

// Complete mock email settings data
export const mockEmailSettings: EmailAlertSettings = {
  channels: mockChannels,
  preferences: mockPreferences,
  alarm_subscriptions: mockAlarmSubscriptions
};

// Mock API response handlers
export const getMockEmailSettings = (): EmailAlertSettings => {
  return mockEmailSettings;
};

export const updateMockPreference = (preferenceId: number, updatedFields: Partial<NotificationPreference>): NotificationPreference => {
  // Find the preference to update
  const preferenceIndex = mockPreferences.findIndex(p => p.id === preferenceId);
  if (preferenceIndex === -1) {
    throw new Error('Preference not found');
  }

  // Update the preference
  const updatedPreference = {
    ...mockPreferences[preferenceIndex],
    ...updatedFields
  };

  // Update the mock data
  mockPreferences[preferenceIndex] = updatedPreference;
  
  // Return the updated preference
  return updatedPreference;
};

export const sendMockTestEmail = (): { message: string } => {
  return { message: 'Test email sent successfully (mock)!' };
};

export const runMockAnomalyScan = (dryRun: boolean): any => {
  return {
    dry_run: dryRun,
    timestamp: new Date().toISOString(),
    scan_duration_ms: 1250,
    total_anomalies: 3,
    alerts_sent: dryRun ? 0 : 2,
    details: [
      {
        type: 'meter_data_gap',
        meter_id: 'meter-123',
        description: 'No data received in last 4 hours',
        severity: 'medium'
      },
      {
        type: 'consumption_spike',
        meter_id: 'meter-456',
        description: 'Consumption spike of 200% compared to baseline',
        severity: 'high'
      },
      {
        type: 'sensor_fault',
        meter_id: 'meter-789',
        description: 'Temperature sensor reading out of expected range',
        severity: 'low'
      }
    ],
    actions_taken: dryRun ? [] : [
      'Sent 2 notification emails',
      'Created 3 system logs',
      'Triggered auto-retry for meter-123'
    ]
  };
};

export interface Recipient {
  id: string;
  name: string;
  email: string;
  enabled: boolean;
  initials: string;
}

export const getMockRecipients = (): Recipient[] => [
  { id: '1', name: 'John Smith', email: '<EMAIL>', enabled: true, initials: 'JS' },
  { id: '2', name: 'Sarah Parker', email: '<EMAIL>', enabled: true, initials: 'SP' },
  { id: '3', name: 'David Chen', email: '<EMAIL>', enabled: false, initials: 'DC' }
];

export const updateRecipientStatus = (id: string, enabled: boolean): void => {
  console.log(`Recipient ${id} status updated to ${enabled}`);
};

export const addNewRecipient = (name: string, email: string): void => {
  console.log(`New recipient added: ${name} (${email})`);
};
