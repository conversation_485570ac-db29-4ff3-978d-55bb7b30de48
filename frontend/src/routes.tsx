// Routes configuration for the application
import { Routes, Route, Navigate } from 'react-router-dom';
import Dashboard from './pages/Dashboard';
import Analytics from './pages/Analytics';
import Meters from './pages/Meters';
import MeterDetail from './pages/MeterDetail';
import Reports from './pages/Reports';
import Compare from './pages/Compare';
import Settings from './pages/Settings';
import MeterDiagram from './pages/MeterDiagram';
import MeterDiagramTest from './pages/MeterDiagramTest';
import Manual from './pages/Manual';
import Alarms from './pages/Alarms';
import AlarmEmailSettingsNew from './pages/AlarmEmailSettingsNew';
import LiveMeterDemo from './pages/LiveMeterDemo';
import Admin from './pages/Admin';
import AdminRoute from './components/auth/AdminRoute';
import TrainingSlides from './pages/TrainingSlides';
import PresentationViewer from './components/training/PresentationViewer';
import ScrollableSlideViewer from './components/training/ScrollableSlideViewer';
import Login from './pages/Login';
import NotificationsNew from './pages/NotificationsNew';

export function AppRoutes() {
  return (
    <Routes>
      <Route path="/login" element={<Login />} />
      <Route path="/" element={<Dashboard />} />
      <Route path="/analytics" element={<Analytics />} />
      <Route path="/meters" element={<Meters />} />
      <Route path="/meter-detail" element={<MeterDetail />} />
      <Route path="/meter-diagram" element={<MeterDiagram />} />
      <Route path="/meter-diagram-test" element={<MeterDiagramTest />} />
      <Route path="/compare" element={<Compare />} />
      <Route path="/notifications" element={<NotificationsNew />} />
      <Route path="/alarms" element={<Alarms />} />
      <Route path="/alarms/:tab" element={<Alarms />} />
      <Route path="/alarm-email-settings" element={<AlarmEmailSettingsNew />} />
      <Route path="/reports" element={<Reports />} />
      <Route path="/settings" element={<Settings />} />
      <Route path="/manual" element={<Manual />} />
      <Route path="/live-meter-demo" element={<LiveMeterDemo />} />
      <Route path="/admin" element={<AdminRoute><Admin /></AdminRoute>} />
      <Route path="/training" element={<TrainingSlides />} />
      <Route path="/training/:presentationId" element={<PresentationViewer />} />
      <Route path="/training/:presentationId/scroll" element={<ScrollableSlideViewer />} />
      {/* Catch-all route for 404s - redirect to dashboard */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
}