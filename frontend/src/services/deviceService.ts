import axios from 'axios';

// Access the API base URL from environment variables (Vite specific)
// Ensure your .env file at the project root has VITE_DJANGO_API_URL defined.
const API_BASE_URL = import.meta.env.VITE_DJANGO_API_URL;
const API_TOKEN = import.meta.env.VITE_DJANGO_API_ACESS_TOKEN

// Interface for a single device relation item from the API
export interface DeviceRelation {
  id: number; // Relation ID
  tag: string | null;
  parent_device_id: string;
  child_device_id: string;
  // Optional fields if needed later, e.g., parent/child names if added to relation endpoint
  // parent_device_name?: string; 
  // child_device_name?: string;
}

// Interface for detailed device information from /devices/ endpoint
export interface DeviceDetails {
    id: number; // Device DB ID
    device_id: string; // Unique string ID used in relations
    name: string;
    model: string; // Will likely need mapping to MeterType
    metadata: { [key: string]: any }; // Or a more specific type if known
    autopilot: any; // Define further if structure is known
    latest_data?: { [key: string]: any }; // Make latest_data optional
    device_relations?: DeviceRelation[]; // Add optional device_relations
}

// Interface for a device within a Zone, from /zones/ endpoint
export interface ZoneDeviceDetail {
  id: number;
  device_id: string;
  name: string;
  model: string;
  metadata: { [key: string]: any };
  autopilot: any;
  // latest_data is typically not present here, expected to be merged from fetchAllDevices
}

// Interface for a Zone from /zones/ endpoint
export interface Zone {
  id: number;
  device_id: string; // Identifier for the zone itself (e.g., "tower_a_floor_1")
  name: string; // Name of the zone (e.g., "Tower A Floor 1")
  metadata: { [key: string]: any };
  devices: ZoneDeviceDetail[];
}

// Generic interface for the API response structure
export interface ApiResponse<T> {
  success: boolean;
  status: number;
  message: string;
  metadata: {
    timestamp: string;
    version: string;
  };
  data: T;
}

/**
 * Fetches child device relations for a given device model.
 * 
 * @param modelType The type of the model (e.g., 'power_meter').
 * @returns A promise that resolves with an array of device relations.
 */
export const fetchChildDeviceRelationsByModel = async (modelType: string): Promise<DeviceRelation[]> => {
  const endpoint = `${API_BASE_URL}/device_relations/child/?model=${modelType}`;

  try {
    const response = await axios.get<ApiResponse<DeviceRelation[]>>(endpoint, {
      headers: {
        Authorization: `Bearer ${API_TOKEN}`,
      },
    });
    if (response.data && response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to fetch device relations by model');
    }
  } catch (error) {
    console.error('Error fetching child device relations by model:', error);
    if (axios.isAxiosError(error)) {
      throw new Error(`API Error: ${error.response?.status} - ${error.message}`);
    } else {
      throw new Error('An unexpected error occurred while fetching device relations by model.');
    }
  }
};

/**
 * Fetches child device relations for a specific device ID.
 * (Keeping as an example)
 * @param deviceId The ID of the parent device.
 * @returns A promise that resolves with an array of device relations.
 */
export const fetchChildDeviceRelationsById = async (deviceId: string): Promise<DeviceRelation[]> => {
  const endpoint = `${API_BASE_URL}/device_relations/child/${deviceId}`;

  try {
    const response = await axios.get<ApiResponse<DeviceRelation[]>>(endpoint, {
      headers: {
        Authorization: `Bearer ${API_TOKEN}`,
      },
    });
    if (response.data && response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to fetch device relations for device ID');
    }
  } catch (error) {
    // console.error still logs the original error for visibility if needed for other types of errors
    // console.error(`Trace: Error fetching child device relations for ID ${deviceId}:`, error); 
    if (axios.isAxiosError(error)) {
      if (error.response?.status === 404) {
        // If 404, it means no child relations found, which is a valid empty state.
        // console.log(`No child relations found for device ID ${deviceId} (404), returning empty array.`);
        return []; // Return an empty array instead of throwing an error.
      }
      // For other Axios errors, re-throw a more specific error.
      throw new Error(`API Error: ${error.response?.status} - ${error.response?.data?.message || error.message}`);
    } else {
      // For non-Axios errors.
      throw new Error('An unexpected error occurred while fetching device relations for device ID.');
    }
  }
};

/**
 * Fetches all devices. Can optionally expand related data like 'latest_data' or 'device_relation'.
 * 
 * @param expandOptions Optional array of strings specifying which related fields to expand (e.g., ['latest_data', 'device_relation']).
 * @returns A promise that resolves with an array of device details.
 */
export const fetchAllDevices = async (expandOptions?: string[]): Promise<DeviceDetails[]> => {
    let endpoint = `${API_BASE_URL}/devices/`;

    if (expandOptions && expandOptions.length > 0) {
        endpoint += `?expand=${expandOptions.join(',')}`;
    }

    try {
        const response = await axios.get<ApiResponse<DeviceDetails[]>>(endpoint, {
          headers: {
            Authorization: `Bearer ${API_TOKEN}`,
          },
        });
        if (response.data && response.data.success) {
            return response.data.data;
        } else {
            throw new Error(response.data.message || 'Failed to fetch devices');
        }
    } catch (error) {
        console.error('Error fetching all devices:', error);
        if (axios.isAxiosError(error)) {
            throw new Error(`API Error: ${error.response?.status} - ${error.message}`);
        } else {
            throw new Error('An unexpected error occurred while fetching devices.');
        }
    }
};

/**
 * Fetches details for a single device by its string device_id.
 * Can optionally expand related data like 'latest_data' or 'device_relation'.
 * 
 * @param deviceId The string ID of the device (e.g., 'C-8DB1').
 * @param expandOptions Optional array of strings specifying which related fields to expand.
 * @returns A promise that resolves with the device details.
 */
export const fetchDeviceById = async (deviceId: string, expandOptions?: string[]): Promise<DeviceDetails> => {
  let endpoint = `${API_BASE_URL}/devices/${deviceId}/`; // Use device_id directly in URL

  if (expandOptions && expandOptions.length > 0) {
    endpoint += `?expand=${expandOptions.join(',')}`;
  }

  try {
    const response = await axios.get<ApiResponse<DeviceDetails>>(endpoint, {
      headers: {
        Authorization: `Bearer ${API_TOKEN}`,
      },
    });
    if (response.data && response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || `Failed to fetch device ${deviceId}`);
    }
  } catch (error) {
    console.error(`Error fetching device ${deviceId}:`, error);
    if (axios.isAxiosError(error)) {
      throw new Error(`API Error fetching device ${deviceId}: ${error.response?.status} - ${error.message}`);
    } else {
      throw new Error(`An unexpected error occurred while fetching device ${deviceId}.`);
    }
  }
};

// New function to fetch zones with their devices
export const fetchZonesWithDevices = async (): Promise<Zone[]> => {
  const endpoint = `${API_BASE_URL}/zones/?expand=devices`;
  try {
    const response = await axios.get<ApiResponse<Zone[]>>(endpoint, {
      headers: {
        Authorization: `Bearer ${API_TOKEN}`, // Token from user
      },
    });
    if (response.data && response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to fetch zones with devices');
    }
  } catch (error) {
    console.error('Error fetching zones with devices:', error);
    if (axios.isAxiosError(error)) {
      throw new Error(`API Error: ${error.response?.status} - ${error.message}`);
    } else {
      throw new Error('An unexpected error occurred while fetching zones with devices.');
    }
  }
};

// Add more service functions here as needed 