import axios from 'axios';

// Access the API base URL from environment variables (Vite specific)
// Ensure your .env file at the project root has VITE_DJANGO_API_URL defined.
const API_BASE_URL = import.meta.env.VITE_DJANGO_API_URL;
const API_TOKEN = import.meta.env.VITE_DJANGO_API_ACESS_TOKEN; // Same token as deviceService

// Generic API Response (can be shared if already defined elsewhere)
export interface ApiResponse<T> {
  success: boolean;
  status: number;
  message: string;
  metadata: {
    timestamp: string;
    version: string;
  };
  data: T;
}

// Request Body Interface
export interface HistoricalDataQuery {
  table_name: string; // e.g., "statistic_data_1day", "energy_data_1day"
  site_id: string;
  device_id: string | string[]; // Modified to accept string or array of strings
  datapoints: string[];
  start_timestamp: string; // ISO 8601 format, e.g., "2024-04-03T17:00:00.000Z"
  end_timestamp: string;   // ISO 8601 format
}

// Response Record Types
export interface StatisticalRecord {
  timestamp: string;
  start_datetime: string;
  end_datetime: string;
  mean_value: number;
  max_value: number;
  min_value: number;
  first_value: number;
  last_value: number;
}

export interface EnergyRecord {
  timestamp: string;
  start_datetime: string;
  end_datetime: string;
  value: number;
  first_value: number;
  last_value: number;
}

// Datapoint specific historical data structure in the response
export interface HistoricalDataRecord<T> { // T can be StatisticalRecord or EnergyRecord
  site_id: string;
  device_id: string;
  datapoint: string;
  model: string;
  records: T[];
}

/**
 * Fetches statistical historical data from TimescaleDB.
 * @param query The query parameters for the historical data.
 * @returns A promise that resolves with an array of statistical data records.
 */
export const fetchStatisticalData = async (
  query: HistoricalDataQuery
): Promise<HistoricalDataRecord<StatisticalRecord>[]> => {
  const endpoint = `${API_BASE_URL}/historical_data/timescaledb/statistical_data/query/`;
  try {
    const response = await axios.post<ApiResponse<HistoricalDataRecord<StatisticalRecord>[]>>(
      endpoint,
      query,
      {
        headers: {
          Authorization: `Bearer ${API_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );
    if (response.data && response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to fetch statistical data');
    }
  } catch (error) {
    console.error('Error fetching statistical data:', error);
    if (axios.isAxiosError(error)) {
      throw new Error(`API Error: ${error.response?.status} - ${error.message}`);
    } else {
      throw new Error('An unexpected error occurred while fetching statistical data.');
    }
  }
};

/**
 * Fetches energy historical data from TimescaleDB.
 * @param query The query parameters for the historical data.
 * @returns A promise that resolves with an array of energy data records.
 */
export const fetchEnergyData = async (
  query: HistoricalDataQuery
): Promise<HistoricalDataRecord<EnergyRecord>[]> => {
  const endpoint = `${API_BASE_URL}/historical_data/timescaledb/energy_data/query/`;
  try {
    const response = await axios.post<ApiResponse<HistoricalDataRecord<EnergyRecord>[]>>(
      endpoint,
      query,
      {
        headers: {
          Authorization: `Bearer ${API_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );
    if (response.data && response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to fetch energy data');
    }
  } catch (error) {
    console.error('Error fetching energy data:', error);
    if (axios.isAxiosError(error)) {
      throw new Error(`API Error: ${error.response?.status} - ${error.message}`);
    } else {
      throw new Error('An unexpected error occurred while fetching energy data.');
    }
  }
}; 