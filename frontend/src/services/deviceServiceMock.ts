// Device Service with Mock Data Support
import { apiClient } from '@/lib/api/enhancedApiClient';
import { mockDataService } from '@/lib/services/mockDataService';

// Re-export the proper types from the original service
export type { 
  DeviceDetails, 
  DeviceRelation, 
  Zone,
  DeviceDatapoint 
} from '../services/deviceService';

// Mock device interface for internal use
interface MockDevice {
  id: string;
  name: string;
  type: string;
  status: string;
  location?: string;
  power?: number;
  energy?: number;
}

// Mock SET building devices
const mockDevices: Record<string, MockDevice> = {
  'chiller_plant': {
    id: 'chiller_plant',
    name: 'Chiller Plant',
    type: 'chiller',
    status: 'online',
    location: 'B3 - Central Plant',
    power: 850,
  },
  'chiller-1': {
    id: 'chiller-1',
    name: 'Chiller 1 (450RT)',
    type: 'chiller',
    status: 'online',
    location: 'B3 - Chiller Room',
    power: 420,
  },
  'chiller-2': {
    id: 'chiller-2',
    name: 'Chiller 2 (450RT)',
    type: 'chiller',
    status: 'online',
    location: 'B3 - Chiller Room',
    power: 430,
  },
  'air_distribution_system': {
    id: 'air_distribution_system',
    name: 'Air Distribution System',
    type: 'hvac',
    status: 'online',
    location: 'Multiple Locations',
    power: 620,
  },
  'tower-a-ahu': {
    id: 'tower-a-ahu',
    name: 'Tower Building AHU',
    type: 'ahu',
    status: 'online',
    location: 'Tower Building - Multiple Floors',
    power: 280,
  },
  'tower-b-ahu': {
    id: 'tower-b-ahu',
    name: 'Podium Building AHU',
    type: 'ahu',
    status: 'online',
    location: 'Podium Building - Multiple Floors',
    power: 200,
  },
  'tower-c-ahu': {
    id: 'tower-c-ahu',
    name: 'Car Park Building AHU',
    type: 'ahu',
    status: 'online',
    location: 'Car Park Building - Multiple Floors',
    power: 140,
  },
  'light_and_power': {
    id: 'light_and_power',
    name: 'Lighting & Power',
    type: 'lighting',
    status: 'online',
    location: 'All Buildings',
    power: 340,
  },
  'data_center_and_others': {
    id: 'data_center_and_others',
    name: 'Data Center & Others',
    type: 'critical',
    status: 'online',
    location: 'Multiple Locations',
    power: 380,
  },
  'trading-servers': {
    id: 'trading-servers',
    name: 'Trading Floor Servers',
    type: 'critical',
    status: 'online',
    location: 'Tower Building - Trading Floor',
    power: 280,
  },
  'data-center': {
    id: 'data-center',
    name: 'Main Data Center',
    type: 'critical',
    status: 'online',
    location: 'Podium Building - Floor 5',
    power: 100,
  },
};


export const fetchDeviceById = async (deviceId: string, expandOptions?: string[]): Promise<DeviceDetails> => {
  if (apiClient.isUsingMockData()) {
    // Use the enhanced API client which handles mock data
    return await apiClient.get(`/devices/${deviceId}`, {
      expand: expandOptions?.join(',')
    });
  }
  
  // Real API call
  const response = await apiClient.get(`/devices/${deviceId}`, {
    expand: expandOptions?.join(',')
  });
  return response;
};

export const fetchChildDeviceRelationsById = async (deviceId: string): Promise<DeviceRelation[]> => {
  if (apiClient.isUsingMockData()) {
    // Use the enhanced API client which handles mock data
    return await apiClient.get(`/device_relations/child/${deviceId}`);
  }
  
  // Real API call
  const response = await apiClient.get(`/device_relations/child/${deviceId}`);
  return response;
};

export const fetchAllDevices = async (expandOptions?: string[]): Promise<DeviceDetails[]> => {
  if (apiClient.isUsingMockData()) {
    // Use the enhanced API client which handles mock data
    return await apiClient.get('/devices/', {
      expand: expandOptions?.join(',')
    });
  }
  
  // Real API call
  const response = await apiClient.get('/devices/', {
    expand: expandOptions?.join(',')
  });
  return response;
};