// Site Service with Mock Data Support
import { apiClient } from '@/lib/api/enhancedApiClient';
import { mockDataService } from '@/lib/services/mockDataService';

// Export existing types
export interface ApiResponse<T> {
  success: boolean;
  status: number;
  message: string;
  metadata: {
    timestamp: string;
    version: string;
  };
  data: T;
}

export interface SiteMetadata { 
  area?: number;
  timezone?: string;
  PFM_Target?: number;
  SBTi_Target?: number;
  co2_kg_per_kwh?: number;
  electricity_cost_per_kwh?: number;
}

export interface SiteData {
  id: string;
  name: string;
  metadata: SiteMetadata;
}

// Mock site data - SET Building
const mockSiteData: SiteData = {
  id: 'set',
  name: 'Stock Exchange of Thailand (SET)',
  metadata: {
    area: 108000, // Total built area in m²
    timezone: 'Asia/Bangkok',
    PFM_Target: 120,
    SBTi_Target: 100,
    co2_kg_per_kwh: 0.435, // Thailand grid emission factor
    electricity_cost_per_kwh: 4.63, // THB per kWh - MEA large commercial rate
  }
};

export const fetchSiteData = async (siteId: string): Promise<SiteData> => {
  // Check if we should use mock data
  if (apiClient.isUsingMockData()) {
    console.log('fetchSiteData - Using mock data');
    // Return mock data for SET site
    if (siteId === 'set') {
      return mockSiteData;
    }
    throw new Error(`Mock site data not found for ${siteId}`);
  }
  
  const endpoint = `/sites/${siteId}/`;
  const response = await apiClient.get<ApiResponse<SiteData>>(endpoint);
  
  if (response.success && response.data) {
    const siteData = response.data;
    const rawMetadata = siteData.metadata as any;
    const parsedMetadata: SiteMetadata = {};

    for (const key in rawMetadata) {
      if (Object.prototype.hasOwnProperty.call(rawMetadata, key)) {
        let value = rawMetadata[key];
        if (typeof value === 'string') {
          value = value.replace(/,$/, '');
          const numValue = parseFloat(value);
          if (!isNaN(numValue)) {
            (parsedMetadata as any)[key] = numValue;
          } else {
            (parsedMetadata as any)[key] = rawMetadata[key];
          }
        } else {
          (parsedMetadata as any)[key] = value;
        }
      }
    }
    siteData.metadata = parsedMetadata;
    return siteData;
  } else {
    throw new Error(response.message || `Failed to fetch site data for ${siteId}`);
  }
};