// Timescale Service with Mock Data Support
import { apiClient } from '@/lib/api/enhancedApiClient';
import { mockDataService } from '@/lib/services/mockDataService';

// Export existing types
export interface ApiResponse<T> {
  success: boolean;
  status: number;
  message: string;
  metadata: {
    timestamp: string;
    version: string;
  };
  data: T;
}

export interface HistoricalDataQuery {
  table_name: string;
  site_id: string;
  device_id: string | string[];
  datapoints: string[];
  start_timestamp: string;
  end_timestamp: string;
}

export interface StatisticalRecord {
  timestamp: string;
  start_datetime: string;
  end_datetime: string;
  mean_value: number;
  max_value: number;
  min_value: number;
  first_value: number;
  last_value: number;
}

export interface EnergyRecord {
  timestamp: string;
  start_datetime: string;
  end_datetime: string;
  value: number;
  first_value: number;
  last_value: number;
}

export interface HistoricalDataRecord<T> {
  site_id: string;
  device_id: string;
  datapoint: string;
  model: string;
  records: T[];
}

// Define base power values for SET building devices (kW)
const deviceBaseValues: Record<string, number> = {
  'main': 2800, // Main building load
  'Main': 2800, // Main building load (alternate)
  'tower_a': 1200, // Tower A
  'tower_b': 950,  // Tower B
  'tower_c': 450,  // Tower C (Education Center)
  'chiller_plant': 850,
  'air_distribution_system': 620,
  'light_and_power': 340,
  'data_center_and_others': 380,
};

// Get base value for device, with fallback logic
const getBaseValue = (deviceId: string): number => {
  // Direct match
  if (deviceBaseValues[deviceId]) {
    return deviceBaseValues[deviceId];
  }
  
  // Pattern matching for meter types
  if (deviceId.includes('CH-') || deviceId.includes('chiller')) {
    return 420; // Chiller equipment
  } else if (deviceId.includes('AHU') || deviceId.includes('ahu')) {
    return 45; // Air handling units
  } else if (deviceId.includes('LP') || deviceId.includes('lighting')) {
    return 25; // Lighting panels
  } else if (deviceId.includes('PP') || deviceId.includes('power')) {
    return 35; // Power panels
  } else if (deviceId.includes('MDB') || deviceId.includes('EMDB')) {
    return 280; // Main distribution boards
  } else if (deviceId.includes('tower_a_floor')) {
    return 85; // Tower A floor average
  } else if (deviceId.includes('tower_b_floor')) {
    return 65; // Tower B floor average
  } else if (deviceId.includes('tower_c_floor')) {
    return 45; // Tower C floor average
  }
  
  // Default fallback
  return 50;
};

// Mock data generator functions - SET Building specific
function generateMockStatisticalData(query: HistoricalDataQuery): HistoricalDataRecord<StatisticalRecord>[] {
  const startDate = new Date(query.start_timestamp);
  const endDate = new Date(query.end_timestamp);
  const deviceIds = Array.isArray(query.device_id) ? query.device_id : [query.device_id];
  
  // Generate data for all requested datapoints
  const results: HistoricalDataRecord<StatisticalRecord>[] = [];
  
  deviceIds.forEach(deviceId => {
    const baseValue = getBaseValue(deviceId);
    
    query.datapoints.forEach(datapoint => {
      const records: StatisticalRecord[] = [];
      
      // Check table type for appropriate data generation
      const is15MinData = query.table_name && query.table_name.includes('15min');
      const isHourlyData = query.table_name && query.table_name.includes('1hour');
      const isDayView = startDate.toDateString() === endDate.toDateString();
      
      if (is15MinData && isDayView) {
        // For 15-minute data (real-time power demand chart)
        const today = new Date();
        const requestedDate = new Date(startDate);
        const isToday = requestedDate.toDateString() === today.toDateString();
        const currentTime = today.getTime();
        
        // Generate 15-minute intervals for the day
        for (let hour = 0; hour < 24; hour++) {
          for (let minute = 0; minute < 60; minute += 15) {
            const timestamp = new Date(requestedDate);
            timestamp.setHours(hour, minute, 0, 0);
            
            // Skip future timestamps
            if (isToday && timestamp.getTime() > currentTime) break;
            
            const isWeekend = timestamp.getDay() === 0 || timestamp.getDay() === 6;
            const isTradingHour = hour >= 9 && hour <= 16 && !isWeekend;
            const isBusinessHour = hour >= 8 && hour <= 18 && !isWeekend;
            
            let multiplier = 1;
            if (isTradingHour) {
              multiplier = 1.4 + Math.sin(minute / 60 * Math.PI) * 0.1; // Vary during hour
            } else if (isBusinessHour) {
              multiplier = 1.2 + Math.sin(minute / 60 * Math.PI) * 0.05;
            } else if (hour < 6 || hour > 22) {
              multiplier = 0.5;
            } else {
              multiplier = 0.8;
            }
            
            // Add some randomness
            multiplier += (Math.random() - 0.5) * 0.1;
            
            // Generate value based on datapoint type
            let value = 0;
            if (datapoint === 'power') {
              value = baseValue * multiplier; // Already in kW
            } else if (datapoint === 'reactive_power') {
              value = baseValue * multiplier * 0.6;
            } else if (datapoint === 'power_factor') {
              value = 0.85 + Math.random() * 0.1;
            } else {
              value = baseValue * multiplier;
            }
            
            records.push({
              timestamp: timestamp.toISOString(),
              start_datetime: timestamp.toISOString(),
              end_datetime: new Date(timestamp.getTime() + 15 * 60 * 1000).toISOString(),
              mean_value: Math.max(0, value),
              max_value: Math.max(0, value * 1.02),
              min_value: Math.max(0, value * 0.98),
              first_value: Math.max(0, value * 0.99),
              last_value: Math.max(0, value * 1.01),
            });
          }
        }
      } else if (isHourlyData && isDayView) {
        // For day view, generate hourly data
        const today = new Date();
        const requestedDate = new Date(startDate);
        const isToday = requestedDate.toDateString() === today.toDateString();
        const currentHour = today.getHours();
        
        // For today, generate data up to current hour + 1 (or max 22)
        // For past dates (like yesterday), generate full 24 hours
        const maxHour = isToday ? Math.min(22, currentHour + 1) : 23;
        
        for (let hour = 0; hour <= maxHour; hour++) {
          const timestamp = new Date(requestedDate);
          timestamp.setHours(hour, 0, 0, 0);
          
          const isWeekend = timestamp.getDay() === 0 || timestamp.getDay() === 6;
          const isTradingHour = hour >= 9 && hour <= 16 && !isWeekend;
          const isBusinessHour = hour >= 8 && hour <= 18 && !isWeekend;
          
          let multiplier = 1;
          if (isTradingHour) {
            multiplier = 1.4; // Higher during trading hours
          } else if (isBusinessHour) {
            multiplier = 1.2;
          } else if (hour < 6 || hour > 22) {
            multiplier = 0.5; // Lower during night
          } else {
            multiplier = 0.8; // Normal hours
          }
          
          // Generate realistic values based on datapoint type
          let value = 0;
          if (datapoint === 'power') {
            value = baseValue * multiplier * 1000; // Convert kW to W
          } else if (datapoint === 'reactive_power') {
            value = baseValue * multiplier * 1000 * 0.6; // Reactive power
          } else if (datapoint === 'power_factor') {
            value = 0.85 + Math.random() * 0.1; // Power factor 0.85-0.95
          } else if (datapoint.startsWith('voltage_l') && datapoint.includes('l')) {
            // Line-to-line voltages (380V nominal)
            value = 380 + Math.random() * 10 - 5;
          } else if (datapoint.startsWith('voltage_l')) {
            // Line-to-neutral voltages (220V nominal)
            value = 220 + Math.random() * 10 - 5;
          } else if (datapoint.startsWith('current_l')) {
            // Phase currents (calculated from power and voltage)
            const powerValue = baseValue * multiplier * 1000;
            value = powerValue / (220 * Math.sqrt(3)) + Math.random() * 2 - 1; // I = P / (V√3)
          } else {
            value = baseValue * multiplier + (Math.random() - 0.5) * baseValue * 0.15;
          }
          
          records.push({
            timestamp: timestamp.toISOString(),
            start_datetime: timestamp.toISOString(),
            end_datetime: new Date(timestamp.getTime() + 60 * 60 * 1000).toISOString(),
            mean_value: Math.max(0, value),
            max_value: Math.max(0, value * 1.08),
            min_value: Math.max(0, value * 0.92),
            first_value: Math.max(0, value * 0.96),
            last_value: Math.max(0, value * 1.04),
          });
        }
      }
      
      // Add the generated records for this datapoint
      results.push({
        site_id: query.site_id,
        device_id: deviceId,
        datapoint: datapoint,
        model: 'statistical',
        records,
      });
    });
  });
  
  return results;
}

function generateMockEnergyData(query: HistoricalDataQuery): HistoricalDataRecord<EnergyRecord>[] {
  const startDate = new Date(query.start_timestamp);
  const endDate = new Date(query.end_timestamp);
  const deviceIds = Array.isArray(query.device_id) ? query.device_id : [query.device_id];
  
  // Use the same base value logic for consistency
  return deviceIds.map(deviceId => {
    const records: EnergyRecord[] = [];
    const currentDate = new Date(startDate);
    const baseValue = getBaseValue(deviceId); // Reuse power values for hourly energy
    
    // Check if this is a day view (same day start and end AND requesting hourly data)
    const isDayView = startDate.toDateString() === endDate.toDateString() && 
                      query.table_name && query.table_name.includes('1hour');
    
    if (isDayView) {
      // For day view, generate hourly data
      const today = new Date();
      const requestedDate = new Date(startDate);
      const isToday = requestedDate.toDateString() === today.toDateString();
      const currentHour = today.getHours();
      
      // For today, generate data up to current hour + 1 (or max 22)
      // For past dates (like yesterday), generate full 24 hours
      const maxHour = isToday ? Math.min(22, currentHour + 1) : 23;
      
      for (let hour = 0; hour <= maxHour; hour++) {
        const timestamp = new Date(requestedDate);
        timestamp.setHours(hour, 0, 0, 0);
        
        const isWeekend = timestamp.getDay() === 0 || timestamp.getDay() === 6;
        const isTradingHour = hour >= 9 && hour <= 16 && !isWeekend;
        const isBusinessHour = hour >= 8 && hour <= 18 && !isWeekend;
        
        let multiplier = 1;
        if (isTradingHour) {
          multiplier = 1.4; // Higher during trading hours
        } else if (isBusinessHour) {
          multiplier = 1.2;
        } else if (hour < 6 || hour > 22) {
          multiplier = 0.5; // Lower during night
        } else {
          multiplier = 0.8; // Normal hours
        }
        
        const value = baseValue * multiplier + (Math.random() - 0.5) * baseValue * 0.15;
        
        records.push({
          timestamp: timestamp.toISOString(),
          start_datetime: timestamp.toISOString(),
          end_datetime: new Date(timestamp.getTime() + 60 * 60 * 1000).toISOString(),
          value: Math.max(0, value),
          first_value: value * 0.95,
          last_value: value * 1.05,
        });
      }
    } else if (query.table_name && query.table_name.includes('1month')) {
      // For year view, generate monthly data
      while (currentDate <= endDate) {
        const monthStart = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        const monthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
        
        // Skip if month is beyond end date
        if (monthStart > endDate) break;
        
        // Calculate monthly consumption (avg daily * days in month)
        const daysInMonth = monthEnd.getDate();
        const monthlyValue = baseValue * 24 * daysInMonth * (0.9 + Math.random() * 0.2);
        
        records.push({
          timestamp: monthStart.toISOString(),
          start_datetime: monthStart.toISOString(),
          end_datetime: monthEnd.toISOString(),
          value: Math.max(0, monthlyValue),
          first_value: monthlyValue * 0.95,
          last_value: monthlyValue * 1.05,
        });
        
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    } else if (query.table_name && query.table_name.includes('1year')) {
      // For multi-year view, generate yearly data
      while (currentDate <= endDate) {
        const yearStart = new Date(currentDate.getFullYear(), 0, 1);
        const yearEnd = new Date(currentDate.getFullYear(), 11, 31);
        
        // Skip if year is beyond end date
        if (yearStart > endDate) break;
        
        // Calculate yearly consumption with growth trend
        const yearsSince2020 = currentDate.getFullYear() - 2020;
        const growthFactor = 1 + (yearsSince2020 * 0.05); // 5% yearly growth
        const yearlyValue = baseValue * 24 * 365 * growthFactor * (0.9 + Math.random() * 0.2);
        
        records.push({
          timestamp: yearStart.toISOString(),
          start_datetime: yearStart.toISOString(),
          end_datetime: yearEnd.toISOString(),
          value: Math.max(0, yearlyValue),
          first_value: yearlyValue * 0.95,
          last_value: yearlyValue * 1.05,
        });
        
        currentDate.setFullYear(currentDate.getFullYear() + 1);
      }
    } else if (query.table_name && query.table_name.includes('1day')) {
      // For daily energy data (used in monthly bar chart)
      while (currentDate <= endDate) {
        const dayOfWeek = currentDate.getDay();
        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
        
        // SET has lower consumption on weekends  
        const multiplier = isWeekend ? 0.65 : 1.0;
        const dailyValue = baseValue * 24 * multiplier + (Math.random() - 0.5) * baseValue * 2;
        
        records.push({
          timestamp: currentDate.toISOString(),
          start_datetime: currentDate.toISOString(),
          end_datetime: new Date(currentDate.getTime() + 24 * 60 * 60 * 1000).toISOString(),
          value: Math.max(0, dailyValue),
          first_value: dailyValue * 0.95,
          last_value: dailyValue * 1.05,
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }
    } else {
      // For other views, generate daily data
      while (currentDate <= endDate) {
        const dayOfWeek = currentDate.getDay();
        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
        
        // SET has lower consumption on weekends  
        const multiplier = isWeekend ? 0.65 : 1.0;
        const dailyValue = baseValue * 24 * multiplier + (Math.random() - 0.5) * baseValue * 2;
        
        records.push({
          timestamp: currentDate.toISOString(),
          start_datetime: currentDate.toISOString(),
          end_datetime: new Date(currentDate.getTime() + 24 * 60 * 60 * 1000).toISOString(),
          value: Math.max(0, dailyValue),
          first_value: dailyValue * 0.95,
          last_value: dailyValue * 1.05,
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }
    
    return {
      site_id: query.site_id,
      device_id: deviceId,
      datapoint: query.datapoints[0],
      model: 'energy',
      records,
    };
  });
}

// Updated service functions
export const fetchStatisticalData = async (
  query: HistoricalDataQuery
): Promise<HistoricalDataRecord<StatisticalRecord>[]> => {
  // Check if we should use mock data
  if (apiClient.isUsingMockData()) {
    console.log('fetchStatisticalData - Using mock data');
    return generateMockStatisticalData(query);
  }
  
  console.log('fetchStatisticalData - Making real API call to:', `/historical_data/timescaledb/statistical_data/query/`);
  const endpoint = `/historical_data/timescaledb/statistical_data/query/`;
  const response = await apiClient.post<ApiResponse<HistoricalDataRecord<StatisticalRecord>[]>>(
    endpoint,
    query
  );
  
  if (response.success) {
    return response.data;
  } else {
    throw new Error(response.message || 'Failed to fetch statistical data');
  }
};

export const fetchEnergyData = async (
  query: HistoricalDataQuery
): Promise<HistoricalDataRecord<EnergyRecord>[]> => {
  // Check if we should use mock data
  if (apiClient.isUsingMockData()) {
    console.log('fetchEnergyData - Using mock data');
    return generateMockEnergyData(query);
  }
  
  console.log('fetchEnergyData - Making real API call to:', `/historical_data/timescaledb/energy_data/query/`);
  const endpoint = `/historical_data/timescaledb/energy_data/query/`;
  const response = await apiClient.post<ApiResponse<HistoricalDataRecord<EnergyRecord>[]>>(
    endpoint,
    query
  );
  
  if (response.success) {
    return response.data;
  } else {
    throw new Error(response.message || 'Failed to fetch energy data');
  }
};