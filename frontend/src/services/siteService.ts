import axios from 'axios';

// Access the API base URL from environment variables (Vite specific)
const API_BASE_URL = import.meta.env.VITE_DJANGO_API_URL;
// Define the token for site services. Assuming it might be the same as timescale for now.
// In a real scenario, this might be a different token or handled by a shared config.
const API_TOKEN = import.meta.env.VITE_DJANGO_API_ACESS_TOKEN; 

export interface ApiResponse<T> {
  success: boolean;
  status: number;
  message: string;
  metadata: {
    timestamp: string;
    version: string;
  };
  data: T;
  }

export interface SiteMetadata { 
  area?: number;
  timezone?: string;
  PFM_Target?: number;
  SBTi_Target?: number;
  co2_kg_per_kwh?: number;
  electricity_cost_per_kwh?: number;
  // Add other metadata fields if needed
}

export interface SiteData {
  id: string;
  name: string;
  metadata: SiteMetadata;
}

/**
 * Fetches specific site data including its metadata.
 * @param siteId The ID of the site to fetch.
 * @returns A promise that resolves with the site data.
 */
export const fetchSiteData = async (siteId: string): Promise<SiteData> => {
  const endpoint = `${API_BASE_URL}/sites/${siteId}/`; 
  try {
    const response = await axios.get<ApiResponse<SiteData>>(
      endpoint,
      {
        headers: {
          Authorization: `Bearer ${API_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (response.data && response.data.success && response.data.data) {
      const siteData = response.data.data;
      const rawMetadata = siteData.metadata as any; 
      const parsedMetadata: SiteMetadata = {};

      for (const key in rawMetadata) {
        if (Object.prototype.hasOwnProperty.call(rawMetadata, key)) {
          let value = rawMetadata[key];
          if (typeof value === 'string') {
            value = value.replace(/,$/, ''); 
            const numValue = parseFloat(value);
            if (!isNaN(numValue)) {
              (parsedMetadata as any)[key] = numValue;
            } else {
              (parsedMetadata as any)[key] = rawMetadata[key]; 
            }
          } else {
            (parsedMetadata as any)[key] = value; 
          }
        }
      }
      siteData.metadata = parsedMetadata;
      return siteData;
    } else {
      throw new Error(response.data.message || `Failed to fetch site data for ${siteId}`);
    }
  } catch (error) {
    console.error(`Error fetching site data for ${siteId}:`, error);
    if (axios.isAxiosError(error)) {
      throw new Error(`API Error: ${error.response?.status} - ${error.message}`);
    } else {
      throw new Error('An unexpected error occurred while fetching site data.');
    }
  }
};
