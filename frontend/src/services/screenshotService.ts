import html2canvas from 'html2canvas';

export interface ScreenshotConfig {
  selector?: string; // CSS selector for specific element
  fullPage?: boolean; // Capture full page or just viewport
  scale?: number; // Quality scale (default 2 for retina)
  backgroundColor?: string; // Background color
  removeElements?: string[]; // CSS selectors of elements to hide
  waitForSelector?: string; // Wait for element to appear
  delay?: number; // Additional delay in ms
}

export interface CapturedScreenshot {
  dataUrl: string;
  width: number;
  height: number;
  timestamp: Date;
  page: string;
  feature?: string;
}

class ScreenshotService {
  private screenshots: Map<string, CapturedScreenshot> = new Map();

  /**
   * Capture a screenshot of the current page or specific element
   */
  async captureScreenshot(
    pageName: string,
    feature?: string,
    config: ScreenshotConfig = {}
  ): Promise<CapturedScreenshot> {
    const {
      selector = 'body',
      fullPage = false,
      scale = 2,
      backgroundColor = '#ffffff',
      removeElements = [],
      waitForSelector,
      delay = 500
    } = config;

    // Hide elements that shouldn't be in screenshots
    const hiddenElements: HTMLElement[] = [];
    const defaultHideSelectors = [
      '.mock-data-indicator', // Hide mock data indicator
      '.auto-refresh-indicator', // Hide auto-refresh indicator
      '.dev-tools', // Hide any dev tools
      ...removeElements
    ];

    defaultHideSelectors.forEach(sel => {
      document.querySelectorAll<HTMLElement>(sel).forEach(el => {
        hiddenElements.push(el);
        el.style.display = 'none';
      });
    });

    try {
      // Wait for specific element if needed
      if (waitForSelector) {
        await this.waitForElement(waitForSelector, 5000);
      }

      // Additional delay for animations to complete
      await new Promise(resolve => setTimeout(resolve, delay));

      // Get the element to capture
      const element = document.querySelector(selector) as HTMLElement;
      if (!element) {
        throw new Error(`Element with selector "${selector}" not found`);
      }

      // Prepare element for screenshot
      const originalOverflow = document.body.style.overflow;
      if (fullPage) {
        document.body.style.overflow = 'visible';
      }

      // Capture the screenshot
      const canvas = await html2canvas(element, {
        scale,
        backgroundColor,
        useCORS: true,
        logging: false,
        windowWidth: element.scrollWidth,
        windowHeight: fullPage ? element.scrollHeight : element.clientHeight,
      });

      // Restore original state
      document.body.style.overflow = originalOverflow;

      // Create screenshot object
      const screenshot: CapturedScreenshot = {
        dataUrl: canvas.toDataURL('image/png'),
        width: canvas.width,
        height: canvas.height,
        timestamp: new Date(),
        page: pageName,
        feature
      };

      // Store screenshot
      const key = feature ? `${pageName}-${feature}` : pageName;
      this.screenshots.set(key, screenshot);

      return screenshot;
    } finally {
      // Restore hidden elements
      hiddenElements.forEach(el => {
        el.style.display = '';
      });
    }
  }

  /**
   * Capture screenshots for all training slides
   */
  async captureTrainingScreenshots(): Promise<Map<string, CapturedScreenshot>> {
    const captures: Array<{
      route: string;
      page: string;
      feature: string;
      config?: ScreenshotConfig;
    }> = [
      // Dashboard screenshots
      {
        route: '/dashboard',
        page: 'dashboard',
        feature: 'overview',
        config: { delay: 2000 } // Wait for charts to load
      },
      {
        route: '/dashboard',
        page: 'dashboard',
        feature: 'load-profile',
        config: { selector: '.building-load-chart', delay: 1000 }
      },
      {
        route: '/dashboard',
        page: 'dashboard',
        feature: 'towers',
        config: { selector: '.tower-overview-section', delay: 1000 }
      },

      // Analytics screenshots
      {
        route: '/analytics',
        page: 'analytics',
        feature: 'consumption',
        config: { delay: 2000 }
      },
      {
        route: '/analytics',
        page: 'analytics',
        feature: 'comparison',
        config: { delay: 2000 }
      },

      // Meters screenshots
      {
        route: '/meters',
        page: 'meters',
        feature: 'management',
        config: { delay: 1500 }
      },
      {
        route: '/meters?view=tower-a',
        page: 'meters',
        feature: 'tower-view',
        config: { delay: 1500 }
      },

      // Alarms screenshots
      {
        route: '/alarms',
        page: 'alarms',
        feature: 'active',
        config: { delay: 1000 }
      },
      {
        route: '/alarms',
        page: 'alarms',
        feature: 'rules',
        config: { selector: '.alarm-rules-tab', delay: 1000 }
      },

      // Meter Comparison
      {
        route: '/compare',
        page: 'compare',
        feature: 'selection',
        config: { delay: 1000 }
      },

      // Meter Diagram
      {
        route: '/meter-diagram',
        page: 'diagram',
        feature: 'view',
        config: { delay: 2000 }
      },

      // Settings
      {
        route: '/settings',
        page: 'settings',
        feature: 'users',
        config: { delay: 1000 }
      },
    ];

    const results = new Map<string, CapturedScreenshot>();

    for (const capture of captures) {
      try {
        // Navigate to the page
        window.location.href = capture.route;
        
        // Wait for navigation and page load
        await new Promise(resolve => {
          const checkReady = setInterval(() => {
            if (document.readyState === 'complete') {
              clearInterval(checkReady);
              resolve(true);
            }
          }, 100);
        });

        // Capture the screenshot
        const screenshot = await this.captureScreenshot(
          capture.page,
          capture.feature,
          capture.config
        );

        results.set(`${capture.page}-${capture.feature}`, screenshot);
      } catch (error) {
        console.error(`Failed to capture ${capture.page}-${capture.feature}:`, error);
      }
    }

    return results;
  }

  /**
   * Get a stored screenshot
   */
  getScreenshot(page: string, feature?: string): CapturedScreenshot | undefined {
    const key = feature ? `${page}-${feature}` : page;
    return this.screenshots.get(key);
  }

  /**
   * Get all stored screenshots
   */
  getAllScreenshots(): Map<string, CapturedScreenshot> {
    return new Map(this.screenshots);
  }

  /**
   * Export screenshots as a downloadable zip file
   */
  async exportScreenshots(): Promise<void> {
    // This would require a zip library like JSZip
    // For now, we'll just download them individually
    this.screenshots.forEach((screenshot, key) => {
      const link = document.createElement('a');
      link.download = `screenshot-${key}-${Date.now()}.png`;
      link.href = screenshot.dataUrl;
      link.click();
    });
  }

  /**
   * Clear all stored screenshots
   */
  clearScreenshots(): void {
    this.screenshots.clear();
  }

  /**
   * Wait for an element to appear
   */
  private waitForElement(selector: string, timeout: number): Promise<void> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const checkElement = () => {
        if (document.querySelector(selector)) {
          resolve();
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`Timeout waiting for element: ${selector}`));
        } else {
          requestAnimationFrame(checkElement);
        }
      };
      
      checkElement();
    });
  }
}

// Export singleton instance
export const screenshotService = new ScreenshotService();