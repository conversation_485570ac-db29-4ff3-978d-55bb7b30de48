#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to check for potential contrast issues in the codebase
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Patterns that indicate potential contrast issues
const problematicPatterns = [
  // White text on light backgrounds
  /(?:className|class)=["`'].*?bg-(?:white|gray-(?:50|100|200|300|400)).*?text-white.*?["`']/g,
  /(?:className|class)=["`'].*?text-white.*?bg-(?:white|gray-(?:50|100|200|300|400)).*?["`']/g,
  
  // White text on light colors
  /(?:className|class)=["`'].*?bg-(?:blue|green|purple|red|orange|yellow|amber)-(?:50|100|200|300|400).*?text-white.*?["`']/g,
  /(?:className|class)=["`'].*?text-white.*?bg-(?:blue|green|purple|red|orange|yellow|amber)-(?:50|100|200|300|400).*?["`']/g,
  
  // Problematic -500 colors with white text
  /(?:className|class)=["`'].*?bg-(?:yellow|orange|amber)-500.*?text-white.*?["`']/g,
  /(?:className|class)=["`'].*?text-white.*?bg-(?:yellow|orange|amber)-500.*?["`']/g,
  
  // Style attributes with white on light backgrounds
  /style=\{[^}]*backgroundColor:\s*['"`]#(?:f[0-9a-f]{2}|e[0-9a-f]{2}|d[0-9a-f]{2})[^}]*color:\s*['"`](?:#fff|#ffffff|white)['"`][^}]*\}/g,
  
  // RGB white on light backgrounds
  /style=\{[^}]*backgroundColor:\s*['"`]rgb\((?:2[0-9]{2}|1[5-9][0-9])[^}]*color:\s*['"`](?:#fff|#ffffff|white|rgb\(255,\s*255,\s*255\))['"`][^}]*\}/g,
];

// Files to check
const filesToCheck = [
  'src/**/*.tsx',
  'src/**/*.jsx',
  'src/**/*.ts',
  'src/**/*.js',
];

let issuesFound = 0;

console.log('🔍 Checking for contrast issues...\n');

filesToCheck.forEach(pattern => {
  const files = glob.sync(pattern, { 
    ignore: ['**/node_modules/**', '**/dist/**', '**/build/**'] 
  });
  
  files.forEach(file => {
    const content = fs.readFileSync(file, 'utf-8');
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      problematicPatterns.forEach(pattern => {
        const matches = line.match(pattern);
        if (matches) {
          issuesFound++;
          console.log(`❌ Potential contrast issue in ${file}:${index + 1}`);
          console.log(`   ${line.trim()}`);
          console.log(`   Pattern: ${matches[0]}\n`);
        }
      });
    });
  });
});

if (issuesFound === 0) {
  console.log('✅ No contrast issues found!');
  process.exit(0);
} else {
  console.log(`\n❌ Found ${issuesFound} potential contrast issues.`);
  console.log('\n💡 Tips:');
  console.log('- Use dark backgrounds (600-900) with white text');
  console.log('- Use light backgrounds (50-300) with dark text (gray-700 or darker)');
  console.log('- Yellow/amber backgrounds need dark text, not white');
  console.log('- Import and use COLOR_COMBINATIONS from @/lib/constants/colors');
  process.exit(1);
}