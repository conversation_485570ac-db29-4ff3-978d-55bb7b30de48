#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Run contrast validation on staged files
echo "🎨 Checking for color contrast issues..."

# Get staged .tsx and .ts files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(tsx?|jsx?)$')

if [ -n "$STAGED_FILES" ]; then
  # Run ESLint on staged files
  npx eslint $STAGED_FILES --rule 'local/no-poor-contrast: error'
  
  if [ $? -ne 0 ]; then
    echo "❌ Color contrast issues found! Please fix them before committing."
    echo "💡 Tip: Use COLOR_COMBINATIONS from @/lib/constants/colors"
    exit 1
  fi
fi

# Run comprehensive contrast check
npm run lint:contrast

if [ $? -ne 0 ]; then
  echo "❌ Additional contrast issues detected! Please fix them before committing."
  exit 1
fi

echo "✅ No contrast issues found!"

# Run other checks
npm run lint-staged