# Screenshot Capture Guide for Training Slides

## Overview
This guide explains how to capture real screenshots for training slides without affecting production.

## Prerequisites
1. Ensure the development server is running: `npm run dev`
2. Enable mock data in the application settings
3. Have a modern browser (Chrome recommended)

## Capturing Screenshots

### Method 1: Automated Capture (Recommended)
1. Navigate to http://localhost:5173/screenshot-capture
2. Review the pages to be captured
3. Click "Start Capture" to begin automated capture
4. Screenshots will be downloaded automatically

### Method 2: Manual Capture
1. Navigate to any page in the application
2. Use the floating camera button (bottom-right corner)
3. Click to capture the current view
4. Download the screenshot

### Method 3: Browser Developer Tools
1. Open Chrome DevTools (F12)
2. Click the device toolbar icon (Ctrl+Shift+M)
3. Set viewport to 1920x1080
4. Use the capture screenshot option (three dots menu)

## Screenshot Specifications
- Resolution: 1920x1080 (Full HD)
- Format: PNG
- Background: Include actual UI elements
- Mock Data: Ensure realistic data is displayed

## Saving Screenshots
1. Create directory: `frontend/public/images/training/`
2. Name format: `{page}-{feature}.png`
   - Example: `dashboard-overview.png`
   - Example: `analytics-consumption.png`

## Integration Steps
1. Capture all required screenshots
2. Save to the training images directory
3. Update slide components to reference real images
4. Test in development environment
5. Commit changes when satisfied

## Important Notes
- Always use mock data for screenshots
- Ensure no sensitive information is visible
- Capture at consistent times for data consistency
- Review all screenshots before committing

## Troubleshooting
- If screenshots are blank: Wait for page to fully load
- If data looks wrong: Verify mock data is enabled
- If layout is broken: Check viewport size is 1920x1080