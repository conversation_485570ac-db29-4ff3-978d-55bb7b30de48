/**
 * ESLint rule to prevent poor text contrast
 * Detects white or light text on light backgrounds
 */

module.exports = {
  meta: {
    type: 'problem',
    docs: {
      description: 'Disallow white or light text on light backgrounds',
      category: 'Accessibility',
      recommended: true,
    },
    messages: {
      poorContrast: 'Poor contrast: {{text}} on {{background}} may be hard to read',
      useColorConstant: 'Use COLOR_COMBINATIONS from @/lib/constants/colors instead of manual color classes',
    },
    fixable: 'code',
  },

  create(context) {
    const forbiddenCombos = [
      // White text on light backgrounds
      { text: /text-white/, backgrounds: /bg-(white|gray-(50|100|200|300|400))/ },
      { text: /text-gray-(100|200|300)/, backgrounds: /bg-(white|gray-(50|100|200|300))/ },
      
      // White text on light color backgrounds
      { text: /text-white/, backgrounds: /bg-(blue|green|purple|red|orange|yellow|amber|indigo)-(50|100|200|300|400)/ },
      
      // Light text on yellow/amber
      { text: /text-(white|gray-(100|200|300))/, backgrounds: /bg-(yellow|amber)-(100|200|300|400|500)/ },
      
      // Dark text on dark backgrounds
      { text: /text-gray-(800|900)/, backgrounds: /bg-(gray-(800|900)|black)/ },
      
      // Catch potential rendering issues with -500 colors
      { text: /text-white/, backgrounds: /bg-(green|yellow|orange|amber)-500/ },
    ];

    return {
      JSXAttribute(node) {
        if (node.name.name !== 'className' || !node.value) return;
        
        const classValue = node.value.type === 'Literal' 
          ? node.value.value 
          : node.value.type === 'JSXExpressionContainer' && node.value.expression.type === 'TemplateLiteral'
          ? node.value.expression.quasis.map(q => q.value.raw).join('')
          : '';
          
        if (!classValue) return;

        // Check for forbidden combinations
        for (const combo of forbiddenCombos) {
          const hasText = combo.text.test(classValue);
          const hasBg = combo.backgrounds.test(classValue);
          
          if (hasText && hasBg) {
            const textMatch = classValue.match(combo.text)?.[0];
            const bgMatch = classValue.match(combo.backgrounds)?.[0];
            
            context.report({
              node,
              messageId: 'poorContrast',
              data: {
                text: textMatch,
                background: bgMatch,
              },
            });
          }
        }

        // Suggest using predefined color combinations
        if (/bg-\w+-\d+.*text-\w+/.test(classValue) && !classValue.includes('hover:')) {
          context.report({
            node,
            messageId: 'useColorConstant',
          });
        }
      },
    };
  },
};