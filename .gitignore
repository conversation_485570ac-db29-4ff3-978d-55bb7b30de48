# Django / Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
!frontend/src/lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.venv
venv/
ENV/
.python-version
db.sqlite3
db.sqlite3-journal
*.log
local_settings.py
media/

# Django static files
staticfiles/
static/admin/
static/rest_framework/

# React / TypeScript / Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.
coverage/
.nyc_output
frontend/dist/
frontend/build/
frontend/.cache/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store
.AppleDouble
.LSOverride
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
nosetests.xml
coverage.xml
*.cover

# Logs and databases
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Deployment
.vercel
.netlify

# Supabase
supabase/.branches/
supabase/.temp/
