# Database configuration
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=postgres
DB_HOST=127.0.0.1
DB_PORT=54322

# Supabase local configuration
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
# Some scripts expect this variable name
SUPABASE_SERVICE_KEY=$SUPABASE_SERVICE_ROLE_KEY

# Django secret key
SECRET_KEY=change-me

# Postal settings
POSTAL_BASE_URL=http://localhost:5002
POSTAL_API_KEY=your-postal-api-key
POSTAL_SERVER_ID=your-server-id

# Express email API server
PORT=3001

# Niagara integration
NIAGARA_BASE_URL=http://your-niagara-server:8080
NIAGARA_USERNAME=your-username
NIAGARA_PASSWORD=your-password
