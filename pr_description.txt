Add frontend/src/lib directory to repository

## Description
This PR adds the previously missing frontend/src/lib directory to the repository. This directory contains essential code for the frontend application including:

- API interfaces and client code
- Configuration files for buildings, meters, and UI
- Constants and context providers
- Custom React hooks
- Utility functions and formatters

## Why was it missing?
The directory was being excluded by the .gitignore file which had a 'lib/' entry. I've updated the .gitignore to specifically exclude the frontend/src/lib directory from being ignored.

## Impact
This should resolve the issues team members were experiencing when trying to run the code from GitHub, as these essential frontend components were missing.

## Testing
Please verify that the frontend application now works correctly after pulling these changes.
