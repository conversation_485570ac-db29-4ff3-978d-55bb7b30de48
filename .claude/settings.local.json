{"permissions": {"allow": ["Bash(git fetch:*)", "Bash(git checkout:*)", "Bash(git pull:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(git add:*)", "Bash(npm run typecheck:*)", "Bash(npm run lint)", "Bash(ls:*)", "Bash(npm ls:*)", "<PERSON><PERSON>(cat:*)", "Bash(pgrep:*)", "Bash(kill:*)", "Bash(grep:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(timeout 15 npm run dev)", "Bash(rg:*)", "Bash(npx tsc:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(claude --version)", "Bash(npm update:*)", "<PERSON><PERSON>(diff:*)", "Bash(find:*)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -A 5 -B 5 \"useEffect\\(\" --type tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -g \"*.tsx\" \"useEffect\\([^,]+,\\s*\\[[^\\]]*\\]\\)\")", "<PERSON><PERSON>(open:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(sed:*)", "Bash(git commit:*)", "Bash(git push:*)", "<PERSON><PERSON>(true)", "Bash(npm run type-check:*)", "Bash(for:*)", "Bash(do echo \"=== $file ===\")", "Bash(done)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -B2 -A2 \"text-white\" --type-add 'tsx:*.tsx' -t tsx)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(# Move root level documentation files (except README.md)\nmv CLAUDE.md docs/\nmv CLAUDE.local.md docs/\nmv EMAIL_SETUP.md docs/setup/\nmv plan.md docs/plans/\nmv screenshot-plan.md docs/plans/\n\n# Move frontend documentation files\nmv frontend/ALARMS_DESIGN_UPDATE.md docs/frontend/\nmv frontend/ALARMS_TEST_PLAN.md docs/frontend/\nmv frontend/COLOR_CONTRAST_GUIDE.md docs/frontend/\nmv frontend/MOCK_DATA_GUIDE.md docs/frontend/\nmv frontend/MOCK_DATA_IMPLEMENTATION.md docs/frontend/\nmv frontend/UI_EXCELLENCE_PLAN.md docs/frontend/\n\necho \"Files moved successfully!\")", "Bash(tree:*)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"bg-(white|gray-(50|100|200|300|400)).*text-white\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"bg-(blue|green|yellow|orange|red|purple|indigo|pink)-(50|100|200|300|400).*text-white\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"bg-white.*text-white\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"backgroundColor.*#(fff|FFF|ffffff|FFFFFF).*color.*#(fff|FFF|ffffff|FFFFFF)\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -B2 -A2 \"text-white.*bg-(white|gray-[1234]00|blue-[1234]00|green-[1234]00)\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"className=.*text-white.*bg-(white|gray-[123]|blue-[123]|green-[123])\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"(bg-gray-[234]00|bg-blue-[234]00|bg-green-[234]00).*text-white\" src/components/settings/meters/ --glob \"*.tsx\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"Badge.*className.*bg-(gray-[234]|blue-[234]|green-[234])00.*text-white\" --glob \"*.tsx\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"Alarm Management\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"AltoTech|Help\" src/components/training/PresentationViewer.tsx --glob \"*.tsx\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"AltoTech\" --glob \"*.tsx\" --glob \"*.ts\")", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -B5 -A5 \"bottom-4\" src/components/training/PresentationViewer.tsx)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -n \"AltoTech|Help\" src/components/training/slides/ --glob \"*.tsx\")", "Bash(node:*)", "Bash(npm install:*)", "Bash(docker build:*)", "Bash(cp:*)", "Bash(docker logs:*)", "Bash(/opt/homebrew/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-darwin/rg -i \"9:00.*22:00\" --type-add 'web:*.{ts,tsx,js,jsx}' --type web -C 2)", "Bash(git reset:*)", "Bash(git restore:*)", "Bash(npm run lint:*)"], "deny": []}}