const express = require('express');
const cors = require('cors');
const axios = require('axios');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Postal configuration
const POSTAL_BASE_URL = process.env.POSTAL_BASE_URL || 'http://localhost:5002';
const POSTAL_API_KEY = process.env.POSTAL_API_KEY || 'your-postal-api-key'; // You'll need to replace this with your actual API key
const POSTAL_SERVER_ID = process.env.POSTAL_SERVER_ID || 'your-server-id'; // You'll need to replace this with your actual server ID

// Test email endpoint
app.post('/api/email/test', async (req, res) => {
  try {
    const { to, subject, text } = req.body;
    
    if (!to || !subject || !text) {
      return res.status(400).json({ 
        success: false, 
        error: 'Missing required fields (to, subject, text)' 
      });
    }

    // Send email using Postal API
    const response = await axios.post(`${POSTAL_BASE_URL}/api/v1/send/message`, {
      to: [to],
      from: '<EMAIL>',
      subject,
      plain_body: text,
      html_body: `
        <div style="font-family: system-ui, -apple-system, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border-radius: 8px; border: 1px solid #eee;">
          <h2 style="color: #1a237e; margin-top: 0;">Alto CERO EMS Notification</h2>
          <p style="color: #333; line-height: 1.5;">${text}</p>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 12px;">
            <p>This is a test email from the Alto CERO Energy Management System.</p>
            <p>Sent at: ${new Date().toLocaleString()}</p>
          </div>
        </div>
      `
    }, {
      headers: {
        'X-Server-API-Key': POSTAL_API_KEY,
        'Content-Type': 'application/json'
      }
    });

    console.log('Test email sent:', response.data.message_id);
    res.status(200).json({ 
      success: true, 
      messageId: response.data.message_id 
    });
  } catch (error) {
    console.error('Error sending email:', error.response?.data || error.message);
    res.status(500).json({ 
      success: false, 
      error: error.response?.data?.error || error.message 
    });
  }
});

// Email settings endpoint
app.post('/api/email/settings', (req, res) => {
  // In a real app, this would save settings to a database
  console.log('Email settings updated:', req.body);
  res.status(200).json({ success: true });
});

// Start server
app.listen(PORT, () => {
  console.log(`Email server running on port ${PORT}`);
  console.log(`Postal web interface available at ${POSTAL_BASE_URL}`);
});
